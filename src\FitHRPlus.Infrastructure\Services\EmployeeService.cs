using FitHRPlus.Application.Common;
using FitHRPlus.Application.DTOs.Employees;
using FitHRPlus.Application.Interfaces;
using FitHRPlus.Domain.Entities;
using FitHRPlus.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FitHRPlus.Infrastructure.Services
{
    /// <summary>
    /// Employee service implementation
    /// تنفيذ خدمة الموظفين
    /// </summary>
    public class EmployeeService : IEmployeeService
    {
        private readonly FitHRContext _context;
        private readonly ILogger<EmployeeService> _logger;

        public EmployeeService(FitHRContext context, ILogger<EmployeeService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<ServiceResult<EmployeeListResponseDto>> GetEmployeesAsync(EmployeeListRequestDto request)
        {
            try
            {
                var query = _context.Employees
                    .Include(e => e.Company)
                    .Include(e => e.Department)
                    .Include(e => e.Position)
                    .Include(e => e.Manager)
                    .AsQueryable();

                // Apply filters
                if (request.CompanyId.HasValue)
                {
                    query = query.Where(e => e.CompanyId == request.CompanyId.Value);
                }

                if (request.DepartmentId.HasValue)
                {
                    query = query.Where(e => e.DepartmentId == request.DepartmentId.Value);
                }

                if (request.PositionId.HasValue)
                {
                    query = query.Where(e => e.PositionId == request.PositionId.Value);
                }

                if (request.ManagerId.HasValue)
                {
                    query = query.Where(e => e.ReportsTo == request.ManagerId.Value);
                }

                if (!string.IsNullOrEmpty(request.EmploymentStatus))
                {
                    // Use IsActive as employment status for now
                    if (request.EmploymentStatus == "Active")
                        query = query.Where(e => e.IsActive);
                    else if (request.EmploymentStatus == "Inactive")
                        query = query.Where(e => !e.IsActive);
                }

                if (!string.IsNullOrEmpty(request.EmploymentType))
                {
                    query = query.Where(e => e.EmploymentType.ToString() == request.EmploymentType);
                }

                if (!string.IsNullOrEmpty(request.Gender))
                {
                    query = query.Where(e => e.Gender.ToString() == request.Gender);
                }

                if (request.IsActive.HasValue)
                {
                    query = query.Where(e => e.IsActive == request.IsActive.Value);
                }

                if (request.HireDateFrom.HasValue)
                {
                    query = query.Where(e => e.HireDate >= request.HireDateFrom.Value);
                }

                if (request.HireDateTo.HasValue)
                {
                    query = query.Where(e => e.HireDate <= request.HireDateTo.Value);
                }

                if (request.SalaryFrom.HasValue)
                {
                    query = query.Where(e => e.BaseSalary >= request.SalaryFrom.Value);
                }

                if (request.SalaryTo.HasValue)
                {
                    query = query.Where(e => e.BaseSalary <= request.SalaryTo.Value);
                }

                // Search filter
                if (!string.IsNullOrEmpty(request.SearchTerm))
                {
                    var searchTerm = request.SearchTerm.ToLower();
                    query = query.Where(e =>
                        e.FirstName.ToLower().Contains(searchTerm) ||
                        e.LastName.ToLower().Contains(searchTerm) ||
                        e.FirstNameAr.ToLower().Contains(searchTerm) ||
                        e.LastNameAr.ToLower().Contains(searchTerm) ||
                        e.EmployeeCode.ToLower().Contains(searchTerm) ||
                        (e.Email != null && e.Email.ToLower().Contains(searchTerm)) ||
                        (e.Phone != null && e.Phone.ToLower().Contains(searchTerm)));
                }

                // Include/exclude inactive and terminated
                if (!request.IncludeInactive)
                {
                    query = query.Where(e => e.IsActive);
                }

                if (!request.IncludeTerminated)
                {
                    query = query.Where(e => e.TerminationDate == null);
                }

                // Get total count before pagination
                var totalCount = await query.CountAsync();

                // Apply sorting
                query = ApplySorting(query, request.SortBy, request.SortDirection);

                // Apply pagination
                var employees = await query
                    .Skip((request.Page - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .Select(e => new EmployeeSummaryDto
                    {
                        Id = e.Id,
                        EmployeeNumber = e.EmployeeCode,
                        FullName = $"{e.FirstName} {e.LastName}",
                        FullNameAr = $"{e.FirstNameAr} {e.LastNameAr}",
                        Email = e.Email,
                        Phone = e.Phone,
                        DepartmentName = e.Department != null ? e.Department.Name : null,
                        PositionTitle = e.Position != null ? e.Position.Title : null,
                        ManagerName = e.Manager != null ? $"{e.Manager.FirstName} {e.Manager.LastName}" : null,
                        HireDate = e.HireDate,
                        EmploymentStatus = e.IsActive ? "Active" : "Inactive",
                        EmploymentType = e.EmploymentType.ToString(),
                        ProfilePicture = e.ProfilePicture,
                        IsActive = e.IsActive,
                        Age = e.DateOfBirth.HasValue ? CalculateAge(e.DateOfBirth.Value) : null,
                        YearsOfService = CalculateYearsOfService(e.HireDate)
                    })
                    .ToListAsync();

                // Calculate pagination info
                var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);
                var hasNextPage = request.Page < totalPages;
                var hasPreviousPage = request.Page > 1;

                // Get filter summary
                var filterSummary = await GetFilterSummaryAsync(request.CompanyId);

                var response = new EmployeeListResponseDto
                {
                    Employees = employees,
                    TotalCount = totalCount,
                    CurrentPage = request.Page,
                    PageSize = request.PageSize,
                    TotalPages = totalPages,
                    HasNextPage = hasNextPage,
                    HasPreviousPage = hasPreviousPage,
                    FilterSummary = filterSummary
                };

                return ServiceResult<EmployeeListResponseDto>.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting employee list");
                return ServiceResult<EmployeeListResponseDto>.Failure(
                    "An error occurred while retrieving employees",
                    "حدث خطأ أثناء استرداد الموظفين",
                    "EMPLOYEE_LIST_ERROR");
            }
        }

        public async Task<ServiceResult<EmployeeDto>> GetEmployeeByIdAsync(Guid id)
        {
            try
            {
                var employee = await _context.Employees
                    .Include(e => e.Company)
                    .Include(e => e.Department)
                    .Include(e => e.Position)
                    .Include(e => e.Manager)
                    .FirstOrDefaultAsync(e => e.Id == id);

                if (employee == null)
                {
                    return ServiceResult<EmployeeDto>.Failure(
                        "Employee not found",
                        "الموظف غير موجود",
                        "EMPLOYEE_NOT_FOUND");
                }

                var employeeDto = MapToEmployeeDto(employee);
                return ServiceResult<EmployeeDto>.Success(employeeDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting employee by ID: {EmployeeId}", id);
                return ServiceResult<EmployeeDto>.Failure(
                    "An error occurred while retrieving employee",
                    "حدث خطأ أثناء استرداد الموظف",
                    "EMPLOYEE_GET_ERROR");
            }
        }

        public async Task<ServiceResult<EmployeeDto>> GetEmployeeByNumberAsync(string employeeNumber, Guid companyId)
        {
            try
            {
                var employee = await _context.Employees
                    .Include(e => e.Company)
                    .Include(e => e.Department)
                    .Include(e => e.Position)
                    .Include(e => e.Manager)
                    .FirstOrDefaultAsync(e => e.EmployeeCode == employeeNumber && e.CompanyId == companyId);

                if (employee == null)
                {
                    return ServiceResult<EmployeeDto>.Failure(
                        "Employee not found",
                        "الموظف غير موجود",
                        "EMPLOYEE_NOT_FOUND");
                }

                var employeeDto = MapToEmployeeDto(employee);
                return ServiceResult<EmployeeDto>.Success(employeeDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting employee by number: {EmployeeNumber}", employeeNumber);
                return ServiceResult<EmployeeDto>.Failure(
                    "An error occurred while retrieving employee",
                    "حدث خطأ أثناء استرداد الموظف",
                    "EMPLOYEE_GET_ERROR");
            }
        }

        // TODO: Implement remaining methods
        public Task<ServiceResult<EmployeeDto>> CreateEmployeeAsync(EmployeeDto employeeDto, Guid createdBy)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<EmployeeDto>> UpdateEmployeeAsync(Guid id, EmployeeDto employeeDto, Guid updatedBy)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<bool>> DeleteEmployeeAsync(Guid id, Guid deletedBy)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<bool>> SetEmployeeActiveStatusAsync(Guid id, bool isActive, Guid updatedBy)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<bool>> TerminateEmployeeAsync(Guid id, DateTime terminationDate, string reason, Guid updatedBy)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<List<EmployeeSummaryDto>>> GetEmployeesByDepartmentAsync(Guid departmentId, bool includeInactive = false)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<List<EmployeeSummaryDto>>> GetEmployeesByManagerAsync(Guid managerId, bool includeInactive = false)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<List<EmployeeSummaryDto>>> GetEmployeesByPositionAsync(Guid positionId, bool includeInactive = false)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<EmployeeStatisticsDto>> GetEmployeeStatisticsAsync(Guid? companyId = null, Guid? departmentId = null)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<List<EmployeeSummaryDto>>> SearchEmployeesAsync(string searchTerm, Guid? companyId = null, int limit = 10)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<bool>> ValidateEmployeeNumberAsync(string employeeNumber, Guid companyId, Guid? excludeEmployeeId = null)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<string>> GenerateEmployeeNumberAsync(Guid companyId, Guid? departmentId = null)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<EmployeeImportResultDto>> ImportEmployeesAsync(byte[] fileData, Guid companyId, Guid importedBy)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<EmployeeExportResultDto>> ExportEmployeesAsync(EmployeeListRequestDto request)
        {
            throw new NotImplementedException();
        }

        // Helper methods
        private static IQueryable<Employee> ApplySorting(IQueryable<Employee> query, string? sortBy, string? sortDirection)
        {
            var isDescending = sortDirection?.ToLower() == "desc";

            return sortBy?.ToLower() switch
            {
                "firstname" => isDescending ? query.OrderByDescending(e => e.FirstName) : query.OrderBy(e => e.FirstName),
                "lastname" => isDescending ? query.OrderByDescending(e => e.LastName) : query.OrderBy(e => e.LastName),
                "employeenumber" => isDescending ? query.OrderByDescending(e => e.EmployeeCode) : query.OrderBy(e => e.EmployeeCode),
                "email" => isDescending ? query.OrderByDescending(e => e.Email) : query.OrderBy(e => e.Email),
                "hiredate" => isDescending ? query.OrderByDescending(e => e.HireDate) : query.OrderBy(e => e.HireDate),
                "department" => isDescending ? query.OrderByDescending(e => e.Department!.Name) : query.OrderBy(e => e.Department!.Name),
                "position" => isDescending ? query.OrderByDescending(e => e.Position!.Title) : query.OrderBy(e => e.Position!.Title),
                "salary" => isDescending ? query.OrderByDescending(e => e.BaseSalary) : query.OrderBy(e => e.BaseSalary),
                _ => query.OrderBy(e => e.FirstName)
            };
        }

        private static int CalculateAge(DateTime dateOfBirth)
        {
            var today = DateTime.Today;
            var age = today.Year - dateOfBirth.Year;
            if (dateOfBirth.Date > today.AddYears(-age)) age--;
            return age;
        }

        private static int CalculateYearsOfService(DateTime hireDate)
        {
            var today = DateTime.Today;
            var years = today.Year - hireDate.Year;
            if (hireDate.Date > today.AddYears(-years)) years--;
            return Math.Max(0, years);
        }

        private async Task<EmployeeFilterSummaryDto> GetFilterSummaryAsync(Guid? companyId)
        {
            var query = _context.Employees.AsQueryable();

            if (companyId.HasValue)
            {
                query = query.Where(e => e.CompanyId == companyId.Value);
            }

            var summary = new EmployeeFilterSummaryDto
            {
                TotalActive = await query.CountAsync(e => e.IsActive),
                TotalInactive = await query.CountAsync(e => !e.IsActive),
                TotalTerminated = await query.CountAsync(e => e.TerminationDate != null),
                TotalMale = await query.CountAsync(e => e.Gender == Domain.Enums.Gender.Male),
                TotalFemale = await query.CountAsync(e => e.Gender == Domain.Enums.Gender.Female)
            };

            // Calculate averages
            var employees = await query.Where(e => e.IsActive).ToListAsync();
            if (employees.Any())
            {
                summary.AverageAge = employees
                    .Where(e => e.DateOfBirth.HasValue)
                    .Average(e => CalculateAge(e.DateOfBirth!.Value));

                summary.AverageYearsOfService = employees
                    .Average(e => CalculateYearsOfService(e.HireDate));
            }

            // Department breakdown
            summary.DepartmentBreakdown = await query
                .Where(e => e.IsActive && e.Department != null)
                .GroupBy(e => new { e.DepartmentId, e.Department!.Name })
                .Select(g => new DepartmentSummaryDto
                {
                    Id = g.Key.DepartmentId,
                    Name = g.Key.Name,
                    EmployeeCount = g.Count()
                })
                .ToListAsync();

            return summary;
        }

        private static EmployeeDto MapToEmployeeDto(Employee employee)
        {
            return new EmployeeDto
            {
                Id = employee.Id,
                EmployeeNumber = employee.EmployeeCode,
                FirstName = employee.FirstName,
                LastName = employee.LastName,
                FirstNameAr = employee.FirstNameAr,
                LastNameAr = employee.LastNameAr,
                Email = employee.Email,
                Phone = employee.Phone,
                Mobile = null, // Not available in current entity
                NationalId = employee.NationalId,
                PassportNumber = employee.PassportNumber,
                DateOfBirth = employee.DateOfBirth,
                Gender = employee.Gender?.ToString(),
                MaritalStatus = employee.MaritalStatus?.ToString(),
                Nationality = employee.Nationality,
                Address = employee.Address,
                City = employee.City,
                Country = null, // Not available in current entity
                HireDate = employee.HireDate,
                TerminationDate = employee.TerminationDate,
                EmploymentStatus = employee.IsActive ? "Active" : "Inactive",
                EmploymentType = employee.EmploymentType.ToString(),
                CompanyId = employee.CompanyId,
                CompanyName = employee.Company?.Name ?? string.Empty,
                DepartmentId = employee.DepartmentId,
                DepartmentName = employee.Department?.Name,
                PositionId = employee.PositionId,
                PositionTitle = employee.Position?.Title,
                ManagerId = employee.ReportsTo,
                ManagerName = employee.Manager != null ? $"{employee.Manager.FirstName} {employee.Manager.LastName}" : null,
                BasicSalary = employee.BaseSalary,
                ProfilePicture = employee.ProfilePicture,
                EmergencyContactName = null, // Not available in current entity
                EmergencyContactPhone = null, // Not available in current entity
                EmergencyContactRelationship = null, // Not available in current entity
                Notes = null, // Not available in current entity
                IsActive = employee.IsActive,
                CreatedAt = employee.CreatedAt,
                UpdatedAt = employee.UpdatedAt,
                CreatedBy = employee.CreatedBy ?? Guid.Empty,
                UpdatedBy = employee.UpdatedBy ?? Guid.Empty
            };
        }
    }
}
