@model FitHRPlus.Web.Models.Shared.AdvancedFiltersViewModel

<!-- Advanced Filters Component -->
<div class="advanced-filters-container">
    <div class="filters-header">
        <div class="filters-title">
            <i class="bi bi-funnel"></i>
            <span>الفلاتر المتقدمة</span>
        </div>
        <div class="filters-actions">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="resetFilters()">
                <i class="bi bi-arrow-clockwise"></i>
                إعادة تعيين
            </button>
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="saveFilterPreset()">
                <i class="bi bi-bookmark"></i>
                حفظ الفلتر
            </button>
            <button type="button" class="btn btn-sm btn-primary" onclick="applyFilters()">
                <i class="bi bi-check"></i>
                تطبيق
            </button>
        </div>
    </div>
    
    <div class="filters-content">
        <div class="row">
            <!-- Date Range Filter -->
            @if (Model.ShowDateFilter)
            {
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="filter-group">
                        <label class="filter-label">الفترة الزمنية</label>
                        <div class="date-range-picker">
                            <div class="date-range-presets">
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setDateRange(&quot;today&quot;)">اليوم</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setDateRange(&quot;week&quot;)">هذا الأسبوع</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setDateRange(&quot;month&quot;)">هذا الشهر</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setDateRange(&quot;year&quot;)">هذا العام</button>
                            </div>
                            <div class="date-inputs">
                                <input type="date" class="form-control" id="startDate" placeholder="من تاريخ">
                                <span class="date-separator">إلى</span>
                                <input type="date" class="form-control" id="endDate" placeholder="إلى تاريخ">
                            </div>
                        </div>
                    </div>
                </div>
            }
            
            <!-- Department Filter -->
            @if (Model.ShowDepartmentFilter)
            {
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="filter-group">
                        <label class="filter-label">القسم</label>
                        <select class="form-select filter-select" id="departmentFilter" multiple>
                            <option value="">جميع الأقسام</option>
                            @if (Model.Departments?.Any() == true)
                            {
                                @foreach (var dept in Model.Departments)
                                {
                                    <option value="@dept.Id">@dept.Name</option>
                                }
                            }
                        </select>
                    </div>
                </div>
            }
            
            <!-- Employee Filter -->
            @if (Model.ShowEmployeeFilter)
            {
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="filter-group">
                        <label class="filter-label">الموظف</label>
                        <select class="form-select filter-select" id="employeeFilter" multiple>
                            <option value="">جميع الموظفين</option>
                            @if (Model.Employees?.Any() == true)
                            {
                                @foreach (var emp in Model.Employees)
                                {
                                    <option value="@emp.Id">@emp.Name</option>
                                }
                            }
                        </select>
                    </div>
                </div>
            }
            
            <!-- Status Filter -->
            @if (Model.ShowStatusFilter)
            {
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="filter-group">
                        <label class="filter-label">الحالة</label>
                        <div class="status-filter-options">
                            @if (Model.StatusOptions?.Any() == true)
                            {
                                @foreach (var status in Model.StatusOptions)
                                {
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="@status.Value" id="<EMAIL>">
                                        <label class="form-check-label" for="<EMAIL>">
                                            <span class="status-indicator <EMAIL>()"></span>
                                            @status.Text
                                        </label>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>
            }
            
            <!-- Priority Filter -->
            @if (Model.ShowPriorityFilter)
            {
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="filter-group">
                        <label class="filter-label">الأولوية</label>
                        <div class="priority-filter-options">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="high" id="priority_high">
                                <label class="form-check-label" for="priority_high">
                                    <span class="priority-indicator priority-high"></span>
                                    عالية
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="medium" id="priority_medium">
                                <label class="form-check-label" for="priority_medium">
                                    <span class="priority-indicator priority-medium"></span>
                                    متوسطة
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="low" id="priority_low">
                                <label class="form-check-label" for="priority_low">
                                    <span class="priority-indicator priority-low"></span>
                                    منخفضة
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            }
            
            <!-- Amount Range Filter -->
            @if (Model.ShowAmountFilter)
            {
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="filter-group">
                        <label class="filter-label">المبلغ</label>
                        <div class="amount-range-filter">
                            <div class="range-inputs">
                                <input type="number" class="form-control" id="minAmount" placeholder="الحد الأدنى">
                                <span class="range-separator">-</span>
                                <input type="number" class="form-control" id="maxAmount" placeholder="الحد الأقصى">
                            </div>
                            <div class="range-slider">
                                <input type="range" class="form-range" id="amountRange" min="0" max="100000" step="1000">
                                <div class="range-values">
                                    <span id="rangeMin">0</span>
                                    <span id="rangeMax">100,000</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
        
        <!-- Custom Filters -->
        @if (Model.CustomFilters?.Any() == true)
        {
            <div class="custom-filters-section">
                <h6 class="custom-filters-title">فلاتر مخصصة</h6>
                <div class="row">
                    @foreach (var filter in Model.CustomFilters)
                    {
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="filter-group">
                                <label class="filter-label">@filter.Label</label>
                                @switch (filter.Type)
                                {
                                    case "select":
                                        <select class="form-select filter-select" id="@filter.Id" @(filter.Multiple ? "multiple" : "")>
                                            @if (filter.Options?.Any() == true)
                                            {
                                                @foreach (var option in filter.Options)
                                                {
                                                    <option value="@option.Value">@option.Text</option>
                                                }
                                            }
                                        </select>
                                        break;
                                    case "text":
                                        <input type="text" class="form-control" id="@filter.Id" placeholder="@filter.Placeholder">
                                        break;
                                    case "number":
                                        <input type="number" class="form-control" id="@filter.Id" placeholder="@filter.Placeholder">
                                        break;
                                    case "checkbox":
                                        <div class="checkbox-filter-options">
                                            @if (filter.Options?.Any() == true)
                                            {
                                                @foreach (var option in filter.Options)
                                                {
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" value="@option.Value" id="@($"{filter.Id}_{option.Value}")">
                                                        <label class="form-check-label" for="@($"{filter.Id}_{option.Value}")">
                                                            @option.Text
                                                        </label>
                                                    </div>
                                                }
                                            }
                                        </div>
                                        break;
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>
        }
    </div>
    
    <!-- Active Filters Display -->
    <div class="active-filters" id="activeFilters" style="display: none;">
        <div class="active-filters-header">
            <span>الفلاتر النشطة:</span>
            <button type="button" class="btn btn-sm btn-ghost" onclick="clearAllFilters()">
                <i class="bi bi-x"></i>
                مسح الكل
            </button>
        </div>
        <div class="active-filters-list" id="activeFiltersList">
            <!-- Active filters will be displayed here -->
        </div>
    </div>
    
    <!-- Filter Presets -->
    <div class="filter-presets" id="filterPresets">
        <div class="presets-header">
            <span>الفلاتر المحفوظة:</span>
        </div>
        <div class="presets-list" id="presetsList">
            <!-- Saved presets will be displayed here -->
        </div>
    </div>
</div>

<style>
.advanced-filters-container {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    margin-bottom: 20px;
    overflow: hidden;
}

.filters-header {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 16px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.filters-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #495057;
}

.filters-actions {
    display: flex;
    gap: 8px;
}

.filters-content {
    padding: 20px;
}

.filter-group {
    margin-bottom: 16px;
}

.filter-label {
    display: block;
    font-size: 13px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 6px;
}

.filter-select {
    font-size: 14px;
}

.date-range-picker {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.date-range-presets {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.date-inputs {
    display: flex;
    align-items: center;
    gap: 8px;
}

.date-separator,
.range-separator {
    font-size: 12px;
    color: #6c757d;
    white-space: nowrap;
}

.status-filter-options,
.priority-filter-options,
.checkbox-filter-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-check-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    cursor: pointer;
}

.status-indicator,
.priority-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.status-active,
.status-indicator.status-approved {
    background: #28a745;
}

.status-indicator.status-pending {
    background: #ffc107;
}

.status-indicator.status-rejected,
.status-indicator.status-cancelled {
    background: #dc3545;
}

.priority-indicator.priority-high {
    background: #dc3545;
}

.priority-indicator.priority-medium {
    background: #ffc107;
}

.priority-indicator.priority-low {
    background: #28a745;
}

.amount-range-filter {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.range-inputs {
    display: flex;
    align-items: center;
    gap: 8px;
}

.range-slider {
    position: relative;
}

.range-values {
    display: flex;
    justify-content: between;
    font-size: 11px;
    color: #6c757d;
    margin-top: 4px;
}

.custom-filters-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.custom-filters-title {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 16px;
}

.active-filters {
    padding: 12px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.active-filters-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 13px;
    font-weight: 500;
    color: #495057;
}

.active-filters-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.active-filter-tag {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: #e9ecef;
    color: #495057;
    padding: 4px 8px;
    border-radius: 16px;
    font-size: 12px;
}

.active-filter-tag .remove-filter {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 14px;
    cursor: pointer;
    padding: 0;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.active-filter-tag .remove-filter:hover {
    background: #dc3545;
    color: white;
}

.filter-presets {
    padding: 12px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.presets-header {
    font-size: 13px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
}

.presets-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.preset-item {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: #fff;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 4px 8px;
    border-radius: 16px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.preset-item:hover {
    background: #e9ecef;
}

.preset-item .delete-preset {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 12px;
    cursor: pointer;
    padding: 0;
    width: 14px;
    height: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.preset-item .delete-preset:hover {
    background: #dc3545;
    color: white;
}

@@media (max-width: 768px) {
    .filters-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .filters-actions {
        justify-content: center;
    }

    .date-range-presets {
        justify-content: center;
    }

    .active-filters-header {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }
}
</style>

<script>
$(document).ready(function() {
    initializeAdvancedFilters();
});

let currentFilters = {};
let filterPresets = [];

function initializeAdvancedFilters() {
    loadFilterPresets();
    setupFilterEvents();
    loadSavedFilters();

    // Initialize multi-select dropdowns
    $('.filter-select[multiple]').each(function() {
        // You can integrate with a library like Select2 here
        // $(this).select2();
    });

    // Initialize range sliders
    $('#amountRange').on('input', function() {
        updateRangeValues();
    });

    updateRangeValues();
}

function setupFilterEvents() {
    // Date range presets
    $('.date-range-presets .btn').on('click', function() {
        const range = $(this).data('range') || $(this).text().toLowerCase();
        setDateRange(range);
    });

    // Filter inputs change events
    $('.filter-select, .form-control, .form-check-input').on('change', function() {
        updateActiveFilters();
    });

    // Range inputs
    $('#minAmount, #maxAmount').on('input', function() {
        updateAmountRange();
    });
}

function setDateRange(range) {
    const today = new Date();
    let startDate, endDate;

    switch(range) {
        case 'today':
            startDate = endDate = today;
            break;
        case 'week':
            startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay());
            endDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay() + 6);
            break;
        case 'month':
            startDate = new Date(today.getFullYear(), today.getMonth(), 1);
            endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            break;
        case 'year':
            startDate = new Date(today.getFullYear(), 0, 1);
            endDate = new Date(today.getFullYear(), 11, 31);
            break;
    }

    if (startDate && endDate) {
        $('#startDate').val(formatDate(startDate));
        $('#endDate').val(formatDate(endDate));
        updateActiveFilters();
    }

    // Update active button
    $('.date-range-presets .btn').removeClass('btn-primary').addClass('btn-outline-secondary');
    $(event.target).removeClass('btn-outline-secondary').addClass('btn-primary');
}

function formatDate(date) {
    return date.toISOString().split('T')[0];
}

function updateRangeValues() {
    const rangeValue = $('#amountRange').val();
    const max = $('#amountRange').attr('max');

    $('#rangeMin').text('0');
    $('#rangeMax').text(formatNumber(rangeValue));

    $('#minAmount').val(0);
    $('#maxAmount').val(rangeValue);
}

function updateAmountRange() {
    const minAmount = $('#minAmount').val() || 0;
    const maxAmount = $('#maxAmount').val() || 100000;

    $('#amountRange').val(maxAmount);
    updateRangeValues();
}

function formatNumber(num) {
    return new Intl.NumberFormat('ar-SA').format(num);
}

function updateActiveFilters() {
    currentFilters = {};

    // Date filters
    const startDate = $('#startDate').val();
    const endDate = $('#endDate').val();
    if (startDate || endDate) {
        currentFilters.dateRange = {
            start: startDate,
            end: endDate,
            label: `${startDate || 'البداية'} - ${endDate || 'النهاية'}`
        };
    }

    // Department filter
    const departments = $('#departmentFilter').val();
    if (departments && departments.length > 0) {
        const departmentNames = departments.map(id =>
            $(`#departmentFilter option[value="${id}"]`).text()
        );
        currentFilters.departments = {
            values: departments,
            label: `الأقسام: ${departmentNames.join(', ')}`
        };
    }

    // Employee filter
    const employees = $('#employeeFilter').val();
    if (employees && employees.length > 0) {
        const employeeNames = employees.map(id =>
            $(`#employeeFilter option[value="${id}"]`).text()
        );
        currentFilters.employees = {
            values: employees,
            label: `الموظفين: ${employeeNames.join(', ')}`
        };
    }

    // Status filters
    const statusFilters = [];
    $('input[id^="status_"]:checked').each(function() {
        const statusValue = $(this).val();
        const statusLabel = $(this).next('label').text().trim();
        statusFilters.push({ value: statusValue, label: statusLabel });
    });
    if (statusFilters.length > 0) {
        currentFilters.status = {
            values: statusFilters.map(s => s.value),
            label: `الحالة: ${statusFilters.map(s => s.label).join(', ')}`
        };
    }

    // Priority filters
    const priorityFilters = [];
    $('input[id^="priority_"]:checked').each(function() {
        const priorityValue = $(this).val();
        const priorityLabel = $(this).next('label').text().trim();
        priorityFilters.push({ value: priorityValue, label: priorityLabel });
    });
    if (priorityFilters.length > 0) {
        currentFilters.priority = {
            values: priorityFilters.map(p => p.value),
            label: `الأولوية: ${priorityFilters.map(p => p.label).join(', ')}`
        };
    }

    // Amount range
    const minAmount = $('#minAmount').val();
    const maxAmount = $('#maxAmount').val();
    if (minAmount || maxAmount) {
        currentFilters.amountRange = {
            min: minAmount,
            max: maxAmount,
            label: `المبلغ: ${formatNumber(minAmount || 0)} - ${formatNumber(maxAmount || 'غير محدد')}`
        };
    }

    displayActiveFilters();
}

function displayActiveFilters() {
    const activeFiltersContainer = $('#activeFilters');
    const activeFiltersList = $('#activeFiltersList');

    activeFiltersList.empty();

    if (Object.keys(currentFilters).length === 0) {
        activeFiltersContainer.hide();
        return;
    }

    activeFiltersContainer.show();

    Object.keys(currentFilters).forEach(filterKey => {
        const filter = currentFilters[filterKey];
        const filterTag = $(`
            <div class="active-filter-tag">
                <span>${filter.label}</span>
                <button class="remove-filter" onclick="removeFilter(&quot;${filterKey}&quot;)">
                    <i class="bi bi-x"></i>
                </button>
            </div>
        `);
        activeFiltersList.append(filterTag);
    });
}

function removeFilter(filterKey) {
    delete currentFilters[filterKey];

    // Clear the corresponding form elements
    switch(filterKey) {
        case 'dateRange':
            $('#startDate, #endDate').val('');
            $('.date-range-presets .btn').removeClass('btn-primary').addClass('btn-outline-secondary');
            break;
        case 'departments':
            $('#departmentFilter').val([]);
            break;
        case 'employees':
            $('#employeeFilter').val([]);
            break;
        case 'status':
            $('input[id^="status_"]').prop('checked', false);
            break;
        case 'priority':
            $('input[id^="priority_"]').prop('checked', false);
            break;
        case 'amountRange':
            $('#minAmount, #maxAmount').val('');
            $('#amountRange').val($('#amountRange').attr('max'));
            updateRangeValues();
            break;
    }

    displayActiveFilters();
    applyFilters();
}

function clearAllFilters() {
    currentFilters = {};

    // Clear all form elements
    $('.filter-select').val([]);
    $('.form-control').val('');
    $('.form-check-input').prop('checked', false);
    $('.date-range-presets .btn').removeClass('btn-primary').addClass('btn-outline-secondary');

    // Reset range slider
    $('#amountRange').val($('#amountRange').attr('max'));
    updateRangeValues();

    displayActiveFilters();
    applyFilters();
}

function resetFilters() {
    clearAllFilters();
}

function applyFilters() {
    // Trigger custom event with current filters
    $(document).trigger('filtersApplied', [currentFilters]);

    // You can also make an AJAX call here to update the data
    // updateDataWithFilters(currentFilters);

    // Save current filters to localStorage
    localStorage.setItem('currentFilters', JSON.stringify(currentFilters));
}

function saveFilterPreset() {
    if (Object.keys(currentFilters).length === 0) {
        alert('لا توجد فلاتر لحفظها');
        return;
    }

    const presetName = prompt('أدخل اسم الفلتر المحفوظ:');
    if (!presetName) return;

    const preset = {
        id: Date.now().toString(),
        name: presetName,
        filters: { ...currentFilters },
        createdAt: new Date().toISOString()
    };

    filterPresets.push(preset);
    saveFilterPresets();
    displayFilterPresets();
}

function loadFilterPreset(presetId) {
    const preset = filterPresets.find(p => p.id === presetId);
    if (!preset) return;

    // Clear current filters
    clearAllFilters();

    // Apply preset filters
    currentFilters = { ...preset.filters };

    // Update form elements based on preset
    Object.keys(currentFilters).forEach(filterKey => {
        const filter = currentFilters[filterKey];

        switch(filterKey) {
            case 'dateRange':
                $('#startDate').val(filter.start);
                $('#endDate').val(filter.end);
                break;
            case 'departments':
                $('#departmentFilter').val(filter.values);
                break;
            case 'employees':
                $('#employeeFilter').val(filter.values);
                break;
            case 'status':
                filter.values.forEach(value => {
                    $(`#status_${value}`).prop('checked', true);
                });
                break;
            case 'priority':
                filter.values.forEach(value => {
                    $(`#priority_${value}`).prop('checked', true);
                });
                break;
            case 'amountRange':
                $('#minAmount').val(filter.min);
                $('#maxAmount').val(filter.max);
                updateAmountRange();
                break;
        }
    });

    displayActiveFilters();
    applyFilters();
}

function deleteFilterPreset(presetId) {
    if (confirm('هل تريد حذف هذا الفلتر المحفوظ؟')) {
        filterPresets = filterPresets.filter(p => p.id !== presetId);
        saveFilterPresets();
        displayFilterPresets();
    }
}

function loadFilterPresets() {
    const saved = localStorage.getItem('filterPresets');
    if (saved) {
        filterPresets = JSON.parse(saved);
        displayFilterPresets();
    }
}

function saveFilterPresets() {
    localStorage.setItem('filterPresets', JSON.stringify(filterPresets));
}

function displayFilterPresets() {
    const presetsList = $('#presetsList');
    presetsList.empty();

    if (filterPresets.length === 0) {
        $('#filterPresets').hide();
        return;
    }

    $('#filterPresets').show();

    filterPresets.forEach(preset => {
        const presetItem = $(`
            <div class="preset-item" onclick="loadFilterPreset(&quot;${preset.id}&quot;)">
                <span>${preset.name}</span>
                <button class="delete-preset" onclick="event.stopPropagation(); deleteFilterPreset(&quot;${preset.id}&quot;)">
                    <i class="bi bi-x"></i>
                </button>
            </div>
        `);
        presetsList.append(presetItem);
    });
}

function loadSavedFilters() {
    const saved = localStorage.getItem('currentFilters');
    if (saved) {
        try {
            const savedFilters = JSON.parse(saved);
            // You can choose to auto-apply saved filters or not
            // currentFilters = savedFilters;
            // displayActiveFilters();
        } catch (e) {
            console.error('Error loading saved filters:', e);
        }
    }
}

// Expose functions globally
window.setDateRange = setDateRange;
window.removeFilter = removeFilter;
window.clearAllFilters = clearAllFilters;
window.resetFilters = resetFilters;
window.applyFilters = applyFilters;
window.saveFilterPreset = saveFilterPreset;
window.loadFilterPreset = loadFilterPreset;
window.deleteFilterPreset = deleteFilterPreset;
</script>
