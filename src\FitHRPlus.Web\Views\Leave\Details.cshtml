@model FitHRPlus.Web.Models.Leave.LeaveRequestDetailsViewModel
@{
    ViewData["Title"] = "تفاصيل طلب الإجازة";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        تفاصيل طلب الإجازة
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Request Header -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <h5>طلب إجازة رقم: @Model.Id</h5>
                            <p class="text-muted">تم إنشاء الطلب في: @Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</p>
                        </div>
                        <div class="col-md-4 text-end">
                            @switch (Model.Status)
                            {
                                case "Pending":
                                    <span class="badge bg-warning fs-6">في انتظار الموافقة</span>
                                    break;
                                case "Approved":
                                    <span class="badge bg-success fs-6">موافق عليه</span>
                                    break;
                                case "Rejected":
                                    <span class="badge bg-danger fs-6">مرفوض</span>
                                    break;
                                case "Cancelled":
                                    <span class="badge bg-secondary fs-6">ملغي</span>
                                    break;
                                default:
                                    <span class="badge bg-light text-dark fs-6">@Model.Status</span>
                                    break;
                            }
                        </div>
                    </div>

                    <!-- Employee Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-user me-2"></i>
                                معلومات الموظف
                            </h5>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>اسم الموظف:</strong>
                            <p>@Model.EmployeeName</p>
                        </div>
                        <div class="col-md-4">
                            <strong>رقم الموظف:</strong>
                            <p>@Model.EmployeeNumber</p>
                        </div>
                        <div class="col-md-4">
                            <strong>القسم:</strong>
                            <p>@Model.DepartmentName</p>
                        </div>
                    </div>

                    <!-- Leave Information -->
                    <div class="row mb-4 mt-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-calendar me-2"></i>
                                تفاصيل الإجازة
                            </h5>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>نوع الإجازة:</strong>
                            <p>@Model.LeaveTypeName</p>
                        </div>
                        <div class="col-md-4">
                            <strong>تاريخ البداية:</strong>
                            <p>@Model.StartDate.ToString("dd/MM/yyyy")</p>
                        </div>
                        <div class="col-md-4">
                            <strong>تاريخ النهاية:</strong>
                            <p>@Model.EndDate.ToString("dd/MM/yyyy")</p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>عدد الأيام:</strong>
                            <p>@Model.TotalDays يوم</p>
                        </div>
                        <div class="col-md-4">
                            <strong>تاريخ العودة المتوقع:</strong>
                            <p>@Model.EndDate.AddDays(1).ToString("dd/MM/yyyy")</p>
                        </div>
                        <div class="col-md-4">
                            <strong>الحالة:</strong>
                            <p>@(Model.Status switch 
                            {
                                "Pending" => "في انتظار الموافقة",
                                "Approved" => "موافق عليه",
                                "Rejected" => "مرفوض",
                                "Cancelled" => "ملغي",
                                _ => Model.Status
                            })</p>
                        </div>
                    </div>

                    <!-- Reason -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <strong>سبب الإجازة:</strong>
                            <p class="border p-3 bg-light">@Model.Reason</p>
                        </div>
                    </div>

                    <!-- Approval Information -->
                    @if (!string.IsNullOrEmpty(Model.ApprovedByName) || !string.IsNullOrEmpty(Model.ApprovalNotes))
                    {
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-check-circle me-2"></i>
                                    معلومات الموافقة
                                </h5>
                            </div>
                        </div>

                        <div class="row mb-3">
                            @if (!string.IsNullOrEmpty(Model.ApprovedByName))
                            {
                                <div class="col-md-6">
                                    <strong>تمت الموافقة بواسطة:</strong>
                                    <p>@Model.ApprovedByName</p>
                                </div>
                            }
                            @if (Model.ApprovedAt.HasValue)
                            {
                                <div class="col-md-6">
                                    <strong>تاريخ الموافقة:</strong>
                                    <p>@Model.ApprovedAt.Value.ToString("dd/MM/yyyy HH:mm")</p>
                                </div>
                            }
                        </div>

                        @if (!string.IsNullOrEmpty(Model.ApprovalNotes))
                        {
                            <div class="row mb-3">
                                <div class="col-12">
                                    <strong>ملاحظات الموافقة:</strong>
                                    <p class="border p-3 bg-light">@Model.ApprovalNotes</p>
                                </div>
                            </div>
                        }
                    }

                    <!-- Leave Balance Information -->
                    <div class="row mb-4 mt-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-chart-pie me-2"></i>
                                رصيد الإجازات
                            </h5>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h6>الرصيد السنوي</h6>
                                    <h4>30</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h6>المستخدم</h6>
                                    <h4>12</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h6>المتبقي</h6>
                                    <h4>18</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h6>في الانتظار</h6>
                                    <h4>@Model.TotalDays</h4>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            @if (Model.Status == "Pending")
                            {
                                <button type="button" class="btn btn-success" onclick="approveRequest('@Model.Id')">
                                    <i class="fas fa-check me-1"></i>
                                    الموافقة على الطلب
                                </button>
                                <button type="button" class="btn btn-danger" onclick="rejectRequest('@Model.Id')">
                                    <i class="fas fa-times me-1"></i>
                                    رفض الطلب
                                </button>
                            }
                            
                            <a href="@Url.Action("Index")" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>
                                العودة للقائمة
                            </a>
                            
                            <button type="button" class="btn btn-info" onclick="printRequest()">
                                <i class="fas fa-print me-1"></i>
                                طباعة
                            </button>

                            @if (Model.Status == "Approved")
                            {
                                <button type="button" class="btn btn-warning" onclick="cancelRequest('@Model.Id')">
                                    <i class="fas fa-ban me-1"></i>
                                    إلغاء الإجازة
                                </button>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Approval Modal -->
<div class="modal fade" id="approvalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">الموافقة على طلب الإجازة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="approvalForm">
                    <div class="mb-3">
                        <label for="approvalNotes" class="form-label">ملاحظات الموافقة (اختياري)</label>
                        <textarea class="form-control" id="approvalNotes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="confirmApproval()">تأكيد الموافقة</button>
            </div>
        </div>
    </div>
</div>

<!-- Rejection Modal -->
<div class="modal fade" id="rejectionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفض طلب الإجازة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="rejectionForm">
                    <div class="mb-3">
                        <label for="rejectionReason" class="form-label">سبب الرفض *</label>
                        <textarea class="form-control" id="rejectionReason" rows="3" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" onclick="confirmRejection()">تأكيد الرفض</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let currentRequestId = '';

        function approveRequest(requestId) {
            currentRequestId = requestId;
            $('#approvalModal').modal('show');
        }

        function rejectRequest(requestId) {
            currentRequestId = requestId;
            $('#rejectionModal').modal('show');
        }

        function confirmApproval() {
            const notes = $('#approvalNotes').val();
            
            $.ajax({
                url: '@Url.Action("Approve")',
                type: 'POST',
                data: {
                    id: currentRequestId,
                    notes: notes,
                    __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                },
                success: function(result) {
                    if (result.success) {
                        $('#approvalModal').modal('hide');
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + result.message);
                    }
                },
                error: function() {
                    alert('حدث خطأ أثناء الموافقة على الطلب');
                }
            });
        }

        function confirmRejection() {
            const reason = $('#rejectionReason').val();
            
            if (!reason.trim()) {
                alert('يرجى إدخال سبب الرفض');
                return;
            }
            
            $.ajax({
                url: '@Url.Action("Reject")',
                type: 'POST',
                data: {
                    id: currentRequestId,
                    reason: reason,
                    __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                },
                success: function(result) {
                    if (result.success) {
                        $('#rejectionModal').modal('hide');
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + result.message);
                    }
                },
                error: function() {
                    alert('حدث خطأ أثناء رفض الطلب');
                }
            });
        }

        function cancelRequest(requestId) {
            if (confirm('هل أنت متأكد من إلغاء هذه الإجازة؟')) {
                $.ajax({
                    url: '@Url.Action("Cancel")',
                    type: 'POST',
                    data: {
                        id: requestId,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            location.reload();
                        } else {
                            alert('حدث خطأ: ' + result.message);
                        }
                    },
                    error: function() {
                        alert('حدث خطأ أثناء إلغاء الإجازة');
                    }
                });
            }
        }

        function printRequest() {
            window.print();
        }
    </script>
}
