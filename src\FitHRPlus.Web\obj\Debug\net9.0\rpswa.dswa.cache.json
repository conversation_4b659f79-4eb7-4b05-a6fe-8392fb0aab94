{"GlobalPropertiesHash": "MNAosThDZyhrrb4xVZeiKjlBz4g377IXTBwyCH1TTH0=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["qGpdMDkyoLpp/bYT1iE7tglDD/hvOPWHpkDgk5bQv3I=", "Q2O9DVDiJ8u0IJPgY65Z64b5b6U9hHLb8NaUrTkeqZI=", "ZBTRXzOsKmF/lGSdflvBVa94Cvncc+/xss+4sokzcys=", "hd9cHU5BOhbwA/T93NjQ99Nkz4vX1Jxtda0h3FjVixw=", "xYAOvE7h+PxHJx3H6snpYfLhOzytHhb8QWmbxtzWt+0=", "CBmsH6LulgXySCrYhuSLOdXhhs0xCQBVEnNc7I49Bns=", "RGgsGqzEUW4vBb1j0R/cK0AHCbqxOG5tC6tXM2HczZU=", "PA917+lZgmA7wzlSmbogsNuVu4ElGYJos4LluLOjIiY=", "2XZBKw3tIhTHataW92h0N/+0WJZ3AbCoAGRdch5swDw=", "EnMzKBIjm2hEdsL5wxCt6yB+2l/XYJf//DcxwMGYGD4=", "uG7u8aVOXlL7/XF+F05gXjnfm2twCT+I6ASnXobo1xc=", "HXrMsEWkNCnMqsEZQHdHmoB0OuRMmvIaivbCXlV1PEw=", "CfEpG4WA3fj71uF+EBG7RC61y2AVRfY/WU5EPh86gEc=", "alCqSmCI4ujfnTFZe722CMoR+tF2BSO09XbI6dG6Yd4=", "TQ3DdA9+GWQPI0j6WSID3+CCCNX906msf/eUUKJbNLg=", "KNkzab7e3yGkO8moNr98FKexP6A2m0VAPMwgy3ygyyc=", "8VPvVNgEp8cH1YrxHuqtOMpmiN6fv0AjD2RS433/ZPI=", "u5pz26UE0MpyJwxjW5aBHP435fnH+OppEq1YrBttGeM=", "wKFKoZtbmQaDpOiT4PPn6xympW+drr9vBgtTFVWm9yY=", "wZgcRlHKmNpowhkmb8qxaZsa2EQlANaan5SCLISZHDU=", "YueZFcQwyIdcb1eo4QVwlYL0R8pCROSO9w4ePiV4x/E=", "ZDOTOxp0uQuAj0C/Lgn7Olzf2idkInarcOfPRK190Hw=", "vK+Its+K0JKHlhthwgMSMIppmpZ/24gEFHoBVh+mHKU=", "ShlghiQZ83vutonVUMIiIvjmEjf4byxleYHI5/CwJFM=", "frB72veC+7mk/KMcNucIYwZzPTyJLFeV76iRiNmbeag=", "4z1AeT06sDtkolxigVdboU/Zl4OXLNJ5LlOOWRFikOk=", "qsNB7NuoFOessq4yyVlFuXhJ2MpVDkH0o0O3X/uUOPI=", "vIGj19D07yf5qFjZE9sg90EWspSFCIippbCVAgDNzYU=", "R4XAN8HLyzNm2vWnbs3hleVT6JV7AWMj7FL+bIzYoUg=", "X5v3SLwiAIH3HUYK9I/s7NPPhqK8prAVNmxLSLC9nKs=", "NKQM/Gnq58nS5blq3NTTxjPE0NApItEwuh2aZUk5gvQ=", "18c8UM1D0fCfBFI/XuQ9EwXk8Bg+/Rhixij4fPrTeI0=", "I5J/1KTqJvxJ7ysVnyEyIypcvXxcYIiaXNTNP0cmOFE=", "OjDz8pNnmPwJHbH0MJM5saB4ggHpV/0/P+R96ekBby0=", "INNulZGhbYZOgovyTXbX+3XX0KZTqsns2NJdbGbrx8s=", "yZSFub9BRhN+RtcOr2UZoUruziuXv1ptp25BAEqkvyM=", "CcpL6xFVMI/lysFc6sQD/TbXjjKsSiGpo6I26B8hJAM=", "0fLRRClDBDkzTrB3Ld+1D6g7SX2Zh/038XwucGMY8Jw=", "+g/fX4ZSuf0TbSdORzIo0hhOK4kvlVnKy7S2JtcisB4=", "OSJZcZP2MTA8tULshQhV36/bdTuahAsiXiTl8KuPtws=", "/WsMQ3uzvEYGJ/VVqO8xbk+TvMe0A20lGM9OMPEvmDQ=", "f62MHjlPDET/uAKIb4U14ZoNCNX6dT7JyW9aK88igfc=", "UJTZR3/otc75pWV6xw/B3K3sCdLKtK/X9fAc/6wQuwg=", "37bA9OFqy5fN0Il0YO2+1VgwHycSbxq6audZEge0C0M=", "CQ+/oov3y5SdO8lP88iR3J3GcdEpWxN9AnEQNjYuVMQ=", "jz95T/nPxBbhmuUfvXaS+9BOw+g9wLlDU865CvTmqjo=", "fdZFMyKOb2iVaRDigTDifjg7oyYcQoq3sUNlPtVwEcc=", "HdPbIFU4zRX98CECXImX6+11rx49YpHTX/tanBguvkc=", "vM3vGv32StRFJmZ/lLe4ZYoE1mtE4tRqWKKyTXiYNRg=", "yIBcvftmFnO9E/jR4ml35f9lZ70BcIVLrtCHiK35AHE=", "vezAwFsY6m6SnIXXyoiBoQ5Ds60D+GGZvurKt+a/lIE=", "/OHT1Z2k2JS2x7jXrx9czsVUMcjTBoxwSrjeZaPKkXk=", "fGVnlLYFIu2t3cITlk9pCcFL7Ouxz8ZYgL7G7yDDRCE=", "EhWM2e+JG+uJn9YYzX5XLYXE6w4meAy5huvuZip2KpM=", "u4dk1z86spIFBaHNKWZ2QdAjxjNHL+jzmf4Axn2rSGE=", "tJdNHnvrIHj5e9j+2MjZ/ODvt73g5B5TubXsfHbRAdg=", "a5iJj1LlIDwDwewZoRdeDGqFxkohvsntbug75u4WuWo=", "9DtI4c+w+S2ZYOzHe00isbQLEG9eGT5/LgFb7MigOdQ=", "xsjlTB+q46d0t2n9FuhpsWcUAJ2cDchyKo65i/SYnS0=", "YAOmG1dx0h++EXJrLhjEGDBzxOtt5wrOKtL73vuloCY=", "hNamFa+llDibvEqr+X7ENiwSxL7EaCd4zWKnXEasnNA=", "VlyPGIuvr7wNfLF/DDH1YGRCC8KEqYhNO1y7JqxDezQ=", "2/T8zRVx/ivVGnk07mfSIovuJwexeANCX2xB40kKk6M=", "NP3lBTahPF2rFqaiolWtJbAYTkn0M60z+Bnc76pYz0c=", "YnLqOyKI1bM8igoIWsWm816aqTI9fTNkc8W6fA9Aif4=", "OFYZhbxOIUt5hncDsDkOpPO6XiaTs7OjHU6IK51Afas=", "CQ/ItQmVX2NeL0qrLTHsSXu8biIdAvuubHgsUlJj4jc=", "AWfnFE6C4GplSnEpwlOaEXFD8hGo5oTviENSD3KRWFM=", "8qZyESf91zvd2w0Zd6rgiuZo7JgFEBMAPFz0JhuqJCc=", "3XDukMbglLsifonk3imuAfJ51sc9BXtb04vFmPXWoNg=", "KvWzx1wx3ambZWEg4zS03CKcWls6GUS7AVbt4fplEJM=", "HKb1JCEeK0A3IF4bqlIIPOD2yeoK2ismeIjgPpcQ0t8=", "8D5YSrfWgf7qJ2SRIrpeFZn7vmgZJjCEsyG7oioXhDs=", "xy6paDvQ0L98vQoSyemaFx4KrkucA1WLniEkEQRIZcU=", "WvMZgu+gFHWiSN81/uUk7gH0haLB7FsfYRPdrYLFMOY=", "UpqSjQtKvLi29pLw78Ur/MQDDafM7o1EPLftunrSVq4=", "/A7mRSQtmAedifkk7gahcbBAFCLPGWkf8kp/UmzJ8wM=", "9K9Q62Yp4z7TvUK6emrpRC2oo1b6S03t2W12/aSk/AQ=", "LpqdI2Lp9zTQqQVHbNmZlnUDzrnJTgo2Vq80l0+T3QQ=", "21Bwl1qJCAN9g6YVL3khPGtqXTqR2uVJXGmQ0+dO814=", "wku++fJU/9v8tGidhBO3V6DNrDnduZl74sGdhUv/vGA=", "FMIAiwaIs7bEhhP4c5NfXDVyF1NOwfVy8B2T3tpC1BY=", "4P4cMBubO+/Z7dtUSjs3+SGhGFi8p7lOLpkPaJft0nk=", "PMzuzAoQCam32g5DIHs+g9PXM5P4aYAuiMQhPxaWoZo=", "s26+7kTr6+fEaBR7cUEOyFBIdw95bWL3t9O3ZvZNux4=", "5sPcqmS1CHqgiQ/2c+DrCiavNsmmr402J8TYre3acVc=", "ktFq3v2jWssMgAuGKJXX2j6n7Rhaq82jzcVUgIo16mg=", "fyn35VihJd7M+a57y7Ju9Il2KA4OsuoKqTWAFEk/wRg=", "WD68JMJucCpGhzQK3Qo4uWIXe7qM0UTj1A2Wq1gzKXA=", "X2Tksx0h1CA8juRjb8IFXUqfm2eSUn6aZG3hoy4LWuc=", "nx/JlP5FwdMJwKFGRQ1WvyUvr4Bahcb7ZJkbae8YNtQ=", "AAK5mqieYagpJNLtx7inobbh/HI2rlF3u9x9iPK8qxk=", "DoSlT8/DSMXuH2khPZLFfXM3s0ndIsr1SmGvfJIqWbY=", "UrXzma/epU17QF8VCJwt9d1pVLgdRx74crtIac46MrA=", "BkZ1A8q/qYoZBwDftIY7GIYCdhtUKCnU/bjbwc9YCJU=", "cvOs1XPc/+k+AjrclqSCW4BCwM6fJpQk8a0Zq3kmo9I=", "Fd1ZaWDP/r62YnTvBa+815xleN1sy09wkcGzBCkzSNw=", "trB3VqOZHE/gMloY9nqUdY/B8jVk57yTr+0g7iXBBu0=", "J+qTDOjfPbtlbrGx8zNXZyq437m6jlD/leiigWpdcQk=", "fcpg5mZBn6XQqjydk7KU1RwLcfTspmIaGMB4BC75NAA=", "tIIWXa2ZcbeTBiOrQ517DRbUnkg659xf8aK12sDJgVk=", "MLXQIvPntpft6J5pGQ7HsIy2mMNFrGU9+K/5SV4WolU=", "LLevAvSDCS0c/RHKadrHp/TLYzdmG6yzLyArm1vDoLA=", "OVil6vycN8o8/hj8zbDaJLiY4rZsA+A0NkqTkKUs1OQ=", "RQJi84561z2UXQcUXSAXGmmQrY6XmL7X27eSFJcqTJA=", "FfSXdC4xVhc2tPAe8a+Jc5wMnHGEeI8fjJXaAB3dqPk=", "R5/Zzr6/Y1PALKUsflyCB+BvMTlv+yp01E3Puvigvv8=", "eQXgoOnqnSkbB0kg0vD9wDCh5oSFLneDggoeGlR4l1Y=", "WSwWQkE8vlJc7XNM9U9aX4bfd/493Ecf/iO3Rak9T9I=", "1HKG6RPOH7Vb7fuu8rrPQKfws1N/jmTyguUM3TwBKjU=", "Idz/H561QDwuBwJgPmizSrFKLJG1EDA5TpwdEDL1qG8=", "ORruuq+PC8Xhag/aOQWBRojfmv0V44cxlTnFmkzFLzg=", "mJdYWOvDBMLHzT2Giu+36CKhSWqMgnGEfOKgKCCnf/k=", "KEbpVj0BxIAKaY4rybg87s4XbsjavVRZ7tjN4alCkP4=", "5IeDD3Gc3uf/8djfJHkQsacYg3nJ7j8vFFiAVjcgoVY=", "7VVpaRAjwLqGnCA5qE3fUL5HJVoRyeg1zd7AZo3eL3k=", "PDA52pUxZPDeC+pAAs9MF2EtFwhca7rXRfvunw8nsBc=", "gOpGWunPrMPLnmt+jXwuLNd8tjkK/nIF1WNhtRKx8Bc=", "lSIdJvmmhaKlTiHgPF9g+Obt6Ra/o+gyaEUrVnOSGW8=", "e8XPwA6/7sVBEuO4bn7oFJseO5F3ZURlXJGjYPARNEM=", "bcIfJ4a0Ck1w4eJ0crN/pdXY/EUTRjVxCWyeVWzLcBA=", "Uih4EWj7BEdZzmH0inAp3FAt5lqRHw6U7BpBGTTakNE=", "G5XitBf1T/nbKUmtbB8CyRUyzk9VwC9ubgJ2duV2hQw=", "Z6IVf0/bBaUtyReYxv7qfWYtkLBDidc7nslPuDyaUIE=", "9DVdssE4mL00Kp+uN1k2ddnVKSNrgl3ZBy2E+Mh2iHs=", "OUKNPsGpPDGrgcUzQ7uXkrtE2nXTUxnOsnLlHEM9OP0=", "4qTSiygDByjk1z6VW4w6l6He/fHzrOU/8Bl9NnMtRx4=", "J5EhcsznnrlarkrX2R4wOHkfrsm3xSgUOnFApEhetmc="], "CachedAssets": {"qGpdMDkyoLpp/bYT1iE7tglDD/hvOPWHpkDgk5bQv3I=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\css\\fithr-components.css", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "css/fithr-components#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rnr274xhvc", "Integrity": "ecAdfnwLBx+SqMVc+IWu2mwY2ejyZVrnOpoGwTKK0nc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\fithr-components.css", "FileLength": 7216, "LastWriteTime": "2025-07-31T00:05:30.0361604+00:00"}, "Q2O9DVDiJ8u0IJPgY65Z64b5b6U9hHLb8NaUrTkeqZI=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\css\\hrms-theme.css", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "css/hrms-theme#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "doecxvn4pl", "Integrity": "TeJQJxUnOoVFET8zVCO8W76/Pfic6b4zkUAM0oOZVcM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\hrms-theme.css", "FileLength": 7983, "LastWriteTime": "2025-08-01T22:26:54.6756474+00:00"}, "ZBTRXzOsKmF/lGSdflvBVa94Cvncc+/xss+4sokzcys=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\css\\rtl.css", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "css/rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "osk98azxr3", "Integrity": "eE51T1VPSYS6IDvieOa0qeJ1plVrsEFltSUNrYL0Nrw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\rtl.css", "FileLength": 6636, "LastWriteTime": "2025-07-24T11:08:25.1453573+00:00"}, "hd9cHU5BOhbwA/T93NjQ99Nkz4vX1Jxtda0h3FjVixw=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\css\\site.css", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "quz05a5yt2", "Integrity": "fu3vflrRpRbJZ+IMZINDW1DwJjZ+gNEBmf8uwkKvCzU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 3668, "LastWriteTime": "2025-07-24T11:07:37.323655+00:00"}, "xYAOvE7h+PxHJx3H6snpYfLhOzytHhb8QWmbxtzWt+0=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\favicon.ico", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-24T08:20:04.024427+00:00"}, "CBmsH6LulgXySCrYhuSLOdXhhs0xCQBVEnNc7I49Bns=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\js\\admin.js", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "js/admin#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hw9vmrs3pm", "Integrity": "4eVaJqipXIMMguBqIf79jLFc56S5GL4OcpGzDczKF/M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\admin.js", "FileLength": 9761, "LastWriteTime": "2025-07-24T11:09:12.3218045+00:00"}, "RGgsGqzEUW4vBb1j0R/cK0AHCbqxOG5tC6tXM2HczZU=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\js\\fithr-common.js", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "js/fithr-common#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8g4cyvnxuq", "Integrity": "iI8w54P1sokK3l8gnEKj441gxuKFxlVsuQlafHQIqQ8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\fithr-common.js", "FileLength": 10721, "LastWriteTime": "2025-07-31T00:06:22.8435718+00:00"}, "PA917+lZgmA7wzlSmbogsNuVu4ElGYJos4LluLOjIiY=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\js\\site.js", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-24T08:20:04.1364279+00:00"}, "2XZBKw3tIhTHataW92h0N/+0WJZ3AbCoAGRdch5swDw=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-24T08:20:03.7264254+00:00"}, "EnMzKBIjm2hEdsL5wxCt6yB+2l/XYJf//DcxwMGYGD4=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-24T08:20:03.7304319+00:00"}, "uG7u8aVOXlL7/XF+F05gXjnfm2twCT+I6ASnXobo1xc=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-24T08:20:03.7314273+00:00"}, "HXrMsEWkNCnMqsEZQHdHmoB0OuRMmvIaivbCXlV1PEw=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-24T08:20:03.7324276+00:00"}, "CfEpG4WA3fj71uF+EBG7RC61y2AVRfY/WU5EPh86gEc=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-24T08:20:03.7354284+00:00"}, "alCqSmCI4ujfnTFZe722CMoR+tF2BSO09XbI6dG6Yd4=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-24T08:20:03.7494286+00:00"}, "TQ3DdA9+GWQPI0j6WSID3+CCCNX906msf/eUUKJbNLg=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-24T08:20:03.753428+00:00"}, "KNkzab7e3yGkO8moNr98FKexP6A2m0VAPMwgy3ygyyc=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-24T08:20:03.7564271+00:00"}, "8VPvVNgEp8cH1YrxHuqtOMpmiN6fv0AjD2RS433/ZPI=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-24T08:20:03.7574259+00:00"}, "u5pz26UE0MpyJwxjW5aBHP435fnH+OppEq1YrBttGeM=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-24T08:20:03.7584251+00:00"}, "wKFKoZtbmQaDpOiT4PPn6xympW+drr9vBgtTFVWm9yY=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-24T08:20:03.760428+00:00"}, "wZgcRlHKmNpowhkmb8qxaZsa2EQlANaan5SCLISZHDU=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-24T08:20:03.7614281+00:00"}, "YueZFcQwyIdcb1eo4QVwlYL0R8pCROSO9w4ePiV4x/E=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-24T08:20:03.7624293+00:00"}, "ZDOTOxp0uQuAj0C/Lgn7Olzf2idkInarcOfPRK190Hw=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-24T08:20:03.7644308+00:00"}, "vK+Its+K0JKHlhthwgMSMIppmpZ/24gEFHoBVh+mHKU=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-24T08:20:03.7654299+00:00"}, "ShlghiQZ83vutonVUMIiIvjmEjf4byxleYHI5/CwJFM=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-24T08:20:03.7694299+00:00"}, "frB72veC+7mk/KMcNucIYwZzPTyJLFeV76iRiNmbeag=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-24T08:20:03.770429+00:00"}, "4z1AeT06sDtkolxigVdboU/Zl4OXLNJ5LlOOWRFikOk=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-24T08:20:03.7884291+00:00"}, "qsNB7NuoFOessq4yyVlFuXhJ2MpVDkH0o0O3X/uUOPI=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-24T08:20:03.7904279+00:00"}, "vIGj19D07yf5qFjZE9sg90EWspSFCIippbCVAgDNzYU=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-24T08:20:03.7924292+00:00"}, "R4XAN8HLyzNm2vWnbs3hleVT6JV7AWMj7FL+bIzYoUg=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-24T08:20:03.7934279+00:00"}, "X5v3SLwiAIH3HUYK9I/s7NPPhqK8prAVNmxLSLC9nKs=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-24T08:20:03.7984279+00:00"}, "NKQM/Gnq58nS5blq3NTTxjPE0NApItEwuh2aZUk5gvQ=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-24T08:20:03.8004285+00:00"}, "18c8UM1D0fCfBFI/XuQ9EwXk8Bg+/Rhixij4fPrTeI0=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-24T08:20:03.8014293+00:00"}, "I5J/1KTqJvxJ7ysVnyEyIypcvXxcYIiaXNTNP0cmOFE=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-24T08:20:03.8034304+00:00"}, "OjDz8pNnmPwJHbH0MJM5saB4ggHpV/0/P+R96ekBby0=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-24T08:20:03.8234275+00:00"}, "INNulZGhbYZOgovyTXbX+3XX0KZTqsns2NJdbGbrx8s=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-24T08:20:03.8264274+00:00"}, "yZSFub9BRhN+RtcOr2UZoUruziuXv1ptp25BAEqkvyM=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-24T08:20:03.8354286+00:00"}, "CcpL6xFVMI/lysFc6sQD/TbXjjKsSiGpo6I26B8hJAM=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-24T08:20:03.8364276+00:00"}, "0fLRRClDBDkzTrB3Ld+1D6g7SX2Zh/038XwucGMY8Jw=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-24T08:20:03.8424279+00:00"}, "+g/fX4ZSuf0TbSdORzIo0hhOK4kvlVnKy7S2JtcisB4=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-24T08:20:03.8444291+00:00"}, "OSJZcZP2MTA8tULshQhV36/bdTuahAsiXiTl8KuPtws=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-24T08:20:03.8494286+00:00"}, "/WsMQ3uzvEYGJ/VVqO8xbk+TvMe0A20lGM9OMPEvmDQ=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-24T08:20:03.8514318+00:00"}, "f62MHjlPDET/uAKIb4U14ZoNCNX6dT7JyW9aK88igfc=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-24T08:20:03.854426+00:00"}, "UJTZR3/otc75pWV6xw/B3K3sCdLKtK/X9fAc/6wQuwg=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-24T08:20:03.8564269+00:00"}, "37bA9OFqy5fN0Il0YO2+1VgwHycSbxq6audZEge0C0M=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-24T08:20:03.8584258+00:00"}, "CQ+/oov3y5SdO8lP88iR3J3GcdEpWxN9AnEQNjYuVMQ=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-24T08:20:03.8604277+00:00"}, "jz95T/nPxBbhmuUfvXaS+9BOw+g9wLlDU865CvTmqjo=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-24T08:20:03.8774268+00:00"}, "fdZFMyKOb2iVaRDigTDifjg7oyYcQoq3sUNlPtVwEcc=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-24T08:20:03.8804291+00:00"}, "HdPbIFU4zRX98CECXImX6+11rx49YpHTX/tanBguvkc=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-24T08:20:03.8824286+00:00"}, "vM3vGv32StRFJmZ/lLe4ZYoE1mtE4tRqWKKyTXiYNRg=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-24T08:20:03.8904284+00:00"}, "yIBcvftmFnO9E/jR4ml35f9lZ70BcIVLrtCHiK35AHE=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-24T08:20:03.8924271+00:00"}, "vezAwFsY6m6SnIXXyoiBoQ5Ds60D+GGZvurKt+a/lIE=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-24T08:20:03.8934279+00:00"}, "/OHT1Z2k2JS2x7jXrx9czsVUMcjTBoxwSrjeZaPKkXk=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-24T08:20:03.8964287+00:00"}, "fGVnlLYFIu2t3cITlk9pCcFL7Ouxz8ZYgL7G7yDDRCE=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-24T08:20:03.7334296+00:00"}, "EhWM2e+JG+uJn9YYzX5XLYXE6w4meAy5huvuZip2KpM=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-24T08:20:04.0444281+00:00"}, "u4dk1z86spIFBaHNKWZ2QdAjxjNHL+jzmf4Axn2rSGE=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-24T08:20:04.0574277+00:00"}, "tJdNHnvrIHj5e9j+2MjZ/ODvt73g5B5TubXsfHbRAdg=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-24T08:20:03.7544309+00:00"}, "a5iJj1LlIDwDwewZoRdeDGqFxkohvsntbug75u4WuWo=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-24T08:20:03.9864277+00:00"}, "9DtI4c+w+S2ZYOzHe00isbQLEG9eGT5/LgFb7MigOdQ=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-24T08:20:04.0224265+00:00"}, "xsjlTB+q46d0t2n9FuhpsWcUAJ2cDchyKo65i/SYnS0=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-07-24T08:20:04.0254296+00:00"}, "YAOmG1dx0h++EXJrLhjEGDBzxOtt5wrOKtL73vuloCY=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-07-24T08:20:04.0264292+00:00"}, "hNamFa+llDibvEqr+X7ENiwSxL7EaCd4zWKnXEasnNA=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-24T08:20:03.75243+00:00"}, "VlyPGIuvr7wNfLF/DDH1YGRCC8KEqYhNO1y7JqxDezQ=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-07-24T08:20:03.9004304+00:00"}, "2/T8zRVx/ivVGnk07mfSIovuJwexeANCX2xB40kKk6M=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-07-24T08:20:03.9014274+00:00"}, "NP3lBTahPF2rFqaiolWtJbAYTkn0M60z+Bnc76pYz0c=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-24T08:20:03.9194277+00:00"}, "YnLqOyKI1bM8igoIWsWm816aqTI9fTNkc8W6fA9Aif4=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-07-24T08:20:03.9224267+00:00"}, "OFYZhbxOIUt5hncDsDkOpPO6XiaTs7OjHU6IK51Afas=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-07-24T08:20:03.9354277+00:00"}, "CQ/ItQmVX2NeL0qrLTHsSXu8biIdAvuubHgsUlJj4jc=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-24T08:20:03.9734274+00:00"}, "AWfnFE6C4GplSnEpwlOaEXFD8hGo5oTviENSD3KRWFM=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-24T08:20:03.7374303+00:00"}}, "CachedCopyCandidates": {}}