using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Application.DTOs.Employees
{
    /// <summary>
    /// Employee list request DTO for filtering and pagination
    /// كائنة نقل بيانات طلب قائمة الموظفين للتصفية والترقيم
    /// </summary>
    public class EmployeeListRequestDto
    {
        /// <summary>
        /// Search term (name, employee number, email, etc.)
        /// مصطلح البحث (الاسم، رقم الموظف، البريد الإلكتروني، إلخ)
        /// </summary>
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Company ID filter
        /// تصفية معرف الشركة
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// Department ID filter
        /// تصفية معرف القسم
        /// </summary>
        public Guid? DepartmentId { get; set; }

        /// <summary>
        /// Position ID filter
        /// تصفية معرف المنصب
        /// </summary>
        public Guid? PositionId { get; set; }

        /// <summary>
        /// Manager ID filter
        /// تصفية معرف المدير
        /// </summary>
        public Guid? ManagerId { get; set; }

        /// <summary>
        /// Employment status filter
        /// تصفية حالة التوظيف
        /// </summary>
        public string? EmploymentStatus { get; set; }

        /// <summary>
        /// Employment type filter
        /// تصفية نوع التوظيف
        /// </summary>
        public string? EmploymentType { get; set; }

        /// <summary>
        /// Gender filter
        /// تصفية الجنس
        /// </summary>
        public string? Gender { get; set; }

        /// <summary>
        /// Active status filter
        /// تصفية الحالة النشطة
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// Hire date from filter
        /// تصفية تاريخ التوظيف من
        /// </summary>
        public DateTime? HireDateFrom { get; set; }

        /// <summary>
        /// Hire date to filter
        /// تصفية تاريخ التوظيف إلى
        /// </summary>
        public DateTime? HireDateTo { get; set; }

        /// <summary>
        /// Age from filter
        /// تصفية العمر من
        /// </summary>
        public int? AgeFrom { get; set; }

        /// <summary>
        /// Age to filter
        /// تصفية العمر إلى
        /// </summary>
        public int? AgeTo { get; set; }

        /// <summary>
        /// Salary from filter
        /// تصفية الراتب من
        /// </summary>
        public decimal? SalaryFrom { get; set; }

        /// <summary>
        /// Salary to filter
        /// تصفية الراتب إلى
        /// </summary>
        public decimal? SalaryTo { get; set; }

        /// <summary>
        /// Sort field
        /// حقل الترتيب
        /// </summary>
        public string? SortBy { get; set; } = "FirstName";

        /// <summary>
        /// Sort direction (asc/desc)
        /// اتجاه الترتيب (تصاعدي/تنازلي)
        /// </summary>
        public string? SortDirection { get; set; } = "asc";

        /// <summary>
        /// Page number (1-based)
        /// رقم الصفحة (يبدأ من 1)
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "Page number must be greater than 0")]
        public int Page { get; set; } = 1;

        /// <summary>
        /// Page size
        /// حجم الصفحة
        /// </summary>
        [Range(1, 100, ErrorMessage = "Page size must be between 1 and 100")]
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// Include inactive employees
        /// تضمين الموظفين غير النشطين
        /// </summary>
        public bool IncludeInactive { get; set; } = false;

        /// <summary>
        /// Include terminated employees
        /// تضمين الموظفين المنتهية خدمتهم
        /// </summary>
        public bool IncludeTerminated { get; set; } = false;
    }

    /// <summary>
    /// Employee list response DTO
    /// كائنة نقل بيانات استجابة قائمة الموظفين
    /// </summary>
    public class EmployeeListResponseDto
    {
        /// <summary>
        /// List of employees
        /// قائمة الموظفين
        /// </summary>
        public List<EmployeeSummaryDto> Employees { get; set; } = new();

        /// <summary>
        /// Total number of employees (before pagination)
        /// العدد الإجمالي للموظفين (قبل الترقيم)
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Current page number
        /// رقم الصفحة الحالية
        /// </summary>
        public int CurrentPage { get; set; }

        /// <summary>
        /// Page size
        /// حجم الصفحة
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// Total number of pages
        /// العدد الإجمالي للصفحات
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// Whether there is a previous page
        /// ما إذا كانت هناك صفحة سابقة
        /// </summary>
        public bool HasPreviousPage { get; set; }

        /// <summary>
        /// Whether there is a next page
        /// ما إذا كانت هناك صفحة تالية
        /// </summary>
        public bool HasNextPage { get; set; }

        /// <summary>
        /// Filter summary
        /// ملخص التصفية
        /// </summary>
        public EmployeeFilterSummaryDto FilterSummary { get; set; } = new();
    }

    /// <summary>
    /// Employee summary DTO for list display
    /// كائنة نقل بيانات ملخص الموظف لعرض القائمة
    /// </summary>
    public class EmployeeSummaryDto
    {
        /// <summary>
        /// Employee ID
        /// معرف الموظف
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Employee number
        /// رقم الموظف
        /// </summary>
        public string EmployeeNumber { get; set; } = string.Empty;

        /// <summary>
        /// Full name
        /// الاسم الكامل
        /// </summary>
        public string FullName { get; set; } = string.Empty;

        /// <summary>
        /// Full name in Arabic
        /// الاسم الكامل بالعربية
        /// </summary>
        public string? FullNameAr { get; set; }

        /// <summary>
        /// Email address
        /// البريد الإلكتروني
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// Phone number
        /// رقم الهاتف
        /// </summary>
        public string? Phone { get; set; }

        /// <summary>
        /// Department name
        /// اسم القسم
        /// </summary>
        public string? DepartmentName { get; set; }

        /// <summary>
        /// Position title
        /// مسمى المنصب
        /// </summary>
        public string? PositionTitle { get; set; }

        /// <summary>
        /// Manager name
        /// اسم المدير
        /// </summary>
        public string? ManagerName { get; set; }

        /// <summary>
        /// Hire date
        /// تاريخ التوظيف
        /// </summary>
        public DateTime HireDate { get; set; }

        /// <summary>
        /// Employment status
        /// حالة التوظيف
        /// </summary>
        public string EmploymentStatus { get; set; } = string.Empty;

        /// <summary>
        /// Employment type
        /// نوع التوظيف
        /// </summary>
        public string? EmploymentType { get; set; }

        /// <summary>
        /// Profile picture URL
        /// رابط صورة الملف الشخصي
        /// </summary>
        public string? ProfilePicture { get; set; }

        /// <summary>
        /// Whether the employee is active
        /// ما إذا كان الموظف نشط
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Age (computed from date of birth)
        /// العمر (محسوب من تاريخ الميلاد)
        /// </summary>
        public int? Age { get; set; }

        /// <summary>
        /// Years of service (computed from hire date)
        /// سنوات الخدمة (محسوبة من تاريخ التوظيف)
        /// </summary>
        public int YearsOfService { get; set; }
    }

    /// <summary>
    /// Employee filter summary DTO
    /// كائنة نقل بيانات ملخص تصفية الموظفين
    /// </summary>
    public class EmployeeFilterSummaryDto
    {
        /// <summary>
        /// Total active employees
        /// إجمالي الموظفين النشطين
        /// </summary>
        public int TotalActive { get; set; }

        /// <summary>
        /// Total inactive employees
        /// إجمالي الموظفين غير النشطين
        /// </summary>
        public int TotalInactive { get; set; }

        /// <summary>
        /// Total terminated employees
        /// إجمالي الموظفين المنتهية خدمتهم
        /// </summary>
        public int TotalTerminated { get; set; }

        /// <summary>
        /// Total male employees
        /// إجمالي الموظفين الذكور
        /// </summary>
        public int TotalMale { get; set; }

        /// <summary>
        /// Total female employees
        /// إجمالي الموظفات الإناث
        /// </summary>
        public int TotalFemale { get; set; }

        /// <summary>
        /// Average age
        /// متوسط العمر
        /// </summary>
        public double AverageAge { get; set; }

        /// <summary>
        /// Average years of service
        /// متوسط سنوات الخدمة
        /// </summary>
        public double AverageYearsOfService { get; set; }

        /// <summary>
        /// Department breakdown
        /// تفصيل الأقسام
        /// </summary>
        public List<DepartmentSummaryDto> DepartmentBreakdown { get; set; } = new();
    }

    /// <summary>
    /// Department summary DTO for filter breakdown
    /// كائنة نقل بيانات ملخص القسم لتفصيل التصفية
    /// </summary>
    public class DepartmentSummaryDto
    {
        /// <summary>
        /// Department ID
        /// معرف القسم
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Department name
        /// اسم القسم
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Number of employees
        /// عدد الموظفين
        /// </summary>
        public int EmployeeCount { get; set; }
    }
}
