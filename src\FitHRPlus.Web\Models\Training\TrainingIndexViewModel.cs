using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Web.Models.Training
{
    public class TrainingIndexViewModel
    {
        public int? TotalPrograms { get; set; }
        public int? CompletedTrainings { get; set; }
        public int? OngoingTrainings { get; set; }
        public int? TotalParticipants { get; set; }
        public int? TechnicalTrainings { get; set; }
        public int? ManagementTrainings { get; set; }
        public int? SoftSkillsTrainings { get; set; }
        public int? SafetyTrainings { get; set; }
        public int? OtherTrainings { get; set; }
        
        public List<TrainingProgramViewModel>? TrainingPrograms { get; set; }
        public List<UpcomingTrainingViewModel>? UpcomingTrainings { get; set; }
    }

    public class TrainingProgramViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? Category { get; set; }
        public string? Status { get; set; }
        public string? StatusAr { get; set; }
        public int? Duration { get; set; }
        public string? Instructor { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int? ParticipantsCount { get; set; }
        public int? MaxParticipants { get; set; }
        public decimal? ProgressPercentage { get; set; }
    }

    public class UpcomingTrainingViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string? Instructor { get; set; }
        public DateTime? StartDate { get; set; }
        public string? StartTime { get; set; }
        public string? EndTime { get; set; }
        public int? ParticipantsCount { get; set; }
    }
}
