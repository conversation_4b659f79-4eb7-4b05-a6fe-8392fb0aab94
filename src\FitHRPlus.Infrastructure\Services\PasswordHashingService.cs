using FitHRPlus.Application.Interfaces;
using Microsoft.Extensions.Options;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace FitHRPlus.Infrastructure.Services
{
    /// <summary>
    /// Password hashing service implementation using PBKDF2
    /// تنفيذ خدمة تشفير كلمات المرور باستخدام PBKDF2
    /// </summary>
    public class PasswordHashingService : IPasswordHashingService
    {
        private readonly PasswordHashingOptions _options;

        public PasswordHashingService(IOptions<PasswordHashingOptions> options)
        {
            _options = options.Value;
        }

        public (string hashedPassword, string salt) HashPassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                throw new ArgumentException("Password cannot be null or empty", nameof(password));

            var salt = GenerateSalt();
            var hashedPassword = HashPasswordWithSalt(password, salt);
            
            return (hashedPassword, salt);
        }

        public bool VerifyPassword(string password, string hashedPassword, string salt)
        {
            if (string.IsNullOrEmpty(password) || string.IsNullOrEmpty(hashedPassword) || string.IsNullOrEmpty(salt))
                return false;

            var computedHash = HashPasswordWithSalt(password, salt);
            return SlowEquals(hashedPassword, computedHash);
        }

        public string GenerateSalt()
        {
            var saltBytes = new byte[_options.SaltSize];
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(saltBytes);
            return Convert.ToBase64String(saltBytes);
        }

        public PasswordValidationResult ValidatePassword(string password)
        {
            var result = new PasswordValidationResult
            {
                IsValid = true,
                Errors = new List<string>(),
                ErrorsAr = new List<string>(),
                Suggestions = new List<string>(),
                SuggestionsAr = new List<string>()
            };

            if (string.IsNullOrEmpty(password))
            {
                result.IsValid = false;
                result.Errors.Add("Password is required");
                result.ErrorsAr.Add("كلمة المرور مطلوبة");
                return result;
            }

            var score = 0;

            // Check length
            if (password.Length < _options.MinimumLength)
            {
                result.IsValid = false;
                result.Errors.Add($"Password must be at least {_options.MinimumLength} characters long");
                result.ErrorsAr.Add($"يجب أن تكون كلمة المرور {_options.MinimumLength} أحرف على الأقل");
            }
            else if (password.Length >= _options.MinimumLength)
            {
                score += 10;
                if (password.Length >= 12) score += 10;
                if (password.Length >= 16) score += 10;
            }

            if (password.Length > _options.MaximumLength)
            {
                result.IsValid = false;
                result.Errors.Add($"Password cannot exceed {_options.MaximumLength} characters");
                result.ErrorsAr.Add($"لا يمكن أن تتجاوز كلمة المرور {_options.MaximumLength} حرف");
            }

            // Check uppercase
            if (_options.RequireUppercase && !Regex.IsMatch(password, @"[A-Z]"))
            {
                result.IsValid = false;
                result.Errors.Add("Password must contain at least one uppercase letter");
                result.ErrorsAr.Add("يجب أن تحتوي كلمة المرور على حرف كبير واحد على الأقل");
                result.Suggestions.Add("Add uppercase letters (A-Z)");
                result.SuggestionsAr.Add("أضف أحرف كبيرة (A-Z)");
            }
            else if (Regex.IsMatch(password, @"[A-Z]"))
            {
                score += 15;
            }

            // Check lowercase
            if (_options.RequireLowercase && !Regex.IsMatch(password, @"[a-z]"))
            {
                result.IsValid = false;
                result.Errors.Add("Password must contain at least one lowercase letter");
                result.ErrorsAr.Add("يجب أن تحتوي كلمة المرور على حرف صغير واحد على الأقل");
                result.Suggestions.Add("Add lowercase letters (a-z)");
                result.SuggestionsAr.Add("أضف أحرف صغيرة (a-z)");
            }
            else if (Regex.IsMatch(password, @"[a-z]"))
            {
                score += 15;
            }

            // Check digits
            if (_options.RequireDigits && !Regex.IsMatch(password, @"\d"))
            {
                result.IsValid = false;
                result.Errors.Add("Password must contain at least one digit");
                result.ErrorsAr.Add("يجب أن تحتوي كلمة المرور على رقم واحد على الأقل");
                result.Suggestions.Add("Add numbers (0-9)");
                result.SuggestionsAr.Add("أضف أرقام (0-9)");
            }
            else if (Regex.IsMatch(password, @"\d"))
            {
                score += 15;
            }

            // Check special characters
            if (_options.RequireSpecialChars && !Regex.IsMatch(password, @"[!@#$%^&*()_+\-=\[\]{};':""\\|,.<>\/?]"))
            {
                result.IsValid = false;
                result.Errors.Add("Password must contain at least one special character");
                result.ErrorsAr.Add("يجب أن تحتوي كلمة المرور على رمز خاص واحد على الأقل");
                result.Suggestions.Add("Add special characters (!@#$%^&*)");
                result.SuggestionsAr.Add("أضف رموز خاصة (!@#$%^&*)");
            }
            else if (Regex.IsMatch(password, @"[!@#$%^&*()_+\-=\[\]{};':""\\|,.<>\/?]"))
            {
                score += 15;
            }

            // Check unique characters
            var uniqueChars = password.Distinct().Count();
            if (uniqueChars < _options.MinimumUniqueChars)
            {
                result.Suggestions.Add($"Use at least {_options.MinimumUniqueChars} different characters");
                result.SuggestionsAr.Add($"استخدم {_options.MinimumUniqueChars} أحرف مختلفة على الأقل");
                score -= 10;
            }
            else
            {
                score += 10;
                if (uniqueChars >= password.Length * 0.7) score += 10;
            }

            // Check for common patterns
            if (Regex.IsMatch(password, @"(.)\1{2,}"))
            {
                result.Suggestions.Add("Avoid repeating characters");
                result.SuggestionsAr.Add("تجنب تكرار الأحرف");
                score -= 15;
            }

            if (Regex.IsMatch(password, @"(012|123|234|345|456|567|678|789|890|abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)", RegexOptions.IgnoreCase))
            {
                result.Suggestions.Add("Avoid sequential characters");
                result.SuggestionsAr.Add("تجنب الأحرف المتسلسلة");
                score -= 15;
            }

            // Calculate strength
            result.StrengthScore = Math.Max(0, Math.Min(100, score));
            result.Strength = result.StrengthScore switch
            {
                < 20 => PasswordStrength.VeryWeak,
                < 40 => PasswordStrength.Weak,
                < 60 => PasswordStrength.Fair,
                < 80 => PasswordStrength.Good,
                < 95 => PasswordStrength.Strong,
                _ => PasswordStrength.VeryStrong
            };

            return result;
        }

        public string GenerateSecurePassword(int length = 12, bool includeSpecialChars = true)
        {
            const string lowercase = "abcdefghijklmnopqrstuvwxyz";
            const string uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            const string digits = "0123456789";
            const string specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?";

            var chars = lowercase + uppercase + digits;
            if (includeSpecialChars)
                chars += specialChars;

            var password = new StringBuilder();
            using var rng = RandomNumberGenerator.Create();

            // Ensure at least one character from each required category
            password.Append(GetRandomChar(lowercase, rng));
            password.Append(GetRandomChar(uppercase, rng));
            password.Append(GetRandomChar(digits, rng));
            
            if (includeSpecialChars)
                password.Append(GetRandomChar(specialChars, rng));

            // Fill the rest randomly
            for (int i = password.Length; i < length; i++)
            {
                password.Append(GetRandomChar(chars, rng));
            }

            // Shuffle the password
            return new string(password.ToString().OrderBy(x => GetRandomNumber(rng)).ToArray());
        }

        private string HashPasswordWithSalt(string password, string salt)
        {
            var saltBytes = Convert.FromBase64String(salt);
            var passwordBytes = Encoding.UTF8.GetBytes(password);

            using var pbkdf2 = new Rfc2898DeriveBytes(passwordBytes, saltBytes, _options.Iterations, HashAlgorithmName.SHA256);
            var hashBytes = pbkdf2.GetBytes(_options.HashSize);
            
            return Convert.ToBase64String(hashBytes);
        }

        private static bool SlowEquals(string a, string b)
        {
            if (a.Length != b.Length)
                return false;

            var diff = 0;
            for (int i = 0; i < a.Length; i++)
            {
                diff |= a[i] ^ b[i];
            }

            return diff == 0;
        }

        private static char GetRandomChar(string chars, RandomNumberGenerator rng)
        {
            var randomBytes = new byte[4];
            rng.GetBytes(randomBytes);
            var randomIndex = Math.Abs(BitConverter.ToInt32(randomBytes, 0)) % chars.Length;
            return chars[randomIndex];
        }

        private static int GetRandomNumber(RandomNumberGenerator rng)
        {
            var randomBytes = new byte[4];
            rng.GetBytes(randomBytes);
            return BitConverter.ToInt32(randomBytes, 0);
        }
    }
}
