using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Application.DTOs.Employees
{
    /// <summary>
    /// Employee data transfer object
    /// كائنة نقل بيانات الموظف
    /// </summary>
    public class EmployeeDto
    {
        /// <summary>
        /// Employee ID
        /// معرف الموظف
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Employee number (unique within company)
        /// رقم الموظف (فريد داخل الشركة)
        /// </summary>
        [Required(ErrorMessage = "Employee number is required")]
        [StringLength(50, ErrorMessage = "Employee number cannot exceed 50 characters")]
        public string EmployeeNumber { get; set; } = string.Empty;

        /// <summary>
        /// First name
        /// الاسم الأول
        /// </summary>
        [Required(ErrorMessage = "First name is required")]
        [StringLength(100, ErrorMessage = "First name cannot exceed 100 characters")]
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// Last name
        /// الاسم الأخير
        /// </summary>
        [Required(ErrorMessage = "Last name is required")]
        [StringLength(100, ErrorMessage = "Last name cannot exceed 100 characters")]
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// First name in Arabic
        /// الاسم الأول بالعربية
        /// </summary>
        [StringLength(100, ErrorMessage = "Arabic first name cannot exceed 100 characters")]
        public string? FirstNameAr { get; set; }

        /// <summary>
        /// Last name in Arabic
        /// الاسم الأخير بالعربية
        /// </summary>
        [StringLength(100, ErrorMessage = "Arabic last name cannot exceed 100 characters")]
        public string? LastNameAr { get; set; }

        /// <summary>
        /// Full name (computed)
        /// الاسم الكامل (محسوب)
        /// </summary>
        public string FullName => $"{FirstName} {LastName}";

        /// <summary>
        /// Full name in Arabic (computed)
        /// الاسم الكامل بالعربية (محسوب)
        /// </summary>
        public string FullNameAr => $"{FirstNameAr} {LastNameAr}";

        /// <summary>
        /// Email address
        /// البريد الإلكتروني
        /// </summary>
        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
        public string? Email { get; set; }

        /// <summary>
        /// Phone number
        /// رقم الهاتف
        /// </summary>
        [Phone(ErrorMessage = "Invalid phone number format")]
        [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
        public string? Phone { get; set; }

        /// <summary>
        /// Mobile number
        /// رقم الجوال
        /// </summary>
        [Phone(ErrorMessage = "Invalid mobile number format")]
        [StringLength(20, ErrorMessage = "Mobile number cannot exceed 20 characters")]
        public string? Mobile { get; set; }

        /// <summary>
        /// National ID number
        /// رقم الهوية الوطنية
        /// </summary>
        [StringLength(20, ErrorMessage = "National ID cannot exceed 20 characters")]
        public string? NationalId { get; set; }

        /// <summary>
        /// Passport number
        /// رقم جواز السفر
        /// </summary>
        [StringLength(20, ErrorMessage = "Passport number cannot exceed 20 characters")]
        public string? PassportNumber { get; set; }

        /// <summary>
        /// Date of birth
        /// تاريخ الميلاد
        /// </summary>
        public DateTime? DateOfBirth { get; set; }

        /// <summary>
        /// Gender (Male/Female)
        /// الجنس (ذكر/أنثى)
        /// </summary>
        public string? Gender { get; set; }

        /// <summary>
        /// Marital status
        /// الحالة الاجتماعية
        /// </summary>
        public string? MaritalStatus { get; set; }

        /// <summary>
        /// Nationality
        /// الجنسية
        /// </summary>
        [StringLength(50, ErrorMessage = "Nationality cannot exceed 50 characters")]
        public string? Nationality { get; set; }

        /// <summary>
        /// Address
        /// العنوان
        /// </summary>
        [StringLength(500, ErrorMessage = "Address cannot exceed 500 characters")]
        public string? Address { get; set; }

        /// <summary>
        /// City
        /// المدينة
        /// </summary>
        [StringLength(100, ErrorMessage = "City cannot exceed 100 characters")]
        public string? City { get; set; }

        /// <summary>
        /// Country
        /// الدولة
        /// </summary>
        [StringLength(100, ErrorMessage = "Country cannot exceed 100 characters")]
        public string? Country { get; set; } = "Egypt";

        /// <summary>
        /// Hire date
        /// تاريخ التوظيف
        /// </summary>
        [Required(ErrorMessage = "Hire date is required")]
        public DateTime HireDate { get; set; }

        /// <summary>
        /// Termination date (if applicable)
        /// تاريخ انتهاء الخدمة (إن وجد)
        /// </summary>
        public DateTime? TerminationDate { get; set; }

        /// <summary>
        /// Employment status
        /// حالة التوظيف
        /// </summary>
        [Required(ErrorMessage = "Employment status is required")]
        public string EmploymentStatus { get; set; } = "Active";

        /// <summary>
        /// Employment type (Full-time, Part-time, Contract, etc.)
        /// نوع التوظيف (دوام كامل، دوام جزئي، عقد، إلخ)
        /// </summary>
        [StringLength(50, ErrorMessage = "Employment type cannot exceed 50 characters")]
        public string? EmploymentType { get; set; }

        /// <summary>
        /// Company ID
        /// معرف الشركة
        /// </summary>
        [Required(ErrorMessage = "Company is required")]
        public Guid CompanyId { get; set; }

        /// <summary>
        /// Company name
        /// اسم الشركة
        /// </summary>
        public string CompanyName { get; set; } = string.Empty;

        /// <summary>
        /// Department ID
        /// معرف القسم
        /// </summary>
        public Guid? DepartmentId { get; set; }

        /// <summary>
        /// Department name
        /// اسم القسم
        /// </summary>
        public string? DepartmentName { get; set; }

        /// <summary>
        /// Position ID
        /// معرف المنصب
        /// </summary>
        public Guid? PositionId { get; set; }

        /// <summary>
        /// Position title
        /// مسمى المنصب
        /// </summary>
        public string? PositionTitle { get; set; }

        /// <summary>
        /// Manager ID
        /// معرف المدير
        /// </summary>
        public Guid? ManagerId { get; set; }

        /// <summary>
        /// Manager name
        /// اسم المدير
        /// </summary>
        public string? ManagerName { get; set; }

        /// <summary>
        /// Basic salary
        /// الراتب الأساسي
        /// </summary>
        public decimal? BasicSalary { get; set; }

        /// <summary>
        /// Profile picture URL
        /// رابط صورة الملف الشخصي
        /// </summary>
        public string? ProfilePicture { get; set; }

        /// <summary>
        /// Emergency contact name
        /// اسم جهة الاتصال في حالات الطوارئ
        /// </summary>
        [StringLength(200, ErrorMessage = "Emergency contact name cannot exceed 200 characters")]
        public string? EmergencyContactName { get; set; }

        /// <summary>
        /// Emergency contact phone
        /// هاتف جهة الاتصال في حالات الطوارئ
        /// </summary>
        [StringLength(20, ErrorMessage = "Emergency contact phone cannot exceed 20 characters")]
        public string? EmergencyContactPhone { get; set; }

        /// <summary>
        /// Emergency contact relationship
        /// علاقة جهة الاتصال في حالات الطوارئ
        /// </summary>
        [StringLength(50, ErrorMessage = "Emergency contact relationship cannot exceed 50 characters")]
        public string? EmergencyContactRelationship { get; set; }

        /// <summary>
        /// Notes
        /// ملاحظات
        /// </summary>
        [StringLength(1000, ErrorMessage = "Notes cannot exceed 1000 characters")]
        public string? Notes { get; set; }

        /// <summary>
        /// Whether the employee is active
        /// ما إذا كان الموظف نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Creation date
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Last update date
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Created by user ID
        /// معرف المستخدم المنشئ
        /// </summary>
        public Guid CreatedBy { get; set; }

        /// <summary>
        /// Updated by user ID
        /// معرف المستخدم المحدث
        /// </summary>
        public Guid UpdatedBy { get; set; }
    }
}
