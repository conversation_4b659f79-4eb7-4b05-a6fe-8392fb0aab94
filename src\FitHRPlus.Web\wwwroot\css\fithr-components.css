/* FitHR Plus Custom Components Styles */
/* أنماط مكونات FitHR Plus المخصصة */

/* Page Header Styles */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 20px 20px;
}

.page-header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.page-title h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.page-title p {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
}

.page-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* Statistics Cards */
.stats-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
    border: none;
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stats-card-body {
    padding: 1.5rem;
}

.stats-card-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.stats-card-info h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: #2d3748;
}

.stats-card-info p {
    font-size: 0.875rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.25rem;
}

.stats-card-info small {
    font-size: 0.75rem;
    color: #718096;
}

.stats-card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stats-card-progress {
    height: 4px;
    background: #e2e8f0;
    border-radius: 2px;
    overflow: hidden;
}

.stats-card-progress .progress-bar {
    height: 100%;
    border-radius: 2px;
    transition: width 0.6s ease;
}

.stats-card-primary .stats-card-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-card-primary .progress-bar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-card-success .stats-card-icon {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.stats-card-success .progress-bar {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.stats-card-warning .stats-card-icon {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.stats-card-warning .progress-bar {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.stats-card-info .stats-card-icon {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

.stats-card-info .progress-bar {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

.stats-card-danger .stats-card-icon {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
}

.stats-card-danger .progress-bar {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
}

/* Activity List */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 12px;
    transition: all 0.2s ease;
}

.activity-item:hover {
    background: #edf2f7;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.activity-content {
    flex: 1;
}

.activity-content h6 {
    font-size: 0.875rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.activity-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #4a5568;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 1rem;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    text-decoration: none;
    color: #4a5568;
    transition: all 0.2s ease;
    cursor: pointer;
}

.quick-action-btn:hover {
    border-color: #667eea;
    color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.quick-action-btn i {
    font-size: 1.5rem;
}

.quick-action-btn span {
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #718096;
}

.empty-state-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h5 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.5rem;
}

.empty-state p {
    font-size: 0.875rem;
    margin-bottom: 1.5rem;
}

/* Chart Container */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Employee Info */
.employee-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.employee-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

.employee-details h6 {
    font-size: 0.875rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.125rem;
}

.employee-details small {
    font-size: 0.75rem;
    color: #718096;
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.status-badge.bg-success {
    background: #48bb78 !important;
    color: white;
}

.status-badge.bg-warning {
    background: #ed8936 !important;
    color: white;
}

.status-badge.bg-danger {
    background: #f56565 !important;
    color: white;
}

.status-badge.bg-info {
    background: #4299e1 !important;
    color: white;
}

/* Salary Amounts */
.salary-amount {
    font-weight: 700;
    font-size: 0.875rem;
}

.salary-amount.basic {
    color: #4299e1;
}

.salary-amount.gross {
    color: #48bb78;
}

.salary-amount.net {
    color: #ed8936;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .page-title h1 {
        font-size: 1.5rem;
    }
    
    .stats-card-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .activity-item {
        flex-direction: column;
        text-align: center;
    }
}

/* RTL Support */
[dir="rtl"] .page-header-content {
    text-align: right;
}

[dir="rtl"] .stats-card-content {
    flex-direction: row-reverse;
}

[dir="rtl"] .employee-info {
    flex-direction: row-reverse;
}

[dir="rtl"] .activity-item {
    flex-direction: row-reverse;
}
