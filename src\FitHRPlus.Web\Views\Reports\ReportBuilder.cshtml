@{
    ViewData["Title"] = "منشئ التقارير المتقدم";
}

<!-- Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-title">
            <h1><i class="bi bi-gear"></i> منشئ التقارير المتقدم</h1>
            <p>أدوات متقدمة لإنشاء تقارير معقدة ومخصصة</p>
        </div>
        <div class="page-actions">
            <a href="@Url.Action("Index")" class="btn btn-secondary">
                <i class="bi bi-arrow-right"></i>
                العودة للتقارير
            </a>
            <button type="button" class="btn btn-info" onclick="saveTemplate()">
                <i class="bi bi-save"></i>
                حفظ كقالب
            </button>
            <button type="button" class="btn btn-success" onclick="generateAdvancedReport()">
                <i class="bi bi-play-circle"></i>
                إنشاء التقرير
            </button>
        </div>
    </div>
</div>

<!-- Advanced Report Builder -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#dataTab">مصادر البيانات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#fieldsTab">الحقول</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#filtersTab">المرشحات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#groupingTab">التجميع</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#sortingTab">الترتيب</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#formatTab">التنسيق</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#previewTab">المعاينة</a>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content">
                    <!-- Data Sources Tab -->
                    <div class="tab-pane fade show active" id="dataTab">
                        <h6>اختر مصادر البيانات</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="employeesData" checked>
                                    <label class="form-check-label" for="employeesData">
                                        <strong>بيانات الموظفين</strong><br>
                                        <small class="text-muted">الاسم، القسم، المنصب، الراتب، تاريخ التوظيف</small>
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="attendanceData">
                                    <label class="form-check-label" for="attendanceData">
                                        <strong>بيانات الحضور والانصراف</strong><br>
                                        <small class="text-muted">أوقات الحضور، الانصراف، ساعات العمل، التأخير</small>
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="payrollData">
                                    <label class="form-check-label" for="payrollData">
                                        <strong>بيانات كشوف المرتبات</strong><br>
                                        <small class="text-muted">الراتب الأساسي، البدلات، الخصومات، صافي الراتب</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="leavesData">
                                    <label class="form-check-label" for="leavesData">
                                        <strong>بيانات الإجازات</strong><br>
                                        <small class="text-muted">نوع الإجازة، تاريخ البداية والنهاية، الحالة</small>
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="departmentsData">
                                    <label class="form-check-label" for="departmentsData">
                                        <strong>بيانات الأقسام</strong><br>
                                        <small class="text-muted">اسم القسم، المدير، عدد الموظفين، الميزانية</small>
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="performanceData">
                                    <label class="form-check-label" for="performanceData">
                                        <strong>بيانات الأداء</strong><br>
                                        <small class="text-muted">تقييمات الأداء، الأهداف، الإنجازات</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Fields Tab -->
                    <div class="tab-pane fade" id="fieldsTab">
                        <h6>اختر الحقول المطلوبة</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">الحقول المتاحة</h6>
                                <div class="available-fields" style="height: 300px; overflow-y: auto; border: 1px solid #dee2e6; padding: 10px;">
                                    <div class="field-item" draggable="true" data-field="employee_name">اسم الموظف</div>
                                    <div class="field-item" draggable="true" data-field="department">القسم</div>
                                    <div class="field-item" draggable="true" data-field="position">المنصب</div>
                                    <div class="field-item" draggable="true" data-field="salary">الراتب</div>
                                    <div class="field-item" draggable="true" data-field="hire_date">تاريخ التوظيف</div>
                                    <div class="field-item" draggable="true" data-field="attendance_date">تاريخ الحضور</div>
                                    <div class="field-item" draggable="true" data-field="check_in">وقت الحضور</div>
                                    <div class="field-item" draggable="true" data-field="check_out">وقت الانصراف</div>
                                    <div class="field-item" draggable="true" data-field="working_hours">ساعات العمل</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">الحقول المحددة</h6>
                                <div class="selected-fields" style="height: 300px; overflow-y: auto; border: 1px solid #dee2e6; padding: 10px;">
                                    <div class="selected-field-item" data-field="employee_name">
                                        اسم الموظف
                                        <button class="btn btn-sm btn-danger float-end" onclick="removeField(this)">×</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filters Tab -->
                    <div class="tab-pane fade" id="filtersTab">
                        <h6>إعداد المرشحات</h6>
                        <div class="filters-container">
                            <div class="filter-row mb-3">
                                <div class="row">
                                    <div class="col-md-3">
                                        <select class="form-select">
                                            <option>اسم الموظف</option>
                                            <option>القسم</option>
                                            <option>المنصب</option>
                                            <option>الراتب</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select">
                                            <option>يساوي</option>
                                            <option>لا يساوي</option>
                                            <option>يحتوي على</option>
                                            <option>أكبر من</option>
                                            <option>أصغر من</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" placeholder="القيمة">
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-danger" onclick="removeFilter(this)">حذف</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-primary" onclick="addFilter()">إضافة مرشح</button>
                    </div>

                    <!-- Grouping Tab -->
                    <div class="tab-pane fade" id="groupingTab">
                        <h6>تجميع البيانات</h6>
                        <div class="mb-3">
                            <label class="form-label">تجميع حسب</label>
                            <select class="form-select">
                                <option value="">بدون تجميع</option>
                                <option>القسم</option>
                                <option>المنصب</option>
                                <option>تاريخ التوظيف (شهر)</option>
                                <option>تاريخ التوظيف (سنة)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الدوال الإحصائية</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="countFunction">
                                <label class="form-check-label" for="countFunction">العدد</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="sumFunction">
                                <label class="form-check-label" for="sumFunction">المجموع</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="avgFunction">
                                <label class="form-check-label" for="avgFunction">المتوسط</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="maxFunction">
                                <label class="form-check-label" for="maxFunction">الحد الأقصى</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="minFunction">
                                <label class="form-check-label" for="minFunction">الحد الأدنى</label>
                            </div>
                        </div>
                    </div>

                    <!-- Sorting Tab -->
                    <div class="tab-pane fade" id="sortingTab">
                        <h6>ترتيب البيانات</h6>
                        <div class="sorting-container">
                            <div class="sort-row mb-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <select class="form-select">
                                            <option>اسم الموظف</option>
                                            <option>القسم</option>
                                            <option>المنصب</option>
                                            <option>الراتب</option>
                                            <option>تاريخ التوظيف</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <select class="form-select">
                                            <option>تصاعدي</option>
                                            <option>تنازلي</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-danger" onclick="removeSort(this)">حذف</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-primary" onclick="addSort()">إضافة ترتيب</button>
                    </div>

                    <!-- Format Tab -->
                    <div class="tab-pane fade" id="formatTab">
                        <h6>تنسيق التقرير</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">عنوان التقرير</label>
                                    <input type="text" class="form-control" value="تقرير مخصص">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">نوع التخطيط</label>
                                    <select class="form-select">
                                        <option>جدول</option>
                                        <option>بطاقات</option>
                                        <option>قائمة</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">حجم الخط</label>
                                    <select class="form-select">
                                        <option>صغير</option>
                                        <option>متوسط</option>
                                        <option>كبير</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">لون الرأس</label>
                                    <input type="color" class="form-control form-control-color" value="#0d6efd">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">إظهار الشعار</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="showLogo" checked>
                                        <label class="form-check-label" for="showLogo">إظهار شعار الشركة</label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">إظهار التاريخ</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="showDate" checked>
                                        <label class="form-check-label" for="showDate">إظهار تاريخ الإنشاء</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Preview Tab -->
                    <div class="tab-pane fade" id="previewTab">
                        <h6>معاينة التقرير</h6>
                        <div class="report-preview">
                            <div class="preview-header text-center mb-4">
                                <h4>تقرير مخصص</h4>
                                <p class="text-muted">تاريخ الإنشاء: @DateTime.Now.ToString("yyyy-MM-dd")</p>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="table-primary">
                                        <tr>
                                            <th>اسم الموظف</th>
                                            <th>القسم</th>
                                            <th>المنصب</th>
                                            <th>الراتب</th>
                                            <th>تاريخ التوظيف</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>أحمد محمد علي</td>
                                            <td>تقنية المعلومات</td>
                                            <td>مطور برمجيات أول</td>
                                            <td>8,500 ج.م</td>
                                            <td>2023-01-15</td>
                                        </tr>
                                        <tr>
                                            <td>فاطمة حسن أحمد</td>
                                            <td>الموارد البشرية</td>
                                            <td>أخصائي موارد بشرية</td>
                                            <td>6,800 ج.م</td>
                                            <td>2023-03-10</td>
                                        </tr>
                                        <tr>
                                            <td>محمد عبدالله حسن</td>
                                            <td>المحاسبة</td>
                                            <td>محاسب أول</td>
                                            <td>7,200 ج.م</td>
                                            <td>2023-02-20</td>
                                        </tr>
                                        <tr>
                                            <td>سارة أحمد محمد</td>
                                            <td>التسويق</td>
                                            <td>أخصائي تسويق</td>
                                            <td>6,500 ج.م</td>
                                            <td>2023-04-05</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
<script>
    function generateAdvancedReport() {
        showToast('جاري إنشاء التقرير المتقدم...', 'info');
        
        // Simulate advanced report generation
        setTimeout(() => {
            showToast('تم إنشاء التقرير المتقدم بنجاح', 'success');
        }, 3000);
    }

    function saveTemplate() {
        const templateName = prompt('أدخل اسم القالب:');
        if (templateName) {
            showToast(`تم حفظ القالب "${templateName}" بنجاح`, 'success');
        }
    }

    function addFilter() {
        const container = $('.filters-container');
        const filterRow = `
            <div class="filter-row mb-3">
                <div class="row">
                    <div class="col-md-3">
                        <select class="form-select">
                            <option>اسم الموظف</option>
                            <option>القسم</option>
                            <option>المنصب</option>
                            <option>الراتب</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select">
                            <option>يساوي</option>
                            <option>لا يساوي</option>
                            <option>يحتوي على</option>
                            <option>أكبر من</option>
                            <option>أصغر من</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" placeholder="القيمة">
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-danger" onclick="removeFilter(this)">حذف</button>
                    </div>
                </div>
            </div>
        `;
        container.append(filterRow);
    }

    function removeFilter(button) {
        $(button).closest('.filter-row').remove();
    }

    function addSort() {
        const container = $('.sorting-container');
        const sortRow = `
            <div class="sort-row mb-3">
                <div class="row">
                    <div class="col-md-6">
                        <select class="form-select">
                            <option>اسم الموظف</option>
                            <option>القسم</option>
                            <option>المنصب</option>
                            <option>الراتب</option>
                            <option>تاريخ التوظيف</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select">
                            <option>تصاعدي</option>
                            <option>تنازلي</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-danger" onclick="removeSort(this)">حذف</button>
                    </div>
                </div>
            </div>
        `;
        container.append(sortRow);
    }

    function removeSort(button) {
        $(button).closest('.sort-row').remove();
    }

    function removeField(button) {
        $(button).closest('.selected-field-item').remove();
    }

    function showToast(message, type) {
        // Simple toast implementation
        const toast = $(`
            <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `);
        
        $('.toast-container').append(toast);
        toast.toast('show');
        
        setTimeout(() => toast.remove(), 5000);
    }

    // Drag and drop for fields
    $(document).ready(function() {
        $('.field-item').on('dragstart', function(e) {
            e.originalEvent.dataTransfer.setData('text/plain', $(this).data('field'));
        });

        $('.selected-fields').on('dragover', function(e) {
            e.preventDefault();
        });

        $('.selected-fields').on('drop', function(e) {
            e.preventDefault();
            const fieldName = e.originalEvent.dataTransfer.getData('text/plain');
            const fieldText = $(`.field-item[data-field="${fieldName}"]`).text();
            
            // Check if field already exists
            if ($(`.selected-field-item[data-field="${fieldName}"]`).length === 0) {
                const selectedField = `
                    <div class="selected-field-item" data-field="${fieldName}">
                        ${fieldText}
                        <button class="btn btn-sm btn-danger float-end" onclick="removeField(this)">×</button>
                    </div>
                `;
                $(this).append(selectedField);
            }
        });
    });
</script>

<style>
.field-item {
    padding: 8px 12px;
    margin-bottom: 5px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    cursor: grab;
    transition: all 0.2s;
}

.field-item:hover {
    background: #e9ecef;
}

.selected-field-item {
    padding: 8px 12px;
    margin-bottom: 5px;
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    border-radius: 4px;
    position: relative;
}

.report-preview {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    background: white;
}
</style>
}
