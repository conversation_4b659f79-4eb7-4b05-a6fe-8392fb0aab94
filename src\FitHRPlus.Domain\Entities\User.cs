using FitHRPlus.Domain.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Domain.Entities
{
    /// <summary>
    /// User entity for system authentication and authorization
    /// كيان المستخدم لمصادقة وتخويل النظام
    /// </summary>
    public class User : BaseEntity
    {
        /// <summary>
        /// Unique username for login
        /// اسم المستخدم الفريد لتسجيل الدخول
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// User email address
        /// البريد الإلكتروني للمستخدم
        /// </summary>
        [Required]
        [MaxLength(100)]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Hashed password
        /// كلمة المرور المشفرة
        /// </summary>
        [Required]
        [MaxLength(500)]
        public string PasswordHash { get; set; } = string.Empty;

        /// <summary>
        /// Password salt for hashing
        /// ملح كلمة المرور للتشفير
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Salt { get; set; } = string.Empty;

        /// <summary>
        /// User first name
        /// الاسم الأول للمستخدم
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// User last name
        /// اسم العائلة للمستخدم
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// User phone number
        /// رقم هاتف المستخدم
        /// </summary>
        [MaxLength(20)]
        public string? Phone { get; set; }

        /// <summary>
        /// Profile picture file path
        /// مسار صورة الملف الشخصي
        /// </summary>
        [MaxLength(500)]
        public string? ProfilePicture { get; set; }

        /// <summary>
        /// Preferred language (ar, en)
        /// اللغة المفضلة
        /// </summary>
        [MaxLength(5)]
        public string PreferredLanguage { get; set; } = "ar";

        /// <summary>
        /// User timezone
        /// المنطقة الزمنية للمستخدم
        /// </summary>
        [MaxLength(50)]
        public string TimeZone { get; set; } = "Africa/Cairo";

        /// <summary>
        /// Email verification status
        /// حالة التحقق من البريد الإلكتروني
        /// </summary>
        public bool IsEmailVerified { get; set; } = false;

        /// <summary>
        /// Phone verification status
        /// حالة التحقق من الهاتف
        /// </summary>
        public bool IsPhoneVerified { get; set; } = false;

        /// <summary>
        /// Two-factor authentication enabled
        /// تفعيل المصادقة الثنائية
        /// </summary>
        public bool TwoFactorEnabled { get; set; } = false;

        /// <summary>
        /// Two-factor authentication secret key
        /// المفتاح السري للمصادقة الثنائية
        /// </summary>
        [MaxLength(100)]
        public string? TwoFactorSecret { get; set; }

        /// <summary>
        /// Last login timestamp
        /// وقت آخر تسجيل دخول
        /// </summary>
        public DateTime? LastLoginAt { get; set; }

        /// <summary>
        /// Last password change timestamp
        /// وقت آخر تغيير لكلمة المرور
        /// </summary>
        public DateTime LastPasswordChangeAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Failed login attempts counter
        /// عداد محاولات تسجيل الدخول الفاشلة
        /// </summary>
        public int FailedLoginAttempts { get; set; } = 0;

        /// <summary>
        /// Account locked until timestamp
        /// الحساب مقفل حتى هذا الوقت
        /// </summary>
        public DateTime? LockedUntil { get; set; }

        // Navigation Properties - خصائص التنقل

        /// <summary>
        /// User roles in different companies
        /// أدوار المستخدم في الشركات المختلفة
        /// </summary>
        public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();

        /// <summary>
        /// Employee record if user is an employee
        /// سجل الموظف إذا كان المستخدم موظفاً
        /// </summary>
        public virtual Employee? Employee { get; set; }

        /// <summary>
        /// Audit logs created by this user
        /// سجلات المراجعة التي أنشأها هذا المستخدم
        /// </summary>
        public virtual ICollection<AuditLog> AuditLogs { get; set; } = new List<AuditLog>();

        /// <summary>
        /// Notifications received by this user
        /// الإشعارات التي تلقاها هذا المستخدم
        /// </summary>
        public virtual ICollection<Notification> Notifications { get; set; } = new List<Notification>();

        // Helper Properties - خصائص مساعدة

        /// <summary>
        /// Full name of the user
        /// الاسم الكامل للمستخدم
        /// </summary>
        public string FullName => $"{FirstName} {LastName}";

        /// <summary>
        /// Check if account is locked
        /// التحقق من قفل الحساب
        /// </summary>
        public bool IsLocked => LockedUntil.HasValue && LockedUntil > DateTime.UtcNow;
    }
}