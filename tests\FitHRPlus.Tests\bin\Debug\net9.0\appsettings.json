{"ConnectionStrings": {"DefaultConnection": "Server=YASSER-PC\\SQL2019;Database=FitHRPlusDB;User Id=sa;Password=*******;TrustServerCertificate=true;MultipleActiveResultSets=true"}, "Jwt": {"SecretKey": "FitHRPlus_Super_Secret_Key_2024_Must_Be_At_Least_32_Characters_Long_For_Security", "Issuer": "FitHRPlus", "Audience": "FitHRPlus-Users", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 30, "ValidateLifetime": true, "ValidateIssuer": true, "ValidateAudience": true, "ValidateIssuerSigningKey": true, "ClockSkewMinutes": 5}, "PasswordHashing": {"Iterations": 100000, "SaltSize": 32, "HashSize": 32, "MinimumLength": 8, "MaximumLength": 100, "RequireUppercase": true, "RequireLowercase": true, "RequireDigits": true, "RequireSpecialChars": true, "MinimumUniqueChars": 4}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}