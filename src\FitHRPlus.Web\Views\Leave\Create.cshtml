@model FitHRPlus.Web.Models.Leave.CreateLeaveRequestViewModel
@{
    ViewData["Title"] = "Create Leave Request / إنشاء طلب إجازة";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-plus-circle text-primary me-2"></i>
                        Create Leave Request / إنشاء طلب إجازة
                    </h2>
                    <p class="text-muted mb-0">Submit a new leave request / تقديم طلب إجازة جديد</p>
                </div>
                <div>
                    <a href="@Url.Action("Index")" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to List / العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <form asp-action="Create" method="post" id="leaveRequestForm">
        <div class="row">
            <!-- Main Form -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>
                            Leave Request Details / تفاصيل طلب الإجازة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="EmployeeId" class="form-label required">Employee / الموظف</label>
                                    <select asp-for="EmployeeId" class="form-select" asp-items="Model.Employees" id="employeeSelect">
                                        <option value="">Select Employee / اختر الموظف</option>
                                    </select>
                                    <span asp-validation-for="EmployeeId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="LeaveTypeId" class="form-label required">Leave Type / نوع الإجازة</label>
                                    <select asp-for="LeaveTypeId" class="form-select" asp-items="Model.LeaveTypes" id="leaveTypeSelect">
                                        <option value="">Select Leave Type / اختر نوع الإجازة</option>
                                    </select>
                                    <span asp-validation-for="LeaveTypeId" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="StartDate" class="form-label required">Start Date / تاريخ البداية</label>
                                    <input asp-for="StartDate" type="date" class="form-control" id="startDate" />
                                    <span asp-validation-for="StartDate" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="EndDate" class="form-label required">End Date / تاريخ النهاية</label>
                                    <input asp-for="EndDate" type="date" class="form-control" id="endDate" />
                                    <span asp-validation-for="EndDate" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Total Days / إجمالي الأيام</label>
                                    <input type="text" class="form-control" id="totalDays" readonly placeholder="Auto calculated / محسوب تلقائياً" />
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Reason" class="form-label">Reason (English) / السبب (بالإنجليزية)</label>
                                    <textarea asp-for="Reason" class="form-control" rows="3" placeholder="Enter reason for leave request"></textarea>
                                    <span asp-validation-for="Reason" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="ReasonAr" class="form-label">Reason (Arabic) / السبب (بالعربية)</label>
                                    <textarea asp-for="ReasonAr" class="form-control" rows="3" placeholder="أدخل سبب طلب الإجازة" dir="rtl"></textarea>
                                    <span asp-validation-for="ReasonAr" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="EmergencyContact" class="form-label">Emergency Contact / جهة الاتصال للطوارئ</label>
                                    <input asp-for="EmergencyContact" type="text" class="form-control" placeholder="Emergency contact person" />
                                    <span asp-validation-for="EmergencyContact" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="EmergencyPhone" class="form-label">Emergency Phone / هاتف الطوارئ</label>
                                    <input asp-for="EmergencyPhone" type="tel" class="form-control" placeholder="Emergency phone number" />
                                    <span asp-validation-for="EmergencyPhone" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="card mt-3">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <a href="@Url.Action("Index")" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>
                                Cancel / إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-save me-1"></i>
                                Submit Request / تقديم الطلب
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Leave Type Information -->
                <div class="card" id="leaveTypeInfo" style="display: none;">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Leave Type Information / معلومات نوع الإجازة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="leaveTypeDetails">
                            <!-- Leave type details will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Leave Balance -->
                <div class="card mt-3" id="leaveBalance" style="display: none;">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-pie me-2"></i>
                            Leave Balance / رصيد الإجازة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="balanceDetails">
                            <!-- Balance details will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Guidelines -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>
                            Guidelines / الإرشادات
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <small>Submit requests at least 3 days in advance / قدم الطلبات قبل 3 أيام على الأقل</small>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <small>Check your leave balance before submitting / تحقق من رصيد إجازتك قبل التقديم</small>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <small>Provide emergency contact information / قدم معلومات الاتصال للطوارئ</small>
                            </li>
                            <li class="mb-0">
                                <i class="fas fa-check text-success me-2"></i>
                                <small>Requests require manager approval / الطلبات تحتاج موافقة المدير</small>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Leave type information data
            var leaveTypeInfo = @Html.Raw(Json.Serialize(Model.LeaveTypeInfo));
            var leaveBalances = @Html.Raw(Json.Serialize(Model.LeaveBalances));

            // Handle leave type selection
            $('#leaveTypeSelect').change(function() {
                var leaveTypeId = $(this).val();
                if (leaveTypeId && leaveTypeInfo[leaveTypeId]) {
                    showLeaveTypeInfo(leaveTypeInfo[leaveTypeId]);
                } else {
                    $('#leaveTypeInfo').hide();
                }
                updateLeaveBalance();
            });

            // Handle employee selection
            $('#employeeSelect').change(function() {
                updateLeaveBalance();
            });

            // Handle date changes
            $('#startDate, #endDate').change(function() {
                calculateTotalDays();
            });

            function showLeaveTypeInfo(info) {
                var html = '<div class="row">';
                
                if (info.maxDaysPerYear) {
                    html += '<div class="col-6"><small class="text-muted">Max Days/Year:</small><br><strong>' + info.maxDaysPerYear + '</strong></div>';
                }
                
                if (info.maxConsecutiveDays) {
                    html += '<div class="col-6"><small class="text-muted">Max Consecutive:</small><br><strong>' + info.maxConsecutiveDays + '</strong></div>';
                }
                
                html += '<div class="col-6"><small class="text-muted">Min Notice:</small><br><strong>' + info.minDaysNotice + ' days</strong></div>';
                html += '<div class="col-6"><small class="text-muted">Paid:</small><br><strong>' + (info.isPaid ? 'Yes' : 'No') + '</strong></div>';
                
                html += '</div>';
                
                if (info.description) {
                    html += '<hr><p class="mb-0"><small>' + info.description + '</small></p>';
                }

                $('#leaveTypeDetails').html(html);
                $('#leaveTypeInfo').show();
            }

            function updateLeaveBalance() {
                var employeeId = $('#employeeSelect').val();
                var leaveTypeId = $('#leaveTypeSelect').val();
                
                if (employeeId && leaveTypeId && leaveBalances[employeeId + '_' + leaveTypeId]) {
                    var balance = leaveBalances[employeeId + '_' + leaveTypeId];
                    var html = '<div class="text-center">';
                    html += '<h4 class="text-primary mb-1">' + balance + '</h4>';
                    html += '<small class="text-muted">Days Available / أيام متاحة</small>';
                    html += '</div>';
                    
                    $('#balanceDetails').html(html);
                    $('#leaveBalance').show();
                } else {
                    $('#leaveBalance').hide();
                }
            }

            function calculateTotalDays() {
                var startDate = $('#startDate').val();
                var endDate = $('#endDate').val();
                
                if (startDate && endDate) {
                    var start = new Date(startDate);
                    var end = new Date(endDate);
                    
                    if (end >= start) {
                        var totalDays = 0;
                        var currentDate = new Date(start);
                        
                        while (currentDate <= end) {
                            // Skip weekends (Friday = 5, Saturday = 6 for Middle East)
                            if (currentDate.getDay() !== 5 && currentDate.getDay() !== 6) {
                                totalDays++;
                            }
                            currentDate.setDate(currentDate.getDate() + 1);
                        }
                        
                        $('#totalDays').val(totalDays + ' days');
                    } else {
                        $('#totalDays').val('Invalid date range');
                    }
                } else {
                    $('#totalDays').val('');
                }
            }

            // Form validation
            $('#leaveRequestForm').submit(function(e) {
                var isValid = true;
                var errorMessage = '';

                // Check required fields
                if (!$('#employeeSelect').val()) {
                    isValid = false;
                    errorMessage += 'Please select an employee.\n';
                }

                if (!$('#leaveTypeSelect').val()) {
                    isValid = false;
                    errorMessage += 'Please select a leave type.\n';
                }

                if (!$('#startDate').val()) {
                    isValid = false;
                    errorMessage += 'Please select start date.\n';
                }

                if (!$('#endDate').val()) {
                    isValid = false;
                    errorMessage += 'Please select end date.\n';
                }

                // Check date validity
                if ($('#startDate').val() && $('#endDate').val()) {
                    var startDate = new Date($('#startDate').val());
                    var endDate = new Date($('#endDate').val());
                    var today = new Date();
                    today.setHours(0, 0, 0, 0);

                    if (startDate < today) {
                        isValid = false;
                        errorMessage += 'Start date cannot be in the past.\n';
                    }

                    if (endDate < startDate) {
                        isValid = false;
                        errorMessage += 'End date cannot be before start date.\n';
                    }
                }

                if (!isValid) {
                    e.preventDefault();
                    alert(errorMessage);
                    return false;
                }

                // Disable submit button to prevent double submission
                $('#submitBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Submitting...');
            });

            // Set minimum date to today
            var today = new Date().toISOString().split('T')[0];
            $('#startDate, #endDate').attr('min', today);
        });
    </script>
}

@section Styles {
    <style>
        .required::after {
            content: " *";
            color: red;
        }
        
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
    </style>
}
