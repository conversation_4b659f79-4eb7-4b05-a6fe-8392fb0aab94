using FitHRPlus.Application.DTOs.Common;
using FitHRPlus.Application.DTOs.Payroll;
using FitHRPlus.Application.Interfaces;
using FitHRPlus.Application.Common;
using FitHRPlus.Domain.Entities;
using FitHRPlus.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FitHRPlus.Application.Services
{
    /// <summary>
    /// Payroll service implementation
    /// تنفيذ خدمة كشوف المرتبات
    /// </summary>
    public class PayrollService : IPayrollService
    {
        private readonly FitHRContext _context;
        private readonly ILogger<PayrollService> _logger;

        public PayrollService(FitHRContext context, ILogger<PayrollService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Get paginated list of payrolls
        /// الحصول على قائمة مقسمة لكشوف المرتبات
        /// </summary>
        public async Task<ServiceResult<PaginatedResult<PayrollDto>>> GetPayrollsAsync(PayrollListDto request)
        {
            try
            {
                var query = _context.Payrolls
                    .Include(p => p.Employee)
                        .ThenInclude(e => e.Department)
                    .Include(p => p.Employee)
                        .ThenInclude(e => e.Position)
                    .Include(p => p.ProcessedByEmployee)
                    .Include(p => p.ApprovedByEmployee)
                    .Include(p => p.PaidByEmployee)
                    .Where(p => p.IsActive);

                // Apply filters
                if (request.CompanyId.HasValue)
                {
                    query = query.Where(p => p.Employee.CompanyId == request.CompanyId.Value);
                }

                if (request.EmployeeId.HasValue)
                {
                    query = query.Where(p => p.EmployeeId == request.EmployeeId.Value);
                }

                if (request.DepartmentId.HasValue)
                {
                    query = query.Where(p => p.Employee.DepartmentId == request.DepartmentId.Value);
                }

                if (request.PayrollMonth.HasValue)
                {
                    query = query.Where(p => p.PayrollMonth == request.PayrollMonth.Value);
                }

                if (request.PayrollYear.HasValue)
                {
                    query = query.Where(p => p.PayrollYear == request.PayrollYear.Value);
                }

                if (!string.IsNullOrEmpty(request.Status))
                {
                    query = query.Where(p => p.Status == request.Status);
                }

                if (!string.IsNullOrEmpty(request.SearchTerm))
                {
                    var searchTerm = request.SearchTerm.ToLower();
                    query = query.Where(p => 
                        p.Employee.FirstName.ToLower().Contains(searchTerm) ||
                        p.Employee.LastName.ToLower().Contains(searchTerm) ||
                        p.Employee.EmployeeCode.ToLower().Contains(searchTerm));
                }

                // Apply sorting
                query = request.SortBy?.ToLower() switch
                {
                    "employeename" => request.SortDirection?.ToLower() == "desc" 
                        ? query.OrderByDescending(p => p.Employee.FirstName)
                        : query.OrderBy(p => p.Employee.FirstName),
                    "department" => request.SortDirection?.ToLower() == "desc"
                        ? query.OrderByDescending(p => p.Employee.Department.Name)
                        : query.OrderBy(p => p.Employee.Department.Name),
                    "netsalary" => request.SortDirection?.ToLower() == "desc"
                        ? query.OrderByDescending(p => p.NetSalary)
                        : query.OrderBy(p => p.NetSalary),
                    "status" => request.SortDirection?.ToLower() == "desc"
                        ? query.OrderByDescending(p => p.Status)
                        : query.OrderBy(p => p.Status),
                    _ => request.SortDirection?.ToLower() == "desc"
                        ? query.OrderByDescending(p => p.PayrollYear).ThenByDescending(p => p.PayrollMonth)
                        : query.OrderBy(p => p.PayrollYear).ThenBy(p => p.PayrollMonth)
                };

                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);

                var items = await query
                    .Skip((request.Page - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .Select(p => new PayrollDto
                    {
                        Id = p.Id,
                        EmployeeId = p.EmployeeId,
                        EmployeeName = $"{p.Employee.FirstName} {p.Employee.LastName}",
                        EmployeeNameAr = $"{p.Employee.FirstNameAr} {p.Employee.LastNameAr}",
                        EmployeeCode = p.Employee.EmployeeCode,
                        DepartmentName = p.Employee.Department.Name,
                        DepartmentNameAr = p.Employee.Department.NameAr,
                        PositionTitle = p.Employee.Position != null ? p.Employee.Position.Title : null,
                        PositionTitleAr = p.Employee.Position != null ? p.Employee.Position.TitleAr : null,
                        PayrollMonth = p.PayrollMonth,
                        PayrollYear = p.PayrollYear,
                        BasicSalary = p.BasicSalary,
                        TotalAllowances = p.TotalAllowances,
                        GrossSalary = p.GrossSalary,
                        TotalDeductions = p.TotalDeductions,
                        NetSalary = p.NetSalary,
                        Status = p.Status,
                        StatusAr = GetStatusArabic(p.Status),
                        ProcessedAt = p.ProcessedAt,
                        ProcessedByName = p.ProcessedByEmployee != null 
                            ? $"{p.ProcessedByEmployee.FirstName} {p.ProcessedByEmployee.LastName}"
                            : null,
                        ApprovedAt = p.ApprovedAt,
                        ApprovedByName = p.ApprovedByEmployee != null 
                            ? $"{p.ApprovedByEmployee.FirstName} {p.ApprovedByEmployee.LastName}"
                            : null,
                        PaidAt = p.PaidAt,
                        PaidByName = p.PaidByEmployee != null 
                            ? $"{p.PaidByEmployee.FirstName} {p.PaidByEmployee.LastName}"
                            : null,
                        CreatedAt = p.CreatedAt,
                        UpdatedAt = p.UpdatedAt
                    })
                    .ToListAsync();

                var result = new PaginatedResult<PayrollDto>
                {
                    Items = items,
                    CurrentPage = request.Page,
                    TotalPages = totalPages,
                    TotalCount = totalCount,
                    PageSize = request.PageSize
                };

                return ServiceResult<PaginatedResult<PayrollDto>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payrolls");
                return ServiceResult<PaginatedResult<PayrollDto>>.Failure("An error occurred while retrieving payrolls");
            }
        }

        /// <summary>
        /// Get payroll by ID
        /// الحصول على كشف المرتبات بالمعرف
        /// </summary>
        public async Task<ServiceResult<PayrollDto>> GetPayrollByIdAsync(Guid id)
        {
            try
            {
                var payroll = await _context.Payrolls
                    .Include(p => p.Employee)
                        .ThenInclude(e => e.Department)
                    .Include(p => p.Employee)
                        .ThenInclude(e => e.Position)
                    .Include(p => p.ProcessedByEmployee)
                    .Include(p => p.ApprovedByEmployee)
                    .Include(p => p.PaidByEmployee)
                    .FirstOrDefaultAsync(p => p.Id == id && p.IsActive);

                if (payroll == null)
                {
                    return ServiceResult<PayrollDto>.Failure("Payroll not found");
                }

                var dto = new PayrollDto
                {
                    Id = payroll.Id,
                    EmployeeId = payroll.EmployeeId,
                    EmployeeName = $"{payroll.Employee.FirstName} {payroll.Employee.LastName}",
                    EmployeeNameAr = $"{payroll.Employee.FirstNameAr} {payroll.Employee.LastNameAr}",
                    EmployeeCode = payroll.Employee.EmployeeCode,
                    DepartmentName = payroll.Employee.Department.Name,
                    DepartmentNameAr = payroll.Employee.Department.NameAr,
                    PositionTitle = payroll.Employee.Position?.Title,
                    PositionTitleAr = payroll.Employee.Position?.TitleAr,
                    PayrollMonth = payroll.PayrollMonth,
                    PayrollYear = payroll.PayrollYear,
                    BasicSalary = payroll.BasicSalary,
                    TotalAllowances = payroll.TotalAllowances,
                    GrossSalary = payroll.GrossSalary,
                    TotalDeductions = payroll.TotalDeductions,
                    IncomeTax = payroll.IncomeTax,
                    SocialInsuranceEmployee = payroll.SocialInsuranceEmployee,
                    SocialInsuranceEmployer = payroll.SocialInsuranceEmployer,
                    MedicalInsurance = payroll.MedicalInsurance,
                    LateDeduction = payroll.LateDeduction,
                    OvertimeHours = payroll.OvertimeHours,
                    OvertimeAmount = payroll.OvertimeAmount,
                    ActualWorkingDays = payroll.ActualWorkingDays,
                    AbsentDays = payroll.AbsentDays,
                    LeaveDays = payroll.LeaveDays,
                    NetSalary = payroll.NetSalary,
                    Status = payroll.Status,
                    StatusAr = GetStatusArabic(payroll.Status),
                    ProcessedAt = payroll.ProcessedAt,
                    ProcessedBy = payroll.ProcessedBy,
                    ProcessedByName = payroll.ProcessedByEmployee != null 
                        ? $"{payroll.ProcessedByEmployee.FirstName} {payroll.ProcessedByEmployee.LastName}"
                        : null,
                    ApprovedAt = payroll.ApprovedAt,
                    ApprovedBy = payroll.ApprovedBy,
                    ApprovedByName = payroll.ApprovedByEmployee != null 
                        ? $"{payroll.ApprovedByEmployee.FirstName} {payroll.ApprovedByEmployee.LastName}"
                        : null,
                    PaidAt = payroll.PaidAt,
                    PaidBy = payroll.PaidBy,
                    PaidByName = payroll.PaidByEmployee != null 
                        ? $"{payroll.PaidByEmployee.FirstName} {payroll.PaidByEmployee.LastName}"
                        : null,
                    Notes = payroll.Notes,
                    NotesAr = payroll.NotesAr,
                    CreatedAt = payroll.CreatedAt,
                    UpdatedAt = payroll.UpdatedAt
                };

                return ServiceResult<PayrollDto>.Success(dto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payroll by ID: {Id}", id);
                return ServiceResult<PayrollDto>.Failure("An error occurred while retrieving the payroll");
            }
        }

        // Helper method to get status in Arabic
        private static string GetStatusArabic(string status)
        {
            return status switch
            {
                "Draft" => "مسودة",
                "Processed" => "معالج",
                "Approved" => "موافق عليه",
                "Paid" => "مدفوع",
                "Rejected" => "مرفوض",
                _ => status
            };
        }

        // Placeholder implementations for other methods
        public Task<ServiceResult<PayrollDto>> CreatePayrollAsync(CreatePayrollDto request, Guid userId)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<PayrollDto>> UpdatePayrollAsync(UpdatePayrollDto request, Guid userId)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<bool>> DeletePayrollAsync(Guid payrollId, Guid userId)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<PayrollDto>> ProcessPayrollAsync(ProcessPayrollDto request, Guid userId)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<PayrollDto>> ApprovePayrollAsync(ApprovePayrollDto request, Guid userId)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<PayrollDto>> PayPayrollAsync(PayPayrollDto request, Guid userId)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<PayrollDto>> RejectPayrollAsync(Guid payrollId, string reason, Guid userId)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<int>> GeneratePayrollsAsync(Guid companyId, int month, int year, Guid userId)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<PayrollDto>> CalculatePayrollAsync(Guid employeeId, int month, int year)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<PayrollStatisticsDto>> GetPayrollStatisticsAsync(Guid companyId, int? month = null, int? year = null)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<List<PayrollDto>>> GetEmployeePayrollHistoryAsync(Guid employeeId, int? year = null)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<byte[]>> ExportPayrollsAsync(PayrollListDto request)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<List<SalaryComponentDto>>> GetSalaryComponentsAsync(Guid companyId, Guid? employeeId = null)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<bool>> ValidatePayrollAsync(Guid payrollId)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<PayrollStatisticsDto>> GetPayrollSummaryAsync(Guid companyId, int fromMonth, int fromYear, int toMonth, int toYear)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<int>> BulkApprovePayrollsAsync(List<Guid> payrollIds, Guid userId)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<int>> BulkPayPayrollsAsync(List<Guid> payrollIds, string paymentMethod, Guid userId)
        {
            throw new NotImplementedException();
        }
    }
}
