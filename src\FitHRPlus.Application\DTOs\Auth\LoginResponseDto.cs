namespace FitHRPlus.Application.DTOs.Auth
{
    /// <summary>
    /// Login response data transfer object
    /// كائنة نقل البيانات لاستجابة تسجيل الدخول
    /// </summary>
    public class LoginResponseDto
    {
        /// <summary>
        /// JWT access token
        /// رمز الوصول JWT
        /// </summary>
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// Refresh token for token renewal
        /// رمز التحديث لتجديد الرمز
        /// </summary>
        public string RefreshToken { get; set; } = string.Empty;

        /// <summary>
        /// Token expiration time in UTC
        /// وقت انتهاء صلاحية الرمز بالتوقيت العالمي
        /// </summary>
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// User information
        /// معلومات المستخدم
        /// </summary>
        public UserInfoDto User { get; set; } = new();

        /// <summary>
        /// User's companies and roles
        /// شركات المستخدم وأدواره
        /// </summary>
        public List<UserCompanyRoleDto> Companies { get; set; } = new();

        /// <summary>
        /// Whether two-factor authentication is required
        /// ما إذا كانت المصادقة الثنائية مطلوبة
        /// </summary>
        public bool RequiresTwoFactor { get; set; } = false;

        /// <summary>
        /// Two-factor authentication token (if required)
        /// رمز المصادقة الثنائية (إذا كان مطلوباً)
        /// </summary>
        public string? TwoFactorToken { get; set; }
    }

    /// <summary>
    /// User information DTO
    /// كائنة نقل بيانات معلومات المستخدم
    /// </summary>
    public class UserInfoDto
    {
        public Guid Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string? ProfilePicture { get; set; }
        public string PreferredLanguage { get; set; } = "ar";
        public string TimeZone { get; set; } = "Africa/Cairo";
        public bool IsEmailVerified { get; set; }
        public bool IsPhoneVerified { get; set; }
        public bool TwoFactorEnabled { get; set; }
        public DateTime? LastLoginAt { get; set; }
    }

    /// <summary>
    /// User company and role information DTO
    /// كائنة نقل بيانات شركة المستخدم ودوره
    /// </summary>
    public class UserCompanyRoleDto
    {
        public Guid CompanyId { get; set; }
        public string CompanyName { get; set; } = string.Empty;
        public string CompanyNameAr { get; set; } = string.Empty;
        public string? CompanyLogo { get; set; }
        public Guid RoleId { get; set; }
        public string RoleName { get; set; } = string.Empty;
        public string RoleNameAr { get; set; } = string.Empty;
        public List<string> Permissions { get; set; } = new();
        public DateTime AssignedAt { get; set; }
        public bool IsActive { get; set; }
    }
}
