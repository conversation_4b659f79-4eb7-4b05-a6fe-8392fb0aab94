@{
    ViewData["Title"] = "جدولة التقارير";
}

<!-- Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-title">
            <h1><i class="bi bi-calendar-event"></i> جدولة التقارير</h1>
            <p>إعداد جدولة تلقائية لإنشاء وإرسال التقارير</p>
        </div>
        <div class="page-actions">
            <a href="@Url.Action("Index")" class="btn btn-secondary">
                <i class="bi bi-arrow-right"></i>
                العودة للتقارير
            </a>
            <button type="button" class="btn btn-primary" onclick="createNewSchedule()">
                <i class="bi bi-plus-circle"></i>
                جدولة جديدة
            </button>
        </div>
    </div>
</div>

<!-- Scheduled Reports -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">التقارير المجدولة</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>اسم التقرير</th>
                                <th>النوع</th>
                                <th>التكرار</th>
                                <th>آخر تشغيل</th>
                                <th>التشغيل التالي</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>تقرير الحضور الشهري</td>
                                <td>حضور وانصراف</td>
                                <td>شهرياً</td>
                                <td>2024-01-01</td>
                                <td>2024-02-01</td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td>
                                    <button class="btn btn-sm btn-primary" onclick="editSchedule(1)">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteSchedule(1)">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>تقرير الرواتب الشهري</td>
                                <td>كشوف مرتبات</td>
                                <td>شهرياً</td>
                                <td>2024-01-01</td>
                                <td>2024-02-01</td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td>
                                    <button class="btn btn-sm btn-primary" onclick="editSchedule(2)">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteSchedule(2)">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Schedule Modal -->
<div class="modal fade" id="scheduleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء جدولة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="scheduleForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم التقرير</label>
                                <input type="text" class="form-control" id="reportName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع التقرير</label>
                                <select class="form-select" id="reportType" required>
                                    <option value="">اختر نوع التقرير</option>
                                    <option value="attendance">تقرير الحضور</option>
                                    <option value="payroll">تقرير الرواتب</option>
                                    <option value="employees">تقرير الموظفين</option>
                                    <option value="leaves">تقرير الإجازات</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">التكرار</label>
                                <select class="form-select" id="frequency" required>
                                    <option value="">اختر التكرار</option>
                                    <option value="daily">يومياً</option>
                                    <option value="weekly">أسبوعياً</option>
                                    <option value="monthly">شهرياً</option>
                                    <option value="quarterly">ربع سنوي</option>
                                    <option value="yearly">سنوياً</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تاريخ البداية</label>
                                <input type="date" class="form-control" id="startDate" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني للإرسال</label>
                        <input type="email" class="form-control" id="emailRecipients" placeholder="<EMAIL>">
                        <small class="form-text text-muted">يمكن إدخال عدة عناوين مفصولة بفاصلة</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveSchedule()">حفظ الجدولة</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
<script>
    function createNewSchedule() {
        $('#scheduleModal').modal('show');
    }

    function saveSchedule() {
        // Validate form
        const form = document.getElementById('scheduleForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        // Get form data
        const data = {
            reportName: $('#reportName').val(),
            reportType: $('#reportType').val(),
            frequency: $('#frequency').val(),
            startDate: $('#startDate').val(),
            emailRecipients: $('#emailRecipients').val()
        };

        // TODO: Send to server
        console.log('Saving schedule:', data);
        
        // Show success message
        showToast('تم حفظ الجدولة بنجاح', 'success');
        $('#scheduleModal').modal('hide');
        
        // Reset form
        form.reset();
    }

    function editSchedule(id) {
        showToast('فتح تعديل الجدولة...', 'info');
        // TODO: Implement edit functionality
    }

    function deleteSchedule(id) {
        if (confirm('هل أنت متأكد من حذف هذه الجدولة؟')) {
            showToast('تم حذف الجدولة بنجاح', 'success');
            // TODO: Implement delete functionality
        }
    }

    function showToast(message, type) {
        // Simple toast implementation
        const toast = $(`
            <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `);
        
        $('.toast-container').append(toast);
        toast.toast('show');
        
        setTimeout(() => toast.remove(), 5000);
    }
</script>
}
