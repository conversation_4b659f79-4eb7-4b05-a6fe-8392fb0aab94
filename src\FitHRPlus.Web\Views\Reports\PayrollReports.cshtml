@model FitHRPlus.Web.Models.Reports.PayrollReportsViewModel
@{
    ViewData["Title"] = "تقارير كشوف المرتبات";
    ViewData["PageTitle"] = "تقارير كشوف المرتبات";
    ViewData["Breadcrumb"] = new List<(string, string)>
    {
        ("الرئيسية", Url.Action("Index", "Home")),
        ("التقارير", Url.Action("Index", "Reports")),
        ("تقارير كشوف المرتبات", "")
    };
}

<div class="container-fluid">
    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر التقرير
                    </h5>
                </div>
                <div class="card-body">
                    <form method="get" id="filterForm">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="fromMonth" class="form-label">الشهر</label>
                                <select name="fromMonth" id="fromMonth" class="form-select">
                                    <option value="1" selected="@(Model.FromMonth == 1)">يناير</option>
                                    <option value="2" selected="@(Model.FromMonth == 2)">فبراير</option>
                                    <option value="3" selected="@(Model.FromMonth == 3)">مارس</option>
                                    <option value="4" selected="@(Model.FromMonth == 4)">أبريل</option>
                                    <option value="5" selected="@(Model.FromMonth == 5)">مايو</option>
                                    <option value="6" selected="@(Model.FromMonth == 6)">يونيو</option>
                                    <option value="7" selected="@(Model.FromMonth == 7)">يوليو</option>
                                    <option value="8" selected="@(Model.FromMonth == 8)">أغسطس</option>
                                    <option value="9" selected="@(Model.FromMonth == 9)">سبتمبر</option>
                                    <option value="10" selected="@(Model.FromMonth == 10)">أكتوبر</option>
                                    <option value="11" selected="@(Model.FromMonth == 11)">نوفمبر</option>
                                    <option value="12" selected="@(Model.FromMonth == 12)">ديسمبر</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="fromYear" class="form-label">السنة</label>
                                <select name="fromYear" id="fromYear" class="form-select">
                                    @for (int year = DateTime.Now.Year - 5; year <= DateTime.Now.Year; year++)
                                    {
                                        <option value="@year" selected="@(Model.FromYear == year)">@year</option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="department" class="form-label">القسم</label>
                                <select name="department" id="department" class="form-select">
                                    <option value="">جميع الأقسام</option>
                                    <option value="hr" selected="@(Model.Department == "hr")">الموارد البشرية</option>
                                    <option value="it" selected="@(Model.Department == "it")">تقنية المعلومات</option>
                                    <option value="finance" selected="@(Model.Department == "finance")">المحاسبة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="reportType" class="form-label">نوع التقرير</label>
                                <select name="reportType" id="reportType" class="form-select">
                                    <option value="summary" selected="@(Model.ReportType == "summary")">ملخص</option>
                                    <option value="detailed" selected="@(Model.ReportType == "detailed")">مفصل</option>
                                    <option value="comparison" selected="@(Model.ReportType == "comparison")">مقارنة</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-1"></i>
                                    تطبيق الفلاتر
                                </button>
                                <button type="button" class="btn btn-success me-2" onclick="exportReport('excel')">
                                    <i class="fas fa-file-excel me-1"></i>
                                    تصدير Excel
                                </button>
                                <button type="button" class="btn btn-danger" onclick="exportReport('pdf')">
                                    <i class="fas fa-file-pdf me-1"></i>
                                    تصدير PDF
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.TotalSalaries.ToString("N0")</h4>
                            <p class="mb-0">إجمالي الرواتب</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.TotalAllowances.ToString("N0")</h4>
                            <p class="mb-0">إجمالي البدلات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-plus-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.TotalDeductions.ToString("N0")</h4>
                            <p class="mb-0">إجمالي الخصومات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-minus-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.NetSalaries.ToString("N0")</h4>
                            <p class="mb-0">صافي الرواتب</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calculator fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        توزيع الرواتب حسب الأقسام
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="salariesByDepartmentChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        اتجاه الرواتب الشهرية
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="salaryTrendChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        تفصيل البدلات
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="allowanceBreakdownChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        تفصيل الخصومات
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="deductionBreakdownChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Table -->
    @if (Model.ReportType == "detailed")
    {
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>
                            تفاصيل كشوف المرتبات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الموظف</th>
                                        <th>القسم</th>
                                        <th>الراتب الأساسي</th>
                                        <th>البدلات</th>
                                        <th>الخصومات</th>
                                        <th>صافي الراتب</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Sample data - replace with actual data -->
                                    <tr>
                                        <td>أحمد محمد علي</td>
                                        <td>تقنية المعلومات</td>
                                        <td>8,000 ريال</td>
                                        <td>1,600 ريال</td>
                                        <td>1,200 ريال</td>
                                        <td>8,400 ريال</td>
                                        <td><span class="badge bg-success">مدفوع</span></td>
                                    </tr>
                                    <tr>
                                        <td>فاطمة سالم أحمد</td>
                                        <td>الموارد البشرية</td>
                                        <td>7,500 ريال</td>
                                        <td>1,500 ريال</td>
                                        <td>1,125 ريال</td>
                                        <td>7,875 ريال</td>
                                        <td><span class="badge bg-success">مدفوع</span></td>
                                    </tr>
                                    <tr>
                                        <td>محمد سالم عبدالله</td>
                                        <td>المحاسبة</td>
                                        <td>6,500 ريال</td>
                                        <td>1,300 ريال</td>
                                        <td>975 ريال</td>
                                        <td>6,825 ريال</td>
                                        <td><span class="badge bg-warning">معلق</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        $(document).ready(function() {
            initializeCharts();
        });

        function initializeCharts() {
            // Salaries by Department Chart
            const salariesByDeptCtx = document.getElementById('salariesByDepartmentChart').getContext('2d');
            new Chart(salariesByDeptCtx, {
                type: 'pie',
                data: {
                    labels: @Html.Raw(Json.Serialize(Model.SalariesByDepartment.Keys)),
                    datasets: [{
                        data: @Html.Raw(Json.Serialize(Model.SalariesByDepartment.Values)),
                        backgroundColor: [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0',
                            '#9966FF'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Salary Trend Chart
            const salaryTrendCtx = document.getElementById('salaryTrendChart').getContext('2d');
            new Chart(salaryTrendCtx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'إجمالي الرواتب',
                        data: [145000, 150000, 148000, 152000, 155000, 160000],
                        borderColor: '#36A2EB',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString() + ' ريال';
                                }
                            }
                        }
                    }
                }
            });

            // Allowance Breakdown Chart
            const allowanceCtx = document.getElementById('allowanceBreakdownChart').getContext('2d');
            new Chart(allowanceCtx, {
                type: 'bar',
                data: {
                    labels: ['بدل المواصلات', 'بدل الطعام', 'بدل السكن', 'بدلات أخرى'],
                    datasets: [{
                        label: 'البدلات',
                        data: [8000, 6000, 7000, 4000],
                        backgroundColor: '#4BC0C0'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString() + ' ريال';
                                }
                            }
                        }
                    }
                }
            });

            // Deduction Breakdown Chart
            const deductionCtx = document.getElementById('deductionBreakdownChart').getContext('2d');
            new Chart(deductionCtx, {
                type: 'bar',
                data: {
                    labels: ['التأمينات', 'الضرائب', 'الغياب', 'أخرى'],
                    datasets: [{
                        label: 'الخصومات',
                        data: [6000, 4000, 2000, 3000],
                        backgroundColor: '#FF6384'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString() + ' ريال';
                                }
                            }
                        }
                    }
                }
            });
        }

        function exportReport(format) {
            const params = new URLSearchParams({
                fromMonth: $('#fromMonth').val(),
                fromYear: $('#fromYear').val(),
                department: $('#department').val(),
                reportType: $('#reportType').val(),
                format: format
            });

            window.open(`@Url.Action("ExportPayrollReport")?${params.toString()}`, '_blank');
        }
    </script>
}
