@model FitHRPlus.Web.Models.Employees.EmployeeListViewModel
@{
    ViewData["Title"] = "قائمة الموظفين";
}

<!-- Employee Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card card-blue">
            <i class="bi bi-people text-primary"></i>
            <h4>@(Model?.TotalCount ?? 0)</h4>
            <p>إجمالي الموظفين</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card card-green">
            <i class="bi bi-person-check text-success"></i>
            <h4>@(Model?.Employees?.Count(e => e.IsActive) ?? 0)</h4>
            <p>الموظفين النشطين</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card card-orange">
            <i class="bi bi-building text-warning"></i>
            <h4>@(Model?.Employees?.Select(e => e.DepartmentName).Distinct().Count() ?? 0)</h4>
            <p>عدد الأقسام</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card card-red">
            <i class="bi bi-person-plus text-danger"></i>
            <h4>@(Model?.Employees?.Count(e => e.HireDate >= DateTime.Now.AddMonths(-1)) ?? 0)</h4>
            <p>موظفين جدد</p>
        </div>
    </div>
</div>

<!-- Employees Table Container -->
<div class="table-container">
    <div class="table-header">
        <h5>
            <i class="bi bi-people me-2"></i>
            قائمة الموظفين
        </h5>
        <a href="@Url.Action("Create")" class="btn btn-success">
            <i class="bi bi-plus-circle me-1"></i>
            إضافة موظف جديد
        </a>
    </div>

    <!-- Search and Filter Section -->
    <div class="row mb-3">
        <div class="col-md-6">
            <div class="input-group">
                <input type="text" class="form-control" placeholder="البحث عن موظف..." id="searchInput">
                <button class="btn btn-outline-secondary" type="button">
                    <i class="bi bi-search"></i>
                </button>
            </div>
        </div>
        <div class="col-md-3">
            <select class="form-select" id="departmentFilter">
                <option value="">جميع الأقسام</option>
                <option value="hr">الموارد البشرية</option>
                <option value="it">تكنولوجيا المعلومات</option>
                <option value="finance">المالية</option>
            </select>
        </div>
        <div class="col-md-3">
            <select class="form-select" id="statusFilter">
                <option value="">جميع الحالات</option>
                <option value="active">نشط</option>
                <option value="inactive">غير نشط</option>
            </select>
        </div>
    </div>

    <!-- Employees Table -->
    <div class="table-responsive">
        <table class="table table-hover">
            <thead style="background: linear-gradient(135deg, #1e3a8a, #2563eb); color: white;">
                <tr>
                    <th>الصورة</th>
                    <th>الاسم</th>
                    <th>رقم الموظف</th>
                    <th>القسم</th>
                    <th>المنصب</th>
                    <th>البريد الإلكتروني</th>
                    <th>الهاتف</th>
                    <th>تاريخ التوظيف</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                @if (Model?.Employees?.Any() == true)
                {
                    @foreach (var employee in Model.Employees)
                    {
                        <tr>
                            <td>
                                @if (!string.IsNullOrEmpty(employee.ProfilePicture))
                                {
                                    <img src="@employee.ProfilePicture" alt="@employee.FullName" class="rounded-circle" width="40" height="40">
                                }
                                else
                                {
                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="bi bi-person text-white"></i>
                                    </div>
                                }
                            </td>
                            <td><strong>@employee.FullName</strong></td>
                            <td><span class="badge bg-light text-dark">@employee.EmployeeNumber</span></td>
                            <td>@employee.DepartmentName</td>
                            <td>@employee.PositionName</td>
                            <td>@employee.Email</td>
                            <td>@employee.PhoneNumber</td>
                            <td>@employee.HireDate.ToString("dd/MM/yyyy")</td>
                            <td>
                                @if (employee.IsActive)
                                {
                                    <span class="badge bg-success">
                                        <i class="bi bi-check-circle me-1"></i>نشط
                                    </span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">
                                        <i class="bi bi-x-circle me-1"></i>غير نشط
                                    </span>
                                }
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="@Url.Action("Details", new { id = employee.Id })" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="@Url.Action("Edit", new { id = employee.Id })" class="btn btn-sm btn-warning" title="تعديل">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" title="حذف" onclick="deleteEmployee('@employee.Id')">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    }
                }
            else
            {
                <tr>
                    <td colspan="10" class="text-center text-muted py-5">
                        <i class="bi bi-people fa-3x mb-3 text-muted"></i>
                        <h5 class="text-muted">لا توجد موظفين مسجلين حالياً</h5>
                        <p class="text-muted">ابدأ بإضافة أول موظف في النظام</p>
                        <a href="@Url.Action("Create")" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-1"></i>
                            إضافة أول موظف
                        </a>
                    </td>
                </tr>
            }
        </tbody>
    </table>
</div>

<!-- Pagination -->
@if (Model?.TotalPages > 1)
{
    <nav aria-label="صفحات الموظفين" class="mt-4">
        <ul class="pagination justify-content-center">
            @if (Model.CurrentPage > 1)
            {
                <li class="page-item">
                    <a class="page-link" href="?page=@(Model.CurrentPage - 1)">
                        <i class="bi bi-chevron-right me-1"></i>السابق
                    </a>
                </li>
            }

            @for (int i = 1; i <= Model.TotalPages; i++)
            {
                <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                    <a class="page-link" href="?page=@i">@i</a>
                </li>
            }

            @if (Model.CurrentPage < Model.TotalPages)
            {
                <li class="page-item">
                    <a class="page-link" href="?page=@(Model.CurrentPage + 1)">
                        التالي<i class="bi bi-chevron-left ms-1"></i>
                    </a>
                </li>
            }
        </ul>
    </nav>
}
</div>

@section Scripts {
    <script>
        function deleteEmployee(employeeId) {
            if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
                $.ajax({
                    url: '@Url.Action("Delete")',
                    type: 'POST',
                    data: {
                        id: employeeId,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            location.reload();
                        } else {
                            alert('حدث خطأ أثناء حذف الموظف: ' + result.message);
                        }
                    },
                    error: function() {
                        alert('حدث خطأ أثناء حذف الموظف');
                    }
                });
            }
        }

        // Search functionality
        $('#searchInput').on('keyup', function() {
            var value = $(this).val().toLowerCase();
            $('tbody tr').filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
            });
        });

        // Department filter
        $('#departmentFilter').on('change', function() {
            var value = $(this).val().toLowerCase();
            $('tbody tr').filter(function() {
                if (value === '') {
                    $(this).show();
                } else {
                    $(this).toggle($(this).find('td:nth-child(4)').text().toLowerCase().indexOf(value) > -1);
                }
            });
        });
    </script>
}
