@model FitHRPlus.Web.Models.Department.DepartmentViewModel
@{
    ViewData["Title"] = "Edit Department / تعديل القسم";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-edit text-warning me-2"></i>
                        Edit Department / تعديل القسم
                    </h2>
                    <p class="text-muted mb-0">Update department information / تحديث معلومات القسم</p>
                </div>
                <div>
                    <a href="@Url.Action("Index")" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to List / العودة للقائمة
                    </a>
                    <a href="@Url.Action("Details", new { id = Model.Id })" class="btn btn-info">
                        <i class="fas fa-eye me-1"></i>
                        View Details / عرض التفاصيل
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>
                        Department Information / معلومات القسم
                    </h5>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post">
                        @Html.AntiForgeryToken()
                        <input asp-for="Id" type="hidden" />
                        <input asp-for="CompanyId" type="hidden" />
                        <input asp-for="CreatedAt" type="hidden" />

                        <div class="row">
                            <!-- Department Name -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="Name" class="form-label">
                                    <i class="fas fa-tag me-1"></i>
                                    Department Name / اسم القسم
                                    <span class="text-danger">*</span>
                                </label>
                                <input asp-for="Name" class="form-control" placeholder="Enter department name" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>

                            <!-- Department Name (Arabic) -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="NameAr" class="form-label">
                                    <i class="fas fa-tag me-1"></i>
                                    Department Name (Arabic) / اسم القسم بالعربية
                                    <span class="text-danger">*</span>
                                </label>
                                <input asp-for="NameAr" class="form-control" placeholder="أدخل اسم القسم بالعربية" dir="rtl" />
                                <span asp-validation-for="NameAr" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Description -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="Description" class="form-label">
                                    <i class="fas fa-align-left me-1"></i>
                                    Description / الوصف
                                </label>
                                <textarea asp-for="Description" class="form-control" rows="4" 
                                          placeholder="Enter department description"></textarea>
                                <span asp-validation-for="Description" class="text-danger"></span>
                            </div>

                            <!-- Description (Arabic) -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="DescriptionAr" class="form-label">
                                    <i class="fas fa-align-right me-1"></i>
                                    Description (Arabic) / الوصف بالعربية
                                </label>
                                <textarea asp-for="DescriptionAr" class="form-control" rows="4" 
                                          placeholder="أدخل وصف القسم بالعربية" dir="rtl"></textarea>
                                <span asp-validation-for="DescriptionAr" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Budget -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="Budget" class="form-label">
                                    <i class="fas fa-dollar-sign me-1"></i>
                                    Budget / الميزانية
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input asp-for="Budget" class="form-control" type="number" step="0.01" 
                                           placeholder="0.00" />
                                </div>
                                <span asp-validation-for="Budget" class="text-danger"></span>
                                <div class="form-text">Optional budget allocation for this department</div>
                            </div>

                            <!-- Status -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="IsActive" class="form-label">
                                    <i class="fas fa-toggle-on me-1"></i>
                                    Status / الحالة
                                </label>
                                <div class="form-check form-switch">
                                    <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                    <label asp-for="IsActive" class="form-check-label">
                                        Active Department / قسم نشط
                                    </label>
                                </div>
                                <div class="form-text">Active departments can have employees assigned</div>
                            </div>
                        </div>

                        <!-- Department Statistics -->
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-chart-bar me-2"></i>
                                            Department Statistics / إحصائيات القسم
                                        </h6>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="text-center">
                                                    <h4 class="text-primary">@Model.EmployeeCount</h4>
                                                    <small class="text-muted">Employees / الموظفين</small>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="text-center">
                                                    <h6 class="text-muted">@Model.CreatedAt.ToString("dd/MM/yyyy")</h6>
                                                    <small class="text-muted">Created / تاريخ الإنشاء</small>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="text-center">
                                                    <h6 class="text-muted">@Model.UpdatedAt.ToString("dd/MM/yyyy")</h6>
                                                    <small class="text-muted">Last Updated / آخر تحديث</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <a href="@Url.Action("Index")" class="btn btn-secondary">
                                            <i class="fas fa-times me-1"></i>
                                            Cancel / إلغاء
                                        </a>
                                    </div>
                                    <div>
                                        <button type="button" class="btn btn-danger me-2" 
                                                onclick="confirmDelete('@Model.Id', '@Model.Name')">
                                            <i class="fas fa-trash me-1"></i>
                                            Delete / حذف
                                        </button>
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-save me-1"></i>
                                            Update Department / تحديث القسم
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete / تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this department?</p>
                <p>هل أنت متأكد من حذف هذا القسم؟</p>
                <p><strong id="departmentName"></strong></p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    This action cannot be undone and may affect employees assigned to this department.
                    <br>
                    هذا الإجراء لا يمكن التراجع عنه وقد يؤثر على الموظفين المعينين في هذا القسم.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel / إلغاء</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">Delete / حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        function confirmDelete(id, name) {
            document.getElementById('departmentName').textContent = name;
            document.getElementById('deleteForm').action = '@Url.Action("Delete")/' + id;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        $(document).ready(function() {
            // Format budget input
            $('#Budget').on('input', function() {
                var value = $(this).val();
                if (value && !isNaN(value)) {
                    $(this).val(parseFloat(value).toFixed(2));
                }
            });

            // Form validation enhancement
            $('form').on('submit', function(e) {
                var isValid = true;
                
                // Check required fields
                if (!$('#Name').val().trim()) {
                    isValid = false;
                    $('#Name').addClass('is-invalid');
                } else {
                    $('#Name').removeClass('is-invalid');
                }
                
                if (!$('#NameAr').val().trim()) {
                    isValid = false;
                    $('#NameAr').addClass('is-invalid');
                } else {
                    $('#NameAr').removeClass('is-invalid');
                }
                
                if (!isValid) {
                    e.preventDefault();
                    toastr.error('Please fill in all required fields / يرجى ملء جميع الحقول المطلوبة');
                }
            });
        });
    </script>
}
