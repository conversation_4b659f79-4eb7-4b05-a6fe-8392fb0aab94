@model FitHRPlus.Web.Models.Settings.SystemSettingsViewModel
@{
    ViewData["Title"] = "إعدادات النظام";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-0">
                        <i class="fas fa-server me-2"></i>
                        إعدادات النظام
                    </h2>
                    <p class="text-muted mb-0">إدارة إعدادات الأمان والنظام</p>
                </div>
                <div>
                    <a href="@Url.Action("Index", "Settings")" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للإعدادات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <form asp-action="System" method="post" class="needs-validation" novalidate>
        <div class="row">
            <div class="col-lg-8">
                <!-- Security Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            إعدادات الأمان
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="SessionTimeout" class="form-label">مهلة الجلسة (بالدقائق)</label>
                                    <input asp-for="SessionTimeout" class="form-control" type="number" min="5" max="480" required>
                                    <div class="form-text">المدة بالدقائق قبل انتهاء صلاحية الجلسة</div>
                                    <span asp-validation-for="SessionTimeout" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="MaxLoginAttempts" class="form-label">الحد الأقصى لمحاولات تسجيل الدخول</label>
                                    <input asp-for="MaxLoginAttempts" class="form-control" type="number" min="3" max="10" required>
                                    <div class="form-text">عدد المحاولات المسموحة قبل حظر الحساب</div>
                                    <span asp-validation-for="MaxLoginAttempts" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="form-check mb-3">
                                    <input asp-for="RequireEmailVerification" class="form-check-input" type="checkbox">
                                    <label asp-for="RequireEmailVerification" class="form-check-label">
                                        تتطلب تأكيد البريد الإلكتروني
                                    </label>
                                    <div class="form-text">يتطلب من المستخدمين الجدد تأكيد بريدهم الإلكتروني</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Password Policy -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-key me-2"></i>
                            سياسة كلمات المرور
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="PasswordMinLength" class="form-label">الحد الأدنى لطول كلمة المرور</label>
                                    <input asp-for="PasswordMinLength" class="form-control" type="number" min="6" max="20" required>
                                    <span asp-validation-for="PasswordMinLength" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input asp-for="RequireSpecialCharacters" class="form-check-input" type="checkbox">
                                    <label asp-for="RequireSpecialCharacters" class="form-check-label">
                                        تتطلب أحرف خاصة (!@@#$%^&*)
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input asp-for="RequireNumbers" class="form-check-input" type="checkbox">
                                    <label asp-for="RequireNumbers" class="form-check-label">
                                        تتطلب أرقام (0-9)
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input asp-for="RequireUppercase" class="form-check-input" type="checkbox">
                                    <label asp-for="RequireUppercase" class="form-check-label">
                                        تتطلب أحرف كبيرة (A-Z)
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input asp-for="RequireLowercase" class="form-check-input" type="checkbox">
                                    <label asp-for="RequireLowercase" class="form-check-label">
                                        تتطلب أحرف صغيرة (a-z)
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Access -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-users me-2"></i>
                            إعدادات الوصول للنظام
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input asp-for="AllowRegistration" class="form-check-input" type="checkbox">
                                    <label asp-for="AllowRegistration" class="form-check-label">
                                        السماح بالتسجيل الجديد
                                    </label>
                                    <div class="form-text">السماح للمستخدمين الجدد بإنشاء حسابات</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input asp-for="MaintenanceMode" class="form-check-input" type="checkbox">
                                    <label asp-for="MaintenanceMode" class="form-check-label">
                                        وضع الصيانة
                                    </label>
                                    <div class="form-text">تعطيل الوصول للنظام مؤقتاً للصيانة</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- System Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            حالة النظام
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>حالة النظام:</span>
                            <span class="badge bg-success">يعمل بشكل طبيعي</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>وضع الصيانة:</span>
                            <span class="badge bg-@(Model.MaintenanceMode ? "warning" : "success")">
                                @(Model.MaintenanceMode ? "مفعل" : "معطل")
                            </span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>التسجيل الجديد:</span>
                            <span class="badge bg-@(Model.AllowRegistration ? "success" : "secondary")">
                                @(Model.AllowRegistration ? "مسموح" : "معطل")
                            </span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span>آخر تحديث:</span>
                            <span class="text-muted">@DateTime.Now.ToString("dd/MM/yyyy HH:mm")</span>
                        </div>
                    </div>
                </div>

                <!-- Password Policy Preview -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-eye me-2"></i>
                            معاينة سياسة كلمة المرور
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="small">
                            <p class="mb-2"><strong>المتطلبات الحالية:</strong></p>
                            <ul class="list-unstyled mb-0">
                                <li class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>
                                    الحد الأدنى @Model.PasswordMinLength أحرف
                                </li>
                                <li class="mb-1">
                                    <i class="fas fa-@(Model.RequireUppercase ? "check text-success" : "times text-muted") me-2"></i>
                                    أحرف كبيرة (A-Z)
                                </li>
                                <li class="mb-1">
                                    <i class="fas fa-@(Model.RequireLowercase ? "check text-success" : "times text-muted") me-2"></i>
                                    أحرف صغيرة (a-z)
                                </li>
                                <li class="mb-1">
                                    <i class="fas fa-@(Model.RequireNumbers ? "check text-success" : "times text-muted") me-2"></i>
                                    أرقام (0-9)
                                </li>
                                <li class="mb-1">
                                    <i class="fas fa-@(Model.RequireSpecialCharacters ? "check text-success" : "times text-muted") me-2"></i>
                                    أحرف خاصة (!@@#$%^&*)
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ التغييرات
                            </button>
                            <a href="@Url.Action("Index", "Settings")" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                        <hr>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-warning" onclick="clearCache()">
                                <i class="fas fa-broom me-2"></i>
                                مسح الذاكرة المؤقتة
                            </button>
                            <button type="button" class="btn btn-info" onclick="restartSystem()">
                                <i class="fas fa-redo me-2"></i>
                                إعادة تشغيل النظام
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        $(document).ready(function() {
            // Form validation
            $('.needs-validation').on('submit', function(e) {
                if (!this.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                $(this).addClass('was-validated');
            });

            // Update password policy preview when settings change
            $('input[type="checkbox"], input[type="number"]').on('change', function() {
                updatePasswordPolicyPreview();
            });

            function updatePasswordPolicyPreview() {
                // This would update the preview in real-time
                // Implementation depends on specific requirements
            }
        });

        function clearCache() {
            if (confirm('هل أنت متأكد من رغبتك في مسح الذاكرة المؤقتة؟')) {
                // Implementation for clearing cache
                alert('تم مسح الذاكرة المؤقتة بنجاح');
            }
        }

        function restartSystem() {
            if (confirm('هل أنت متأكد من رغبتك في إعادة تشغيل النظام؟ سيتم قطع الاتصال مؤقتاً.')) {
                // Implementation for system restart
                alert('سيتم إعادة تشغيل النظام خلال دقائق قليلة');
            }
        }
    </script>
}
