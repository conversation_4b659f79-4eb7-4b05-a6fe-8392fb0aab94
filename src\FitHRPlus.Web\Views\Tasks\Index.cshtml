@model FitHRPlus.Web.Models.Tasks.TasksIndexViewModel
@{
    ViewData["Title"] = "إدارة المهام";
}

<!-- Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-title">
            <h1><i class="bi bi-list-task"></i> إدارة المهام</h1>
            <p>تنظيم ومتابعة مهام الموظفين والمشاريع</p>
        </div>
        <div class="page-actions">
            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#newTaskModal">
                <i class="bi bi-plus-circle"></i>
                مهمة جديدة
            </button>
            <button type="button" class="btn btn-primary" onclick="viewTaskBoard()">
                <i class="bi bi-kanban"></i>
                لوحة المهام
            </button>
            <div class="btn-group">
                <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="bi bi-download"></i>
                    تصدير
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="exportTasks('excel')">
                        <i class="bi bi-file-earmark-excel me-2"></i>Excel
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportTasks('pdf')">
                        <i class="bi bi-file-earmark-pdf me-2"></i>PDF
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-primary">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@(Model.TotalTasks ?? 0)</h3>
                        <p>إجمالي المهام</p>
                        <small>جميع المهام في النظام</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-list-check"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: 100%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-success">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@(Model.CompletedTasks ?? 0)</h3>
                        <p>المهام المكتملة</p>
                        <small>المهام المنجزة بنجاح</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-check-circle"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: @(Model.TotalTasks > 0 ? (Model.CompletedTasks * 100 / Model.TotalTasks) : 0)%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-warning">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@(Model.InProgressTasks ?? 0)</h3>
                        <p>المهام الجارية</p>
                        <small>المهام قيد التنفيذ حالياً</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-arrow-clockwise"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: @(Model.TotalTasks > 0 ? (Model.InProgressTasks * 100 / Model.TotalTasks) : 0)%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-danger">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@(Model.OverdueTasks ?? 0)</h3>
                        <p>المهام المتأخرة</p>
                        <small>المهام المتجاوزة للموعد المحدد</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-exclamation-triangle"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: @(Model.TotalTasks > 0 ? (Model.OverdueTasks * 100 / Model.TotalTasks) : 0)%"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <div class="col-lg-8">
        <!-- Tasks List -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title">قائمة المهام</h5>
                    <div class="card-actions">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary active" onclick="filterTasks('all')">
                                الكل
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="filterTasks('pending')">
                                معلقة
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="filterTasks('in-progress')">
                                جارية
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="filterTasks('completed')">
                                مكتملة
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="filterTasks('overdue')">
                                متأخرة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                @if (Model.Tasks?.Any() == true)
                {
                    <div class="tasks-list">
                        @foreach (var task in Model.Tasks)
                        {
                            <div class="task-item @(task.Status?.ToLower().Replace(" ", "-")) @(task.IsOverdue ? "overdue" : "")" data-status="@task.Status?.ToLower().Replace(" ", "-")">
                                <div class="task-content">
                                    <div class="task-header">
                                        <div class="task-info">
                                            <div class="task-priority <EMAIL>?.ToLower()">
                                                <i class="bi bi-flag-fill"></i>
                                            </div>
                                            <div class="task-details">
                                                <h6>@task.Title</h6>
                                                <small>@task.ProjectName</small>
                                            </div>
                                        </div>
                                        <div class="task-meta">
                                            <span class="task-status <EMAIL>?.ToLower().Replace(" ", "-")">
                                                @task.StatusAr
                                            </span>
                                            <span class="task-due-date @(task.IsOverdue ? "overdue" : "")">
                                                <i class="bi bi-calendar"></i>
                                                @task.DueDate?.ToString("dd/MM/yyyy")
                                            </span>
                                        </div>
                                    </div>
                                    <div class="task-body">
                                        <p class="task-description">@task.Description</p>
                                        <div class="task-details-grid">
                                            <div class="detail-item">
                                                <span class="detail-label">المسؤول:</span>
                                                <span class="detail-value">
                                                    <div class="assignee-info">
                                                        <div class="assignee-avatar">
                                                            @task.AssigneeName?.Substring(0, 1).ToUpper()
                                                        </div>
                                                        <span>@task.AssigneeName</span>
                                                    </div>
                                                </span>
                                            </div>
                                            <div class="detail-item">
                                                <span class="detail-label">تاريخ البداية:</span>
                                                <span class="detail-value">@task.StartDate?.ToString("dd/MM/yyyy")</span>
                                            </div>
                                            <div class="detail-item">
                                                <span class="detail-label">الأولوية:</span>
                                                <span class="detail-value <EMAIL>?.ToLower()">
                                                    @task.PriorityAr
                                                </span>
                                            </div>
                                            <div class="detail-item">
                                                <span class="detail-label">التقدم:</span>
                                                <span class="detail-value">@(task.ProgressPercentage ?? 0)%</span>
                                            </div>
                                        </div>
                                        @if (task.Status == "In Progress")
                                        {
                                            <div class="task-progress">
                                                <div class="progress-info">
                                                    <span>التقدم: @(task.ProgressPercentage ?? 0)%</span>
                                                </div>
                                                <div class="progress">
                                                    <div class="progress-bar" style="width: @(task.ProgressPercentage ?? 0)%"></div>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                    <div class="task-actions">
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewTask('@task.Id')">
                                            <i class="bi bi-eye"></i>
                                            عرض
                                        </button>
                                        @if (task.Status == "Pending")
                                        {
                                            <button class="btn btn-sm btn-outline-success" onclick="startTask('@task.Id')">
                                                <i class="bi bi-play"></i>
                                                بدء
                                            </button>
                                        }
                                        @if (task.Status == "In Progress")
                                        {
                                            <button class="btn btn-sm btn-outline-info" onclick="updateProgress('@task.Id')">
                                                <i class="bi bi-arrow-up"></i>
                                                تحديث التقدم
                                            </button>
                                        }
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editTask('@task.Id')">
                                            <i class="bi bi-pencil"></i>
                                            تعديل
                                        </button>
                                        @if (task.Status != "Completed")
                                        {
                                            <button class="btn btn-sm btn-outline-success" onclick="completeTask('@task.Id')">
                                                <i class="bi bi-check"></i>
                                                إكمال
                                            </button>
                                        }
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="bi bi-list-task"></i>
                        </div>
                        <h5>لا توجد مهام</h5>
                        <p>لم يتم العثور على أي مهام</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newTaskModal">
                            <i class="bi bi-plus-circle"></i>
                            إضافة مهمة جديدة
                        </button>
                    </div>
                }
            </div>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Task Progress Chart -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">حالة المهام</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="taskStatusChart" style="height: 250px;"></canvas>
                </div>
            </div>
        </div>
        
        <!-- My Tasks -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">مهامي</h5>
            </div>
            <div class="card-body">
                <div class="my-tasks-list">
                    @if (Model.MyTasks?.Any() == true)
                    {
                        @foreach (var task in Model.MyTasks.Take(5))
                        {
                            <div class="my-task-item">
                                <div class="task-priority <EMAIL>?.ToLower()">
                                    <i class="bi bi-flag-fill"></i>
                                </div>
                                <div class="task-info">
                                    <h6>@task.Title</h6>
                                    <small>@task.ProjectName</small>
                                    <div class="task-due">
                                        <i class="bi bi-calendar"></i>
                                        @task.DueDate?.ToString("dd/MM/yyyy")
                                    </div>
                                </div>
                                <div class="task-status">
                                    <span class="<EMAIL>?.ToLower().Replace(" ", "-")">
                                        @task.StatusAr
                                    </span>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center text-muted">
                            <i class="bi bi-check-circle"></i>
                            <p>لا توجد مهام مخصصة لك</p>
                        </div>
                    }
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="quick-actions">
                    <button class="quick-action-btn" data-bs-toggle="modal" data-bs-target="#newTaskModal">
                        <i class="bi bi-plus-circle"></i>
                        <span>مهمة جديدة</span>
                    </button>
                    <button class="quick-action-btn" onclick="viewTaskBoard()">
                        <i class="bi bi-kanban"></i>
                        <span>لوحة المهام</span>
                    </button>
                    <button class="quick-action-btn" onclick="viewTaskReports()">
                        <i class="bi bi-graph-up"></i>
                        <span>تقارير المهام</span>
                    </button>
                    <button class="quick-action-btn" onclick="manageProjects()">
                        <i class="bi bi-folder"></i>
                        <span>إدارة المشاريع</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        $(document).ready(function() {
            loadTaskStatusChart();
        });

        function loadTaskStatusChart() {
            const ctx = document.getElementById('taskStatusChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['معلقة', 'جارية', 'مكتملة', 'متأخرة'],
                    datasets: [{
                        data: [
                            @(Model.PendingTasks ?? 0),
                            @(Model.InProgressTasks ?? 0),
                            @(Model.CompletedTasks ?? 0),
                            @(Model.OverdueTasks ?? 0)
                        ],
                        backgroundColor: [
                            '#6c757d',
                            '#ffc107',
                            '#28a745',
                            '#dc3545'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: {
                                    family: 'Tajawal'
                                },
                                padding: 15
                            }
                        }
                    }
                }
            });
        }

        function filterTasks(status) {
            $('.btn-group .btn').removeClass('active');
            $(`button[onclick="filterTasks('${status}')"]`).addClass('active');

            $('.task-item').each(function() {
                const itemStatus = $(this).data('status');
                const isOverdue = $(this).hasClass('overdue');

                let show = false;
                if (status === 'all') {
                    show = true;
                } else if (status === 'overdue') {
                    show = isOverdue;
                } else {
                    show = itemStatus === status;
                }

                $(this).toggle(show);
            });
        }

        function viewTask(id) {
            window.location.href = `@Url.Action("Details", "Tasks")/${id}`;
        }

        function editTask(id) {
            window.location.href = `@Url.Action("Edit", "Tasks")/${id}`;
        }

        function startTask(id) {
            if (confirm('هل تريد بدء هذه المهمة؟')) {
                $.ajax({
                    url: '@Url.Action("Start", "Tasks")',
                    type: 'POST',
                    data: {
                        id: id,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            showToast('تم بدء المهمة بنجاح', 'success');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showToast('حدث خطأ: ' + result.message, 'error');
                        }
                    },
                    error: function() {
                        showToast('حدث خطأ أثناء بدء المهمة', 'error');
                    }
                });
            }
        }

        function completeTask(id) {
            if (confirm('هل تريد إكمال هذه المهمة؟')) {
                $.ajax({
                    url: '@Url.Action("Complete", "Tasks")',
                    type: 'POST',
                    data: {
                        id: id,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            showToast('تم إكمال المهمة بنجاح', 'success');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showToast('حدث خطأ: ' + result.message, 'error');
                        }
                    },
                    error: function() {
                        showToast('حدث خطأ أثناء إكمال المهمة', 'error');
                    }
                });
            }
        }

        function updateProgress(id) {
            const progress = prompt('أدخل نسبة التقدم (0-100):');
            if (progress !== null && !isNaN(progress) && progress >= 0 && progress <= 100) {
                $.ajax({
                    url: '@Url.Action("UpdateProgress", "Tasks")',
                    type: 'POST',
                    data: {
                        id: id,
                        progress: progress,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            showToast('تم تحديث التقدم بنجاح', 'success');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showToast('حدث خطأ: ' + result.message, 'error');
                        }
                    },
                    error: function() {
                        showToast('حدث خطأ أثناء تحديث التقدم', 'error');
                    }
                });
            }
        }

        function viewTaskBoard() {
            window.location.href = '@Url.Action("Board", "Tasks")';
        }

        function viewTaskReports() {
            window.location.href = '@Url.Action("Reports", "Tasks")';
        }

        function manageProjects() {
            window.location.href = '@Url.Action("Index", "Projects")';
        }

        function exportTasks(format) {
            showToast(`جاري تصدير البيانات بصيغة ${format.toUpperCase()}...`, 'info');
            setTimeout(() => {
                window.open(`@Url.Action("Export", "Tasks")?format=${format}`, '_blank');
                showToast('تم تصدير البيانات بنجاح', 'success');
            }, 2000);
        }

        function showToast(message, type = 'info') {
            const bgClass = type === 'success' ? 'bg-success' :
                           type === 'warning' ? 'bg-warning' :
                           type === 'error' ? 'bg-danger' : 'bg-info';

            const toast = $(`
                <div class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `);

            let container = $('.toast-container');
            if (container.length === 0) {
                container = $('<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
                $('body').append(container);
            }

            container.append(toast);
            const bsToast = new bootstrap.Toast(toast[0]);
            bsToast.show();

            toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    </script>
}
