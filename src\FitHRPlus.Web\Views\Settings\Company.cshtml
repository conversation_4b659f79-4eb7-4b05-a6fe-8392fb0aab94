@model FitHRPlus.Web.Models.Settings.CompanySettingsViewModel
@{
    ViewData["Title"] = "إعدادات الشركة";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-0">
                        <i class="fas fa-building me-2"></i>
                        إعدادات الشركة
                    </h2>
                    <p class="text-muted mb-0">إدارة معلومات الشركة والإعدادات الأساسية</p>
                </div>
                <div>
                    <a href="@Url.Action("Index", "Settings")" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للإعدادات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <form asp-action="Company" method="post" class="needs-validation" novalidate>
        <div class="row">
            <div class="col-lg-8">
                <!-- Company Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات الشركة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="CompanyName" class="form-label">اسم الشركة (English)</label>
                                    <input asp-for="CompanyName" class="form-control" required>
                                    <span asp-validation-for="CompanyName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="CompanyNameAr" class="form-label">اسم الشركة (العربية)</label>
                                    <input asp-for="CompanyNameAr" class="form-control">
                                    <span asp-validation-for="CompanyNameAr" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Address" class="form-label">العنوان (English)</label>
                                    <textarea asp-for="Address" class="form-control" rows="3"></textarea>
                                    <span asp-validation-for="Address" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="AddressAr" class="form-label">العنوان (العربية)</label>
                                    <textarea asp-for="AddressAr" class="form-control" rows="3"></textarea>
                                    <span asp-validation-for="AddressAr" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="Phone" class="form-label">رقم الهاتف</label>
                                    <input asp-for="Phone" class="form-control" type="tel">
                                    <span asp-validation-for="Phone" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="Email" class="form-label">البريد الإلكتروني</label>
                                    <input asp-for="Email" class="form-control" type="email">
                                    <span asp-validation-for="Email" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="Website" class="form-label">الموقع الإلكتروني</label>
                                    <input asp-for="Website" class="form-control" type="url">
                                    <span asp-validation-for="Website" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="TaxNumber" class="form-label">الرقم الضريبي</label>
                                    <input asp-for="TaxNumber" class="form-control">
                                    <span asp-validation-for="TaxNumber" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="CommercialRegister" class="form-label">السجل التجاري</label>
                                    <input asp-for="CommercialRegister" class="form-control">
                                    <span asp-validation-for="CommercialRegister" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Working Hours -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clock me-2"></i>
                            ساعات العمل
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="WorkingHoursStart" class="form-label">وقت بداية العمل</label>
                                    <input asp-for="WorkingHoursStart" class="form-control" type="time" required>
                                    <span asp-validation-for="WorkingHoursStart" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="WorkingHoursEnd" class="form-label">وقت نهاية العمل</label>
                                    <input asp-for="WorkingHoursEnd" class="form-control" type="time" required>
                                    <span asp-validation-for="WorkingHoursEnd" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="WeekendDays" class="form-label">أيام العطلة الأسبوعية</label>
                                    <select asp-for="WeekendDays" class="form-select">
                                        <option value="Friday,Saturday">الجمعة والسبت</option>
                                        <option value="Saturday,Sunday">السبت والأحد</option>
                                        <option value="Friday">الجمعة فقط</option>
                                        <option value="Saturday">السبت فقط</option>
                                        <option value="Sunday">الأحد فقط</option>
                                    </select>
                                    <span asp-validation-for="WeekendDays" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Financial Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-money-bill me-2"></i>
                            الإعدادات المالية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Currency" class="form-label">العملة</label>
                                    <select asp-for="Currency" class="form-select">
                                        <option value="EGP">الجنيه المصري (EGP)</option>
                                        <option value="SAR">الريال السعودي (SAR)</option>
                                        <option value="AED">الدرهم الإماراتي (AED)</option>
                                        <option value="USD">الدولار الأمريكي (USD)</option>
                                        <option value="EUR">اليورو (EUR)</option>
                                    </select>
                                    <span asp-validation-for="Currency" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="CurrencySymbol" class="form-label">رمز العملة</label>
                                    <input asp-for="CurrencySymbol" class="form-control" required>
                                    <span asp-validation-for="CurrencySymbol" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="IncomeTaxRate" class="form-label">معدل ضريبة الدخل (%)</label>
                                    <div class="input-group">
                                        <input asp-for="IncomeTaxRate" class="form-control" type="number" step="0.01" min="0" max="1">
                                        <span class="input-group-text">%</span>
                                    </div>
                                    <span asp-validation-for="IncomeTaxRate" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="SocialInsuranceEmployeeRate" class="form-label">معدل التأمين الاجتماعي للموظف (%)</label>
                                    <div class="input-group">
                                        <input asp-for="SocialInsuranceEmployeeRate" class="form-control" type="number" step="0.01" min="0" max="1">
                                        <span class="input-group-text">%</span>
                                    </div>
                                    <span asp-validation-for="SocialInsuranceEmployeeRate" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="SocialInsuranceEmployerRate" class="form-label">معدل التأمين الاجتماعي لصاحب العمل (%)</label>
                                    <div class="input-group">
                                        <input asp-for="SocialInsuranceEmployerRate" class="form-control" type="number" step="0.01" min="0" max="1">
                                        <span class="input-group-text">%</span>
                                    </div>
                                    <span asp-validation-for="SocialInsuranceEmployerRate" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="MedicalInsuranceRate" class="form-label">معدل التأمين الطبي (%)</label>
                                    <div class="input-group">
                                        <input asp-for="MedicalInsuranceRate" class="form-control" type="number" step="0.01" min="0" max="1">
                                        <span class="input-group-text">%</span>
                                    </div>
                                    <span asp-validation-for="MedicalInsuranceRate" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Notification Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bell me-2"></i>
                            إعدادات الإشعارات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check mb-3">
                            <input asp-for="EnableEmailNotifications" class="form-check-input" type="checkbox">
                            <label asp-for="EnableEmailNotifications" class="form-check-label">
                                تفعيل إشعارات البريد الإلكتروني
                            </label>
                        </div>
                        <div class="form-check mb-3">
                            <input asp-for="EnableSmsNotifications" class="form-check-input" type="checkbox">
                            <label asp-for="EnableSmsNotifications" class="form-check-label">
                                تفعيل الإشعارات النصية
                            </label>
                        </div>
                        <div class="form-check mb-3">
                            <input asp-for="EnablePushNotifications" class="form-check-input" type="checkbox">
                            <label asp-for="EnablePushNotifications" class="form-check-label">
                                تفعيل الإشعارات الفورية
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Localization Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-globe me-2"></i>
                            إعدادات اللغة والمنطقة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label asp-for="DefaultLanguage" class="form-label">اللغة الافتراضية</label>
                            <select asp-for="DefaultLanguage" class="form-select">
                                <option value="ar">العربية</option>
                                <option value="en">English</option>
                            </select>
                            <span asp-validation-for="DefaultLanguage" class="text-danger"></span>
                        </div>
                        <div class="mb-3">
                            <label asp-for="TimeZone" class="form-label">المنطقة الزمنية</label>
                            <select asp-for="TimeZone" class="form-select">
                                <option value="Africa/Cairo">القاهرة (GMT+2)</option>
                                <option value="Asia/Riyadh">الرياض (GMT+3)</option>
                                <option value="Asia/Dubai">دبي (GMT+4)</option>
                                <option value="UTC">UTC (GMT+0)</option>
                            </select>
                            <span asp-validation-for="TimeZone" class="text-danger"></span>
                        </div>
                        <div class="mb-3">
                            <label asp-for="DateFormat" class="form-label">تنسيق التاريخ</label>
                            <select asp-for="DateFormat" class="form-select">
                                <option value="dd/MM/yyyy">يوم/شهر/سنة</option>
                                <option value="MM/dd/yyyy">شهر/يوم/سنة</option>
                                <option value="yyyy-MM-dd">سنة-شهر-يوم</option>
                            </select>
                            <span asp-validation-for="DateFormat" class="text-danger"></span>
                        </div>
                        <div class="mb-3">
                            <label asp-for="TimeFormat" class="form-label">تنسيق الوقت</label>
                            <select asp-for="TimeFormat" class="form-select">
                                <option value="HH:mm">24 ساعة</option>
                                <option value="hh:mm tt">12 ساعة</option>
                            </select>
                            <span asp-validation-for="TimeFormat" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ التغييرات
                            </button>
                            <a href="@Url.Action("Index", "Settings")" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        $(document).ready(function() {
            // Form validation
            $('.needs-validation').on('submit', function(e) {
                if (!this.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                $(this).addClass('was-validated');
            });

            // Currency symbol auto-update
            $('#Currency').on('change', function() {
                var currency = $(this).val();
                var symbols = {
                    'EGP': 'ج.م',
                    'SAR': 'ر.س',
                    'AED': 'د.إ',
                    'USD': '$',
                    'EUR': '€'
                };
                $('#CurrencySymbol').val(symbols[currency] || '');
            });
        });
    </script>
}
