[{"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_6", "RelativePath": "", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "FitHRPlus.Web.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "FitHRPlus.Application.DTOs.Auth.LoginRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "FitHRPlus.Web.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "FitHRPlus.Web.Controllers.LogoutRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "FitHRPlus.Web.Controllers.AuthController", "Method": "GetProfile", "RelativePath": "api/Auth/profile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "FitHRPlus.Web.Controllers.AuthController", "Method": "RefreshToken", "RelativePath": "api/Auth/refresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "FitHRPlus.Web.Controllers.RefreshTokenRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "FitHRPlus.Web.Controllers.AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "FitHRPlus.Application.DTOs.Auth.RegisterRequestDto", "IsRequired": true}], "ReturnTypes": []}]