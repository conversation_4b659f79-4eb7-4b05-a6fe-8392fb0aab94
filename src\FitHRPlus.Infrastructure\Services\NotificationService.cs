using FitHRPlus.Application.Common;
using FitHRPlus.Application.DTOs.Common;
using FitHRPlus.Application.DTOs.Notification;
using FitHRPlus.Application.Interfaces;
using FitHRPlus.Domain.Entities;
using FitHRPlus.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FitHRPlus.Infrastructure.Services
{
    /// <summary>
    /// Notification service implementation
    /// تنفيذ خدمة الإشعارات
    /// </summary>
    public class NotificationService : INotificationService
    {
        private readonly FitHRContext _context;
        private readonly ILogger<NotificationService> _logger;

        public NotificationService(FitHRContext context, ILogger<NotificationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Get paginated list of notifications
        /// الحصول على قائمة مقسمة للإشعارات
        /// </summary>
        public async Task<ServiceResult<PaginatedResult<NotificationDto>>> GetNotificationsAsync(NotificationListDto request)
        {
            try
            {
                var query = _context.Notifications
                    .Where(n => n.UserId == request.UserId)
                    .AsQueryable();

                // Apply filters
                if (!string.IsNullOrEmpty(request.Type))
                {
                    query = query.Where(n => n.Type == request.Type);
                }

                if (!string.IsNullOrEmpty(request.Category))
                {
                    query = query.Where(n => n.Category == request.Category);
                }

                if (!string.IsNullOrEmpty(request.Priority))
                {
                    query = query.Where(n => n.Priority == request.Priority);
                }

                if (request.IsRead.HasValue)
                {
                    query = query.Where(n => n.IsRead == request.IsRead.Value);
                }

                if (!string.IsNullOrEmpty(request.SearchTerm))
                {
                    query = query.Where(n => n.Title.Contains(request.SearchTerm) || 
                                           n.Message.Contains(request.SearchTerm));
                }

                // Apply sorting
                query = request.SortDirection?.ToLower() == "asc" 
                    ? query.OrderBy(n => n.CreatedAt)
                    : query.OrderByDescending(n => n.CreatedAt);

                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);

                var notifications = await query
                    .Skip((request.Page - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .Select(n => new NotificationDto
                    {
                        Id = n.Id,
                        UserId = n.UserId,
                        Title = n.Title,
                        TitleAr = n.TitleAr,
                        Message = n.Message,
                        MessageAr = n.MessageAr,
                        Type = n.Type,
                        Category = n.Category,
                        Priority = n.Priority,
                        IsRead = n.IsRead,
                        ReadAt = n.ReadAt,
                        ActionUrl = n.ActionUrl,
                        Icon = n.Icon,
                        CreatedAt = n.CreatedAt,
                        ExpiresAt = n.ExpiresAt,
                        TimeAgo = GetTimeAgo(n.CreatedAt)
                    })
                    .ToListAsync();

                var result = new PaginatedResult<NotificationDto>
                {
                    Items = notifications,
                    CurrentPage = request.Page,
                    TotalPages = totalPages,
                    TotalCount = totalCount,
                    PageSize = request.PageSize
                };

                return ServiceResult<PaginatedResult<NotificationDto>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting notifications for user {UserId}", request.UserId);
                return ServiceResult<PaginatedResult<NotificationDto>>.Failure("حدث خطأ أثناء جلب الإشعارات");
            }
        }

        /// <summary>
        /// Get notification by ID
        /// الحصول على الإشعار بالمعرف
        /// </summary>
        public async Task<ServiceResult<NotificationDto>> GetNotificationByIdAsync(Guid id)
        {
            try
            {
                var notification = await _context.Notifications
                    .FirstOrDefaultAsync(n => n.Id == id);

                if (notification == null)
                {
                    return ServiceResult<NotificationDto>.Failure("الإشعار غير موجود");
                }

                var dto = new NotificationDto
                {
                    Id = notification.Id,
                    UserId = notification.UserId,
                    Title = notification.Title,
                    TitleAr = notification.TitleAr,
                    Message = notification.Message,
                    MessageAr = notification.MessageAr,
                    Type = notification.Type,
                    Category = notification.Category,
                    Priority = notification.Priority,
                    IsRead = notification.IsRead,
                    ReadAt = notification.ReadAt,
                    ActionUrl = notification.ActionUrl,
                    Icon = notification.Icon,
                    CreatedAt = notification.CreatedAt,
                    ExpiresAt = notification.ExpiresAt,
                    TimeAgo = GetTimeAgo(notification.CreatedAt)
                };

                return ServiceResult<NotificationDto>.Success(dto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting notification {Id}", id);
                return ServiceResult<NotificationDto>.Failure("حدث خطأ أثناء جلب الإشعار");
            }
        }

        /// <summary>
        /// Create new notification
        /// إنشاء إشعار جديد
        /// </summary>
        public async Task<ServiceResult<NotificationDto>> CreateNotificationAsync(CreateNotificationDto request)
        {
            try
            {
                var notification = new Notification
                {
                    Id = Guid.NewGuid(),
                    UserId = request.UserId,
                    Title = request.Title,
                    TitleAr = request.TitleAr ?? request.Title,
                    Message = request.Message,
                    MessageAr = request.MessageAr ?? request.Message,
                    Type = request.Type,
                    Category = request.Category,
                    Priority = request.Priority,
                    ActionUrl = request.ActionUrl,
                    Icon = request.Icon,
                    ExpiresAt = request.ExpiresAt,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Notifications.Add(notification);
                await _context.SaveChangesAsync();

                var dto = new NotificationDto
                {
                    Id = notification.Id,
                    UserId = notification.UserId,
                    Title = notification.Title,
                    TitleAr = notification.TitleAr,
                    Message = notification.Message,
                    MessageAr = notification.MessageAr,
                    Type = notification.Type,
                    Category = notification.Category,
                    Priority = notification.Priority,
                    IsRead = notification.IsRead,
                    ReadAt = notification.ReadAt,
                    ActionUrl = notification.ActionUrl,
                    Icon = notification.Icon,
                    CreatedAt = notification.CreatedAt,
                    ExpiresAt = notification.ExpiresAt,
                    TimeAgo = GetTimeAgo(notification.CreatedAt)
                };

                return ServiceResult<NotificationDto>.Success(dto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating notification");
                return ServiceResult<NotificationDto>.Failure("حدث خطأ أثناء إنشاء الإشعار");
            }
        }

        /// <summary>
        /// Mark notification as read
        /// تحديد الإشعار كمقروء
        /// </summary>
        public async Task<ServiceResult<bool>> MarkAsReadAsync(Guid notificationId, Guid userId)
        {
            try
            {
                var notification = await _context.Notifications
                    .FirstOrDefaultAsync(n => n.Id == notificationId && n.UserId == userId);

                if (notification == null)
                {
                    return ServiceResult<bool>.Failure("الإشعار غير موجود");
                }

                if (!notification.IsRead)
                {
                    notification.IsRead = true;
                    notification.ReadAt = DateTime.UtcNow;
                    notification.UpdatedAt = DateTime.UtcNow;

                    await _context.SaveChangesAsync();
                }

                return ServiceResult<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking notification as read {Id}", notificationId);
                return ServiceResult<bool>.Failure("حدث خطأ أثناء تحديث الإشعار");
            }
        }

        /// <summary>
        /// Get notification statistics for user
        /// الحصول على إحصائيات الإشعارات للمستخدم
        /// </summary>
        public async Task<ServiceResult<NotificationStatisticsDto>> GetNotificationStatisticsAsync(Guid userId)
        {
            try
            {
                var notifications = await _context.Notifications
                    .Where(n => n.UserId == userId)
                    .ToListAsync();

                var stats = new NotificationStatisticsDto
                {
                    TotalNotifications = notifications.Count,
                    UnreadNotifications = notifications.Count(n => !n.IsRead),
                    ReadNotifications = notifications.Count(n => n.IsRead),
                    TodayNotifications = notifications.Count(n => n.CreatedAt.Date == DateTime.Today),
                    ThisWeekNotifications = notifications.Count(n => n.CreatedAt >= DateTime.Today.AddDays(-7)),
                    ThisMonthNotifications = notifications.Count(n => n.CreatedAt >= DateTime.Today.AddDays(-30))
                };

                return ServiceResult<NotificationStatisticsDto>.Success(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting notification statistics for user {UserId}", userId);
                return ServiceResult<NotificationStatisticsDto>.Failure("حدث خطأ أثناء جلب الإحصائيات");
            }
        }

        /// <summary>
        /// Get unread notification count for user
        /// الحصول على عدد الإشعارات غير المقروءة للمستخدم
        /// </summary>
        public async Task<ServiceResult<int>> GetUnreadCountAsync(Guid userId)
        {
            try
            {
                var count = await _context.Notifications
                    .CountAsync(n => n.UserId == userId && !n.IsRead);

                return ServiceResult<int>.Success(count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unread count for user {UserId}", userId);
                return ServiceResult<int>.Failure("حدث خطأ أثناء جلب عدد الإشعارات غير المقروءة");
            }
        }

        /// <summary>
        /// Get recent notifications for user
        /// الحصول على الإشعارات الحديثة للمستخدم
        /// </summary>
        public async Task<ServiceResult<List<NotificationDto>>> GetRecentNotificationsAsync(Guid userId, int limit = 10)
        {
            try
            {
                var notifications = await _context.Notifications
                    .Where(n => n.UserId == userId)
                    .OrderByDescending(n => n.CreatedAt)
                    .Take(limit)
                    .Select(n => new NotificationDto
                    {
                        Id = n.Id,
                        UserId = n.UserId,
                        Title = n.Title,
                        TitleAr = n.TitleAr,
                        Message = n.Message,
                        MessageAr = n.MessageAr,
                        Type = n.Type,
                        Category = n.Category,
                        Priority = n.Priority,
                        IsRead = n.IsRead,
                        ReadAt = n.ReadAt,
                        ActionUrl = n.ActionUrl,
                        Icon = n.Icon,
                        CreatedAt = n.CreatedAt,
                        ExpiresAt = n.ExpiresAt,
                        TimeAgo = GetTimeAgo(n.CreatedAt)
                    })
                    .ToListAsync();

                return ServiceResult<List<NotificationDto>>.Success(notifications);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recent notifications for user {UserId}", userId);
                return ServiceResult<List<NotificationDto>>.Failure("حدث خطأ أثناء جلب الإشعارات الحديثة");
            }
        }

        /// <summary>
        /// Helper method to calculate time ago
        /// طريقة مساعدة لحساب الوقت المنقضي
        /// </summary>
        private static string GetTimeAgo(DateTime dateTime)
        {
            var timeSpan = DateTime.UtcNow - dateTime;

            if (timeSpan.TotalMinutes < 1)
                return "الآن";
            if (timeSpan.TotalMinutes < 60)
                return $"منذ {(int)timeSpan.TotalMinutes} دقيقة";
            if (timeSpan.TotalHours < 24)
                return $"منذ {(int)timeSpan.TotalHours} ساعة";
            if (timeSpan.TotalDays < 7)
                return $"منذ {(int)timeSpan.TotalDays} يوم";
            if (timeSpan.TotalDays < 30)
                return $"منذ {(int)(timeSpan.TotalDays / 7)} أسبوع";
            if (timeSpan.TotalDays < 365)
                return $"منذ {(int)(timeSpan.TotalDays / 30)} شهر";

            return $"منذ {(int)(timeSpan.TotalDays / 365)} سنة";
        }

        // Placeholder implementations for other interface methods
        public Task<ServiceResult<NotificationDto>> CreateNotificationFromTemplateAsync(Guid templateId, Guid userId, Dictionary<string, string> parameters)
        {
            return Task.FromResult(ServiceResult<NotificationDto>.Failure("غير مدعوم حالياً"));
        }

        public async Task<ServiceResult<int>> MarkAllAsReadAsync(Guid userId)
        {
            try
            {
                var notifications = await _context.Notifications
                    .Where(n => n.UserId == userId && !n.IsRead)
                    .ToListAsync();

                foreach (var notification in notifications)
                {
                    notification.IsRead = true;
                    notification.ReadAt = DateTime.UtcNow;
                    notification.UpdatedAt = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                return ServiceResult<int>.Success(notifications.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking all notifications as read for user {UserId}", userId);
                return ServiceResult<int>.Failure("حدث خطأ أثناء تحديث الإشعارات");
            }
        }

        public async Task<ServiceResult<bool>> MarkAsUnreadAsync(Guid notificationId, Guid userId)
        {
            try
            {
                var notification = await _context.Notifications
                    .FirstOrDefaultAsync(n => n.Id == notificationId && n.UserId == userId);

                if (notification == null)
                {
                    return ServiceResult<bool>.Failure("الإشعار غير موجود");
                }

                notification.IsRead = false;
                notification.ReadAt = null;
                notification.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return ServiceResult<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking notification as unread {Id}", notificationId);
                return ServiceResult<bool>.Failure("حدث خطأ أثناء تحديث الإشعار");
            }
        }

        public Task<ServiceResult<bool>> DeleteNotificationAsync(Guid notificationId, Guid userId)
        {
            return Task.FromResult(ServiceResult<bool>.Failure("غير مدعوم حالياً"));
        }

        public Task<ServiceResult<int>> BulkActionAsync(BulkNotificationActionDto request, Guid userId)
        {
            return Task.FromResult(ServiceResult<int>.Failure("غير مدعوم حالياً"));
        }

        public Task<ServiceResult<NotificationDto>> SendNotificationAsync(Guid userId, string title, string message, string type = "Info", string category = "System", string? actionUrl = null)
        {
            return Task.FromResult(ServiceResult<NotificationDto>.Failure("غير مدعوم حالياً"));
        }

        public Task<ServiceResult<int>> SendBulkNotificationAsync(List<Guid> userIds, string title, string message, string type = "Info", string category = "System", string? actionUrl = null)
        {
            return Task.FromResult(ServiceResult<int>.Failure("غير مدعوم حالياً"));
        }

        public Task<ServiceResult<int>> SendCompanyNotificationAsync(Guid companyId, string title, string message, string type = "Info", string category = "System", string? actionUrl = null)
        {
            return Task.FromResult(ServiceResult<int>.Failure("غير مدعوم حالياً"));
        }

        public Task<ServiceResult<int>> CleanupExpiredNotificationsAsync()
        {
            return Task.FromResult(ServiceResult<int>.Failure("غير مدعوم حالياً"));
        }

        public Task<ServiceResult<List<NotificationTemplateDto>>> GetNotificationTemplatesAsync(string? category = null)
        {
            return Task.FromResult(ServiceResult<List<NotificationTemplateDto>>.Failure("غير مدعوم حالياً"));
        }

        public Task<ServiceResult<NotificationTemplateDto>> CreateNotificationTemplateAsync(CreateNotificationTemplateDto request)
        {
            return Task.FromResult(ServiceResult<NotificationTemplateDto>.Failure("غير مدعوم حالياً"));
        }

        public Task<ServiceResult<NotificationPreferencesDto>> GetNotificationPreferencesAsync(Guid userId)
        {
            return Task.FromResult(ServiceResult<NotificationPreferencesDto>.Failure("غير مدعوم حالياً"));
        }

        public Task<ServiceResult<NotificationPreferencesDto>> UpdateNotificationPreferencesAsync(Guid userId, UpdateNotificationPreferencesDto request)
        {
            return Task.FromResult(ServiceResult<NotificationPreferencesDto>.Failure("غير مدعوم حالياً"));
        }

        public Task<ServiceResult<bool>> SendEmailNotificationAsync(Guid userId, string subject, string body)
        {
            return Task.FromResult(ServiceResult<bool>.Failure("غير مدعوم حالياً"));
        }

        public Task<ServiceResult<bool>> SendPushNotificationAsync(Guid userId, string title, string body, Dictionary<string, string>? data = null)
        {
            return Task.FromResult(ServiceResult<bool>.Failure("غير مدعوم حالياً"));
        }

        public Task<ServiceResult<bool>> SendSmsNotificationAsync(Guid userId, string message)
        {
            return Task.FromResult(ServiceResult<bool>.Failure("غير مدعوم حالياً"));
        }
    }
}
