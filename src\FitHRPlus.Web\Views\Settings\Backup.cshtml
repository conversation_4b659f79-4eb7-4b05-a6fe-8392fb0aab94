@model FitHRPlus.Web.Models.Settings.BackupSettingsViewModel
@{
    ViewData["Title"] = "النسخ الاحتياطي والاستعادة";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-0">
                        <i class="fas fa-database me-2"></i>
                        النسخ الاحتياطي والاستعادة
                    </h2>
                    <p class="text-muted mb-0">إدارة النسخ الاحتياطية للبيانات</p>
                </div>
                <div>
                    <a href="@Url.Action("Index", "Settings")" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للإعدادات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Backup Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-download me-2"></i>
                        إجراءات النسخ الاحتياطي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-grid gap-2">
                                <form asp-action="CreateBackup" method="post" style="display: inline;">
                                    <button type="submit" class="btn btn-primary btn-lg w-100">
                                        <i class="fas fa-download me-2"></i>
                                        إنشاء نسخة احتياطية الآن
                                    </button>
                                </form>
                                <button type="button" class="btn btn-success btn-lg w-100" onclick="scheduleBackup()">
                                    <i class="fas fa-clock me-2"></i>
                                    جدولة نسخة احتياطية
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-warning btn-lg w-100" onclick="uploadBackup()">
                                    <i class="fas fa-upload me-2"></i>
                                    رفع نسخة احتياطية
                                </button>
                                <button type="button" class="btn btn-danger btn-lg w-100" onclick="restoreBackup()">
                                    <i class="fas fa-undo me-2"></i>
                                    استعادة من نسخة احتياطية
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Backup History -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>
                        سجل النسخ الاحتياطية
                    </h5>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshBackupList()">
                        <i class="fas fa-sync-alt me-1"></i>
                        تحديث
                    </button>
                </div>
                <div class="card-body">
                    @if (Model.BackupFiles.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم الملف</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>حجم الملف</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var backup in Model.BackupFiles)
                                    {
                                        <tr>
                                            <td>
                                                <i class="fas fa-file-archive me-2 text-primary"></i>
                                                @backup.FileName
                                            </td>
                                            <td>@backup.CreatedDate.ToString("dd/MM/yyyy HH:mm")</td>
                                            <td>@backup.FileSize</td>
                                            <td>
                                                <span class="badge bg-@(backup.Status == "مكتمل" ? "success" : backup.Status == "فشل" ? "danger" : "warning")">
                                                    @backup.Status
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-primary" onclick="downloadBackup('@backup.FilePath')">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-success" onclick="restoreFromBackup('@backup.FilePath')">
                                                        <i class="fas fa-undo"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-danger" onclick="deleteBackup('@backup.FilePath')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد نسخ احتياطية متاحة</p>
                            <p class="text-muted small">قم بإنشاء نسخة احتياطية أولى لحماية بياناتك</p>
                        </div>
                    }
                </div>
            </div>

            <!-- Backup Settings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        إعدادات النسخ الاحتياطي
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input asp-for="AutoBackupEnabled" class="form-check-input" type="checkbox">
                                    <label asp-for="AutoBackupEnabled" class="form-check-label">
                                        تفعيل النسخ الاحتياطي التلقائي
                                    </label>
                                </div>
                                <div class="mb-3">
                                    <label asp-for="BackupFrequency" class="form-label">تكرار النسخ الاحتياطي</label>
                                    <select asp-for="BackupFrequency" class="form-select">
                                        <option value="Daily">يومياً</option>
                                        <option value="Weekly">أسبوعياً</option>
                                        <option value="Monthly">شهرياً</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="BackupRetentionDays" class="form-label">مدة الاحتفاظ بالنسخ (بالأيام)</label>
                                    <input asp-for="BackupRetentionDays" class="form-control" type="number" min="1" max="365">
                                    <div class="form-text">سيتم حذف النسخ الأقدم من هذه المدة تلقائياً</div>
                                </div>
                            </div>
                        </div>
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Backup Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        حالة النسخ الاحتياطي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>النسخ التلقائي:</span>
                        <span class="badge bg-@(Model.AutoBackupEnabled ? "success" : "secondary")">
                            @(Model.AutoBackupEnabled ? "مفعل" : "معطل")
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>التكرار:</span>
                        <span class="text-muted">@Model.BackupFrequency</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>آخر نسخة احتياطية:</span>
                        <span class="text-muted">
                            @(Model.LastBackupDate?.ToString("dd/MM/yyyy") ?? "لا يوجد")
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>حجم آخر نسخة:</span>
                        <span class="text-muted">@(Model.LastBackupSize ?? "غير محدد")</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>حالة آخر نسخة:</span>
                        <span class="badge bg-success">@(Model.LastBackupStatus ?? "مكتمل")</span>
                    </div>
                </div>
            </div>

            <!-- Storage Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-hdd me-2"></i>
                        معلومات التخزين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span class="small">المساحة المستخدمة</span>
                            <span class="small">2.5 GB / 10 GB</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-primary" style="width: 25%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span class="small">النسخ الاحتياطية</span>
                            <span class="small">1.2 GB</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-info" style="width: 12%"></div>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <small class="text-muted">المساحة المتاحة: 7.5 GB</small>
                    </div>
                </div>
            </div>

            <!-- Quick Tips -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        نصائح مهمة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="small">
                        <div class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            قم بإنشاء نسخ احتياطية بانتظام
                        </div>
                        <div class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            احتفظ بنسخ في أماكن متعددة
                        </div>
                        <div class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            اختبر استعادة النسخ دورياً
                        </div>
                        <div class="mb-2">
                            <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                            لا تحذف النسخ القديمة فوراً
                        </div>
                        <div>
                            <i class="fas fa-shield-alt text-info me-2"></i>
                            تأكد من تشفير النسخ الحساسة
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Upload Backup Modal -->
<div class="modal fade" id="uploadBackupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفع نسخة احتياطية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadBackupForm">
                    <div class="mb-3">
                        <label for="backupFile" class="form-label">اختر ملف النسخة الاحتياطية</label>
                        <input type="file" class="form-control" id="backupFile" accept=".zip,.sql,.bak">
                        <div class="form-text">الملفات المدعومة: .zip, .sql, .bak</div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="verifyBackup">
                            <label class="form-check-label" for="verifyBackup">
                                التحقق من سلامة الملف قبل الرفع
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="performUpload()">رفع</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function scheduleBackup() {
            alert('سيتم إضافة ميزة جدولة النسخ الاحتياطية قريباً');
        }

        function uploadBackup() {
            $('#uploadBackupModal').modal('show');
        }

        function performUpload() {
            var fileInput = document.getElementById('backupFile');
            if (!fileInput.files.length) {
                alert('يرجى اختيار ملف أولاً');
                return;
            }
            
            // Simulate upload process
            alert('جاري رفع النسخة الاحتياطية...');
            $('#uploadBackupModal').modal('hide');
        }

        function restoreBackup() {
            if (confirm('هل أنت متأكد من رغبتك في استعادة النظام من نسخة احتياطية؟ سيتم استبدال البيانات الحالية.')) {
                alert('سيتم بدء عملية الاستعادة قريباً');
            }
        }

        function downloadBackup(filePath) {
            alert('جاري تحميل النسخة الاحتياطية: ' + filePath);
        }

        function restoreFromBackup(filePath) {
            if (confirm('هل أنت متأكد من رغبتك في الاستعادة من هذه النسخة؟')) {
                alert('جاري الاستعادة من: ' + filePath);
            }
        }

        function deleteBackup(filePath) {
            if (confirm('هل أنت متأكد من رغبتك في حذف هذه النسخة الاحتياطية؟')) {
                alert('تم حذف النسخة الاحتياطية: ' + filePath);
                location.reload();
            }
        }

        function refreshBackupList() {
            location.reload();
        }
    </script>
}
