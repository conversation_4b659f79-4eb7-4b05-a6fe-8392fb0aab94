using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace FitHRPlus.Web.Models.Leave
{
    /// <summary>
    /// Leave request list view model
    /// نموذج عرض قائمة طلبات الإجازات
    /// </summary>
    public class LeaveRequestListViewModel
    {
        public List<LeaveRequestViewModel> LeaveRequests { get; set; } = new();
        
        // Filter properties
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
        public string? Status { get; set; }
        public Guid? EmployeeId { get; set; }
        public Guid? LeaveTypeId { get; set; }
        public string? SearchTerm { get; set; }
        
        // Pagination
        public int CurrentPage { get; set; } = 1;
        public int TotalPages { get; set; }
        public int TotalCount { get; set; }
        public int PageSize { get; set; } = 20;
        
        // Filter options
        public List<SelectListItem> Employees { get; set; } = new();
        public List<SelectListItem> LeaveTypes { get; set; } = new();
        public List<SelectListItem> StatusOptions { get; set; } = new();
        
        // Statistics
        public LeaveStatisticsViewModel? Statistics { get; set; }
    }

    /// <summary>
    /// Leave request view model
    /// نموذج عرض طلب الإجازة
    /// </summary>
    public class LeaveRequestViewModel
    {
        public Guid Id { get; set; }
        public Guid EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string EmployeeNameAr { get; set; } = string.Empty;
        public string EmployeeCode { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string DepartmentNameAr { get; set; } = string.Empty;
        
        public Guid LeaveTypeId { get; set; }
        public string LeaveTypeName { get; set; } = string.Empty;
        public string LeaveTypeNameAr { get; set; } = string.Empty;
        
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalDays { get; set; }
        
        public string? Reason { get; set; }
        public string? ReasonAr { get; set; }
        
        public string Status { get; set; } = "Pending";
        public string StatusAr { get; set; } = "في الانتظار";
        public string StatusBadgeClass { get; set; } = "badge-warning";
        
        public Guid? ApprovedBy { get; set; }
        public string? ApprovedByName { get; set; }
        public DateTime? ApprovedAt { get; set; }
        
        public string? RejectionReason { get; set; }
        public string? RejectionReasonAr { get; set; }
        
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        
        public bool CanApprove { get; set; }
        public bool CanReject { get; set; }
        public bool CanCancel { get; set; }
        public bool CanEdit { get; set; }
    }

    /// <summary>
    /// Create leave request view model
    /// نموذج عرض إنشاء طلب الإجازة
    /// </summary>
    public class CreateLeaveRequestViewModel
    {
        [Required(ErrorMessage = "الموظف مطلوب")]
        [Display(Name = "Employee / الموظف")]
        public Guid EmployeeId { get; set; }
        
        [Required(ErrorMessage = "نوع الإجازة مطلوب")]
        [Display(Name = "Leave Type / نوع الإجازة")]
        public Guid LeaveTypeId { get; set; }
        
        [Required(ErrorMessage = "تاريخ البداية مطلوب")]
        [DataType(DataType.Date)]
        [Display(Name = "Start Date / تاريخ البداية")]
        public DateTime StartDate { get; set; }
        
        [Required(ErrorMessage = "تاريخ النهاية مطلوب")]
        [DataType(DataType.Date)]
        [Display(Name = "End Date / تاريخ النهاية")]
        public DateTime EndDate { get; set; }
        
        [Display(Name = "Total Days / إجمالي الأيام")]
        public decimal TotalDays { get; set; }
        
        [MaxLength(1000, ErrorMessage = "السبب لا يمكن أن يتجاوز 1000 حرف")]
        [Display(Name = "Reason / السبب")]
        public string? Reason { get; set; }
        
        [MaxLength(1000, ErrorMessage = "السبب بالعربية لا يمكن أن يتجاوز 1000 حرف")]
        [Display(Name = "Reason (Arabic) / السبب بالعربية")]
        public string? ReasonAr { get; set; }
        
        [MaxLength(200, ErrorMessage = "جهة الاتصال للطوارئ لا يمكن أن تتجاوز 200 حرف")]
        [Display(Name = "Emergency Contact / جهة الاتصال للطوارئ")]
        public string? EmergencyContact { get; set; }
        
        [MaxLength(20, ErrorMessage = "هاتف الطوارئ لا يمكن أن يتجاوز 20 حرف")]
        [Display(Name = "Emergency Phone / هاتف الطوارئ")]
        public string? EmergencyPhone { get; set; }
        
        // Form data
        public List<SelectListItem> Employees { get; set; } = new();
        public List<SelectListItem> LeaveTypes { get; set; } = new();
        public Dictionary<Guid, LeaveTypeInfoViewModel> LeaveTypeInfo { get; set; } = new();
        public Dictionary<string, decimal> LeaveBalances { get; set; } = new();
    }

    /// <summary>
    /// Leave request details view model
    /// نموذج عرض تفاصيل طلب الإجازة
    /// </summary>
    public class LeaveRequestDetailsViewModel
    {
        public Guid Id { get; set; }
        public Guid EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string EmployeeNameAr { get; set; } = string.Empty;
        public string EmployeeCode { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string DepartmentNameAr { get; set; } = string.Empty;
        public string? EmployeePhoto { get; set; }
        
        public Guid LeaveTypeId { get; set; }
        public string LeaveTypeName { get; set; } = string.Empty;
        public string LeaveTypeNameAr { get; set; } = string.Empty;
        public string? LeaveTypeDescription { get; set; }
        public string? LeaveTypeDescriptionAr { get; set; }
        public bool IsPaid { get; set; }
        
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalDays { get; set; }
        
        public string? Reason { get; set; }
        public string? ReasonAr { get; set; }
        
        public string Status { get; set; } = "Pending";
        public string StatusAr { get; set; } = "في الانتظار";
        public string StatusBadgeClass { get; set; } = "badge-warning";
        
        public Guid? ApprovedBy { get; set; }
        public string? ApprovedByName { get; set; }
        public DateTime? ApprovedAt { get; set; }
        
        public string? RejectionReason { get; set; }
        public string? RejectionReasonAr { get; set; }
        
        public string? EmergencyContact { get; set; }
        public string? EmergencyPhone { get; set; }
        
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        
        public bool CanApprove { get; set; }
        public bool CanReject { get; set; }
        public bool CanCancel { get; set; }
        public bool CanEdit { get; set; }
        
        // Leave balance information
        public decimal CurrentBalance { get; set; }
        public decimal BalanceAfterRequest { get; set; }
        
        // Timeline
        public List<LeaveRequestTimelineItem> Timeline { get; set; } = new();

        // Additional properties for compatibility
        public string EmployeeNumber { get; set; } = string.Empty;
        public string? ApprovalNotes { get; set; }
    }

    /// <summary>
    /// Leave type info view model
    /// نموذج عرض معلومات نوع الإجازة
    /// </summary>
    public class LeaveTypeInfoViewModel
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string NameAr { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? DescriptionAr { get; set; }
        public int? MaxDaysPerYear { get; set; }
        public int? MaxConsecutiveDays { get; set; }
        public int MinDaysNotice { get; set; }
        public bool IsPaid { get; set; }
        public bool RequiresApproval { get; set; }
        public bool RequiresDocument { get; set; }
        public bool CarryForward { get; set; }
        public int CarryForwardLimit { get; set; }
        public string? Gender { get; set; }
    }

    /// <summary>
    /// Leave statistics view model
    /// نموذج عرض إحصائيات الإجازات
    /// </summary>
    public class LeaveStatisticsViewModel
    {
        public int TotalRequests { get; set; }
        public int PendingRequests { get; set; }
        public int ApprovedRequests { get; set; }
        public int RejectedRequests { get; set; }
        public int CancelledRequests { get; set; }
        
        public decimal TotalDaysRequested { get; set; }
        public decimal TotalDaysApproved { get; set; }
        public decimal TotalDaysUsed { get; set; }
        
        public Dictionary<string, int> RequestsByType { get; set; } = new();
        public Dictionary<string, int> RequestsByMonth { get; set; } = new();
        public Dictionary<string, decimal> DaysByType { get; set; } = new();
    }

    /// <summary>
    /// Leave request timeline item
    /// عنصر الجدول الزمني لطلب الإجازة
    /// </summary>
    public class LeaveRequestTimelineItem
    {
        public DateTime Date { get; set; }
        public string Action { get; set; } = string.Empty;
        public string ActionAr { get; set; } = string.Empty;
        public string? UserName { get; set; }
        public string? Notes { get; set; }
        public string IconClass { get; set; } = "fas fa-circle";
        public string ColorClass { get; set; } = "text-primary";
    }

    /// <summary>
    /// Leave balance view model
    /// نموذج عرض رصيد الإجازة
    /// </summary>
    public class LeaveBalanceViewModel
    {
        public Guid Id { get; set; }
        public Guid EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string EmployeeNameAr { get; set; } = string.Empty;
        
        public Guid LeaveTypeId { get; set; }
        public string LeaveTypeName { get; set; } = string.Empty;
        public string LeaveTypeNameAr { get; set; } = string.Empty;
        
        public int Year { get; set; }
        public decimal EntitledDays { get; set; }
        public decimal UsedDays { get; set; }
        public decimal RemainingDays { get; set; }
        public decimal CarriedForwardDays { get; set; }
        
        public decimal UsagePercentage { get; set; }
        public string ProgressBarClass { get; set; } = "bg-success";
    }

    /// <summary>
    /// Leave balances overview view model
    /// نموذج عرض أرصدة الإجازات الشامل
    /// </summary>
    public class LeaveBalancesOverviewViewModel
    {
        public Guid CompanyId { get; set; }
        public List<EmployeeBalanceViewModel> Employees { get; set; } = new();
    }

    /// <summary>
    /// Employee balance view model
    /// نموذج عرض رصيد الموظف
    /// </summary>
    public class EmployeeBalanceViewModel
    {
        public Guid EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string EmployeeCode { get; set; } = string.Empty;
        public string Department { get; set; } = string.Empty;
        public List<LeaveTypeBalanceViewModel> LeaveBalances { get; set; } = new();
    }

    /// <summary>
    /// Leave type balance view model
    /// نموذج عرض رصيد نوع الإجازة
    /// </summary>
    public class LeaveTypeBalanceViewModel
    {
        public Guid LeaveTypeId { get; set; }
        public string LeaveTypeName { get; set; } = string.Empty;
        public int TotalDays { get; set; }
        public int UsedDays { get; set; }
        public int RemainingDays { get; set; }
        public int Year { get; set; }
    }
}
