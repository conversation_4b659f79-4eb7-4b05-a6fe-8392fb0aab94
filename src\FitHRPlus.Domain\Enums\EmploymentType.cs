namespace FitHRPlus.Domain.Enums
{
    /// <summary>
    /// Employment type enumeration
    /// تعداد نوع التوظيف
    /// </summary>
    public enum EmploymentType
    {
        /// <summary>
        /// Full-time employment - دوام كامل
        /// </summary>
        FullTime = 1,

        /// <summary>
        /// Part-time employment - دوام جزئي
        /// </summary>
        PartTime = 2,

        /// <summary>
        /// Contract employment - عقد مؤقت
        /// </summary>
        Contract = 3,

        /// <summary>
        /// Internship - تدريب
        /// </summary>
        Intern = 4,

        /// <summary>
        /// Consultant - استشاري
        /// </summary>
        Consultant = 5
    }
}