﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\FitHRPlus.Application\FitHRPlus.Application.csproj" />
    <ProjectReference Include="..\FitHRPlus.Persistence\FitHRPlus.Persistence.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.0" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.2.1" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.2.1" />
  </ItemGroup>

</Project>
