# مواصفات تصميم واجهات المستخدم ثنائية اللغة
## Bilingual UI/UX Design Specifications

## 1. مبادئ التصميم الأساسية
### Core Design Principles

### 1.1 فلسفة التصميم
- **البساطة والوضوح**: واجهات بسيطة وسهلة الفهم
- **الاتساق**: تجربة موحدة عبر جميع الصفحات
- **إمكانية الوصول**: متوافق مع معايير WCAG 2.1 AA
- **الاستجابة**: يعمل بكفاءة على جميع الأجهزة
- **الثقافة المحلية**: يراعي الثقافة المصرية والعربية

### 1.2 نظام الألوان
```css
/* Primary Colors - الألوان الأساسية */
:root {
  --primary-blue: #1e40af;      /* أزرق مؤسسي */
  --primary-green: #059669;     /* أخضر نجاح */
  --primary-red: #dc2626;       /* أحمر تحذير */
  --primary-orange: #ea580c;    /* برتقالي تنبيه */
  
  /* Secondary Colors - الألوان الثانوية */
  --secondary-gray: #6b7280;    /* رمادي نص */
  --secondary-light: #f9fafb;   /* خلفية فاتحة */
  --secondary-dark: #111827;    /* نص داكن */
  
  /* Egyptian Theme - الطابع المصري */
  --egyptian-gold: #d4af37;     /* ذهبي مصري */
  --egyptian-sand: #f4e4bc;     /* رملي */
  --nile-blue: #4682b4;         /* أزرق النيل */
}
```

### 1.3 الخطوط والطباعة
```css
/* Arabic Fonts - الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;600;700&display=swap');

/* English Fonts - الخطوط الإنجليزية */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap');

/* Font Families */
.font-arabic {
  font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
}

.font-english {
  font-family: 'Roboto', 'Open Sans', sans-serif;
}

.font-arabic-traditional {
  font-family: 'Amiri', serif;
}
```

---

## 2. نظام الشبكة والتخطيط
### Grid System and Layout

### 2.1 نظام الشبكة المرن
```css
/* Responsive Grid System */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.grid {
  display: grid;
  gap: 20px;
}

.grid-cols-1 { grid-template-columns: 1fr; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* RTL Support */
[dir="rtl"] .grid {
  direction: rtl;
}

[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}
```

### 2.2 تخطيط الصفحة الرئيسية
```mermaid
graph TB
    subgraph "Header - الرأس"
        A[Logo]
        B[Navigation Menu]
        C[Language Toggle]
        D[User Profile]
    end
    
    subgraph "Sidebar - الشريط الجانبي"
        E[Main Menu]
        F[Quick Actions]
        G[Notifications]
    end
    
    subgraph "Main Content - المحتوى الرئيسي"
        H[Dashboard Cards]
        I[Charts & Analytics]
        J[Recent Activities]
        K[Quick Stats]
    end
    
    subgraph "Footer - التذييل"
        L[Company Info]
        M[Support Links]
        N[Version Info]
    end
    
    A --> E
    B --> H
    C --> I
    D --> J
    E --> H
    F --> I
    G --> J
    H --> L
```

---

## 3. مكونات واجهة المستخدم
### UI Components

### 3.1 الأزرار (Buttons)
```html
<!-- Primary Button -->
<button class="btn btn-primary">
  <span class="btn-text" data-en="Save" data-ar="حفظ"></span>
  <i class="btn-icon fas fa-save"></i>
</button>

<!-- Secondary Button -->
<button class="btn btn-secondary">
  <span class="btn-text" data-en="Cancel" data-ar="إلغاء"></span>
</button>

<!-- Danger Button -->
<button class="btn btn-danger">
  <span class="btn-text" data-en="Delete" data-ar="حذف"></span>
  <i class="btn-icon fas fa-trash"></i>
</button>
```

```css
.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background: var(--primary-blue);
  color: white;
}

.btn-primary:hover {
  background: #1d4ed8;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
}

[dir="rtl"] .btn {
  flex-direction: row-reverse;
}
```

### 3.2 النماذج (Forms)
```html
<div class="form-group">
  <label class="form-label" data-en="Employee Name" data-ar="اسم الموظف"></label>
  <input type="text" class="form-input" placeholder-en="Enter employee name" placeholder-ar="أدخل اسم الموظف">
  <span class="form-error" data-en="This field is required" data-ar="هذا الحقل مطلوب"></span>
</div>

<div class="form-group">
  <label class="form-label" data-en="Department" data-ar="القسم"></label>
  <select class="form-select">
    <option value="" data-en="Select Department" data-ar="اختر القسم"></option>
    <option value="hr" data-en="Human Resources" data-ar="الموارد البشرية"></option>
    <option value="it" data-en="Information Technology" data-ar="تكنولوجيا المعلومات"></option>
  </select>
</div>
```

```css
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--secondary-dark);
}

.form-input, .form-select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.form-input:focus, .form-select:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

[dir="rtl"] .form-input, [dir="rtl"] .form-select {
  text-align: right;
}
```

### 3.3 البطاقات (Cards)
```html
<div class="card">
  <div class="card-header">
    <h3 class="card-title" data-en="Employee Statistics" data-ar="إحصائيات الموظفين"></h3>
    <div class="card-actions">
      <button class="btn-icon" title-en="Refresh" title-ar="تحديث">
        <i class="fas fa-sync-alt"></i>
      </button>
    </div>
  </div>
  <div class="card-body">
    <div class="stat-item">
      <div class="stat-value">1,234</div>
      <div class="stat-label" data-en="Total Employees" data-ar="إجمالي الموظفين"></div>
    </div>
  </div>
</div>
```

```css
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.card-header {
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-body {
  padding: 20px;
}

[dir="rtl"] .card-header {
  flex-direction: row-reverse;
}
```

---

## 4. لوحة التحكم الرئيسية
### Main Dashboard Design

### 4.1 تخطيط لوحة التحكم
```html
<div class="dashboard">
  <!-- Quick Stats -->
  <div class="dashboard-stats">
    <div class="stat-card stat-primary">
      <div class="stat-icon">
        <i class="fas fa-users"></i>
      </div>
      <div class="stat-content">
        <div class="stat-number">1,234</div>
        <div class="stat-label" data-en="Total Employees" data-ar="إجمالي الموظفين"></div>
      </div>
    </div>
    
    <div class="stat-card stat-success">
      <div class="stat-icon">
        <i class="fas fa-clock"></i>
      </div>
      <div class="stat-content">
        <div class="stat-number">98.5%</div>
        <div class="stat-label" data-en="Attendance Rate" data-ar="معدل الحضور"></div>
      </div>
    </div>
    
    <div class="stat-card stat-warning">
      <div class="stat-icon">
        <i class="fas fa-calendar-alt"></i>
      </div>
      <div class="stat-content">
        <div class="stat-number">45</div>
        <div class="stat-label" data-en="Pending Leaves" data-ar="الإجازات المعلقة"></div>
      </div>
    </div>
    
    <div class="stat-card stat-info">
      <div class="stat-icon">
        <i class="fas fa-money-bill-wave"></i>
      </div>
      <div class="stat-content">
        <div class="stat-number">2.5M</div>
        <div class="stat-label" data-en="Monthly Payroll" data-ar="الرواتب الشهرية"></div>
      </div>
    </div>
  </div>
  
  <!-- Charts Section -->
  <div class="dashboard-charts">
    <div class="chart-container">
      <div class="chart-header">
        <h3 data-en="Attendance Trends" data-ar="اتجاهات الحضور"></h3>
      </div>
      <canvas id="attendanceChart"></canvas>
    </div>
    
    <div class="chart-container">
      <div class="chart-header">
        <h3 data-en="Department Distribution" data-ar="توزيع الأقسام"></h3>
      </div>
      <canvas id="departmentChart"></canvas>
    </div>
  </div>
  
  <!-- Recent Activities -->
  <div class="dashboard-activities">
    <div class="activity-header">
      <h3 data-en="Recent Activities" data-ar="الأنشطة الحديثة"></h3>
    </div>
    <div class="activity-list">
      <div class="activity-item">
        <div class="activity-icon">
          <i class="fas fa-user-plus"></i>
        </div>
        <div class="activity-content">
          <div class="activity-title" data-en="New employee added" data-ar="تم إضافة موظف جديد"></div>
          <div class="activity-time">2 hours ago</div>
        </div>
      </div>
    </div>
  </div>
</div>
```

### 4.2 الرسوم البيانية التفاعلية
```javascript
// Attendance Chart Configuration
const attendanceChartConfig = {
  type: 'line',
  data: {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [{
      label: getCurrentLanguage() === 'ar' ? 'معدل الحضور' : 'Attendance Rate',
      data: [95, 97, 94, 98, 96, 99],
      borderColor: '#1e40af',
      backgroundColor: 'rgba(30, 64, 175, 0.1)',
      tension: 0.4
    }]
  },
  options: {
    responsive: true,
    plugins: {
      legend: {
        labels: {
          font: {
            family: getCurrentLanguage() === 'ar' ? 'Cairo' : 'Roboto'
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100
      }
    }
  }
};

// Department Distribution Chart
const departmentChartConfig = {
  type: 'doughnut',
  data: {
    labels: getCurrentLanguage() === 'ar' 
      ? ['الموارد البشرية', 'تكنولوجيا المعلومات', 'المالية', 'التسويق']
      : ['HR', 'IT', 'Finance', 'Marketing'],
    datasets: [{
      data: [30, 25, 20, 25],
      backgroundColor: ['#1e40af', '#059669', '#dc2626', '#ea580c']
    }]
  }
};
```

---

## 5. صفحات إدارة الموظفين
### Employee Management Pages

### 5.1 قائمة الموظفين
```html
<div class="employee-list-page">
  <div class="page-header">
    <h1 class="page-title" data-en="Employee Management" data-ar="إدارة الموظفين"></h1>
    <div class="page-actions">
      <button class="btn btn-primary">
        <i class="fas fa-plus"></i>
        <span data-en="Add Employee" data-ar="إضافة موظف"></span>
      </button>
    </div>
  </div>
  
  <div class="filters-section">
    <div class="filter-group">
      <input type="text" class="form-input" placeholder-en="Search employees..." placeholder-ar="البحث عن الموظفين...">
    </div>
    <div class="filter-group">
      <select class="form-select">
        <option value="" data-en="All Departments" data-ar="جميع الأقسام"></option>
      </select>
    </div>
    <div class="filter-group">
      <select class="form-select">
        <option value="" data-en="All Status" data-ar="جميع الحالات"></option>
        <option value="active" data-en="Active" data-ar="نشط"></option>
        <option value="inactive" data-en="Inactive" data-ar="غير نشط"></option>
      </select>
    </div>
  </div>
  
  <div class="employee-table">
    <table class="data-table">
      <thead>
        <tr>
          <th data-en="Photo" data-ar="الصورة"></th>
          <th data-en="Name" data-ar="الاسم"></th>
          <th data-en="Employee ID" data-ar="رقم الموظف"></th>
          <th data-en="Department" data-ar="القسم"></th>
          <th data-en="Position" data-ar="المنصب"></th>
          <th data-en="Status" data-ar="الحالة"></th>
          <th data-en="Actions" data-ar="الإجراءات"></th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>
            <img src="avatar.jpg" class="employee-avatar" alt="Employee Photo">
          </td>
          <td>
            <div class="employee-name">أحمد محمد علي</div>
            <div class="employee-email"><EMAIL></div>
          </td>
          <td>EMP001</td>
          <td data-en="Human Resources" data-ar="الموارد البشرية"></td>
          <td data-en="HR Manager" data-ar="مدير الموارد البشرية"></td>
          <td>
            <span class="status-badge status-active" data-en="Active" data-ar="نشط"></span>
          </td>
          <td>
            <div class="action-buttons">
              <button class="btn-icon" title-en="View" title-ar="عرض">
                <i class="fas fa-eye"></i>
              </button>
              <button class="btn-icon" title-en="Edit" title-ar="تعديل">
                <i class="fas fa-edit"></i>
              </button>
              <button class="btn-icon btn-danger" title-en="Delete" title-ar="حذف">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
```

### 5.2 نموذج إضافة/تعديل الموظف
```html
<div class="employee-form-page">
  <div class="page-header">
    <h1 class="page-title" data-en="Add New Employee" data-ar="إضافة موظف جديد"></h1>
  </div>
  
  <form class="employee-form">
    <div class="form-sections">
      <!-- Personal Information -->
      <div class="form-section">
        <h3 class="section-title" data-en="Personal Information" data-ar="المعلومات الشخصية"></h3>
        
        <div class="form-row">
          <div class="form-group">
            <label class="form-label" data-en="First Name (Arabic)" data-ar="الاسم الأول (عربي)"></label>
            <input type="text" class="form-input" required>
          </div>
          <div class="form-group">
            <label class="form-label" data-en="First Name (English)" data-ar="الاسم الأول (إنجليزي)"></label>
            <input type="text" class="form-input" required>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label class="form-label" data-en="Last Name (Arabic)" data-ar="اسم العائلة (عربي)"></label>
            <input type="text" class="form-input" required>
          </div>
          <div class="form-group">
            <label class="form-label" data-en="Last Name (English)" data-ar="اسم العائلة (إنجليزي)"></label>
            <input type="text" class="form-input" required>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label class="form-label" data-en="National ID" data-ar="الرقم القومي"></label>
            <input type="text" class="form-input" pattern="[0-9]{14}" required>
          </div>
          <div class="form-group">
            <label class="form-label" data-en="Date of Birth" data-ar="تاريخ الميلاد"></label>
            <input type="date" class="form-input" required>
          </div>
        </div>
      </div>
      
      <!-- Job Information -->
      <div class="form-section">
        <h3 class="section-title" data-en="Job Information" data-ar="معلومات الوظيفة"></h3>
        
        <div class="form-row">
          <div class="form-group">
            <label class="form-label" data-en="Employee Code" data-ar="رقم الموظف"></label>
            <input type="text" class="form-input" required>
          </div>
          <div class="form-group">
            <label class="form-label" data-en="Department" data-ar="القسم"></label>
            <select class="form-select" required>
              <option value="" data-en="Select Department" data-ar="اختر القسم"></option>
            </select>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label class="form-label" data-en="Job Title (Arabic)" data-ar="المسمى الوظيفي (عربي)"></label>
            <input type="text" class="form-input" required>
          </div>
          <div class="form-group">
            <label class="form-label" data-en="Job Title (English)" data-ar="المسمى الوظيفي (إنجليزي)"></label>
            <input type="text" class="form-input" required>
          </div>
        </div>
      </div>
    </div>
    
    <div class="form-actions">
      <button type="submit" class="btn btn-primary">
        <i class="fas fa-save"></i>
        <span data-en="Save Employee" data-ar="حفظ الموظف"></span>
      </button>
      <button type="button" class="btn btn-secondary">
        <span data-en="Cancel" data-ar="إلغاء"></span>
      </button>
    </div>
  </form>
</div>
```

---

## 6. نظام التبديل بين اللغات
### Language Switching System

### 6.1 مكون تبديل اللغة
```html
<div class="language-switcher">
  <button class="lang-btn" data-lang="ar" onclick="switchLanguage('ar')">
    <img src="flags/egypt.svg" alt="العربية" class="flag-icon">
    <span>العربية</span>
  </button>
  <button class="lang-btn" data-lang="en" onclick="switchLanguage('en')">
    <img src="flags/usa.svg" alt="English" class="flag-icon">
    <span>English</span>
  </button>
</div>
```

### 6.2 JavaScript لتبديل اللغات
```javascript
// Language Management System
class LanguageManager {
  constructor() {
    this.currentLanguage = localStorage.getItem('language') || 'ar';
    this.translations = {};
    this.init();
  }
  
  async init() {
    await this.loadTranslations();
    this.applyLanguage(this.currentLanguage);
  }
  
  async loadTranslations() {
    try {
      const response = await fetch('/api/translations');
      this.translations = await response.json();
    } catch (error) {
      console.error('Failed to load translations:', error);
    }
  }
  
  switchLanguage(lang) {
    this.currentLanguage = lang;
    localStorage.setItem('language', lang);
    this.applyLanguage(lang);
  }
  
  applyLanguage(lang) {
    // Update document direction
    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.lang = lang;
    
    // Update text content
    document.querySelectorAll('[data-en][data-ar]').forEach(element => {
      const text = lang === 'ar' ? element.getAttribute('data-ar') : element.getAttribute('data-en');
      element.textContent = text;
    });
    
    // Update placeholders
    document.querySelectorAll('[placeholder-en][placeholder-ar]').forEach(element => {
      const placeholder = lang === 'ar' ? element.getAttribute('placeholder-ar') : element.getAttribute('placeholder-en');
      element.placeholder = placeholder;
    });
    
    // Update titles
    document.querySelectorAll('[title-en][title-ar]').forEach(element => {
      const title = lang === 'ar' ? element.getAttribute('title-ar') : element.getAttribute('title-en');
      element.title = title;
    });
    
    // Update active language button
    document.querySelectorAll('.lang-btn').forEach(btn => {
      btn.classList.toggle('active', btn.getAttribute('data-lang') === lang);
    });
    
    // Update body class for styling
    document.body.className = document.body.className.replace(/lang-\w+/, '') + ` lang-${lang}`;
  }
  
  getCurrentLanguage() {
    return this.currentLanguage;
  }
  
  translate(key) {
    return this.translations[this.currentLanguage]?.[key] || key;
  }
}

// Initialize language manager
const languageManager = new LanguageManager();

// Global functions
function switchLanguage(lang) {
  languageManager.switchLanguage(lang);
}

function getCurrentLanguage() {
  return languageManager.getCurrentLanguage();
}

function t(key) {
  return languageManager.translate(key);
}
```

---

## 7. التصميم المتجاوب
### Responsive Design

### 7.1 نقاط التوقف (Breakpoints)
```css
/* Breakpoints */
:root {
  --mobile: 480px;
  --tablet: 768px;
  --desktop: 1024px;
  --large: 1200px;
}

/* Mobile First Approach */
@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }
  
  .dashboard-stats {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    flex-direction: column;
  }
  
  .data-table {
    font-size: 14px;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .sidebar {
    transform: translateX(-100%);
    position: fixed;
    z-index: 1000;
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
}

@media (min-width: 769px) {
  .dashboard-stats {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .sidebar {
    position: relative;
    transform: none;
  }
}
```

### 7.2 تحسينات الهاتف المحمول
```css
/* Mobile Optimizations */
@media (max-width: 768px) {
  /* Touch-friendly buttons */
  .btn, .btn-icon {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Larger form inputs */
  .form-input, .form-select {
    padding: 16px;
    font-size: 16px; /* Prevents zoom on iOS */
  }
  
  /* Mobile navigation */
  .mobile-nav {
    display: block;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e5e7eb;
    padding: 10px;
  }
  
  .mobile-nav-item {
    flex: 1;
    text-align: center;
    padding: 10px;
    color: var(--secondary-gray);
  }
  
  .mobile-nav-item.active {
    color: var(--primary-blue);
  }
  
  /* Hide desktop sidebar on mobile */
  .sidebar {
    display: none;
  }
  
  /* Adjust main content */
  .main-content {
    margin-left: 0;
    padding-bottom: 80px; /* Space for mobile nav */
  }
}
```

---

## 8. إمكانية الوصول
### Accessibility Features

### 8.1 دعم قارئ الشاشة
```html
<!-- ARIA Labels and Roles -->
<nav role="navigation" aria-label="Main navigation">
  <ul>
    <li><a href="/dashboard" aria-current="page">Dashboard</a></li>
    <li><a href="/employees">Employees</a></li>
  </ul>
</nav>

<!-- Form