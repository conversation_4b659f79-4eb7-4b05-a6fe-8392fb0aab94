@model FitHRPlus.Web.Models.Leave.LeaveBalancesOverviewViewModel
@{
    ViewData["Title"] = "أرصدة الإجازات";
}

<!-- Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-title">
            <h1><i class="bi bi-calendar-check"></i> أرصدة الإجازات</h1>
            <p>عرض أرصدة الإجازات لجميع الموظفين</p>
        </div>
        <div class="page-actions">
            <a href="@Url.Action("Index")" class="btn btn-secondary">
                <i class="bi bi-arrow-right"></i>
                العودة لقائمة الإجازات
            </a>
            <button type="button" class="btn btn-primary" onclick="exportBalances()">
                <i class="bi bi-download"></i>
                تصدير التقرير
            </button>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-primary">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3 id="totalEmployees">@Model.Employees.Count</h3>
                        <p>إجمالي الموظفين</p>
                        <small>الموظفين المسجلين في النظام</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-people"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-success">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3 id="totalAvailableDays">0</h3>
                        <p>إجمالي الأيام المتاحة</p>
                        <small>مجموع الأيام المتبقية</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-calendar-plus"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-warning">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3 id="totalUsedDays">0</h3>
                        <p>إجمالي الأيام المستخدمة</p>
                        <small>مجموع الأيام المأخوذة</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-calendar-minus"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-info">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3 id="averageBalance">0</h3>
                        <p>متوسط الرصيد</p>
                        <small>متوسط الأيام المتبقية للموظف</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-graph-up"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">البحث بالاسم</label>
                        <input type="text" class="form-control" id="searchEmployee" placeholder="اسم الموظف...">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">القسم</label>
                        <select class="form-select" id="filterDepartment">
                            <option value="">جميع الأقسام</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">نوع الإجازة</label>
                        <select class="form-select" id="filterLeaveType">
                            <option value="">جميع أنواع الإجازات</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">حالة الرصيد</label>
                        <select class="form-select" id="filterBalanceStatus">
                            <option value="">جميع الحالات</option>
                            <option value="high">رصيد عالي (أكثر من 15 يوم)</option>
                            <option value="medium">رصيد متوسط (5-15 يوم)</option>
                            <option value="low">رصيد منخفض (أقل من 5 أيام)</option>
                            <option value="zero">لا يوجد رصيد</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Leave Balances Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">أرصدة الإجازات</h5>
            </div>
            <div class="card-body">
                @if (Model.Employees.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover" id="balancesTable">
                            <thead class="table-light">
                                <tr>
                                    <th>الموظف</th>
                                    <th>القسم</th>
                                    <th>الإجازة السنوية</th>
                                    <th>الإجازة المرضية</th>
                                    <th>إجازة الطوارئ</th>
                                    <th>إجمالي المتبقي</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var employee in Model.Employees)
                                {
                                    var annualLeave = employee.LeaveBalances.FirstOrDefault(b => b.LeaveTypeName.Contains("سنوية") || b.LeaveTypeName.Contains("Annual"));
                                    var sickLeave = employee.LeaveBalances.FirstOrDefault(b => b.LeaveTypeName.Contains("مرضية") || b.LeaveTypeName.Contains("Sick"));
                                    var emergencyLeave = employee.LeaveBalances.FirstOrDefault(b => b.LeaveTypeName.Contains("طوارئ") || b.LeaveTypeName.Contains("Emergency"));
                                    var totalRemaining = employee.LeaveBalances.Sum(b => b.RemainingDays);
                                    
                                    <tr data-employee="@employee.EmployeeName.ToLower()" 
                                        data-department="@employee.Department.ToLower()" 
                                        data-total-remaining="@totalRemaining">
                                        <td>
                                            <div class="employee-info">
                                                <div class="employee-avatar">@employee.EmployeeName.Substring(0, 1)</div>
                                                <div class="employee-details">
                                                    <h6 class="mb-0">@employee.EmployeeName</h6>
                                                    <small class="text-muted">@employee.EmployeeCode</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>@employee.Department</td>
                                        <td>
                                            @if (annualLeave != null)
                                            {
                                                <div class="balance-info">
                                                    <span class="balance-remaining">@annualLeave.RemainingDays</span>
                                                    <small class="text-muted">من @annualLeave.TotalDays</small>
                                                    <div class="progress mt-1" style="height: 4px;">
                                                        <div class="progress-bar bg-success" style="width: @((double)annualLeave.RemainingDays / annualLeave.TotalDays * 100)%"></div>
                                                    </div>
                                                </div>
                                            }
                                            else
                                            {
                                                <span class="text-muted">غير محدد</span>
                                            }
                                        </td>
                                        <td>
                                            @if (sickLeave != null)
                                            {
                                                <div class="balance-info">
                                                    <span class="balance-remaining">@sickLeave.RemainingDays</span>
                                                    <small class="text-muted">من @sickLeave.TotalDays</small>
                                                    <div class="progress mt-1" style="height: 4px;">
                                                        <div class="progress-bar bg-info" style="width: @((double)sickLeave.RemainingDays / sickLeave.TotalDays * 100)%"></div>
                                                    </div>
                                                </div>
                                            }
                                            else
                                            {
                                                <span class="text-muted">غير محدد</span>
                                            }
                                        </td>
                                        <td>
                                            @if (emergencyLeave != null)
                                            {
                                                <div class="balance-info">
                                                    <span class="balance-remaining">@emergencyLeave.RemainingDays</span>
                                                    <small class="text-muted">من @emergencyLeave.TotalDays</small>
                                                    <div class="progress mt-1" style="height: 4px;">
                                                        <div class="progress-bar bg-warning" style="width: @((double)emergencyLeave.RemainingDays / emergencyLeave.TotalDays * 100)%"></div>
                                                    </div>
                                                </div>
                                            }
                                            else
                                            {
                                                <span class="text-muted">غير محدد</span>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge bg-primary fs-6">@totalRemaining يوم</span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="viewEmployeeDetails('@employee.EmployeeId')" title="عرض التفاصيل">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-success" onclick="createLeaveRequest('@employee.EmployeeId')" title="إنشاء طلب إجازة">
                                                    <i class="bi bi-plus-circle"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="bi bi-inbox display-1 text-muted"></i>
                        <h5 class="mt-3 text-muted">لا توجد بيانات أرصدة إجازات</h5>
                        <p class="text-muted">لم يتم العثور على أي موظفين أو أرصدة إجازات</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
<script>
    $(document).ready(function() {
        // Calculate and display statistics
        calculateStatistics();
        
        // Populate filter dropdowns
        populateFilters();
        
        // Setup search and filter functionality
        setupFilters();
    });

    function calculateStatistics() {
        let totalAvailable = 0;
        let totalUsed = 0;
        let employeeCount = 0;

        $('#balancesTable tbody tr').each(function() {
            const totalRemaining = parseInt($(this).data('total-remaining')) || 0;
            totalAvailable += totalRemaining;
            employeeCount++;
        });

        // Calculate used days (this would need actual data from server)
        // For now, we'll estimate based on typical usage
        totalUsed = Math.round(totalAvailable * 0.3); // Assume 30% usage

        const averageBalance = employeeCount > 0 ? Math.round(totalAvailable / employeeCount) : 0;

        $('#totalAvailableDays').text(totalAvailable);
        $('#totalUsedDays').text(totalUsed);
        $('#averageBalance').text(averageBalance);
    }

    function populateFilters() {
        const departments = new Set();
        const leaveTypes = new Set();

        $('#balancesTable tbody tr').each(function() {
            const department = $(this).data('department');
            if (department) departments.add(department);
        });

        // Populate department filter
        departments.forEach(dept => {
            $('#filterDepartment').append(`<option value="${dept}">${dept}</option>`);
        });

        // Add common leave types
        const commonLeaveTypes = ['الإجازة السنوية', 'الإجازة المرضية', 'إجازة الطوارئ'];
        commonLeaveTypes.forEach(type => {
            $('#filterLeaveType').append(`<option value="${type}">${type}</option>`);
        });
    }

    function setupFilters() {
        $('#searchEmployee, #filterDepartment, #filterLeaveType, #filterBalanceStatus').on('input change', function() {
            filterTable();
        });
    }

    function filterTable() {
        const searchTerm = $('#searchEmployee').val().toLowerCase();
        const departmentFilter = $('#filterDepartment').val().toLowerCase();
        const balanceStatusFilter = $('#filterBalanceStatus').val();

        $('#balancesTable tbody tr').each(function() {
            const row = $(this);
            const employeeName = row.data('employee');
            const department = row.data('department');
            const totalRemaining = parseInt(row.data('total-remaining')) || 0;

            let showRow = true;

            // Search filter
            if (searchTerm && !employeeName.includes(searchTerm)) {
                showRow = false;
            }

            // Department filter
            if (departmentFilter && department !== departmentFilter) {
                showRow = false;
            }

            // Balance status filter
            if (balanceStatusFilter) {
                switch (balanceStatusFilter) {
                    case 'high':
                        if (totalRemaining <= 15) showRow = false;
                        break;
                    case 'medium':
                        if (totalRemaining < 5 || totalRemaining > 15) showRow = false;
                        break;
                    case 'low':
                        if (totalRemaining >= 5) showRow = false;
                        break;
                    case 'zero':
                        if (totalRemaining > 0) showRow = false;
                        break;
                }
            }

            row.toggle(showRow);
        });
    }

    function viewEmployeeDetails(employeeId) {
        // Redirect to employee details or show modal
        window.location.href = `@Url.Action("Details", "Employee")?id=${employeeId}`;
    }

    function createLeaveRequest(employeeId) {
        // Redirect to create leave request with pre-selected employee
        window.location.href = `@Url.Action("Create", "Leave")?employeeId=${employeeId}`;
    }

    function exportBalances() {
        showToast('جاري تصدير تقرير أرصدة الإجازات...', 'info');
        
        // TODO: Implement actual export functionality
        setTimeout(() => {
            showToast('تم تصدير التقرير بنجاح', 'success');
        }, 2000);
    }

    function showToast(message, type = 'info') {
        const bgClass = type === 'success' ? 'bg-success' :
                       type === 'warning' ? 'bg-warning' :
                       type === 'error' ? 'bg-danger' : 'bg-info';

        const toast = $(`
            <div class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `);

        let container = $('.toast-container');
        if (container.length === 0) {
            container = $('<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
            $('body').append(container);
        }

        container.append(toast);
        const bsToast = new bootstrap.Toast(toast[0]);
        bsToast.show();

        toast.on('hidden.bs.toast', function() {
            $(this).remove();
        });
    }
</script>

<style>
.employee-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.employee-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.employee-details h6 {
    font-size: 14px;
    font-weight: 600;
}

.employee-details small {
    font-size: 12px;
}

.balance-info {
    text-align: center;
}

.balance-remaining {
    font-weight: bold;
    font-size: 16px;
    color: #2563eb;
}

.progress {
    border-radius: 2px;
}

.stats-card-primary .stats-card-icon {
    color: #3b82f6;
}

.stats-card-success .stats-card-icon {
    color: #10b981;
}

.stats-card-warning .stats-card-icon {
    color: #f59e0b;
}

.stats-card-info .stats-card-icon {
    color: #6366f1;
}
</style>
}
