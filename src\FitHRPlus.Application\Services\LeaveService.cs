using FitHRPlus.Application.DTOs.Common;
using FitHRPlus.Application.DTOs.Employee;
using FitHRPlus.Application.DTOs.Leave;
using FitHRPlus.Application.Interfaces;
using FitHRPlus.Application.Common;
using FitHRPlus.Domain.Entities;
using FitHRPlus.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FitHRPlus.Application.Services
{
    /// <summary>
    /// Leave management service implementation
    /// تنفيذ خدمة إدارة الإجازات
    /// </summary>
    public class LeaveService : ILeaveService
    {
        private readonly FitHRContext _context;
        private readonly ILogger<LeaveService> _logger;

        public LeaveService(FitHRContext context, ILogger<LeaveService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Get paginated list of leave requests
        /// الحصول على قائمة مقسمة لطلبات الإجازات
        /// </summary>
        public async Task<ServiceResult<PaginatedResult<LeaveRequestDto>>> GetLeaveRequestsAsync(LeaveRequestListDto request)
        {
            try
            {
                var query = _context.LeaveRequests
                    .Include(lr => lr.Employee)
                        .ThenInclude(e => e.Department)
                    .Include(lr => lr.LeaveType)
                    .Include(lr => lr.ApprovedByEmployee)
                    .Where(lr => lr.IsActive);

                // Apply filters
                if (request.CompanyId.HasValue)
                {
                    query = query.Where(lr => lr.Employee.CompanyId == request.CompanyId.Value);
                }

                if (request.EmployeeId.HasValue)
                {
                    query = query.Where(lr => lr.EmployeeId == request.EmployeeId.Value);
                }

                if (request.LeaveTypeId.HasValue)
                {
                    query = query.Where(lr => lr.LeaveTypeId == request.LeaveTypeId.Value);
                }

                if (!string.IsNullOrEmpty(request.Status))
                {
                    query = query.Where(lr => lr.Status == request.Status);
                }

                if (request.DateFrom.HasValue)
                {
                    query = query.Where(lr => lr.StartDate >= request.DateFrom.Value);
                }

                if (request.DateTo.HasValue)
                {
                    query = query.Where(lr => lr.EndDate <= request.DateTo.Value);
                }

                if (!string.IsNullOrEmpty(request.SearchTerm))
                {
                    var searchTerm = request.SearchTerm.ToLower();
                    query = query.Where(lr => 
                        lr.Employee.FirstName.ToLower().Contains(searchTerm) ||
                        lr.Employee.LastName.ToLower().Contains(searchTerm) ||
                        lr.Employee.EmployeeCode.ToLower().Contains(searchTerm) ||
                        lr.LeaveType.Name.ToLower().Contains(searchTerm) ||
                        lr.LeaveType.NameAr.ToLower().Contains(searchTerm));
                }

                // Apply sorting
                query = request.SortBy?.ToLower() switch
                {
                    "employeename" => request.SortDirection?.ToLower() == "desc" 
                        ? query.OrderByDescending(lr => lr.Employee.FirstName)
                        : query.OrderBy(lr => lr.Employee.FirstName),
                    "leavetype" => request.SortDirection?.ToLower() == "desc"
                        ? query.OrderByDescending(lr => lr.LeaveType.Name)
                        : query.OrderBy(lr => lr.LeaveType.Name),
                    "startdate" => request.SortDirection?.ToLower() == "desc"
                        ? query.OrderByDescending(lr => lr.StartDate)
                        : query.OrderBy(lr => lr.StartDate),
                    "status" => request.SortDirection?.ToLower() == "desc"
                        ? query.OrderByDescending(lr => lr.Status)
                        : query.OrderBy(lr => lr.Status),
                    _ => request.SortDirection?.ToLower() == "desc"
                        ? query.OrderByDescending(lr => lr.CreatedAt)
                        : query.OrderBy(lr => lr.CreatedAt)
                };

                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);

                var items = await query
                    .Skip((request.Page - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .Select(lr => new LeaveRequestDto
                    {
                        Id = lr.Id,
                        EmployeeId = lr.EmployeeId,
                        EmployeeName = $"{lr.Employee.FirstName} {lr.Employee.LastName}",
                        EmployeeNameAr = $"{lr.Employee.FirstNameAr} {lr.Employee.LastNameAr}",
                        EmployeeCode = lr.Employee.EmployeeCode,
                        DepartmentName = lr.Employee.Department.Name,
                        DepartmentNameAr = lr.Employee.Department.NameAr,
                        LeaveTypeId = lr.LeaveTypeId,
                        LeaveTypeName = lr.LeaveType.Name,
                        LeaveTypeNameAr = lr.LeaveType.NameAr,
                        StartDate = lr.StartDate,
                        EndDate = lr.EndDate,
                        TotalDays = lr.TotalDays,
                        Reason = lr.Reason,
                        ReasonAr = lr.ReasonAr,
                        Status = lr.Status,
                        StatusAr = GetStatusArabic(lr.Status),
                        ApprovedBy = lr.ApprovedBy,
                        ApprovedByName = lr.ApprovedByEmployee != null 
                            ? $"{lr.ApprovedByEmployee.FirstName} {lr.ApprovedByEmployee.LastName}"
                            : null,
                        ApprovedAt = lr.ApprovedAt,
                        RejectionReason = lr.RejectionReason,
                        RejectionReasonAr = lr.RejectionReasonAr,
                        EmergencyContact = lr.EmergencyContact,
                        EmergencyPhone = lr.EmergencyPhone,
                        CreatedAt = lr.CreatedAt,
                        UpdatedAt = lr.UpdatedAt,
                        CanApprove = lr.Status == "Pending",
                        CanReject = lr.Status == "Pending",
                        CanCancel = lr.Status == "Pending" || lr.Status == "Approved",
                        CanEdit = lr.Status == "Pending"
                    })
                    .ToListAsync();

                var result = new PaginatedResult<LeaveRequestDto>
                {
                    Items = items,
                    CurrentPage = request.Page,
                    TotalPages = totalPages,
                    TotalCount = totalCount,
                    PageSize = request.PageSize
                };

                return ServiceResult<PaginatedResult<LeaveRequestDto>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting leave requests");
                return ServiceResult<PaginatedResult<LeaveRequestDto>>.Failure("An error occurred while retrieving leave requests");
            }
        }

        /// <summary>
        /// Get leave request by ID
        /// الحصول على طلب الإجازة بالمعرف
        /// </summary>
        public async Task<ServiceResult<LeaveRequestDto>> GetLeaveRequestByIdAsync(Guid id)
        {
            try
            {
                var leaveRequest = await _context.LeaveRequests
                    .Include(lr => lr.Employee)
                        .ThenInclude(e => e.Department)
                    .Include(lr => lr.LeaveType)
                    .Include(lr => lr.ApprovedByEmployee)
                    .FirstOrDefaultAsync(lr => lr.Id == id && lr.IsActive);

                if (leaveRequest == null)
                {
                    return ServiceResult<LeaveRequestDto>.Failure("Leave request not found");
                }

                var dto = new LeaveRequestDto
                {
                    Id = leaveRequest.Id,
                    EmployeeId = leaveRequest.EmployeeId,
                    EmployeeName = $"{leaveRequest.Employee.FirstName} {leaveRequest.Employee.LastName}",
                    EmployeeNameAr = $"{leaveRequest.Employee.FirstNameAr} {leaveRequest.Employee.LastNameAr}",
                    EmployeeCode = leaveRequest.Employee.EmployeeCode,
                    DepartmentName = leaveRequest.Employee.Department.Name,
                    DepartmentNameAr = leaveRequest.Employee.Department.NameAr,
                    LeaveTypeId = leaveRequest.LeaveTypeId,
                    LeaveTypeName = leaveRequest.LeaveType.Name,
                    LeaveTypeNameAr = leaveRequest.LeaveType.NameAr,
                    StartDate = leaveRequest.StartDate,
                    EndDate = leaveRequest.EndDate,
                    TotalDays = leaveRequest.TotalDays,
                    Reason = leaveRequest.Reason,
                    ReasonAr = leaveRequest.ReasonAr,
                    Status = leaveRequest.Status,
                    StatusAr = GetStatusArabic(leaveRequest.Status),
                    ApprovedBy = leaveRequest.ApprovedBy,
                    ApprovedByName = leaveRequest.ApprovedByEmployee != null 
                        ? $"{leaveRequest.ApprovedByEmployee.FirstName} {leaveRequest.ApprovedByEmployee.LastName}"
                        : null,
                    ApprovedAt = leaveRequest.ApprovedAt,
                    RejectionReason = leaveRequest.RejectionReason,
                    RejectionReasonAr = leaveRequest.RejectionReasonAr,
                    EmergencyContact = leaveRequest.EmergencyContact,
                    EmergencyPhone = leaveRequest.EmergencyPhone,
                    CreatedAt = leaveRequest.CreatedAt,
                    UpdatedAt = leaveRequest.UpdatedAt,
                    CanApprove = leaveRequest.Status == "Pending",
                    CanReject = leaveRequest.Status == "Pending",
                    CanCancel = leaveRequest.Status == "Pending" || leaveRequest.Status == "Approved",
                    CanEdit = leaveRequest.Status == "Pending"
                };

                return ServiceResult<LeaveRequestDto>.Success(dto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting leave request by ID: {Id}", id);
                return ServiceResult<LeaveRequestDto>.Failure("An error occurred while retrieving the leave request");
            }
        }

        /// <summary>
        /// Create new leave request
        /// إنشاء طلب إجازة جديد
        /// </summary>
        public async Task<ServiceResult<LeaveRequestDto>> CreateLeaveRequestAsync(CreateLeaveRequestDto request, Guid userId)
        {
            try
            {
                // Validate the request
                var validationResult = await ValidateLeaveRequestAsync(request.EmployeeId, request.LeaveTypeId, request.StartDate, request.EndDate);
                if (!validationResult.IsSuccess)
                {
                    return ServiceResult<LeaveRequestDto>.Failure(validationResult.ErrorMessage!);
                }

                // Calculate total days
                var employee = await _context.Employees
                    .Include(e => e.Company)
                    .FirstOrDefaultAsync(e => e.Id == request.EmployeeId);

                if (employee == null)
                {
                    return ServiceResult<LeaveRequestDto>.Failure("Employee not found");
                }

                var daysResult = await CalculateLeaveDaysAsync(request.StartDate, request.EndDate, employee.CompanyId);
                if (!daysResult.IsSuccess)
                {
                    return ServiceResult<LeaveRequestDto>.Failure(daysResult.ErrorMessage!);
                }

                var leaveRequest = new LeaveRequest
                {
                    Id = Guid.NewGuid(),
                    EmployeeId = request.EmployeeId,
                    LeaveTypeId = request.LeaveTypeId,
                    StartDate = request.StartDate,
                    EndDate = request.EndDate,
                    TotalDays = daysResult.Data,
                    Reason = request.Reason,
                    ReasonAr = request.ReasonAr,
                    Status = "Pending",
                    EmergencyContact = request.EmergencyContact,
                    EmergencyPhone = request.EmergencyPhone,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    CreatedBy = userId,
                    UpdatedBy = userId,
                    IsActive = true
                };

                _context.LeaveRequests.Add(leaveRequest);
                await _context.SaveChangesAsync();

                return await GetLeaveRequestByIdAsync(leaveRequest.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating leave request");
                return ServiceResult<LeaveRequestDto>.Failure("An error occurred while creating the leave request");
            }
        }

        // Helper method to get status in Arabic
        private static string GetStatusArabic(string status)
        {
            return status switch
            {
                "Pending" => "في الانتظار",
                "Approved" => "موافق عليه",
                "Rejected" => "مرفوض",
                "Cancelled" => "ملغي",
                _ => status
            };
        }

        // Additional methods will be implemented in the next part
        public Task<ServiceResult<LeaveRequestDto>> UpdateLeaveRequestAsync(UpdateLeaveRequestDto request, Guid userId)
        {
            throw new NotImplementedException();
        }

        public async Task<ServiceResult<LeaveRequestDto>> ApproveLeaveRequestAsync(ApproveLeaveRequestDto request, Guid userId)
        {
            try
            {
                var leaveRequest = await _context.LeaveRequests
                    .Include(lr => lr.Employee)
                    .FirstOrDefaultAsync(lr => lr.Id == request.LeaveRequestId && lr.IsActive);

                if (leaveRequest == null)
                {
                    return ServiceResult<LeaveRequestDto>.Failure("Leave request not found");
                }

                if (leaveRequest.Status != "Pending")
                {
                    return ServiceResult<LeaveRequestDto>.Failure("Only pending requests can be approved");
                }

                // Update leave request
                leaveRequest.Status = "Approved";
                leaveRequest.ApprovedBy = userId;
                leaveRequest.ApprovedAt = DateTime.UtcNow;
                leaveRequest.UpdatedAt = DateTime.UtcNow;
                leaveRequest.UpdatedBy = userId;

                // Update leave balance
                var currentYear = DateTime.Today.Year;
                var balance = await _context.LeaveBalances
                    .FirstOrDefaultAsync(lb => lb.EmployeeId == leaveRequest.EmployeeId &&
                                             lb.LeaveTypeId == leaveRequest.LeaveTypeId &&
                                             lb.Year == currentYear);

                if (balance != null)
                {
                    balance.UsedDays += leaveRequest.TotalDays;
                    balance.RemainingDays -= leaveRequest.TotalDays;
                    balance.UpdatedAt = DateTime.UtcNow;
                    balance.UpdatedBy = userId;
                }

                await _context.SaveChangesAsync();

                return await GetLeaveRequestByIdAsync(leaveRequest.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving leave request: {Id}", request.LeaveRequestId);
                return ServiceResult<LeaveRequestDto>.Failure("An error occurred while approving the leave request");
            }
        }

        public async Task<ServiceResult<LeaveRequestDto>> RejectLeaveRequestAsync(RejectLeaveRequestDto request, Guid userId)
        {
            try
            {
                var leaveRequest = await _context.LeaveRequests
                    .FirstOrDefaultAsync(lr => lr.Id == request.LeaveRequestId && lr.IsActive);

                if (leaveRequest == null)
                {
                    return ServiceResult<LeaveRequestDto>.Failure("Leave request not found");
                }

                if (leaveRequest.Status != "Pending")
                {
                    return ServiceResult<LeaveRequestDto>.Failure("Only pending requests can be rejected");
                }

                // Update leave request
                leaveRequest.Status = "Rejected";
                leaveRequest.ApprovedBy = userId;
                leaveRequest.ApprovedAt = DateTime.UtcNow;
                leaveRequest.RejectionReason = request.RejectionReason;
                leaveRequest.RejectionReasonAr = request.RejectionReasonAr;
                leaveRequest.UpdatedAt = DateTime.UtcNow;
                leaveRequest.UpdatedBy = userId;

                await _context.SaveChangesAsync();

                return await GetLeaveRequestByIdAsync(leaveRequest.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting leave request: {Id}", request.LeaveRequestId);
                return ServiceResult<LeaveRequestDto>.Failure("An error occurred while rejecting the leave request");
            }
        }

        public Task<ServiceResult<LeaveRequestDto>> CancelLeaveRequestAsync(Guid leaveRequestId, Guid userId)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<bool>> DeleteLeaveRequestAsync(Guid leaveRequestId, Guid userId)
        {
            throw new NotImplementedException();
        }

        public async Task<ServiceResult<List<LeaveTypeDto>>> GetLeaveTypesAsync(Guid companyId)
        {
            try
            {
                var leaveTypes = await _context.LeaveTypes
                    .Where(lt => lt.CompanyId == companyId && lt.IsActive)
                    .Select(lt => new LeaveTypeDto
                    {
                        Id = lt.Id,
                        Name = lt.Name,
                        NameAr = lt.NameAr,
                        Description = lt.Description,
                        DescriptionAr = lt.DescriptionAr,
                        MaxDaysPerYear = lt.MaxDaysPerYear,
                        MaxConsecutiveDays = lt.MaxConsecutiveDays,
                        MinDaysNotice = lt.MinDaysNotice,
                        IsPaid = lt.IsPaid,
                        RequiresApproval = lt.RequiresApproval,
                        RequiresDocument = lt.RequiresDocument,
                        CarryForward = lt.CarryForward,
                        CarryForwardLimit = lt.CarryForwardLimit,
                        Gender = lt.Gender,
                        IsActive = lt.IsActive
                    })
                    .OrderBy(lt => lt.Name)
                    .ToListAsync();

                return ServiceResult<List<LeaveTypeDto>>.Success(leaveTypes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting leave types for company: {CompanyId}", companyId);
                return ServiceResult<List<LeaveTypeDto>>.Failure("An error occurred while retrieving leave types");
            }
        }

        public async Task<ServiceResult<LeaveTypeDto>> GetLeaveTypeByIdAsync(Guid id)
        {
            try
            {
                var leaveType = await _context.LeaveTypes
                    .Where(lt => lt.Id == id && lt.IsActive)
                    .Select(lt => new LeaveTypeDto
                    {
                        Id = lt.Id,
                        Name = lt.Name,
                        NameAr = lt.NameAr,
                        Description = lt.Description,
                        DescriptionAr = lt.DescriptionAr,
                        MaxDaysPerYear = lt.MaxDaysPerYear,
                        MaxConsecutiveDays = lt.MaxConsecutiveDays,
                        MinDaysNotice = lt.MinDaysNotice,
                        IsPaid = lt.IsPaid,
                        RequiresApproval = lt.RequiresApproval,
                        RequiresDocument = lt.RequiresDocument,
                        CarryForward = lt.CarryForward,
                        CarryForwardLimit = lt.CarryForwardLimit,
                        Gender = lt.Gender,
                        IsActive = lt.IsActive
                    })
                    .FirstOrDefaultAsync();

                if (leaveType == null)
                {
                    return ServiceResult<LeaveTypeDto>.Failure("Leave type not found");
                }

                return ServiceResult<LeaveTypeDto>.Success(leaveType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting leave type by ID: {Id}", id);
                return ServiceResult<LeaveTypeDto>.Failure("An error occurred while retrieving the leave type");
            }
        }

        public async Task<ServiceResult<List<LeaveBalanceDto>>> GetLeaveBalancesAsync(Guid employeeId, int? year = null)
        {
            try
            {
                var currentYear = year ?? DateTime.Today.Year;

                var balances = await _context.LeaveBalances
                    .Include(lb => lb.Employee)
                    .Include(lb => lb.LeaveType)
                    .Where(lb => lb.EmployeeId == employeeId && lb.Year == currentYear && lb.IsActive)
                    .Select(lb => new LeaveBalanceDto
                    {
                        Id = lb.Id,
                        EmployeeId = lb.EmployeeId,
                        EmployeeName = $"{lb.Employee.FirstName} {lb.Employee.LastName}",
                        EmployeeNameAr = $"{lb.Employee.FirstNameAr} {lb.Employee.LastNameAr}",
                        LeaveTypeId = lb.LeaveTypeId,
                        LeaveTypeName = lb.LeaveType.Name,
                        LeaveTypeNameAr = lb.LeaveType.NameAr,
                        Year = lb.Year,
                        EntitledDays = lb.EntitledDays,
                        UsedDays = lb.UsedDays,
                        RemainingDays = lb.RemainingDays,
                        CarriedForwardDays = lb.CarriedForwardDays,
                        CreatedAt = lb.CreatedAt,
                        UpdatedAt = lb.UpdatedAt
                    })
                    .ToListAsync();

                return ServiceResult<List<LeaveBalanceDto>>.Success(balances);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting leave balances for employee: {EmployeeId}", employeeId);
                return ServiceResult<List<LeaveBalanceDto>>.Failure("An error occurred while retrieving leave balances");
            }
        }

        public async Task<ServiceResult<LeaveBalanceDto>> GetLeaveBalanceAsync(Guid employeeId, Guid leaveTypeId, int? year = null)
        {
            try
            {
                var currentYear = year ?? DateTime.Today.Year;

                var balance = await _context.LeaveBalances
                    .Include(lb => lb.Employee)
                    .Include(lb => lb.LeaveType)
                    .Where(lb => lb.EmployeeId == employeeId && lb.LeaveTypeId == leaveTypeId && lb.Year == currentYear && lb.IsActive)
                    .Select(lb => new LeaveBalanceDto
                    {
                        Id = lb.Id,
                        EmployeeId = lb.EmployeeId,
                        EmployeeName = $"{lb.Employee.FirstName} {lb.Employee.LastName}",
                        EmployeeNameAr = $"{lb.Employee.FirstNameAr} {lb.Employee.LastNameAr}",
                        LeaveTypeId = lb.LeaveTypeId,
                        LeaveTypeName = lb.LeaveType.Name,
                        LeaveTypeNameAr = lb.LeaveType.NameAr,
                        Year = lb.Year,
                        EntitledDays = lb.EntitledDays,
                        UsedDays = lb.UsedDays,
                        RemainingDays = lb.RemainingDays,
                        CarriedForwardDays = lb.CarriedForwardDays,
                        CreatedAt = lb.CreatedAt,
                        UpdatedAt = lb.UpdatedAt
                    })
                    .FirstOrDefaultAsync();

                if (balance == null)
                {
                    return ServiceResult<LeaveBalanceDto>.Failure("Leave balance not found");
                }

                return ServiceResult<LeaveBalanceDto>.Success(balance);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting leave balance for employee: {EmployeeId}, leave type: {LeaveTypeId}", employeeId, leaveTypeId);
                return ServiceResult<LeaveBalanceDto>.Failure("An error occurred while retrieving the leave balance");
            }
        }

        public async Task<ServiceResult<LeaveBalanceDto>> UpdateLeaveBalanceAsync(Guid employeeId, Guid leaveTypeId, int year, decimal entitledDays, Guid userId)
        {
            try
            {
                var balance = await _context.LeaveBalances
                    .FirstOrDefaultAsync(lb => lb.EmployeeId == employeeId && lb.LeaveTypeId == leaveTypeId && lb.Year == year);

                if (balance == null)
                {
                    // Create new balance
                    balance = new LeaveBalance
                    {
                        Id = Guid.NewGuid(),
                        EmployeeId = employeeId,
                        LeaveTypeId = leaveTypeId,
                        Year = year,
                        EntitledDays = entitledDays,
                        UsedDays = 0,
                        RemainingDays = entitledDays,
                        CarriedForwardDays = 0,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        CreatedBy = userId,
                        UpdatedBy = userId,
                        IsActive = true
                    };
                    _context.LeaveBalances.Add(balance);
                }
                else
                {
                    // Update existing balance
                    balance.EntitledDays = entitledDays;
                    balance.RemainingDays = entitledDays - balance.UsedDays + balance.CarriedForwardDays;
                    balance.UpdatedAt = DateTime.UtcNow;
                    balance.UpdatedBy = userId;
                }

                await _context.SaveChangesAsync();
                return await GetLeaveBalanceAsync(employeeId, leaveTypeId, year);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating leave balance for employee: {EmployeeId}, leave type: {LeaveTypeId}", employeeId, leaveTypeId);
                return ServiceResult<LeaveBalanceDto>.Failure("An error occurred while updating the leave balance");
            }
        }

        public async Task<ServiceResult<decimal>> CalculateLeaveDaysAsync(DateTime startDate, DateTime endDate, Guid companyId)
        {
            try
            {
                if (startDate > endDate)
                {
                    return ServiceResult<decimal>.Failure("Start date cannot be after end date");
                }

                var totalDays = 0m;
                var currentDate = startDate;

                // Get company holidays
                var holidays = await _context.Holidays
                    .Where(h => h.CompanyId == companyId && h.IsActive)
                    .Select(h => h.Date.Date)
                    .ToListAsync();

                while (currentDate <= endDate)
                {
                    // Skip weekends (Friday and Saturday for Middle East)
                    if (currentDate.DayOfWeek != DayOfWeek.Friday && currentDate.DayOfWeek != DayOfWeek.Saturday)
                    {
                        // Skip holidays
                        if (!holidays.Contains(currentDate.Date))
                        {
                            totalDays++;
                        }
                    }
                    currentDate = currentDate.AddDays(1);
                }

                return ServiceResult<decimal>.Success(totalDays);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating leave days");
                return ServiceResult<decimal>.Failure("An error occurred while calculating leave days");
            }
        }

        public async Task<ServiceResult<bool>> ValidateLeaveRequestAsync(Guid employeeId, Guid leaveTypeId, DateTime startDate, DateTime endDate)
        {
            try
            {
                // Basic date validation
                if (startDate > endDate)
                {
                    return ServiceResult<bool>.Failure("Start date cannot be after end date");
                }

                if (startDate < DateTime.Today)
                {
                    return ServiceResult<bool>.Failure("Cannot request leave for past dates");
                }

                // Get leave type
                var leaveType = await _context.LeaveTypes
                    .FirstOrDefaultAsync(lt => lt.Id == leaveTypeId && lt.IsActive);

                if (leaveType == null)
                {
                    return ServiceResult<bool>.Failure("Leave type not found");
                }

                // Check minimum notice period
                var daysDifference = (startDate - DateTime.Today).Days;
                if (daysDifference < leaveType.MinDaysNotice)
                {
                    return ServiceResult<bool>.Failure($"Minimum {leaveType.MinDaysNotice} days notice required");
                }

                // Calculate requested days
                var employee = await _context.Employees
                    .FirstOrDefaultAsync(e => e.Id == employeeId);

                if (employee == null)
                {
                    return ServiceResult<bool>.Failure("Employee not found");
                }

                var daysResult = await CalculateLeaveDaysAsync(startDate, endDate, employee.CompanyId);
                if (!daysResult.IsSuccess)
                {
                    return ServiceResult<bool>.Failure(daysResult.ErrorMessage!);
                }

                var requestedDays = daysResult.Data;

                // Check maximum consecutive days
                if (leaveType.MaxConsecutiveDays.HasValue && requestedDays > leaveType.MaxConsecutiveDays.Value)
                {
                    return ServiceResult<bool>.Failure($"Maximum {leaveType.MaxConsecutiveDays.Value} consecutive days allowed");
                }

                // Check leave balance
                var currentYear = DateTime.Today.Year;
                var balance = await _context.LeaveBalances
                    .FirstOrDefaultAsync(lb => lb.EmployeeId == employeeId &&
                                             lb.LeaveTypeId == leaveTypeId &&
                                             lb.Year == currentYear);

                if (balance != null && balance.RemainingDays < requestedDays)
                {
                    return ServiceResult<bool>.Failure($"Insufficient leave balance. Available: {balance.RemainingDays} days");
                }

                // Check for overlapping requests
                var overlappingRequests = await _context.LeaveRequests
                    .Where(lr => lr.EmployeeId == employeeId &&
                               lr.IsActive &&
                               lr.Status != "Rejected" &&
                               lr.Status != "Cancelled" &&
                               ((lr.StartDate <= endDate && lr.EndDate >= startDate)))
                    .CountAsync();

                if (overlappingRequests > 0)
                {
                    return ServiceResult<bool>.Failure("Overlapping leave request exists");
                }

                return ServiceResult<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating leave request");
                return ServiceResult<bool>.Failure("An error occurred while validating the leave request");
            }
        }

        public async Task<ServiceResult<LeaveStatisticsDto>> GetLeaveStatisticsAsync(Guid companyId, Guid? employeeId = null, int? year = null)
        {
            try
            {
                var currentYear = year ?? DateTime.Today.Year;
                var query = _context.LeaveRequests
                    .Include(lr => lr.Employee)
                    .Include(lr => lr.LeaveType)
                    .Where(lr => lr.Employee.CompanyId == companyId && lr.IsActive);

                if (employeeId.HasValue)
                {
                    query = query.Where(lr => lr.EmployeeId == employeeId.Value);
                }

                // Filter by year
                query = query.Where(lr => lr.StartDate.Year == currentYear || lr.EndDate.Year == currentYear);

                var requests = await query.ToListAsync();

                var statistics = new LeaveStatisticsDto
                {
                    TotalRequests = requests.Count,
                    PendingRequests = requests.Count(r => r.Status == "Pending"),
                    ApprovedRequests = requests.Count(r => r.Status == "Approved"),
                    RejectedRequests = requests.Count(r => r.Status == "Rejected"),
                    CancelledRequests = requests.Count(r => r.Status == "Cancelled"),
                    TotalDaysRequested = requests.Sum(r => r.TotalDays),
                    TotalDaysApproved = requests.Where(r => r.Status == "Approved").Sum(r => r.TotalDays),
                    TotalDaysUsed = requests.Where(r => r.Status == "Approved" && r.EndDate < DateTime.Today).Sum(r => r.TotalDays),
                    RequestsByType = requests.GroupBy(r => r.LeaveType.Name).ToDictionary(g => g.Key, g => g.Count()),
                    RequestsByMonth = requests.GroupBy(r => r.StartDate.ToString("yyyy-MM")).ToDictionary(g => g.Key, g => g.Count()),
                    DaysByType = requests.GroupBy(r => r.LeaveType.Name).ToDictionary(g => g.Key, g => g.Sum(r => r.TotalDays))
                };

                return ServiceResult<LeaveStatisticsDto>.Success(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting leave statistics");
                return ServiceResult<LeaveStatisticsDto>.Failure("An error occurred while retrieving leave statistics");
            }
        }

        public async Task<ServiceResult<List<EmployeeDto>>> GetLeaveApproversAsync(Guid companyId, Guid employeeId)
        {
            try
            {
                // For now, return empty list - this would need proper implementation based on organizational hierarchy
                var approvers = new List<EmployeeDto>();
                return ServiceResult<List<EmployeeDto>>.Success(approvers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting leave approvers");
                return ServiceResult<List<EmployeeDto>>.Failure("An error occurred while retrieving leave approvers");
            }
        }

        public async Task<ServiceResult<List<LeaveRequestDto>>> GetPendingLeaveRequestsForApprovalAsync(Guid userId)
        {
            try
            {
                // For now, return empty list - this would need proper implementation based on user roles
                var requests = new List<LeaveRequestDto>();
                return ServiceResult<List<LeaveRequestDto>>.Success(requests);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending leave requests");
                return ServiceResult<List<LeaveRequestDto>>.Failure("An error occurred while retrieving pending leave requests");
            }
        }

        public async Task<ServiceResult<bool>> InitializeLeaveBalancesAsync(Guid employeeId, DateTime hireDate, Guid userId)
        {
            try
            {
                // For now, return success - this would need proper implementation
                return ServiceResult<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing leave balances");
                return ServiceResult<bool>.Failure("An error occurred while initializing leave balances");
            }
        }

        public async Task<ServiceResult<bool>> ProcessYearEndCarryForwardAsync(Guid companyId, int fromYear, int toYear, Guid userId)
        {
            try
            {
                // For now, return success - this would need proper implementation
                return ServiceResult<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing year-end carry forward");
                return ServiceResult<bool>.Failure("An error occurred while processing year-end carry forward");
            }
        }
    }
}
