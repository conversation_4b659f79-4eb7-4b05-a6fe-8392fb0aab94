using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace FitHRPlus.Web.Controllers
{
    /// <summary>
    /// Dashboard controller for main admin interface
    /// وحدة تحكم لوحة التحكم للواجهة الإدارية الرئيسية
    /// </summary>
    [Authorize]
    public class DashboardController : Controller
    {
        private readonly ILogger<DashboardController> _logger;

        public DashboardController(ILogger<DashboardController> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Main dashboard page
        /// الصفحة الرئيسية للوحة التحكم
        /// </summary>
        /// <returns>Dashboard view</returns>
        public IActionResult Index()
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var username = User.FindFirst(ClaimTypes.Name)?.Value;
            var fullName = User.FindFirst("FullName")?.Value;
            var preferredLanguage = User.FindFirst("PreferredLanguage")?.Value ?? "ar";

            var dashboardData = new AdminDashboardViewModel
            {
                UserId = Guid.TryParse(userId, out var id) ? id : Guid.Empty,
                Username = username ?? "Unknown",
                FullName = fullName ?? "Unknown User",
                PreferredLanguage = preferredLanguage,
                Roles = User.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList(),
                Companies = User.FindAll("CompanyName").Select(c => c.Value).ToList(),
                Permissions = User.FindAll("Permission").Select(c => c.Value).ToList(),
                LastLoginTime = DateTime.Now, // This should come from user data
                
                // Sample statistics - these should come from actual data services
                Statistics = new DashboardStatistics
                {
                    TotalEmployees = 150,
                    PresentToday = 142,
                    AbsentToday = 8,
                    OnLeave = 5,
                    PendingRequests = 12,
                    TotalCompanies = User.FindAll("CompanyName").Count(),
                    ActiveUsers = 45,
                    SystemAlerts = 3
                }
            };

            _logger.LogInformation("Dashboard accessed by user: {Username}", username);

            return View(dashboardData);
        }

        /// <summary>
        /// Get dashboard statistics as JSON
        /// الحصول على إحصائيات لوحة التحكم كـ JSON
        /// </summary>
        /// <returns>JSON statistics</returns>
        [HttpGet]
        public IActionResult GetStatistics()
        {
            // This should fetch real data from services
            var statistics = new
            {
                employees = new
                {
                    total = 150,
                    present = 142,
                    absent = 8,
                    onLeave = 5
                },
                attendance = new
                {
                    todayRate = 94.7,
                    weeklyAverage = 92.3,
                    monthlyAverage = 91.8
                },
                requests = new
                {
                    pending = 12,
                    approved = 45,
                    rejected = 3
                },
                system = new
                {
                    alerts = 3,
                    notifications = 8,
                    lastBackup = DateTime.Now.AddHours(-6)
                }
            };

            return Json(statistics);
        }

        /// <summary>
        /// Get recent activities
        /// الحصول على الأنشطة الحديثة
        /// </summary>
        /// <returns>JSON activities</returns>
        [HttpGet]
        public IActionResult GetRecentActivities()
        {
            // This should fetch real data from services
            var activities = new[]
            {
                new
                {
                    id = 1,
                    type = "attendance",
                    message = "Ahmed Ali checked in",
                    messageAr = "أحمد علي سجل الحضور",
                    timestamp = DateTime.Now.AddMinutes(-15),
                    icon = "fas fa-clock",
                    color = "success"
                },
                new
                {
                    id = 2,
                    type = "leave_request",
                    message = "Sara Mohamed requested leave",
                    messageAr = "سارة محمد طلبت إجازة",
                    timestamp = DateTime.Now.AddMinutes(-30),
                    icon = "fas fa-calendar-alt",
                    color = "warning"
                },
                new
                {
                    id = 3,
                    type = "user_registration",
                    message = "New user registered: Omar Hassan",
                    messageAr = "مستخدم جديد مسجل: عمر حسن",
                    timestamp = DateTime.Now.AddHours(-1),
                    icon = "fas fa-user-plus",
                    color = "info"
                },
                new
                {
                    id = 4,
                    type = "system",
                    message = "System backup completed",
                    messageAr = "تم إكمال النسخ الاحتياطي للنظام",
                    timestamp = DateTime.Now.AddHours(-6),
                    icon = "fas fa-database",
                    color = "primary"
                }
            };

            return Json(activities);
        }

        /// <summary>
        /// Change user language preference
        /// تغيير تفضيل لغة المستخدم
        /// </summary>
        /// <param name="language">Language code (ar/en)</param>
        /// <returns>JSON result</returns>
        [HttpPost]
        public IActionResult ChangeLanguage(string language)
        {
            if (language != "ar" && language != "en")
            {
                return BadRequest(new { success = false, message = "Invalid language code" });
            }

            // Store language preference in cookie
            Response.Cookies.Append("PreferredLanguage", language, new CookieOptions
            {
                Expires = DateTimeOffset.UtcNow.AddYears(1),
                HttpOnly = true,
                Secure = Request.IsHttps,
                SameSite = SameSiteMode.Lax
            });

            _logger.LogInformation("User {Username} changed language to {Language}", 
                User.FindFirst(ClaimTypes.Name)?.Value, language);

            return Json(new { success = true, language = language });
        }
    }

    /// <summary>
    /// Admin dashboard view model
    /// نموذج عرض لوحة التحكم الإدارية
    /// </summary>
    public class AdminDashboardViewModel
    {
        public Guid UserId { get; set; }
        public string Username { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string PreferredLanguage { get; set; } = "ar";
        public List<string> Roles { get; set; } = new();
        public List<string> Companies { get; set; } = new();
        public List<string> Permissions { get; set; } = new();
        public DateTime LastLoginTime { get; set; }
        public DashboardStatistics Statistics { get; set; } = new();
        public List<RecentActivity> RecentActivities { get; set; } = new();
        public List<PendingLeaveRequest> PendingLeaveRequests { get; set; } = new();
    }

    public class RecentActivity
    {
        public string Description { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
    }

    public class PendingLeaveRequest
    {
        public Guid Id { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string LeaveType { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
    }

    /// <summary>
    /// Dashboard statistics model
    /// نموذج إحصائيات لوحة التحكم
    /// </summary>
    public class DashboardStatistics
    {
        public int TotalEmployees { get; set; }
        public int PresentToday { get; set; }
        public int AbsentToday { get; set; }
        public int OnLeave { get; set; }
        public int PendingRequests { get; set; }
        public int PendingLeaveRequests { get; set; }
        public int PendingNotifications { get; set; }
        public int TotalCompanies { get; set; }
        public int ActiveUsers { get; set; }
        public int SystemAlerts { get; set; }
        
        public double AttendanceRate => TotalEmployees > 0 ? (double)PresentToday / TotalEmployees * 100 : 0;
    }
}
