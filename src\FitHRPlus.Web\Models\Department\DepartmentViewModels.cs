using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Web.Models.Department
{
    /// <summary>
    /// Department view model
    /// نموذج عرض القسم
    /// </summary>
    public class DepartmentViewModel
    {
        public Guid Id { get; set; }

        [Required(ErrorMessage = "Company ID is required")]
        public Guid CompanyId { get; set; }

        [Required(ErrorMessage = "Department name is required")]
        [StringLength(100, ErrorMessage = "Department name cannot exceed 100 characters")]
        [Display(Name = "Department Name")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "Arabic department name is required")]
        [StringLength(100, ErrorMessage = "Arabic department name cannot exceed 100 characters")]
        [Display(Name = "Department Name (Arabic)")]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
        [Display(Name = "Description")]
        public string? Description { get; set; }

        [StringLength(500, ErrorMessage = "Arabic description cannot exceed 500 characters")]
        [Display(Name = "Description (Arabic)")]
        public string? DescriptionAr { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "Budget must be a positive number")]
        [Display(Name = "Budget")]
        public decimal? Budget { get; set; }

        [Display(Name = "Employee Count")]
        public int EmployeeCount { get; set; }

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "Created At")]
        public DateTime CreatedAt { get; set; }

        [Display(Name = "Updated At")]
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// Department list view model
    /// نموذج عرض قائمة الأقسام
    /// </summary>
    public class DepartmentListViewModel
    {
        public List<DepartmentViewModel> Departments { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SearchTerm { get; set; }

        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
    }

    /// <summary>
    /// Department option view model for dropdowns
    /// نموذج خيار القسم للقوائم المنسدلة
    /// </summary>
    public class DepartmentOptionViewModel
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string NameAr { get; set; } = string.Empty;
        public string DisplayName => $"{Name} / {NameAr}";
    }
}
