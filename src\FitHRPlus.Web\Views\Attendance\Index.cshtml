@model FitHRPlus.Web.Models.Admin.AttendanceListViewModel
@{
    ViewData["Title"] = "إدارة الحضور والانصراف";
}

<!-- Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-title">
            <h1><i class="bi bi-person-check"></i> إدارة الحضور والانصراف</h1>
            <p>متابعة وإدارة حضور وانصراف الموظفين</p>
        </div>
        <div class="page-actions">
            <a href="@Url.Action("CheckIn")" class="btn btn-success">
                <i class="bi bi-box-arrow-in-right"></i>
                تسجيل حضور
            </a>
            <a href="@Url.Action("CheckOut")" class="btn btn-warning">
                <i class="bi bi-box-arrow-left"></i>
                تسجيل انصراف
            </a>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#filterModal">
                <i class="bi bi-funnel"></i>
                تصفية
            </button>
            <a href="@Url.Action("DailyReport")" class="btn btn-info">
                <i class="bi bi-graph-up"></i>
                التقرير اليومي
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-primary">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@Model.Statistics.TotalRecords</h3>
                        <p>إجمالي السجلات</p>
                        <small>جميع سجلات الحضور والانصراف</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-people"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: 100%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-success">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@Model.Statistics.TotalPresentDays</h3>
                        <p>أيام الحضور</p>
                        <small>إجمالي أيام الحضور المسجلة</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-check-circle"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: @(Model.Statistics.TotalRecords > 0 ? (Model.Statistics.TotalPresentDays * 100 / Model.Statistics.TotalRecords) : 0)%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-warning">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@Model.Statistics.TotalLateArrivals</h3>
                        <p>حالات التأخير</p>
                        <small>عدد مرات التأخير في الحضور</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-clock-history"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: @(Model.Statistics.TotalRecords > 0 ? (Model.Statistics.TotalLateArrivals * 100 / Model.Statistics.TotalRecords) : 0)%"></div>
                </div>
            </div>
        </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-info">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@Model.Statistics.TotalOvertimeRecords</h3>
                        <p>ساعات إضافية</p>
                        <small>عدد سجلات الوقت الإضافي</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-plus-circle"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: @(Model.Statistics.TotalRecords > 0 ? (Model.Statistics.TotalOvertimeRecords * 100 / Model.Statistics.TotalRecords) : 0)%"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <div class="col-lg-8">
        <!-- Attendance Table Card -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title">سجلات الحضور والانصراف</h5>
                    <div class="card-actions">
                        <select class="form-select form-select-sm" style="width: auto;" onchange="changePageSize(this.value)">
                            @foreach (var size in Model.PageSizes)
                            {
                                <option value="@size" selected="@(size == Model.PageSize)">@size عنصر</option>
                            }
                        </select>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                @if (Model.Attendances.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>
                                        <a href="javascript:void(0)" onclick="sortBy('employee')" class="text-decoration-none">
                                            الموظف
                                            <i class="bi bi-arrow-down-up ms-1"></i>
                                        </a>
                                    </th>
                                    <th>
                                        <a href="javascript:void(0)" onclick="sortBy('date')" class="text-decoration-none">
                                            التاريخ
                                            <i class="bi bi-arrow-down-up ms-1"></i>
                                        </a>
                                    </th>
                                    <th>
                                        <a href="javascript:void(0)" onclick="sortBy('checkin')" class="text-decoration-none">
                                            وقت الحضور
                                            <i class="bi bi-arrow-down-up ms-1"></i>
                                        </a>
                                    </th>
                                    <th>
                                        <a href="javascript:void(0)" onclick="sortBy('checkout')" class="text-decoration-none">
                                            وقت الانصراف
                                            <i class="bi bi-arrow-down-up ms-1"></i>
                                        </a>
                                    </th>
                                    <th>ساعات العمل</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var attendance in Model.Attendances)
                                {
                                    <tr class="attendance-row">
                                        <td>
                                            <div class="employee-info">
                                                <div class="employee-avatar">
                                                    @attendance.EmployeeName.Substring(0, 1).ToUpper()
                                                </div>
                                                <div class="employee-details">
                                                    <h6>@attendance.EmployeeName</h6>
                                                    <small>@attendance.EmployeeNumber</small>
                                                    @if (!string.IsNullOrEmpty(attendance.DepartmentName))
                                                    {
                                                        <div class="employee-department">@attendance.DepartmentName</div>
                                                    }
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="date-info">
                                                <span class="date">@attendance.Date.ToString("dd/MM/yyyy")</span>
                                                <small class="day">@attendance.Date.ToString("dddd")</small>
                                            </div>
                                        </td>
                                        <td>
                                            @if (attendance.CheckInTime.HasValue)
                                            {
                                                <div class="time-info">
                                                    <span class="time">@attendance.CheckInTimeFormatted</span>
                                                    @if (attendance.IsLate)
                                                    {
                                                        <small class="status-late">تأخير @attendance.LateMinutes دقيقة</small>
                                                    }
                                                </div>
                                            }
                                            else
                                            {
                                                <span class="time-empty">--:--</span>
                                            }
                                        </td>
                                        <td>
                                            @if (attendance.CheckOutTime.HasValue)
                                            {
                                                <div class="time-info">
                                                    <span class="time">@attendance.CheckOutTimeFormatted</span>
                                                    @if (attendance.IsEarlyDeparture)
                                                    {
                                                        <small class="status-early">مغادرة مبكرة @attendance.EarlyDepartureMinutes دقيقة</small>
                                                    }
                                                </div>
                                            }
                                            else
                                            {
                                                <span class="time-empty">--:--</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="working-hours">
                                                <span class="hours">@attendance.WorkingHoursFormatted</span>
                                                @if (attendance.HasOvertime)
                                                {
                                                    <small class="overtime">إضافي: @attendance.OvertimeHoursFormatted</small>
                                                }
                                            </div>
                                        </td>
                                        <td>
                                            <span class="status-badge @attendance.StatusBadgeClass">@attendance.StatusDisplayText</span>
                                            @if (!attendance.IsApproved)
                                            {
                                                <div class="approval-status">في انتظار الموافقة</div>
                                            }
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-ghost dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="bi bi-three-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="@Url.Action("Details", new { id = attendance.Id })">
                                                        <i class="bi bi-eye me-2"></i>عرض التفاصيل
                                                    </a></li>
                                                    @if (!attendance.IsApproved)
                                                    {
                                                        <li><a class="dropdown-item" href="javascript:void(0)" onclick="approveAttendance('@attendance.Id')">
                                                            <i class="bi bi-check me-2"></i>موافقة
                                                        </a></li>
                                                    }
                                                    <li><a class="dropdown-item text-danger" href="javascript:void(0)" onclick="deleteAttendance('@attendance.Id')">
                                                        <i class="bi bi-trash me-2"></i>حذف
                                                        </a></li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="bi bi-person-x"></i>
                            </div>
                            <h5>لا توجد سجلات حضور</h5>
                            <p>لم يتم العثور على أي سجلات حضور وانصراف</p>
                            <a href="@Url.Action("CheckIn")" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i>
                                تسجيل حضور جديد
                            </a>
                        </div>
                    }
                </div>

                <!-- Pagination -->
                @if (Model.TotalPages > 1)
                {
                    <div class="card-footer">
                        <nav aria-label="صفحات الحضور">
                            <ul class="pagination justify-content-center mb-0">
                                @if (Model.HasPreviousPage)
                                {
                                    <li class="page-item">
                                        <a class="page-link" href="javascript:void(0)" onclick="goToPage(@(Model.CurrentPage - 1))">
                                            <i class="bi bi-chevron-right"></i>
                                            السابق
                                        </a>
                                    </li>
                                }

                                @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                                {
                                    <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                        <a class="page-link" href="javascript:void(0)" onclick="goToPage(@i)">@i</a>
                                    </li>
                                }

                                @if (Model.HasNextPage)
                                {
                                    <li class="page-item">
                                        <a class="page-link" href="javascript:void(0)" onclick="goToPage(@(Model.CurrentPage + 1))">
                                            التالي
                                            <i class="bi bi-chevron-left"></i>
                                        </a>
                                    </li>
                                }
                            </ul>
                        </nav>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Attendance Chart -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">إحصائيات الحضور</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="attendanceStatsChart" style="height: 250px;"></canvas>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">الأنشطة الأخيرة</h5>
            </div>
            <div class="card-body">
                <div class="activity-list">
                    <div class="activity-item">
                        <div class="activity-icon bg-success">
                            <i class="bi bi-box-arrow-in-right"></i>
                        </div>
                        <div class="activity-content">
                            <h6>تسجيل حضور</h6>
                            <p>أحمد محمد - 08:30 ص</p>
                            <small>منذ 5 دقائق</small>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon bg-warning">
                            <i class="bi bi-clock"></i>
                        </div>
                        <div class="activity-content">
                            <h6>تأخير في الحضور</h6>
                            <p>سارة أحمد - 09:15 ص</p>
                            <small>منذ 15 دقيقة</small>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon bg-info">
                            <i class="bi bi-box-arrow-left"></i>
                        </div>
                        <div class="activity-content">
                            <h6>تسجيل انصراف</h6>
                            <p>محمد علي - 05:00 م</p>
                            <small>منذ ساعة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="quick-actions">
                    <a href="@Url.Action("CheckIn")" class="quick-action-btn">
                        <i class="bi bi-box-arrow-in-right"></i>
                        <span>تسجيل حضور</span>
                    </a>
                    <a href="@Url.Action("CheckOut")" class="quick-action-btn">
                        <i class="bi bi-box-arrow-left"></i>
                        <span>تسجيل انصراف</span>
                    </a>
                    <a href="@Url.Action("DailyReport")" class="quick-action-btn">
                        <i class="bi bi-graph-up"></i>
                        <span>التقرير اليومي</span>
                    </a>
                    <button class="quick-action-btn" onclick="exportAttendance()">
                        <i class="bi bi-download"></i>
                        <span>تصدير البيانات</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        $(document).ready(function() {
            loadAttendanceChart();
            initializeFilters();
        });

        function loadAttendanceChart() {
            const ctx = document.getElementById('attendanceStatsChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['حاضر', 'متأخر', 'غائب', 'وقت إضافي'],
                    datasets: [{
                        data: [
                            @Model.Statistics.TotalPresentDays,
                            @Model.Statistics.TotalLateArrivals,
                            @(Model.Statistics.TotalRecords - Model.Statistics.TotalPresentDays),
                            @Model.Statistics.TotalOvertimeRecords
                        ],
                        backgroundColor: [
                            '#28a745',
                            '#ffc107',
                            '#dc3545',
                            '#17a2b8'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: {
                                    family: 'Tajawal'
                                },
                                padding: 20
                            }
                        }
                    }
                }
            });
        }

        function initializeFilters() {
            // Initialize date pickers and other filters
            $('.date-picker').datepicker({
                format: 'dd/mm/yyyy',
                autoclose: true,
                todayHighlight: true
            });
        }

        function exportAttendance() {
            const btn = $('button[onclick="exportAttendance()"]');
            const originalText = btn.html();
            btn.html('<i class="bi bi-arrow-repeat spin"></i> جاري التصدير...');
            btn.prop('disabled', true);

            // Simulate export process
            setTimeout(() => {
                window.open('@Url.Action("Export", "Attendance")', '_blank');
                btn.html(originalText);
                btn.prop('disabled', false);
                showToast('تم تصدير البيانات بنجاح', 'success');
            }, 2000);
        }

        function approveAttendance(id) {
            if (confirm('هل تريد الموافقة على هذا السجل؟')) {
                $.ajax({
                    url: '@Url.Action("Approve", "Attendance")',
                    type: 'POST',
                    data: {
                        id: id,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            showToast('تم الموافقة على السجل بنجاح', 'success');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showToast('حدث خطأ: ' + result.message, 'error');
                        }
                    },
                    error: function() {
                        showToast('حدث خطأ أثناء الموافقة على السجل', 'error');
                    }
                });
            }
        }

        function deleteAttendance(id) {
            if (confirm('هل تريد حذف هذا السجل؟ لا يمكن التراجع عن هذا الإجراء.')) {
                $.ajax({
                    url: '@Url.Action("Delete", "Attendance")',
                    type: 'POST',
                    data: {
                        id: id,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            showToast('تم حذف السجل بنجاح', 'success');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showToast('حدث خطأ: ' + result.message, 'error');
                        }
                    },
                    error: function() {
                        showToast('حدث خطأ أثناء حذف السجل', 'error');
                    }
                });
            }
        }

        function goToPage(page) {
            const url = new URL(window.location);
            url.searchParams.set('page', page);
            window.location.href = url.toString();
        }

        function changePageSize(size) {
            const url = new URL(window.location);
            url.searchParams.set('pageSize', size);
            url.searchParams.set('page', 1);
            window.location.href = url.toString();
        }

        function sortBy(column) {
            const url = new URL(window.location);
            const currentSort = url.searchParams.get('sortBy');
            const currentOrder = url.searchParams.get('sortOrder');

            if (currentSort === column && currentOrder === 'asc') {
                url.searchParams.set('sortOrder', 'desc');
            } else {
                url.searchParams.set('sortOrder', 'asc');
            }

            url.searchParams.set('sortBy', column);
            window.location.href = url.toString();
        }

        function showToast(message, type = 'info') {
            const bgClass = type === 'success' ? 'bg-success' :
                           type === 'warning' ? 'bg-warning' :
                           type === 'error' ? 'bg-danger' : 'bg-info';

            const toast = $(`
                <div class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `);

            let container = $('.toast-container');
            if (container.length === 0) {
                container = $('<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
                $('body').append(container);
            }

            container.append(toast);
            const bsToast = new bootstrap.Toast(toast[0]);
            bsToast.show();

            toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    </script>
}

<!-- Filter Modal -->
<div class="modal fade" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="filterModalLabel">Filter Attendance Records / تصفية سجلات الحضور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="filterForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="employeeId" class="form-label">Employee / الموظف</label>
                                <select class="form-select" id="employeeId" name="EmployeeId">
                                    <option value="">All Employees</option>
                                    @foreach (var employee in Model.Employees)
                                    {
                                        <option value="@employee.Id" selected="@(employee.Id == Model.EmployeeId)">
                                            @employee.FullName (@employee.EmployeeNumber)
                                        </option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="departmentId" class="form-label">Department / القسم</label>
                                <select class="form-select" id="departmentId" name="DepartmentId">
                                    <option value="">All Departments</option>
                                    @foreach (var department in Model.Departments)
                                    {
                                        <option value="@department.Id" selected="@(department.Id == Model.DepartmentId)">
                                            @department.Name
                                        </option>
                                    }
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="dateFrom" class="form-label">Date From / من تاريخ</label>
                                <input type="date" class="form-control" id="dateFrom" name="DateFrom" value="@Model.DateFrom?.ToString("yyyy-MM-dd")">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="dateTo" class="form-label">Date To / إلى تاريخ</label>
                                <input type="date" class="form-control" id="dateTo" name="DateTo" value="@Model.DateTo?.ToString("yyyy-MM-dd")">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="attendanceStatus" class="form-label">Status / الحالة</label>
                                <select class="form-select" id="attendanceStatus" name="AttendanceStatus">
                                    <option value="">All Statuses</option>
                                    @foreach (var status in Model.AttendanceStatuses)
                                    {
                                        <option value="@status" selected="@(status == Model.AttendanceStatus)">@status</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="searchTerm" class="form-label">Search / البحث</label>
                                <input type="text" class="form-control" id="searchTerm" name="SearchTerm" value="@Model.SearchTerm" placeholder="Search by name, number...">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="showLateOnly" name="ShowLateOnly" value="true" @(Model.ShowLateOnly == true ? "checked" : "")>
                                <label class="form-check-label" for="showLateOnly">Late Only / المتأخرين فقط</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="showEarlyDepartureOnly" name="ShowEarlyDepartureOnly" value="true" @(Model.ShowEarlyDepartureOnly == true ? "checked" : "")>
                                <label class="form-check-label" for="showEarlyDepartureOnly">Early Departure Only / المغادرين مبكراً فقط</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="showOvertimeOnly" name="ShowOvertimeOnly" value="true" @(Model.ShowOvertimeOnly == true ? "checked" : "")>
                                <label class="form-check-label" for="showOvertimeOnly">Overtime Only / العمل الإضافي فقط</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="showUnapprovedOnly" name="ShowUnapprovedOnly" value="true" @(Model.ShowUnapprovedOnly == true ? "checked" : "")>
                                <label class="form-check-label" for="showUnapprovedOnly">Unapproved Only / غير المعتمد فقط</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="clearFilters()">Clear / مسح</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close / إغلاق</button>
                    <button type="submit" class="btn btn-primary">Apply Filters / تطبيق التصفية</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Filter form submission
        document.getElementById('filterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            applyFilters();
        });

        function applyFilters() {
            const form = document.getElementById('filterForm');
            const formData = new FormData(form);
            const params = new URLSearchParams();

            for (let [key, value] of formData.entries()) {
                if (value) {
                    params.append(key, value);
                }
            }

            // Add current page and page size
            params.append('Page', 1);
            params.append('PageSize', @Model.PageSize);

            window.location.href = '@Url.Action("Index")?' + params.toString();
        }

        function clearFilters() {
            document.getElementById('filterForm').reset();
        }

        function sortBy(field) {
            const currentSort = '@Model.SortBy';
            const currentDirection = '@Model.SortDirection';
            let newDirection = 'asc';

            if (currentSort === field && currentDirection === 'asc') {
                newDirection = 'desc';
            }

            const params = new URLSearchParams(window.location.search);
            params.set('SortBy', field);
            params.set('SortDirection', newDirection);
            params.set('Page', 1);

            window.location.href = '@Url.Action("Index")?' + params.toString();
        }

        function goToPage(page) {
            const params = new URLSearchParams(window.location.search);
            params.set('Page', page);
            window.location.href = '@Url.Action("Index")?' + params.toString();
        }

        function changePageSize(pageSize) {
            const params = new URLSearchParams(window.location.search);
            params.set('PageSize', pageSize);
            params.set('Page', 1);
            window.location.href = '@Url.Action("Index")?' + params.toString();
        }

        function approveAttendance(id) {
            if (confirm('Are you sure you want to approve this attendance record?')) {
                fetch('@Url.Action("Approve")', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({ id: id })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while approving the record.');
                });
            }
        }

        function deleteAttendance(id) {
            if (confirm('Are you sure you want to delete this attendance record? This action cannot be undone.')) {
                fetch('@Url.Action("Delete")', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({ id: id })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the record.');
                });
            }
        }
    </script>
}
