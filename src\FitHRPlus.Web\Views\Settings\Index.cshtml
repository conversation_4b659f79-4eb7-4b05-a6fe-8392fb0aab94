@model FitHRPlus.Web.Models.Settings.SettingsViewModel
@{
    ViewData["Title"] = "إعدادات النظام";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-0">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات النظام
                    </h2>
                    <p class="text-muted mb-0">إدارة إعدادات النظام والشركة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Cards -->
    <div class="row g-4">
        <!-- Company Settings -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-building fa-3x text-primary"></i>
                    </div>
                    <h5 class="card-title">إعدادات الشركة</h5>
                    <p class="card-text text-muted">
                        إدارة معلومات الشركة، ساعات العمل، والإعدادات المالية
                    </p>
                    <a href="@Url.Action("Company", "Settings")" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الإعدادات
                    </a>
                </div>
            </div>
        </div>

        <!-- System Settings -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-server fa-3x text-success"></i>
                    </div>
                    <h5 class="card-title">إعدادات النظام</h5>
                    <p class="card-text text-muted">
                        إدارة إعدادات الأمان، الجلسات، وكلمات المرور
                    </p>
                    <a href="@Url.Action("System", "Settings")" class="btn btn-success">
                        <i class="fas fa-cogs me-2"></i>
                        إدارة النظام
                    </a>
                </div>
            </div>
        </div>

        <!-- Notification Settings -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-bell fa-3x text-warning"></i>
                    </div>
                    <h5 class="card-title">إعدادات الإشعارات</h5>
                    <p class="card-text text-muted">
                        إدارة إعدادات الإشعارات والتنبيهات
                    </p>
                    <a href="@Url.Action("Index", "Notifications")" class="btn btn-warning">
                        <i class="fas fa-bell me-2"></i>
                        إدارة الإشعارات
                    </a>
                </div>
            </div>
        </div>

        <!-- Backup & Restore -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-database fa-3x text-info"></i>
                    </div>
                    <h5 class="card-title">النسخ الاحتياطي</h5>
                    <p class="card-text text-muted">
                        إنشاء واستعادة النسخ الاحتياطية للبيانات
                    </p>
                    <a href="@Url.Action("Backup", "Settings")" class="btn btn-info">
                        <i class="fas fa-download me-2"></i>
                        إدارة النسخ
                    </a>
                </div>
            </div>
        </div>

        <!-- User Management -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-users fa-3x text-secondary"></i>
                    </div>
                    <h5 class="card-title">إدارة المستخدمين</h5>
                    <p class="card-text text-muted">
                        إدارة حسابات المستخدمين والصلاحيات
                    </p>
                    <a href="@Url.Action("Index", "Employees")" class="btn btn-secondary">
                        <i class="fas fa-user-cog me-2"></i>
                        إدارة المستخدمين
                    </a>
                </div>
            </div>
        </div>

        <!-- Reports Settings -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-chart-bar fa-3x text-danger"></i>
                    </div>
                    <h5 class="card-title">إعدادات التقارير</h5>
                    <p class="card-text text-muted">
                        إدارة إعدادات التقارير والتحليلات
                    </p>
                    <a href="#" class="btn btn-danger disabled">
                        <i class="fas fa-chart-line me-2"></i>
                        قريباً
                    </a>
                </div>
            </div>
        </div>
    </div>

    @if (Model?.CompanySettings != null)
    {
        <!-- Quick Settings Overview -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            نظرة سريعة على الإعدادات الحالية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>اسم الشركة:</strong></td>
                                        <td>@Model.CompanySettings.CompanyNameAr</td>
                                    </tr>
                                    <tr>
                                        <td><strong>ساعات العمل:</strong></td>
                                        <td>@Model.CompanySettings.WorkingHoursStart.ToString(@"hh\:mm") - @Model.CompanySettings.WorkingHoursEnd.ToString(@"hh\:mm")</td>
                                    </tr>
                                    <tr>
                                        <td><strong>العملة:</strong></td>
                                        <td>@Model.CompanySettings.Currency (@Model.CompanySettings.CurrencySymbol)</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>اللغة الافتراضية:</strong></td>
                                        <td>@(Model.CompanySettings.DefaultLanguage == "ar" ? "العربية" : "English")</td>
                                    </tr>
                                    <tr>
                                        <td><strong>المنطقة الزمنية:</strong></td>
                                        <td>@Model.CompanySettings.TimeZone</td>
                                    </tr>
                                    <tr>
                                        <td><strong>تنسيق التاريخ:</strong></td>
                                        <td>@Model.CompanySettings.DateFormat</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Add any JavaScript functionality here
            console.log('Settings page loaded');
        });
    </script>
}
