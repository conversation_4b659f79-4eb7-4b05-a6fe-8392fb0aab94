namespace FitHRPlus.Application.Common
{
    /// <summary>
    /// Generic service result wrapper
    /// غلاف نتيجة الخدمة العامة
    /// </summary>
    /// <typeparam name="T">Result data type</typeparam>
    public class ServiceResult<T>
    {
        /// <summary>
        /// Indicates if the operation was successful
        /// يشير إلى ما إذا كانت العملية ناجحة
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// Result data (if successful)
        /// بيانات النتيجة (في حالة النجاح)
        /// </summary>
        public T? Data { get; set; }

        /// <summary>
        /// Error message (if failed)
        /// رسالة الخطأ (في حالة الفشل)
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Arabic error message (if failed)
        /// رسالة الخطأ بالعربية (في حالة الفشل)
        /// </summary>
        public string? ErrorMessageAr { get; set; }

        /// <summary>
        /// Error code for client handling
        /// رمز الخطأ للتعامل من جانب العميل
        /// </summary>
        public string? ErrorCode { get; set; }

        /// <summary>
        /// Validation errors (if any)
        /// أخطاء التحقق (إن وجدت)
        /// </summary>
        public Dictionary<string, List<string>>? ValidationErrors { get; set; }

        /// <summary>
        /// Additional metadata
        /// بيانات إضافية
        /// </summary>
        public Dictionary<string, object>? Metadata { get; set; }

        /// <summary>
        /// Create a successful result
        /// إنشاء نتيجة ناجحة
        /// </summary>
        /// <param name="data">Result data</param>
        /// <param name="metadata">Additional metadata</param>
        /// <returns>Successful service result</returns>
        public static ServiceResult<T> Success(T data, Dictionary<string, object>? metadata = null)
        {
            return new ServiceResult<T>
            {
                IsSuccess = true,
                Data = data,
                Metadata = metadata
            };
        }

        /// <summary>
        /// Create a failed result
        /// إنشاء نتيجة فاشلة
        /// </summary>
        /// <param name="errorMessage">Error message in English</param>
        /// <param name="errorMessageAr">Error message in Arabic</param>
        /// <param name="errorCode">Error code</param>
        /// <param name="validationErrors">Validation errors</param>
        /// <returns>Failed service result</returns>
        public static ServiceResult<T> Failure(
            string errorMessage,
            string? errorMessageAr = null,
            string? errorCode = null,
            Dictionary<string, List<string>>? validationErrors = null)
        {
            return new ServiceResult<T>
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                ErrorMessageAr = errorMessageAr,
                ErrorCode = errorCode,
                ValidationErrors = validationErrors
            };
        }

        /// <summary>
        /// Create a validation error result
        /// إنشاء نتيجة خطأ في التحقق
        /// </summary>
        /// <param name="validationErrors">Validation errors</param>
        /// <returns>Validation error service result</returns>
        public static ServiceResult<T> ValidationError(Dictionary<string, List<string>> validationErrors)
        {
            return new ServiceResult<T>
            {
                IsSuccess = false,
                ErrorMessage = "Validation failed",
                ErrorMessageAr = "فشل في التحقق من صحة البيانات",
                ErrorCode = "VALIDATION_ERROR",
                ValidationErrors = validationErrors
            };
        }
    }

    /// <summary>
    /// Non-generic service result for operations that don't return data
    /// نتيجة خدمة غير عامة للعمليات التي لا ترجع بيانات
    /// </summary>
    public class ServiceResult : ServiceResult<object>
    {
        /// <summary>
        /// Create a successful result without data
        /// إنشاء نتيجة ناجحة بدون بيانات
        /// </summary>
        /// <param name="metadata">Additional metadata</param>
        /// <returns>Successful service result</returns>
        public static ServiceResult Success(Dictionary<string, object>? metadata = null)
        {
            return new ServiceResult
            {
                IsSuccess = true,
                Metadata = metadata
            };
        }

        /// <summary>
        /// Create a failed result without data
        /// إنشاء نتيجة فاشلة بدون بيانات
        /// </summary>
        /// <param name="errorMessage">Error message in English</param>
        /// <param name="errorMessageAr">Error message in Arabic</param>
        /// <param name="errorCode">Error code</param>
        /// <param name="validationErrors">Validation errors</param>
        /// <returns>Failed service result</returns>
        public new static ServiceResult Failure(
            string errorMessage,
            string? errorMessageAr = null,
            string? errorCode = null,
            Dictionary<string, List<string>>? validationErrors = null)
        {
            return new ServiceResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                ErrorMessageAr = errorMessageAr,
                ErrorCode = errorCode,
                ValidationErrors = validationErrors
            };
        }
    }
}
