namespace FitHRPlus.Web.Models.Dashboard
{
    /// <summary>
    /// Dashboard view model
    /// نموذج عرض لوحة التحكم
    /// </summary>
    public class DashboardViewModel
    {
        public Guid CompanyId { get; set; }

        // Basic Statistics
        public int TotalEmployees { get; set; }
        public int ActiveEmployees { get; set; }
        public int InactiveEmployees { get; set; }
        public int TotalDepartments { get; set; }
        public int TodayAttendance { get; set; }
        public int PendingLeaveRequests { get; set; }
        public int PendingPayrolls { get; set; }
        public decimal TotalSalaries { get; set; }

        // Chart Data
        public Dictionary<string, int> EmployeesByDepartment { get; set; } = new();
        public Dictionary<string, decimal> AttendanceByMonth { get; set; } = new();
        public Dictionary<string, decimal> SalariesByDepartment { get; set; } = new();
        public Dictionary<string, int> LeavesByType { get; set; } = new();

        // Recent Activities
        public List<DashboardActivityViewModel> RecentActivities { get; set; } = new();

        // Quick Stats
        public List<DashboardQuickStatViewModel> QuickStats { get; set; } = new();

        // Alerts and Notifications
        public List<DashboardAlertViewModel> Alerts { get; set; } = new();

        // Upcoming Events
        public List<DashboardEventViewModel> UpcomingEvents { get; set; } = new();

        // Performance Metrics
        public DashboardPerformanceViewModel Performance { get; set; } = new();

        // Calculated Properties
        public decimal AttendanceRate => TotalEmployees > 0 ? (decimal)TodayAttendance / TotalEmployees * 100 : 0;
        public decimal ActiveEmployeePercentage => TotalEmployees > 0 ? (decimal)ActiveEmployees / TotalEmployees * 100 : 0;
        public string AttendanceRateDisplay => $"{AttendanceRate:F1}%";
        public string ActiveEmployeePercentageDisplay => $"{ActiveEmployeePercentage:F1}%";
    }

    /// <summary>
    /// Dashboard activity view model
    /// نموذج عرض نشاط لوحة التحكم
    /// </summary>
    public class DashboardActivityViewModel
    {
        public string Type { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string DescriptionAr { get; set; } = string.Empty;
        public DateTime Time { get; set; }
        public string Icon { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public string? Url { get; set; }
        public string? UserName { get; set; }
        public string? UserAvatar { get; set; }

        // Calculated Properties
        public string TimeAgo => GetTimeAgo(Time);

        private static string GetTimeAgo(DateTime dateTime)
        {
            var timeSpan = DateTime.Now - dateTime;
            
            if (timeSpan.TotalMinutes < 1)
                return "Just now / الآن";
            if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes}m ago / منذ {(int)timeSpan.TotalMinutes} دقيقة";
            if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours}h ago / منذ {(int)timeSpan.TotalHours} ساعة";
            if (timeSpan.TotalDays < 7)
                return $"{(int)timeSpan.TotalDays}d ago / منذ {(int)timeSpan.TotalDays} يوم";
            
            return dateTime.ToString("MMM dd, yyyy");
        }
    }

    /// <summary>
    /// Dashboard quick stat view model
    /// نموذج عرض إحصائية سريعة لوحة التحكم
    /// </summary>
    public class DashboardQuickStatViewModel
    {
        public string Title { get; set; } = string.Empty;
        public string TitleAr { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public string? Change { get; set; }
        public string? ChangeType { get; set; } // positive, negative, neutral
        public string? Url { get; set; }
        public string? Description { get; set; }
        public string? DescriptionAr { get; set; }

        // Calculated Properties
        public string ChangeClass => ChangeType switch
        {
            "positive" => "text-success",
            "negative" => "text-danger",
            _ => "text-muted"
        };

        public string ChangeIcon => ChangeType switch
        {
            "positive" => "fas fa-arrow-up",
            "negative" => "fas fa-arrow-down",
            _ => "fas fa-minus"
        };
    }

    /// <summary>
    /// Dashboard alert view model
    /// نموذج عرض تنبيه لوحة التحكم
    /// </summary>
    public class DashboardAlertViewModel
    {
        public string Type { get; set; } = string.Empty; // success, warning, danger, info
        public string Title { get; set; } = string.Empty;
        public string TitleAr { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string MessageAr { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string? ActionUrl { get; set; }
        public string? ActionText { get; set; }
        public string? ActionTextAr { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsDismissible { get; set; } = true;

        // Calculated Properties
        public string AlertClass => Type switch
        {
            "success" => "alert-success",
            "warning" => "alert-warning",
            "danger" => "alert-danger",
            "info" => "alert-info",
            _ => "alert-secondary"
        };
    }

    /// <summary>
    /// Dashboard event view model
    /// نموذج عرض حدث لوحة التحكم
    /// </summary>
    public class DashboardEventViewModel
    {
        public string Title { get; set; } = string.Empty;
        public string TitleAr { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string DescriptionAr { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public string Type { get; set; } = string.Empty; // meeting, holiday, deadline, training
        public string Icon { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public string? Location { get; set; }
        public string? Url { get; set; }
        public List<string> Attendees { get; set; } = new();

        // Calculated Properties
        public string DateDisplay => Date.ToString("MMM dd, yyyy");
        public string TimeDisplay => Date.ToString("HH:mm");
        public bool IsToday => Date.Date == DateTime.Today;
        public bool IsTomorrow => Date.Date == DateTime.Today.AddDays(1);
        public int DaysUntil => (Date.Date - DateTime.Today).Days;
    }

    /// <summary>
    /// Dashboard performance view model
    /// نموذج عرض أداء لوحة التحكم
    /// </summary>
    public class DashboardPerformanceViewModel
    {
        public decimal AttendanceRate { get; set; }
        public decimal ProductivityScore { get; set; }
        public decimal EmployeeSatisfaction { get; set; }
        public decimal TurnoverRate { get; set; }
        public decimal LeaveUtilization { get; set; }
        public decimal OvertimeRate { get; set; }

        // Trend data (last 6 months)
        public List<decimal> AttendanceTrend { get; set; } = new();
        public List<decimal> ProductivityTrend { get; set; } = new();
        public List<decimal> SatisfactionTrend { get; set; } = new();

        // Calculated Properties
        public string AttendanceRateDisplay => $"{AttendanceRate:F1}%";
        public string ProductivityScoreDisplay => $"{ProductivityScore:F1}/10";
        public string EmployeeSatisfactionDisplay => $"{EmployeeSatisfaction:F1}/5";
        public string TurnoverRateDisplay => $"{TurnoverRate:F1}%";
        public string LeaveUtilizationDisplay => $"{LeaveUtilization:F1}%";
        public string OvertimeRateDisplay => $"{OvertimeRate:F1}%";

        public string AttendanceRateClass => AttendanceRate >= 95 ? "text-success" : AttendanceRate >= 90 ? "text-warning" : "text-danger";
        public string ProductivityScoreClass => ProductivityScore >= 8 ? "text-success" : ProductivityScore >= 6 ? "text-warning" : "text-danger";
        public string EmployeeSatisfactionClass => EmployeeSatisfaction >= 4 ? "text-success" : EmployeeSatisfaction >= 3 ? "text-warning" : "text-danger";
        public string TurnoverRateClass => TurnoverRate <= 5 ? "text-success" : TurnoverRate <= 10 ? "text-warning" : "text-danger";
    }

    /// <summary>
    /// Dashboard widget view model
    /// نموذج عرض ودجت لوحة التحكم
    /// </summary>
    public class DashboardWidgetViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string TitleAr { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // chart, stat, list, calendar
        public string Size { get; set; } = "col-md-6"; // Bootstrap column classes
        public int Order { get; set; }
        public bool IsVisible { get; set; } = true;
        public bool IsCollapsible { get; set; } = true;
        public bool IsCollapsed { get; set; } = false;
        public Dictionary<string, object> Data { get; set; } = new();
        public Dictionary<string, string> Settings { get; set; } = new();
    }

    /// <summary>
    /// Dashboard summary view model
    /// نموذج عرض ملخص لوحة التحكم
    /// </summary>
    public class DashboardSummaryViewModel
    {
        public DateTime Date { get; set; } = DateTime.Today;
        public string Period { get; set; } = "Today"; // Today, This Week, This Month, This Year

        // Employee Summary
        public int TotalEmployees { get; set; }
        public int PresentToday { get; set; }
        public int AbsentToday { get; set; }
        public int OnLeave { get; set; }
        public int LateArrivals { get; set; }

        // Financial Summary
        public decimal TotalSalaries { get; set; }
        public decimal TotalAllowances { get; set; }
        public decimal TotalDeductions { get; set; }
        public decimal NetPayroll { get; set; }

        // Leave Summary
        public int PendingLeaveRequests { get; set; }
        public int ApprovedLeaves { get; set; }
        public int RejectedLeaves { get; set; }
        public decimal LeaveUtilizationRate { get; set; }

        // Performance Summary
        public decimal OverallAttendanceRate { get; set; }
        public decimal AverageWorkingHours { get; set; }
        public decimal OvertimeHours { get; set; }
        public decimal ProductivityIndex { get; set; }

        // Calculated Properties
        public decimal PresentPercentage => TotalEmployees > 0 ? (decimal)PresentToday / TotalEmployees * 100 : 0;
        public decimal AbsentPercentage => TotalEmployees > 0 ? (decimal)AbsentToday / TotalEmployees * 100 : 0;
        public decimal OnLeavePercentage => TotalEmployees > 0 ? (decimal)OnLeave / TotalEmployees * 100 : 0;
    }
}
