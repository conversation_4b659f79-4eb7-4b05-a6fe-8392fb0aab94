using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Web.Models.Employees
{
    public class EmployeeListViewModel
    {
        public List<EmployeeListItemViewModel> Employees { get; set; } = new();
        public int CurrentPage { get; set; } = 1;
        public int TotalPages { get; set; } = 1;
        public int TotalCount { get; set; } = 0;
        public string? SearchTerm { get; set; }
        public string? DepartmentFilter { get; set; }
        public string? PositionFilter { get; set; }

        // Additional filter properties
        public Guid? DepartmentId { get; set; }
        public Guid? PositionId { get; set; }
        public string? EmploymentStatus { get; set; }
        public string? EmploymentType { get; set; }
        public string? Gender { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? HireDateFrom { get; set; }
        public DateTime? HireDateTo { get; set; }
        public int PageSize { get; set; } = 10;
    }

    public class EmployeeListItemViewModel
    {
        public Guid Id { get; set; }
        public string FullName { get; set; } = string.Empty;
        public string EmployeeNumber { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string PositionName { get; set; } = string.Empty;
        public string PositionTitle { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public DateTime HireDate { get; set; }
        public bool IsActive { get; set; }
        public string? ProfilePicture { get; set; }
    }

    public class EmployeeViewModel
    {
        public Guid Id { get; set; }
        public Guid CompanyId { get; set; }

        [Required(ErrorMessage = "الاسم الأول مطلوب")]
        [Display(Name = "الاسم الأول")]
        public string FirstName { get; set; } = string.Empty;

        [Display(Name = "الاسم الأوسط")]
        public string? MiddleName { get; set; }

        [Required(ErrorMessage = "الاسم الأخير مطلوب")]
        [Display(Name = "الاسم الأخير")]
        public string LastName { get; set; } = string.Empty;

        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [Display(Name = "البريد الإلكتروني")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "رقم الهاتف مطلوب")]
        [Display(Name = "رقم الهاتف")]
        public string PhoneNumber { get; set; } = string.Empty;

        [Display(Name = "تاريخ الميلاد")]
        public DateTime? DateOfBirth { get; set; }

        [Display(Name = "الجنس")]
        public string? Gender { get; set; }

        [Display(Name = "رقم الهوية الوطنية")]
        public string? NationalId { get; set; }

        [Required(ErrorMessage = "رقم الموظف مطلوب")]
        [Display(Name = "رقم الموظف")]
        public string EmployeeNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "القسم مطلوب")]
        [Display(Name = "القسم")]
        public Guid DepartmentId { get; set; }

        [Required(ErrorMessage = "المنصب مطلوب")]
        [Display(Name = "المنصب")]
        public Guid? PositionId { get; set; }

        [Required(ErrorMessage = "تاريخ التوظيف مطلوب")]
        [Display(Name = "تاريخ التوظيف")]
        public DateTime HireDate { get; set; }

        [Required(ErrorMessage = "الراتب الأساسي مطلوب")]
        [Display(Name = "الراتب الأساسي")]
        [Range(0, double.MaxValue, ErrorMessage = "الراتب يجب أن يكون أكبر من صفر")]
        public decimal Salary { get; set; }

        [Display(Name = "نوع التوظيف")]
        public string? EmploymentType { get; set; }

        [Display(Name = "العنوان")]
        public string? Address { get; set; }

        [Display(Name = "الصورة الشخصية")]
        public string? ProfilePicture { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        // Navigation properties for display
        public string DepartmentName { get; set; } = string.Empty;
        public string PositionName { get; set; } = string.Empty;
        public string FullName => $"{FirstName} {MiddleName} {LastName}".Trim();

        // Additional properties for compatibility
        public string FirstNameAr { get; set; } = string.Empty;
        public string LastNameAr { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string PositionTitle { get; set; } = string.Empty;
        public string ManagerName { get; set; } = string.Empty;
        public string EmploymentStatus { get; set; } = string.Empty;
        public int Age => DateOfBirth.HasValue ? DateTime.Today.Year - DateOfBirth.Value.Year : 0;
        public int YearsOfService => DateTime.Today.Year - HireDate.Year;
    }

    public class EmployeeDetailsViewModel : EmployeeViewModel
    {
        public int WorkingDays { get; set; }
        public decimal AttendanceRate { get; set; }
        public int RemainingLeaves { get; set; }
        public int OvertimeHours { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
