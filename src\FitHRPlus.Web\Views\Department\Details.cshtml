@model FitHRPlus.Web.Models.Department.DepartmentViewModel
@{
    ViewData["Title"] = "Department Details / تفاصيل القسم";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-building text-info me-2"></i>
                        Department Details / تفاصيل القسم
                    </h2>
                    <p class="text-muted mb-0">View department information / عرض معلومات القسم</p>
                </div>
                <div>
                    <a href="@Url.Action("Index")" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to List / العودة للقائمة
                    </a>
                    <a href="@Url.Action("Edit", new { id = Model.Id })" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i>
                        Edit / تعديل
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Information -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Department Information / معلومات القسم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Department Name / اسم القسم:</label>
                            <p class="form-control-plaintext">@Model.Name</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Department Name (Arabic) / اسم القسم بالعربية:</label>
                            <p class="form-control-plaintext" dir="rtl">@Model.NameAr</p>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Description) || !string.IsNullOrEmpty(Model.DescriptionAr))
                    {
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Description / الوصف:</label>
                                <p class="form-control-plaintext">
                                    @(Model.Description ?? "-")
                                </p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Description (Arabic) / الوصف بالعربية:</label>
                                <p class="form-control-plaintext" dir="rtl">
                                    @(Model.DescriptionAr ?? "-")
                                </p>
                            </div>
                        </div>
                    }

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Budget / الميزانية:</label>
                            <p class="form-control-plaintext">
                                @if (Model.Budget.HasValue)
                                {
                                    <span class="badge bg-info fs-6">@Model.Budget.Value.ToString("C")</span>
                                }
                                else
                                {
                                    <span class="text-muted">Not specified / غير محدد</span>
                                }
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Status / الحالة:</label>
                            <p class="form-control-plaintext">
                                @if (Model.IsActive)
                                {
                                    <span class="badge bg-success fs-6">Active / نشط</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger fs-6">Inactive / غير نشط</span>
                                }
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Created At / تاريخ الإنشاء:</label>
                            <p class="form-control-plaintext">
                                <i class="fas fa-calendar me-1"></i>
                                @Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Last Updated / آخر تحديث:</label>
                            <p class="form-control-plaintext">
                                <i class="fas fa-clock me-1"></i>
                                @Model.UpdatedAt.ToString("dd/MM/yyyy HH:mm")
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Card -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Statistics / الإحصائيات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="display-4 text-primary">@Model.EmployeeCount</div>
                        <p class="text-muted mb-0">Total Employees</p>
                        <small class="text-muted">إجمالي الموظفين</small>
                    </div>

                    @if (Model.Budget.HasValue)
                    {
                        <div class="text-center mb-4">
                            <div class="h4 text-info">@Model.Budget.Value.ToString("C")</div>
                            <p class="text-muted mb-0">Allocated Budget</p>
                            <small class="text-muted">الميزانية المخصصة</small>
                        </div>
                    }

                    <div class="d-grid gap-2">
                        <a href="@Url.Action("Index", "Employees", new { departmentId = Model.Id })" 
                           class="btn btn-outline-primary">
                            <i class="fas fa-users me-1"></i>
                            View Employees / عرض الموظفين
                        </a>
                        <a href="@Url.Action("Index", "Position", new { departmentId = Model.Id })" 
                           class="btn btn-outline-secondary">
                            <i class="fas fa-briefcase me-1"></i>
                            View Positions / عرض المناصب
                        </a>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions / إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="@Url.Action("Create", "Position", new { departmentId = Model.Id })" 
                           class="btn btn-success">
                            <i class="fas fa-plus me-1"></i>
                            Add Position / إضافة منصب
                        </a>
                        <a href="@Url.Action("Create", "Employees", new { departmentId = Model.Id })" 
                           class="btn btn-primary">
                            <i class="fas fa-user-plus me-1"></i>
                            Add Employee / إضافة موظف
                        </a>
                        <button type="button" class="btn btn-outline-danger" 
                                onclick="confirmDelete('@Model.Id', '@Model.Name')">
                            <i class="fas fa-trash me-1"></i>
                            Delete Department / حذف القسم
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity (if needed) -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        Recent Activity / النشاط الأخير
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center py-4">
                        <i class="fas fa-clock fa-2x text-muted mb-3"></i>
                        <p class="text-muted">No recent activity to display</p>
                        <small class="text-muted">لا يوجد نشاط حديث للعرض</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete / تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this department?</p>
                <p>هل أنت متأكد من حذف هذا القسم؟</p>
                <p><strong id="departmentName"></strong></p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    This action cannot be undone and may affect employees assigned to this department.
                    <br>
                    هذا الإجراء لا يمكن التراجع عنه وقد يؤثر على الموظفين المعينين في هذا القسم.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel / إلغاء</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">Delete / حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(id, name) {
            document.getElementById('departmentName').textContent = name;
            document.getElementById('deleteForm').action = '@Url.Action("Delete")/' + id;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
}
