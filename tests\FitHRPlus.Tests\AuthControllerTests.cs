using FitHRPlus.Application.DTOs.Auth;
using FitHRPlus.Application.Interfaces;
using FitHRPlus.Application.Common;
using FitHRPlus.Web.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace FitHRPlus.Tests
{
    /// <summary>
    /// Unit tests for AuthController
    /// اختبارات الوحدة لوحدة تحكم المصادقة
    /// </summary>
    public class AuthControllerTests
    {
        private readonly Mock<IAuthService> _mockAuthService;
        private readonly Mock<ILogger<AuthController>> _mockLogger;
        private readonly AuthController _controller;

        public AuthControllerTests()
        {
            _mockAuthService = new Mock<IAuthService>();
            _mockLogger = new Mock<ILogger<AuthController>>();
            _controller = new AuthController(_mockAuthService.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task Register_ValidRequest_ReturnsOkResult()
        {
            // Arrange
            var request = new RegisterRequestDto
            {
                Username = "testuser",
                Email = "<EMAIL>",
                Password = "Test123!@#",
                ConfirmPassword = "Test123!@#",
                FirstName = "Test",
                LastName = "User",
                AcceptTerms = true
            };

            var expectedResponse = new LoginResponseDto
            {
                AccessToken = "test-token",
                RefreshToken = "test-refresh-token",
                ExpiresAt = DateTime.UtcNow.AddHours(1),
                User = new UserInfoDto
                {
                    Id = Guid.NewGuid(),
                    Username = "testuser",
                    Email = "<EMAIL>",
                    FirstName = "Test",
                    LastName = "User"
                }
            };

            _mockAuthService.Setup(x => x.RegisterAsync(It.IsAny<RegisterRequestDto>()))
                .ReturnsAsync(ServiceResult<LoginResponseDto>.Success(expectedResponse));

            // Act
            var result = await _controller.Register(request);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(200, okResult.StatusCode);
            
            // Verify the service was called
            _mockAuthService.Verify(x => x.RegisterAsync(It.IsAny<RegisterRequestDto>()), Times.Once);
        }

        [Fact]
        public async Task Register_InvalidRequest_ReturnsBadRequest()
        {
            // Arrange
            var request = new RegisterRequestDto
            {
                Username = "testuser",
                Email = "<EMAIL>",
                Password = "Test123!@#",
                ConfirmPassword = "Test123!@#",
                FirstName = "Test",
                LastName = "User",
                AcceptTerms = true
            };

            var validationErrors = new Dictionary<string, List<string>>
            {
                { "Username", new List<string> { "Username is already taken" } }
            };

            _mockAuthService.Setup(x => x.RegisterAsync(It.IsAny<RegisterRequestDto>()))
                .ReturnsAsync(ServiceResult<LoginResponseDto>.ValidationError(validationErrors));

            // Act
            var result = await _controller.Register(request);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal(400, badRequestResult.StatusCode);
        }

        [Fact]
        public async Task Login_ValidCredentials_ReturnsOkResult()
        {
            // Arrange
            var request = new LoginRequestDto
            {
                UsernameOrEmail = "testuser",
                Password = "Test123!@#"
            };

            var expectedResponse = new LoginResponseDto
            {
                AccessToken = "test-token",
                RefreshToken = "test-refresh-token",
                ExpiresAt = DateTime.UtcNow.AddHours(1),
                User = new UserInfoDto
                {
                    Id = Guid.NewGuid(),
                    Username = "testuser",
                    Email = "<EMAIL>"
                }
            };

            _mockAuthService.Setup(x => x.LoginAsync(It.IsAny<LoginRequestDto>()))
                .ReturnsAsync(ServiceResult<LoginResponseDto>.Success(expectedResponse));

            // Act
            var result = await _controller.Login(request);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact]
        public async Task Login_InvalidCredentials_ReturnsBadRequest()
        {
            // Arrange
            var request = new LoginRequestDto
            {
                UsernameOrEmail = "testuser",
                Password = "wrongpassword"
            };

            _mockAuthService.Setup(x => x.LoginAsync(It.IsAny<LoginRequestDto>()))
                .ReturnsAsync(ServiceResult<LoginResponseDto>.Failure(
                    "Invalid username/email or password",
                    "اسم المستخدم/البريد الإلكتروني أو كلمة المرور غير صحيحة",
                    "INVALID_CREDENTIALS"));

            // Act
            var result = await _controller.Login(request);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal(400, badRequestResult.StatusCode);
        }
    }
}
