using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using FitHRPlus.Application.Interfaces;
using FitHRPlus.Application.DTOs.Employees;
using FitHRPlus.Web.Models.Position;
using FitHRPlus.Web.Models.Department;
using System.Security.Claims;

namespace FitHRPlus.Web.Controllers
{
    /// <summary>
    /// Position management controller
    /// كونترولر إدارة المناصب
    /// </summary>
    [Authorize]
    [Route("[controller]")]
    public class PositionController : Controller
    {
        private readonly IPositionService _positionService;
        private readonly IDepartmentService _departmentService;
        private readonly ILogger<PositionController> _logger;

        public PositionController(
            IPositionService positionService,
            IDepartmentService departmentService,
            ILogger<PositionController> logger)
        {
            _positionService = positionService;
            _departmentService = departmentService;
            _logger = logger;
        }

        /// <summary>
        /// Position list page
        /// صفحة قائمة المناصب
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Index([FromQuery] PositionListRequestDto request)
        {
            try
            {
                // Get current user's company
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (Guid.TryParse(companyIdClaim, out var companyId))
                {
                    request.CompanyId = companyId;
                }

                var result = await _positionService.GetPositionsAsync(request);

                if (result.IsSuccess)
                {
                    var viewModel = new PositionListViewModel
                    {
                        Positions = result.Data?.Items?.Select(MapToPositionViewModel).ToList() ?? new List<PositionViewModel>(),
                        TotalCount = result.Data?.TotalCount ?? 0,
                        PageNumber = request.PageNumber,
                        PageSize = request.PageSize,
                        SearchTerm = request.SearchTerm
                    };

                    return View(viewModel);
                }

                TempData["ErrorMessage"] = result.ErrorMessage;
                return View(new PositionListViewModel());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading positions");
                TempData["ErrorMessage"] = "An error occurred while loading positions";
                return View(new PositionListViewModel());
            }
        }

        /// <summary>
        /// Position details page
        /// صفحة تفاصيل المنصب
        /// </summary>
        [HttpGet("{id}")]
        public async Task<IActionResult> Details(Guid id)
        {
            try
            {
                var result = await _positionService.GetPositionByIdAsync(id);

                if (result.IsSuccess)
                {
                    var viewModel = MapToPositionViewModel(result.Data!);
                    return View(viewModel);
                }

                TempData["ErrorMessage"] = result.ErrorMessage;
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading position details for ID: {PositionId}", id);
                TempData["ErrorMessage"] = "An error occurred while loading position details";
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// Create position page (GET)
        /// صفحة إنشاء منصب (GET)
        /// </summary>
        [HttpGet("Create")]
        public async Task<IActionResult> Create()
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "Company information not found";
                    return RedirectToAction(nameof(Index));
                }

                var viewModel = new PositionViewModel
                {
                    CompanyId = companyId,
                    IsActive = true
                };

                await LoadDepartmentsAsync(viewModel, companyId);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading create position page");
                TempData["ErrorMessage"] = "An error occurred while loading the page";
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// Create position (POST)
        /// إنشاء منصب (POST)
        /// </summary>
        [HttpPost("Create")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(PositionViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                    if (Guid.TryParse(companyIdClaim, out var companyId))
                    {
                        await LoadDepartmentsAsync(model, companyId);
                    }
                    return View(model);
                }

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    TempData["ErrorMessage"] = "User information not found";
                    return View(model);
                }

                var createDto = new CreatePositionDto
                {
                    CompanyId = model.CompanyId,
                    DepartmentId = model.DepartmentId,
                    Title = model.Title,
                    TitleAr = model.TitleAr,
                    Description = model.Description,
                    DescriptionAr = model.DescriptionAr,
                    MinSalary = model.MinSalary,
                    MaxSalary = model.MaxSalary,
                    CreatedBy = userId
                };

                var result = await _positionService.CreatePositionAsync(createDto);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = "Position created successfully";
                    return RedirectToAction(nameof(Index));
                }

                TempData["ErrorMessage"] = result.ErrorMessage;
                var companyIdClaim2 = User.FindFirst("CompanyId")?.Value;
                if (Guid.TryParse(companyIdClaim2, out var companyId2))
                {
                    await LoadDepartmentsAsync(model, companyId2);
                }
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating position");
                TempData["ErrorMessage"] = "An error occurred while creating the position";
                return View(model);
            }
        }

        /// <summary>
        /// Edit position page (GET)
        /// صفحة تعديل منصب (GET)
        /// </summary>
        [HttpGet("Edit/{id}")]
        public async Task<IActionResult> Edit(Guid id)
        {
            try
            {
                var result = await _positionService.GetPositionByIdAsync(id);

                if (result.IsSuccess)
                {
                    var viewModel = MapToPositionViewModel(result.Data!);
                    await LoadDepartmentsAsync(viewModel, viewModel.CompanyId);
                    return View(viewModel);
                }

                TempData["ErrorMessage"] = result.ErrorMessage;
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading position for edit. ID: {PositionId}", id);
                TempData["ErrorMessage"] = "An error occurred while loading position details";
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// Edit position (POST)
        /// تعديل منصب (POST)
        /// </summary>
        [HttpPost("Edit/{id}")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(Guid id, PositionViewModel model)
        {
            try
            {
                if (id != model.Id)
                {
                    TempData["ErrorMessage"] = "Invalid position ID";
                    return RedirectToAction(nameof(Index));
                }

                if (!ModelState.IsValid)
                {
                    await LoadDepartmentsAsync(model, model.CompanyId);
                    return View(model);
                }

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    TempData["ErrorMessage"] = "User information not found";
                    return View(model);
                }

                var updateDto = new UpdatePositionDto
                {
                    Id = model.Id,
                    DepartmentId = model.DepartmentId,
                    Title = model.Title,
                    TitleAr = model.TitleAr,
                    Description = model.Description,
                    DescriptionAr = model.DescriptionAr,
                    MinSalary = model.MinSalary,
                    MaxSalary = model.MaxSalary,
                    IsActive = model.IsActive,
                    UpdatedBy = userId
                };

                var result = await _positionService.UpdatePositionAsync(updateDto);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = "Position updated successfully";
                    return RedirectToAction(nameof(Index));
                }

                TempData["ErrorMessage"] = result.ErrorMessage;
                await LoadDepartmentsAsync(model, model.CompanyId);
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating position. ID: {PositionId}", id);
                TempData["ErrorMessage"] = "An error occurred while updating the position";
                return View(model);
            }
        }

        /// <summary>
        /// Delete position
        /// حذف منصب
        /// </summary>
        [HttpPost("Delete/{id}")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    TempData["ErrorMessage"] = "User information not found";
                    return RedirectToAction(nameof(Index));
                }

                var result = await _positionService.DeletePositionAsync(id, userId);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = "Position deleted successfully";
                }
                else
                {
                    TempData["ErrorMessage"] = result.ErrorMessage;
                }

                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting position. ID: {PositionId}", id);
                TempData["ErrorMessage"] = "An error occurred while deleting the position";
                return RedirectToAction(nameof(Index));
            }
        }

        #region Private Methods

        private static PositionViewModel MapToPositionViewModel(PositionDto dto)
        {
            return new PositionViewModel
            {
                Id = dto.Id,
                CompanyId = dto.CompanyId,
                DepartmentId = dto.DepartmentId,
                DepartmentName = dto.DepartmentName,
                Title = dto.Title,
                TitleAr = dto.TitleAr,
                Description = dto.Description,
                DescriptionAr = dto.DescriptionAr,
                MinSalary = dto.MinSalary,
                MaxSalary = dto.MaxSalary,
                EmployeeCount = dto.EmployeeCount,
                IsActive = dto.IsActive,
                CreatedAt = dto.CreatedAt,
                UpdatedAt = dto.UpdatedAt
            };
        }

        private async Task LoadDepartmentsAsync(PositionViewModel viewModel, Guid companyId)
        {
            try
            {
                var departmentsRequest = new DepartmentListRequestDto
                {
                    CompanyId = companyId,
                    PageSize = 1000,
                    IsActive = true
                };

                var departmentsResult = await _departmentService.GetDepartmentsAsync(departmentsRequest);
                if (departmentsResult.IsSuccess && departmentsResult.Data?.Items != null)
                {
                    viewModel.Departments = departmentsResult.Data.Items
                        .Select(d => new DepartmentOptionViewModel
                        {
                            Id = d.Id,
                            Name = d.Name,
                            NameAr = d.NameAr
                        }).ToList();
                }
                else
                {
                    viewModel.Departments = new List<DepartmentOptionViewModel>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading departments for position form");
                viewModel.Departments = new List<DepartmentOptionViewModel>();
            }
        }

        #endregion
    }
}
