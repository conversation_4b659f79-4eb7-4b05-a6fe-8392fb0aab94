using FitHRPlus.Domain.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Domain.Entities
{
    /// <summary>
    /// Position entity representing job positions within departments
    /// كيان المنصب الذي يمثل المناصب الوظيفية داخل الأقسام
    /// </summary>
    public class Position : BaseEntity
    {
        /// <summary>
        /// Company ID that owns this position
        /// معرف الشركة التي تملك هذا المنصب
        /// </summary>
        [Required]
        public Guid CompanyId { get; set; }

        /// <summary>
        /// Department ID where this position belongs
        /// معرف القسم الذي ينتمي إليه هذا المنصب
        /// </summary>
        [Required]
        public Guid DepartmentId { get; set; }

        /// <summary>
        /// Position title in English
        /// مسمى المنصب بالإنجليزية
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Position title in Arabic
        /// مسمى المنصب بالعربية
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string TitleAr { get; set; } = string.Empty;

        /// <summary>
        /// Position description in English
        /// وصف المنصب بالإنجليزية
        /// </summary>
        [MaxLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// Position description in Arabic
        /// وصف المنصب بالعربية
        /// </summary>
        [MaxLength(1000)]
        public string? DescriptionAr { get; set; }

        /// <summary>
        /// Position level (Entry, Junior, Senior, Manager, Director)
        /// مستوى المنصب
        /// </summary>
        [MaxLength(50)]
        public string? Level { get; set; }

        /// <summary>
        /// Minimum salary for this position
        /// الحد الأدنى للراتب لهذا المنصب
        /// </summary>
        public decimal? MinSalary { get; set; }

        /// <summary>
        /// Maximum salary for this position
        /// الحد الأقصى للراتب لهذا المنصب
        /// </summary>
        public decimal? MaxSalary { get; set; }

        /// <summary>
        /// Required skills as JSON array
        /// المهارات المطلوبة كمصفوفة JSON
        /// </summary>
        public string? RequiredSkills { get; set; }

        /// <summary>
        /// Job responsibilities as JSON array
        /// المسؤوليات الوظيفية كمصفوفة JSON
        /// </summary>
        public string? Responsibilities { get; set; }

        // Navigation Properties - خصائص التنقل

        /// <summary>
        /// Company that owns this position
        /// الشركة التي تملك هذا المنصب
        /// </summary>
        public virtual Company Company { get; set; } = null!;

        /// <summary>
        /// Department where this position belongs
        /// القسم الذي ينتمي إليه هذا المنصب
        /// </summary>
        public virtual Department Department { get; set; } = null!;

        /// <summary>
        /// Employees holding this position
        /// الموظفون الذين يشغلون هذا المنصب
        /// </summary>
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
    }
}