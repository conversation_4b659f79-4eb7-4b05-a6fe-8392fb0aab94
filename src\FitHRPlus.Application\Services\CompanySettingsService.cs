using FitHRPlus.Application.DTOs.CompanySettings;
using FitHRPlus.Application.Interfaces;
using FitHRPlus.Application.Common;
using FitHRPlus.Domain.Entities;
using FitHRPlus.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FitHRPlus.Application.Services
{
    /// <summary>
    /// Company settings service implementation
    /// تطبيق خدمة إعدادات الشركة
    /// </summary>
    public class CompanySettingsService : ICompanySettingsService
    {
        private readonly FitHRContext _context;
        private readonly ILogger<CompanySettingsService> _logger;

        public CompanySettingsService(FitHRContext context, ILogger<CompanySettingsService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<CompanySettingsDto?> GetCompanySettingsAsync(Guid companyId)
        {
            try
            {
                var settings = await _context.CompanySettings
                    .FirstOrDefaultAsync(cs => cs.CompanyId == companyId);

                if (settings == null)
                {
                    // Initialize default settings if not found
                    return await InitializeDefaultSettingsAsync(companyId);
                }

                return MapToDto(settings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting company settings for company {CompanyId}", companyId);
                throw;
            }
        }

        public async Task<CompanySettingsDto> CreateOrUpdateCompanySettingsAsync(Guid companyId, CreateUpdateCompanySettingsDto dto)
        {
            try
            {
                var existingSettings = await _context.CompanySettings
                    .FirstOrDefaultAsync(cs => cs.CompanyId == companyId);

                if (existingSettings == null)
                {
                    // Create new settings
                    var newSettings = new CompanySettings
                    {
                        Id = Guid.NewGuid(),
                        CompanyId = companyId,
                        WorkingHoursStart = dto.WorkingHoursStart,
                        WorkingHoursEnd = dto.WorkingHoursEnd,
                        WorkingHoursPerDay = dto.WorkingHoursPerDay,
                        WorkingDaysPerWeek = dto.WorkingDaysPerWeek,
                        BreakTimeStart = dto.BreakTimeStart,
                        BreakTimeEnd = dto.BreakTimeEnd,
                        BreakTimeMinutes = dto.BreakTimeMinutes,
                        WeekendDays = dto.WeekendDays,
                        LateGracePeriodMinutes = dto.LateGracePeriodMinutes,
                        EarlyLeaveGracePeriodMinutes = dto.EarlyLeaveGracePeriodMinutes,
                        OvertimeThresholdHours = dto.OvertimeThresholdHours,
                        OvertimeMultiplier = dto.OvertimeMultiplier,
                        AnnualLeaveEntitlement = dto.AnnualLeaveEntitlement,
                        SickLeaveEntitlement = dto.SickLeaveEntitlement,
                        MaternityLeaveEntitlement = dto.MaternityLeaveEntitlement,
                        PaternityLeaveEntitlement = dto.PaternityLeaveEntitlement,
                        PayrollCutoffDay = dto.PayrollCutoffDay,
                        PayrollPaymentDay = dto.PayrollPaymentDay,
                        Currency = dto.Currency,
                        CurrencySymbol = dto.CurrencySymbol,
                        IncomeTaxRate = dto.IncomeTaxRate,
                        SocialInsuranceEmployeeRate = dto.SocialInsuranceEmployeeRate,
                        SocialInsuranceEmployerRate = dto.SocialInsuranceEmployerRate,
                        MedicalInsuranceRate = dto.MedicalInsuranceRate,
                        EnableEmailNotifications = dto.EnableEmailNotifications,
                        EnableSmsNotifications = dto.EnableSmsNotifications,
                        EnablePushNotifications = dto.EnablePushNotifications,
                        DefaultLanguage = dto.DefaultLanguage,
                        TimeZone = dto.TimeZone,
                        DateFormat = dto.DateFormat,
                        TimeFormat = dto.TimeFormat
                    };

                    _context.CompanySettings.Add(newSettings);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Created company settings for company {CompanyId}", companyId);
                    return MapToDto(newSettings);
                }
                else
                {
                    // Update existing settings
                    existingSettings.WorkingHoursStart = dto.WorkingHoursStart;
                    existingSettings.WorkingHoursEnd = dto.WorkingHoursEnd;
                    existingSettings.WorkingHoursPerDay = dto.WorkingHoursPerDay;
                    existingSettings.WorkingDaysPerWeek = dto.WorkingDaysPerWeek;
                    existingSettings.BreakTimeStart = dto.BreakTimeStart;
                    existingSettings.BreakTimeEnd = dto.BreakTimeEnd;
                    existingSettings.BreakTimeMinutes = dto.BreakTimeMinutes;
                    existingSettings.WeekendDays = dto.WeekendDays;
                    existingSettings.LateGracePeriodMinutes = dto.LateGracePeriodMinutes;
                    existingSettings.EarlyLeaveGracePeriodMinutes = dto.EarlyLeaveGracePeriodMinutes;
                    existingSettings.OvertimeThresholdHours = dto.OvertimeThresholdHours;
                    existingSettings.OvertimeMultiplier = dto.OvertimeMultiplier;
                    existingSettings.AnnualLeaveEntitlement = dto.AnnualLeaveEntitlement;
                    existingSettings.SickLeaveEntitlement = dto.SickLeaveEntitlement;
                    existingSettings.MaternityLeaveEntitlement = dto.MaternityLeaveEntitlement;
                    existingSettings.PaternityLeaveEntitlement = dto.PaternityLeaveEntitlement;
                    existingSettings.PayrollCutoffDay = dto.PayrollCutoffDay;
                    existingSettings.PayrollPaymentDay = dto.PayrollPaymentDay;
                    existingSettings.Currency = dto.Currency;
                    existingSettings.CurrencySymbol = dto.CurrencySymbol;
                    existingSettings.IncomeTaxRate = dto.IncomeTaxRate;
                    existingSettings.SocialInsuranceEmployeeRate = dto.SocialInsuranceEmployeeRate;
                    existingSettings.SocialInsuranceEmployerRate = dto.SocialInsuranceEmployerRate;
                    existingSettings.MedicalInsuranceRate = dto.MedicalInsuranceRate;
                    existingSettings.EnableEmailNotifications = dto.EnableEmailNotifications;
                    existingSettings.EnableSmsNotifications = dto.EnableSmsNotifications;
                    existingSettings.EnablePushNotifications = dto.EnablePushNotifications;
                    existingSettings.DefaultLanguage = dto.DefaultLanguage;
                    existingSettings.TimeZone = dto.TimeZone;
                    existingSettings.DateFormat = dto.DateFormat;
                    existingSettings.TimeFormat = dto.TimeFormat;

                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Updated company settings for company {CompanyId}", companyId);
                    return MapToDto(existingSettings);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating/updating company settings for company {CompanyId}", companyId);
                throw;
            }
        }

        public async Task<List<WorkScheduleDto>> GetWorkSchedulesAsync(Guid companyId)
        {
            try
            {
                var schedules = await _context.WorkSchedules
                    .Include(ws => ws.WorkScheduleDays)
                    .Where(ws => ws.CompanyId == companyId)
                    .OrderBy(ws => ws.Name)
                    .ToListAsync();

                return schedules.Select(MapWorkScheduleToDto).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting work schedules for company {CompanyId}", companyId);
                throw;
            }
        }

        public async Task<WorkScheduleDto> CreateWorkScheduleAsync(Guid companyId, CreateWorkScheduleDto dto)
        {
            try
            {
                // If this is set as default, unset other defaults
                if (dto.IsDefault)
                {
                    var existingDefaults = await _context.WorkSchedules
                        .Where(ws => ws.CompanyId == companyId && ws.IsDefault)
                        .ToListAsync();

                    foreach (var schedule in existingDefaults)
                    {
                        schedule.IsDefault = false;
                    }
                }

                var newSchedule = new WorkSchedule
                {
                    Id = Guid.NewGuid(),
                    CompanyId = companyId,
                    Name = dto.Name,
                    NameAr = dto.NameAr,
                    Description = dto.Description,
                    DescriptionAr = dto.DescriptionAr,
                    IsDefault = dto.IsDefault
                };

                _context.WorkSchedules.Add(newSchedule);

                // Add schedule days
                foreach (var dayDto in dto.WorkScheduleDays)
                {
                    var scheduleDay = new WorkScheduleDay
                    {
                        Id = Guid.NewGuid(),
                        WorkScheduleId = newSchedule.Id,
                        DayOfWeek = dayDto.DayOfWeek,
                        IsWorkingDay = dayDto.IsWorkingDay,
                        StartTime = dayDto.StartTime,
                        EndTime = dayDto.EndTime,
                        BreakStartTime = dayDto.BreakStartTime,
                        BreakEndTime = dayDto.BreakEndTime,
                        WorkingHours = dayDto.WorkingHours
                    };

                    _context.WorkScheduleDays.Add(scheduleDay);
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("Created work schedule {ScheduleId} for company {CompanyId}", newSchedule.Id, companyId);

                // Reload with days
                var createdSchedule = await _context.WorkSchedules
                    .Include(ws => ws.WorkScheduleDays)
                    .FirstAsync(ws => ws.Id == newSchedule.Id);

                return MapWorkScheduleToDto(createdSchedule);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating work schedule for company {CompanyId}", companyId);
                throw;
            }
        }

        public async Task<WorkScheduleDto> UpdateWorkScheduleAsync(Guid scheduleId, UpdateWorkScheduleDto dto)
        {
            try
            {
                var schedule = await _context.WorkSchedules
                    .Include(ws => ws.WorkScheduleDays)
                    .FirstOrDefaultAsync(ws => ws.Id == scheduleId);

                if (schedule == null)
                {
                    throw new ArgumentException($"Work schedule with ID {scheduleId} not found");
                }

                // If this is set as default, unset other defaults
                if (dto.IsDefault && !schedule.IsDefault)
                {
                    var existingDefaults = await _context.WorkSchedules
                        .Where(ws => ws.CompanyId == schedule.CompanyId && ws.IsDefault && ws.Id != scheduleId)
                        .ToListAsync();

                    foreach (var existingSchedule in existingDefaults)
                    {
                        existingSchedule.IsDefault = false;
                    }
                }

                // Update schedule properties
                schedule.Name = dto.Name;
                schedule.NameAr = dto.NameAr;
                schedule.Description = dto.Description;
                schedule.DescriptionAr = dto.DescriptionAr;
                schedule.IsDefault = dto.IsDefault;

                // Remove existing days
                _context.WorkScheduleDays.RemoveRange(schedule.WorkScheduleDays);

                // Add updated days
                foreach (var dayDto in dto.WorkScheduleDays)
                {
                    var scheduleDay = new WorkScheduleDay
                    {
                        Id = Guid.NewGuid(),
                        WorkScheduleId = schedule.Id,
                        DayOfWeek = dayDto.DayOfWeek,
                        IsWorkingDay = dayDto.IsWorkingDay,
                        StartTime = dayDto.StartTime,
                        EndTime = dayDto.EndTime,
                        BreakStartTime = dayDto.BreakStartTime,
                        BreakEndTime = dayDto.BreakEndTime,
                        WorkingHours = dayDto.WorkingHours
                    };

                    _context.WorkScheduleDays.Add(scheduleDay);
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated work schedule {ScheduleId}", scheduleId);

                // Reload with days
                var updatedSchedule = await _context.WorkSchedules
                    .Include(ws => ws.WorkScheduleDays)
                    .FirstAsync(ws => ws.Id == scheduleId);

                return MapWorkScheduleToDto(updatedSchedule);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating work schedule {ScheduleId}", scheduleId);
                throw;
            }
        }

        public async Task<bool> DeleteWorkScheduleAsync(Guid scheduleId)
        {
            try
            {
                var schedule = await _context.WorkSchedules
                    .Include(ws => ws.WorkScheduleDays)
                    .FirstOrDefaultAsync(ws => ws.Id == scheduleId);

                if (schedule == null)
                {
                    return false;
                }

                // Check if this schedule is being used by employees
                var employeeCount = await _context.Employees
                    .CountAsync(e => e.WorkScheduleId == scheduleId);

                if (employeeCount > 0)
                {
                    throw new InvalidOperationException($"Cannot delete work schedule. It is being used by {employeeCount} employee(s).");
                }

                _context.WorkScheduleDays.RemoveRange(schedule.WorkScheduleDays);
                _context.WorkSchedules.Remove(schedule);

                await _context.SaveChangesAsync();

                _logger.LogInformation("Deleted work schedule {ScheduleId}", scheduleId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting work schedule {ScheduleId}", scheduleId);
                throw;
            }
        }

        public async Task<WorkScheduleDto?> GetWorkScheduleByIdAsync(Guid scheduleId)
        {
            try
            {
                var schedule = await _context.WorkSchedules
                    .Include(ws => ws.WorkScheduleDays)
                    .FirstOrDefaultAsync(ws => ws.Id == scheduleId);

                return schedule != null ? MapWorkScheduleToDto(schedule) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting work schedule {ScheduleId}", scheduleId);
                throw;
            }
        }

        public async Task<bool> SetDefaultWorkScheduleAsync(Guid companyId, Guid scheduleId)
        {
            try
            {
                var schedule = await _context.WorkSchedules
                    .FirstOrDefaultAsync(ws => ws.Id == scheduleId && ws.CompanyId == companyId);

                if (schedule == null)
                {
                    return false;
                }

                // Unset other defaults
                var existingDefaults = await _context.WorkSchedules
                    .Where(ws => ws.CompanyId == companyId && ws.IsDefault && ws.Id != scheduleId)
                    .ToListAsync();

                foreach (var existingSchedule in existingDefaults)
                {
                    existingSchedule.IsDefault = false;
                }

                schedule.IsDefault = true;
                await _context.SaveChangesAsync();

                _logger.LogInformation("Set work schedule {ScheduleId} as default for company {CompanyId}", scheduleId, companyId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting default work schedule {ScheduleId} for company {CompanyId}", scheduleId, companyId);
                throw;
            }
        }

        public async Task<List<HolidayDto>> GetHolidaysAsync(Guid companyId, int? year = null)
        {
            try
            {
                var query = _context.Holidays.Where(h => h.CompanyId == companyId);

                if (year.HasValue)
                {
                    query = query.Where(h => h.Date.Year == year.Value);
                }

                var holidays = await query
                    .OrderBy(h => h.Date)
                    .ToListAsync();

                return holidays.Select(MapHolidayToDto).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting holidays for company {CompanyId}", companyId);
                throw;
            }
        }

        public async Task<HolidayDto> CreateHolidayAsync(Guid companyId, CreateHolidayDto dto)
        {
            try
            {
                var holiday = new Holiday
                {
                    Id = Guid.NewGuid(),
                    CompanyId = companyId,
                    Name = dto.Name,
                    NameAr = dto.NameAr,
                    Date = dto.Date,
                    IsRecurring = dto.IsRecurring,
                    RecurrenceType = dto.RecurrenceType,
                    IsOptional = dto.IsOptional,
                    AppliesTo = dto.AppliesTo
                };

                _context.Holidays.Add(holiday);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Created holiday {HolidayId} for company {CompanyId}", holiday.Id, companyId);
                return MapHolidayToDto(holiday);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating holiday for company {CompanyId}", companyId);
                throw;
            }
        }

        public async Task<HolidayDto> UpdateHolidayAsync(Guid holidayId, UpdateHolidayDto dto)
        {
            try
            {
                var holiday = await _context.Holidays.FirstOrDefaultAsync(h => h.Id == holidayId);

                if (holiday == null)
                {
                    throw new ArgumentException($"Holiday with ID {holidayId} not found");
                }

                holiday.Name = dto.Name;
                holiday.NameAr = dto.NameAr;
                holiday.Date = dto.Date;
                holiday.IsRecurring = dto.IsRecurring;
                holiday.RecurrenceType = dto.RecurrenceType;
                holiday.IsOptional = dto.IsOptional;
                holiday.AppliesTo = dto.AppliesTo;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated holiday {HolidayId}", holidayId);
                return MapHolidayToDto(holiday);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating holiday {HolidayId}", holidayId);
                throw;
            }
        }

        public async Task<bool> DeleteHolidayAsync(Guid holidayId)
        {
            try
            {
                var holiday = await _context.Holidays.FirstOrDefaultAsync(h => h.Id == holidayId);

                if (holiday == null)
                {
                    return false;
                }

                _context.Holidays.Remove(holiday);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Deleted holiday {HolidayId}", holidayId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting holiday {HolidayId}", holidayId);
                throw;
            }
        }

        public async Task<bool> IsHolidayAsync(Guid companyId, DateTime date)
        {
            try
            {
                return await _context.Holidays
                    .AnyAsync(h => h.CompanyId == companyId && h.Date.Date == date.Date);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if date {Date} is holiday for company {CompanyId}", date, companyId);
                throw;
            }
        }

        public async Task<bool> IsWeekendAsync(Guid companyId, DateTime date)
        {
            try
            {
                var settings = await GetCompanySettingsAsync(companyId);
                if (settings == null) return false;

                var weekendDays = settings.WeekendDaysList;
                return weekendDays.Contains(date.DayOfWeek.ToString());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if date {Date} is weekend for company {CompanyId}", date, companyId);
                throw;
            }
        }

        public async Task<int> GetWorkingDaysBetweenAsync(Guid companyId, DateTime startDate, DateTime endDate)
        {
            try
            {
                var settings = await GetCompanySettingsAsync(companyId);
                if (settings == null) return 0;

                var weekendDays = settings.WeekendDaysList;
                var holidays = await GetHolidaysAsync(companyId, startDate.Year);
                var holidayDates = holidays.Select(h => h.Date.Date).ToHashSet();

                int workingDays = 0;
                for (var date = startDate.Date; date <= endDate.Date; date = date.AddDays(1))
                {
                    if (!weekendDays.Contains(date.DayOfWeek.ToString()) && !holidayDates.Contains(date))
                    {
                        workingDays++;
                    }
                }

                return workingDays;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating working days between {StartDate} and {EndDate} for company {CompanyId}",
                    startDate, endDate, companyId);
                throw;
            }
        }

        public async Task<decimal> CalculateOvertimeAsync(Guid companyId, decimal hoursWorked)
        {
            try
            {
                var settings = await GetCompanySettingsAsync(companyId);
                if (settings == null) return 0;

                if (hoursWorked <= settings.OvertimeThresholdHours)
                {
                    return 0;
                }

                var overtimeHours = hoursWorked - settings.OvertimeThresholdHours;
                return overtimeHours * settings.OvertimeMultiplier;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating overtime for {HoursWorked} hours for company {CompanyId}",
                    hoursWorked, companyId);
                throw;
            }
        }

        public async Task<(TimeSpan start, TimeSpan end)> GetWorkingHoursAsync(Guid companyId)
        {
            try
            {
                var settings = await GetCompanySettingsAsync(companyId);
                if (settings == null)
                {
                    return (new TimeSpan(8, 0, 0), new TimeSpan(17, 0, 0));
                }

                return (settings.WorkingHoursStart, settings.WorkingHoursEnd);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting working hours for company {CompanyId}", companyId);
                throw;
            }
        }

        public async Task<bool> IsEmployeeLateAsync(Guid companyId, TimeSpan arrivalTime, TimeSpan workStartTime)
        {
            try
            {
                var settings = await GetCompanySettingsAsync(companyId);
                if (settings == null) return false;

                var graceTime = workStartTime.Add(TimeSpan.FromMinutes(settings.LateGracePeriodMinutes));
                return arrivalTime > graceTime;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if employee is late for company {CompanyId}", companyId);
                throw;
            }
        }

        public async Task<bool> IsEmployeeEarlyLeaveAsync(Guid companyId, TimeSpan departureTime, TimeSpan workEndTime)
        {
            try
            {
                var settings = await GetCompanySettingsAsync(companyId);
                if (settings == null) return false;

                var graceTime = workEndTime.Subtract(TimeSpan.FromMinutes(settings.EarlyLeaveGracePeriodMinutes));
                return departureTime < graceTime;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if employee left early for company {CompanyId}", companyId);
                throw;
            }
        }

        public async Task<Dictionary<string, decimal>> GetLeaveEntitlementsAsync(Guid companyId)
        {
            try
            {
                var settings = await GetCompanySettingsAsync(companyId);
                if (settings == null) return new Dictionary<string, decimal>();

                return new Dictionary<string, decimal>
                {
                    { "Annual", settings.AnnualLeaveEntitlement },
                    { "Sick", settings.SickLeaveEntitlement },
                    { "Maternity", settings.MaternityLeaveEntitlement },
                    { "Paternity", settings.PaternityLeaveEntitlement }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting leave entitlements for company {CompanyId}", companyId);
                throw;
            }
        }

        public async Task<TaxInsuranceRatesDto> GetTaxInsuranceRatesAsync(Guid companyId)
        {
            try
            {
                var settings = await GetCompanySettingsAsync(companyId);
                if (settings == null)
                {
                    return new TaxInsuranceRatesDto
                    {
                        IncomeTaxRate = 0.14m,
                        SocialInsuranceEmployeeRate = 0.11m,
                        SocialInsuranceEmployerRate = 0.185m,
                        MedicalInsuranceRate = 0.03m
                    };
                }

                return new TaxInsuranceRatesDto
                {
                    IncomeTaxRate = settings.IncomeTaxRate,
                    SocialInsuranceEmployeeRate = settings.SocialInsuranceEmployeeRate,
                    SocialInsuranceEmployerRate = settings.SocialInsuranceEmployerRate,
                    MedicalInsuranceRate = settings.MedicalInsuranceRate
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting tax and insurance rates for company {CompanyId}", companyId);
                throw;
            }
        }

        public async Task<CompanySettingsDto> InitializeDefaultSettingsAsync(Guid companyId)
        {
            try
            {
                var defaultSettings = new CompanySettings
                {
                    Id = Guid.NewGuid(),
                    CompanyId = companyId,
                    WorkingHoursStart = new TimeSpan(8, 0, 0),
                    WorkingHoursEnd = new TimeSpan(17, 0, 0),
                    WorkingHoursPerDay = 8.0m,
                    WorkingDaysPerWeek = 5.0m,
                    BreakTimeStart = new TimeSpan(12, 0, 0),
                    BreakTimeEnd = new TimeSpan(13, 0, 0),
                    BreakTimeMinutes = 60,
                    WeekendDays = "Friday,Saturday",
                    LateGracePeriodMinutes = 15,
                    EarlyLeaveGracePeriodMinutes = 15,
                    OvertimeThresholdHours = 8.0m,
                    OvertimeMultiplier = 1.5m,
                    AnnualLeaveEntitlement = 21.0m,
                    SickLeaveEntitlement = 30.0m,
                    MaternityLeaveEntitlement = 90.0m,
                    PaternityLeaveEntitlement = 3.0m,
                    PayrollCutoffDay = 25,
                    PayrollPaymentDay = 30,
                    Currency = "EGP",
                    CurrencySymbol = "ج.م",
                    IncomeTaxRate = 0.14m,
                    SocialInsuranceEmployeeRate = 0.11m,
                    SocialInsuranceEmployerRate = 0.185m,
                    MedicalInsuranceRate = 0.03m,
                    EnableEmailNotifications = true,
                    EnableSmsNotifications = false,
                    EnablePushNotifications = true,
                    DefaultLanguage = "ar",
                    TimeZone = "Africa/Cairo",
                    DateFormat = "dd/MM/yyyy",
                    TimeFormat = "HH:mm"
                };

                _context.CompanySettings.Add(defaultSettings);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Initialized default settings for company {CompanyId}", companyId);
                return MapToDto(defaultSettings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing default settings for company {CompanyId}", companyId);
                throw;
            }
        }

        /// <summary>
        /// Update company settings
        /// تحديث إعدادات الشركة
        /// </summary>
        public async Task<ServiceResult<CompanySettingsDto>> UpdateCompanySettingsAsync(Guid companyId, UpdateCompanySettingsDto dto)
        {
            try
            {
                var settings = await _context.CompanySettings
                    .FirstOrDefaultAsync(s => s.CompanyId == companyId);

                if (settings == null)
                {
                    return ServiceResult<CompanySettingsDto>.Failure("إعدادات الشركة غير موجودة");
                }

                // Update properties
                if (!string.IsNullOrEmpty(dto.CompanyName))
                    settings.CompanyName = dto.CompanyName;
                if (!string.IsNullOrEmpty(dto.CompanyNameAr))
                    settings.CompanyNameAr = dto.CompanyNameAr;
                if (!string.IsNullOrEmpty(dto.Address))
                    settings.Address = dto.Address;
                if (!string.IsNullOrEmpty(dto.AddressAr))
                    settings.AddressAr = dto.AddressAr;
                if (!string.IsNullOrEmpty(dto.Phone))
                    settings.Phone = dto.Phone;
                if (!string.IsNullOrEmpty(dto.Email))
                    settings.Email = dto.Email;
                if (!string.IsNullOrEmpty(dto.Website))
                    settings.Website = dto.Website;
                if (!string.IsNullOrEmpty(dto.TaxNumber))
                    settings.TaxNumber = dto.TaxNumber;
                if (!string.IsNullOrEmpty(dto.CommercialRegister))
                    settings.CommercialRegister = dto.CommercialRegister;

                settings.WorkingHoursStart = dto.WorkingHoursStart;
                settings.WorkingHoursEnd = dto.WorkingHoursEnd;
                settings.WeekendDays = dto.WeekendDays;
                settings.Currency = dto.Currency;
                settings.CurrencySymbol = dto.CurrencySymbol;
                settings.IncomeTaxRate = dto.IncomeTaxRate;
                settings.SocialInsuranceEmployeeRate = dto.SocialInsuranceEmployeeRate;
                settings.SocialInsuranceEmployerRate = dto.SocialInsuranceEmployerRate;
                settings.MedicalInsuranceRate = dto.MedicalInsuranceRate;
                settings.EnableEmailNotifications = dto.EnableEmailNotifications;
                settings.EnableSmsNotifications = dto.EnableSmsNotifications;
                settings.EnablePushNotifications = dto.EnablePushNotifications;
                settings.DefaultLanguage = dto.DefaultLanguage;
                settings.TimeZone = dto.TimeZone;
                settings.DateFormat = dto.DateFormat;
                settings.TimeFormat = dto.TimeFormat;
                settings.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return ServiceResult<CompanySettingsDto>.Success(MapToDto(settings));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating company settings for company {CompanyId}", companyId);
                return ServiceResult<CompanySettingsDto>.Failure("حدث خطأ أثناء تحديث إعدادات الشركة");
            }
        }

        // Helper mapping methods
        private static CompanySettingsDto MapToDto(CompanySettings settings)
        {
            return new CompanySettingsDto
            {
                Id = settings.Id,
                CompanyId = settings.CompanyId,
                CompanyName = settings.CompanyName,
                CompanyNameAr = settings.CompanyNameAr,
                Address = settings.Address,
                AddressAr = settings.AddressAr,
                Phone = settings.Phone,
                Email = settings.Email,
                Website = settings.Website,
                TaxNumber = settings.TaxNumber,
                CommercialRegister = settings.CommercialRegister,
                WorkingHoursStart = settings.WorkingHoursStart,
                WorkingHoursEnd = settings.WorkingHoursEnd,
                WorkingHoursPerDay = settings.WorkingHoursPerDay,
                WorkingDaysPerWeek = settings.WorkingDaysPerWeek,
                BreakTimeStart = settings.BreakTimeStart,
                BreakTimeEnd = settings.BreakTimeEnd,
                BreakTimeMinutes = settings.BreakTimeMinutes,
                WeekendDays = settings.WeekendDays,
                LateGracePeriodMinutes = settings.LateGracePeriodMinutes,
                EarlyLeaveGracePeriodMinutes = settings.EarlyLeaveGracePeriodMinutes,
                OvertimeThresholdHours = settings.OvertimeThresholdHours,
                OvertimeMultiplier = settings.OvertimeMultiplier,
                AnnualLeaveEntitlement = settings.AnnualLeaveEntitlement,
                SickLeaveEntitlement = settings.SickLeaveEntitlement,
                MaternityLeaveEntitlement = settings.MaternityLeaveEntitlement,
                PaternityLeaveEntitlement = settings.PaternityLeaveEntitlement,
                PayrollCutoffDay = settings.PayrollCutoffDay,
                PayrollPaymentDay = settings.PayrollPaymentDay,
                Currency = settings.Currency,
                CurrencySymbol = settings.CurrencySymbol,
                IncomeTaxRate = settings.IncomeTaxRate,
                SocialInsuranceEmployeeRate = settings.SocialInsuranceEmployeeRate,
                SocialInsuranceEmployerRate = settings.SocialInsuranceEmployerRate,
                MedicalInsuranceRate = settings.MedicalInsuranceRate,
                EnableEmailNotifications = settings.EnableEmailNotifications,
                EnableSmsNotifications = settings.EnableSmsNotifications,
                EnablePushNotifications = settings.EnablePushNotifications,
                DefaultLanguage = settings.DefaultLanguage,
                TimeZone = settings.TimeZone,
                DateFormat = settings.DateFormat,
                TimeFormat = settings.TimeFormat,
                CreatedAt = settings.CreatedAt,
                UpdatedAt = settings.UpdatedAt
            };
        }

        private static WorkScheduleDto MapWorkScheduleToDto(WorkSchedule schedule)
        {
            return new WorkScheduleDto
            {
                Id = schedule.Id,
                CompanyId = schedule.CompanyId,
                Name = schedule.Name,
                NameAr = schedule.NameAr,
                Description = schedule.Description,
                DescriptionAr = schedule.DescriptionAr,
                IsDefault = schedule.IsDefault,
                WorkScheduleDays = schedule.WorkScheduleDays.Select(MapWorkScheduleDayToDto).ToList(),
                CreatedAt = schedule.CreatedAt,
                UpdatedAt = schedule.UpdatedAt
            };
        }

        private static WorkScheduleDayDto MapWorkScheduleDayToDto(WorkScheduleDay day)
        {
            return new WorkScheduleDayDto
            {
                Id = day.Id,
                WorkScheduleId = day.WorkScheduleId,
                DayOfWeek = day.DayOfWeek,
                IsWorkingDay = day.IsWorkingDay,
                StartTime = day.StartTime,
                EndTime = day.EndTime,
                BreakStartTime = day.BreakStartTime,
                BreakEndTime = day.BreakEndTime,
                WorkingHours = day.WorkingHours
            };
        }

        private static HolidayDto MapHolidayToDto(Holiday holiday)
        {
            return new HolidayDto
            {
                Id = holiday.Id,
                CompanyId = holiday.CompanyId,
                Name = holiday.Name,
                NameAr = holiday.NameAr,
                Date = holiday.Date,
                IsRecurring = holiday.IsRecurring,
                RecurrenceType = holiday.RecurrenceType,
                IsOptional = holiday.IsOptional,
                AppliesTo = holiday.AppliesTo,
                CreatedAt = holiday.CreatedAt,
                UpdatedAt = holiday.UpdatedAt
            };
        }
    }
}
