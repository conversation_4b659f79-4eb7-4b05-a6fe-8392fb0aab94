using FitHRPlus.Application.DTOs.Auth;
using FitHRPlus.Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace FitHRPlus.Web.Controllers
{
    /// <summary>
    /// Authentication controller for login, registration, and user management
    /// وحدة تحكم المصادقة لتسجيل الدخول والتسجيل وإدارة المستخدمين
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(IAuthService authService, ILogger<AuthController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// User login endpoint
        /// نقطة نهاية تسجيل دخول المستخدم
        /// </summary>
        /// <param name="request">Login request data</param>
        /// <returns>Login response with token and user info</returns>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login([FromBody] LoginRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Invalid input data",
                        messageAr = "بيانات الإدخال غير صالحة",
                        errors = ModelState.ToDictionary(
                            kvp => kvp.Key,
                            kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                        )
                    });
                }

                var result = await _authService.LoginAsync(request);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Successful login for user: {UsernameOrEmail}", request.UsernameOrEmail);
                    
                    return Ok(new
                    {
                        success = true,
                        message = "Login successful",
                        messageAr = "تم تسجيل الدخول بنجاح",
                        data = result.Data
                    });
                }

                _logger.LogWarning("Failed login attempt for user: {UsernameOrEmail}. Error: {Error}", 
                    request.UsernameOrEmail, result.ErrorMessage);

                return BadRequest(new
                {
                    success = false,
                    message = result.ErrorMessage,
                    messageAr = result.ErrorMessageAr,
                    errorCode = result.ErrorCode,
                    validationErrors = result.ValidationErrors
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during login for user: {UsernameOrEmail}", request.UsernameOrEmail);
                
                return StatusCode(500, new
                {
                    success = false,
                    message = "An unexpected error occurred",
                    messageAr = "حدث خطأ غير متوقع",
                    errorCode = "INTERNAL_ERROR"
                });
            }
        }

        /// <summary>
        /// User registration endpoint
        /// نقطة نهاية تسجيل المستخدم
        /// </summary>
        /// <param name="request">Registration request data</param>
        /// <returns>Registration response</returns>
        [HttpPost("register")]
        [AllowAnonymous]
        public async Task<IActionResult> Register([FromBody] RegisterRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Invalid input data",
                        messageAr = "بيانات الإدخال غير صالحة",
                        errors = ModelState.ToDictionary(
                            kvp => kvp.Key,
                            kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                        )
                    });
                }

                var result = await _authService.RegisterAsync(request);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Successful registration for user: {Username}", request.Username);
                    
                    return Ok(new
                    {
                        success = true,
                        message = "Registration successful",
                        messageAr = "تم التسجيل بنجاح",
                        data = result.Data
                    });
                }

                _logger.LogWarning("Failed registration attempt for user: {Username}. Error: {Error}", 
                    request.Username, result.ErrorMessage);

                return BadRequest(new
                {
                    success = false,
                    message = result.ErrorMessage,
                    messageAr = result.ErrorMessageAr,
                    errorCode = result.ErrorCode,
                    validationErrors = result.ValidationErrors
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during registration for user: {Username}", request.Username);
                
                return StatusCode(500, new
                {
                    success = false,
                    message = "An unexpected error occurred",
                    messageAr = "حدث خطأ غير متوقع",
                    errorCode = "INTERNAL_ERROR"
                });
            }
        }

        /// <summary>
        /// Refresh access token endpoint
        /// نقطة نهاية تحديث رمز الوصول
        /// </summary>
        /// <param name="request">Refresh token request</param>
        /// <returns>New access token</returns>
        [HttpPost("refresh")]
        [AllowAnonymous]
        public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenRequestDto request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.RefreshToken))
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Refresh token is required",
                        messageAr = "رمز التحديث مطلوب",
                        errorCode = "MISSING_REFRESH_TOKEN"
                    });
                }

                var result = await _authService.RefreshTokenAsync(request.RefreshToken);

                if (result.IsSuccess)
                {
                    return Ok(new
                    {
                        success = true,
                        message = "Token refreshed successfully",
                        messageAr = "تم تحديث الرمز بنجاح",
                        data = result.Data
                    });
                }

                return BadRequest(new
                {
                    success = false,
                    message = result.ErrorMessage,
                    messageAr = result.ErrorMessageAr,
                    errorCode = result.ErrorCode
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during token refresh");
                
                return StatusCode(500, new
                {
                    success = false,
                    message = "An unexpected error occurred",
                    messageAr = "حدث خطأ غير متوقع",
                    errorCode = "INTERNAL_ERROR"
                });
            }
        }

        /// <summary>
        /// User logout endpoint
        /// نقطة نهاية تسجيل خروج المستخدم
        /// </summary>
        /// <param name="request">Logout request</param>
        /// <returns>Logout response</returns>
        [HttpPost("logout")]
        [Authorize]
        public async Task<IActionResult> Logout([FromBody] LogoutRequestDto request)
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Invalid user token",
                        messageAr = "رمز المستخدم غير صالح",
                        errorCode = "INVALID_TOKEN"
                    });
                }

                var result = await _authService.LogoutAsync(userId, request.RefreshToken);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Successful logout for user: {UserId}", userId);
                    
                    return Ok(new
                    {
                        success = true,
                        message = "Logout successful",
                        messageAr = "تم تسجيل الخروج بنجاح"
                    });
                }

                return BadRequest(new
                {
                    success = false,
                    message = result.ErrorMessage,
                    messageAr = result.ErrorMessageAr,
                    errorCode = result.ErrorCode
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during logout");
                
                return StatusCode(500, new
                {
                    success = false,
                    message = "An unexpected error occurred",
                    messageAr = "حدث خطأ غير متوقع",
                    errorCode = "INTERNAL_ERROR"
                });
            }
        }

        /// <summary>
        /// Get current user profile
        /// الحصول على ملف المستخدم الحالي
        /// </summary>
        /// <returns>User profile information</returns>
        [HttpGet("profile")]
        [Authorize]
        public async Task<IActionResult> GetProfile()
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Invalid user token",
                        messageAr = "رمز المستخدم غير صالح",
                        errorCode = "INVALID_TOKEN"
                    });
                }

                // TODO: Implement GetUserProfileAsync in AuthService
                return Ok(new
                {
                    success = true,
                    message = "Profile retrieved successfully",
                    messageAr = "تم استرداد الملف الشخصي بنجاح",
                    data = new
                    {
                        userId = userId,
                        username = User.FindFirst(ClaimTypes.Name)?.Value,
                        email = User.FindFirst(ClaimTypes.Email)?.Value,
                        roles = User.FindAll(ClaimTypes.Role).Select(c => c.Value).ToArray(),
                        permissions = User.FindAll("permission").Select(c => c.Value).ToArray()
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error while retrieving user profile");
                
                return StatusCode(500, new
                {
                    success = false,
                    message = "An unexpected error occurred",
                    messageAr = "حدث خطأ غير متوقع",
                    errorCode = "INTERNAL_ERROR"
                });
            }
        }
    }

    /// <summary>
    /// Refresh token request DTO
    /// كائنة نقل بيانات طلب تحديث الرمز
    /// </summary>
    public class RefreshTokenRequestDto
    {
        public string RefreshToken { get; set; } = string.Empty;
    }

    /// <summary>
    /// Logout request DTO
    /// كائنة نقل بيانات طلب تسجيل الخروج
    /// </summary>
    public class LogoutRequestDto
    {
        public string RefreshToken { get; set; } = string.Empty;
    }
}
