{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["fFfnaMvzwbTod5CDuW8j0SyOhydmpXXltZ3u45wiVxA=", "t67nnWJ9obGfx+YJUuKwRJ8xCrRuz29I4nUEomDvyCM=", "E9dSR/y9brJHMTfBzxR31YcFJFalPWFF6/jEFURFLCI=", "jedM+epY6FX2akRjOZXhfuIkDRChl2UgLQUKm5OP3b8=", "aAwsUPYib8wt93kxtZ7e6deV3F9HM7s9EXUoPHZviAA=", "MWC6GZ4mBEmAhHOlax3Ei8cZp/ZZTmdicS7XwldNSy8=", "2QI53iFX+Rh7Qm5S18D6Yt5z6Gw3kXoLdn4lZ+jcgQ8=", "5tvH/2JRRNWPqRMrtICy3rUp5xtMjlIGcvcrk2Rt4d0=", "0lTdgkZHSnUJSqkBNmd/qbxZFibdqqqTZLdMvC9e47s=", "89bjWedFC/d0H0ZAjN8bs9NjLykZJytGVjtIwxjKXfk=", "JDXbMVHzA3PkiIaeaBv8ph1cNo6iCWUqkIF7HL7M2g8=", "C2RQ5FfzFsJD8O1ykZqenC45cTPgoQ8/h6C/bRwIeFg=", "UZ7zGXdqAD8EqpGVkK0EkFiFnAC/nmBNdzP9QDM/K5w=", "yNFXFa043SE49LPsvvE8ReykE5l3jyWGHhirVW8cDAo=", "KUvGKn3MRBDI2ctJAPqLf4RSXsju4prilINmXBLjGLw=", "flCWSw/AWHJyx7/qtK3nVIr4ccOlqiV4SH9VIv29PeQ=", "Btd7q7Cn0Oz8YerMwDF/T4GBc0z2QFfgT8n6JM4R11M=", "UrcTY6EtilVaIJMeMDevCw8iciUVGY2AJxu7e4FyKe4=", "ePVBArLCDtqXutgPaE3A0mxSGYCZ7cX+Ibxt2496wXc=", "plBxiamBbX3iPXz6bLwvpiX4ZWQBQJb5VYN+amp+0PA=", "uKx1ZJF/3WuXmsXzMZ6VtTt0Y6vwrVHdXeeEB+7sE+w=", "Mlq4XbcCq8a+qzSrgk748+sa8jW5bBwyLyA+wUY7QfQ=", "clqDnoHsgdQr0SyggytUVm4dK5vOX05frqEjwq8fhAU=", "TnEN1nwBo82xhGnQAKGv31R6CWxH1Q7jL9mUPHRyROI=", "sVV1XDfeYjl5N8jJ56xK5Qqh4Se5MgcHv6fkDDpuong=", "0P8bOKfpiHRcbzMTdWsbRHbyWSFFJKV1L5mzY5Rgstw=", "IZfTKHa557aOgEygzqoziHBhnW2NG8xld/C66Tce0a4=", "+If4hFGUCgKnqk69Y1H4DjHENR/GuOB7J2512EgFSeA=", "/gUR52fOo6yy26KqjbILube9pNItUUZmeD8POoiC880=", "jzQyfihvvLt1ZFv0rw8yhGMZkP2u81dXv8t5Pq47ndA=", "O5EW2JXDvAJaDvuMJZuTGUX21WRmPnh7C7pnhobMW3w=", "uoDL587Mi9HYDFJLVVhi98+uUKAoIOWp8O7nZKaI8Yw=", "Gou8sitcZVsmgGyNNW68qdM0T3VeU/7A7AjCQoOVyFI=", "CXE3MO01ia2TFrW4DFPWTgVYt+kE9zSKG19xIaE8Hho=", "eR8vSuuO12fBka1WLDFMEpZPiTrlXdVn1itHo4tWmsY=", "HJv2cVfpgNQb2kv1veKVPUu8mz1Va0XoLml4Q1XUwBY=", "u/Lfb0rv76rz9v6tjS5kwp1uy141t9M9s+on5PZeOrs=", "szlcqC9g/9oRP0pevexTMZwqohGvyHfhpcXbkGSfd1U=", "g5pcyuUXrzXbZgAUqduZCerKT/qWCe24OH2Z10S/1rc=", "O/AgF4+VXcuvP8ENccmjT70IKfJ2uTydXWl9mjFn6YQ=", "CLA4y/Jkic8RCBhCpSHoXjxFXMCe1DDgcYXhnkSE1cQ=", "4BVHx0f1lAnpOHVK02dDVzMaHXDLxq3OWhr/FF2Htis=", "3+SrvFoIMq22rcs7L1szjHxeqFbW6y3BHcScyauKY2k=", "+WdX1WDSMGZkjA2r9EGuqNSuyvULa0rsdvqa28oixoo=", "VWE8ms5fNRKU77wpA6b3RP5kjdCmG1gA6dvZpFdaSUI=", "Y5OevsJL3n9+3iH5St0vJTVp9FiWUiAOTo+WyAZzzrY=", "GDDJ3weljSM3Mbod8XKM/PkNAwlJMaKDO02ia904Jo0=", "hlvzSIRioDyxYfnogSEIo04ZK/DMBmvsEP14VZTTW7Y=", "tf+Z4ovVbLIHtcMM8BTcDECjrl9f+C4ihvQO0+5jP0g=", "fbVgYpzFqwZGslnUDgtp/AJwaGjrMowiU6Hpctq0nfM=", "atRTT6g7LsV6vcl4Qb5Guwl34enu1WHbZo+AIgZ+O20=", "YVChefNDF3al8DLz5ehblV6uVMQr3nJP/iHwJJSURqA=", "37T5ILX/8cdUKyQ/J/WkpYrldtra/EnbKku/+Q0v3ms=", "LZL5CxT9imksNRZASFGzxwWFXbVJZrEUI/4GdJONwXs=", "apy1fy/Z4/6oUF7MTNkk30k6Sk2flAJxrxp/QBoMvEg=", "Jr0kssY3XVE4nMFWtj5BH+4wxOclzpgdHr8WpnvZQoc=", "8fFeyFYlu6DAPpkoPNDJ1VaaorIIl65YAxBnT86LLVU=", "MlWjA58t4mEjzFDVIUQK6/fOyTmIPJavCgq+eXeh6Ok=", "tjTzN9F21iAZLHgshzLczkDw0qnuegiAuZPKDCb0WS8=", "fj7hXfMWCOLRP8RhuVy9wVlFarqq7NLxtq834VzzJBo=", "pVkKt5lQMxj1aWrUkOZiEwgovuqvV3qUVgnMOLC9v/E=", "sXmvIf4vd4S1B/LlCfvh5fQGtIwoZxJC1tYkyLyhJh8=", "VVNE+RBP4m9WUUv96qxiQTCOOMj0pRgP1l6cnq4/WNg=", "F4McG0pXYgHOgDGApuUEHFYMZAh4TGQQv7pjoCZ9Mik=", "wMywWiLzfrzUhAeMo0kKZIOixr4ALijgnkdIH5rGK0o=", "hVF7lKzzb4LW4TNcoVLPLY8hRnrDkHJIG7YYqdGcIiU=", "QAn6L/2iKnNKlH6FVQ0cFId4rxU0VrpbCOeTxKmQDk4="], "CachedAssets": {"F4McG0pXYgHOgDGApuUEHFYMZAh4TGQQv7pjoCZ9Mik=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\igq9a5l9y1-2z0ns9nrw6.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-30T22:20:44.2660748+00:00"}, "t67nnWJ9obGfx+YJUuKwRJ8xCrRuz29I4nUEomDvyCM=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\60o4qpnwlo-doecxvn4pl.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "css/hrms-theme#[.{fingerprint=doecxvn4pl}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\css\\hrms-theme.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vw3nbzbc6d", "Integrity": "cYWG2Q5xWIyJ2Ib/wLdHE3Z21x/my/IAwaH3WWEm6ys=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\css\\hrms-theme.css", "FileLength": 1976, "LastWriteTime": "2025-08-01T22:45:23.5857897+00:00"}, "E9dSR/y9brJHMTfBzxR31YcFJFalPWFF6/jEFURFLCI=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\zsheap6nkw-osk98azxr3.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "css/rtl#[.{fingerprint=osk98azxr3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\css\\rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v6vbxgh4j0", "Integrity": "p4Ps+VffLTdWCIQeL+RJwyS46E0BcNqdmLKQDDMqs3M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\css\\rtl.css", "FileLength": 1454, "LastWriteTime": "2025-07-30T22:20:44.0546504+00:00"}, "jedM+epY6FX2akRjOZXhfuIkDRChl2UgLQUKm5OP3b8=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\8fugkat1v6-quz05a5yt2.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "css/site#[.{fingerprint=quz05a5yt2}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "llam996o6s", "Integrity": "11Oz+ULu6nKQBG2XKTOv4UfiXhUtKFxYOAXGDvQBPVw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\css\\site.css", "FileLength": 1246, "LastWriteTime": "2025-07-30T22:20:44.0576501+00:00"}, "aAwsUPYib8wt93kxtZ7e6deV3F9HM7s9EXUoPHZviAA=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\x550k00s8r-61n19gt1b8.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-07-30T22:20:44.0596504+00:00"}, "MWC6GZ4mBEmAhHOlax3Ei8cZp/ZZTmdicS7XwldNSy8=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\uxe6to63nt-hw9vmrs3pm.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "js/admin#[.{fingerprint=hw9vmrs3pm}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\js\\admin.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p1d7mxby3c", "Integrity": "zWcVaEAEX1ZssZL5D5qxrn1Hz+J1UpdTHzbQq+qOaYw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\js\\admin.js", "FileLength": 3212, "LastWriteTime": "2025-07-30T22:20:44.0636498+00:00"}, "5tvH/2JRRNWPqRMrtICy3rUp5xtMjlIGcvcrk2Rt4d0=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\2v4nicpiji-xtxxf3hu2r.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-30T22:20:44.0646501+00:00"}, "0lTdgkZHSnUJSqkBNmd/qbxZFibdqqqTZLdMvC9e47s=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\ki3r61yzz0-bqjiyaj88i.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-30T22:20:44.0676494+00:00"}, "89bjWedFC/d0H0ZAjN8bs9NjLykZJytGVjtIwxjKXfk=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\igv6iadg1y-c2jlpeoesf.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-30T22:20:44.075653+00:00"}, "JDXbMVHzA3PkiIaeaBv8ph1cNo6iCWUqkIF7HL7M2g8=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\1epeeebuwn-erw9l3u2r3.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-30T22:20:44.0312232+00:00"}, "C2RQ5FfzFsJD8O1ykZqenC45cTPgoQ8/h6C/bRwIeFg=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\ftpgohtqt5-aexeepp0ev.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-30T22:20:44.0536496+00:00"}, "UZ7zGXdqAD8EqpGVkK0EkFiFnAC/nmBNdzP9QDM/K5w=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\mxfc0hoqnl-d7shbmvgxk.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-30T22:20:44.0586487+00:00"}, "yNFXFa043SE49LPsvvE8ReykE5l3jyWGHhirVW8cDAo=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\f8fo60gef8-ausgxo2sd3.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-30T22:20:44.208741+00:00"}, "KUvGKn3MRBDI2ctJAPqLf4RSXsju4prilINmXBLjGLw=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\7edo5a8cxg-k8d9w2qqmf.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-30T22:20:44.2780805+00:00"}, "flCWSw/AWHJyx7/qtK3nVIr4ccOlqiV4SH9VIv29PeQ=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\r6enmaoefx-cosvhxvwiu.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-30T22:20:44.2820779+00:00"}, "Btd7q7Cn0Oz8YerMwDF/T4GBc0z2QFfgT8n6JM4R11M=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\3ig76qlt54-ub07r2b239.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-30T22:20:44.2840761+00:00"}, "UrcTY6EtilVaIJMeMDevCw8iciUVGY2AJxu7e4FyKe4=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\zb30z4ue9n-fvhpjtyr6v.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-30T22:20:44.199742+00:00"}, "ePVBArLCDtqXutgPaE3A0mxSGYCZ7cX+Ibxt2496wXc=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\yk4f4l534f-b7pk76d08c.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-30T22:20:44.0322239+00:00"}, "plBxiamBbX3iPXz6bLwvpiX4ZWQBQJb5VYN+amp+0PA=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\zw84ekdqcb-fsbi9cje9m.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-30T22:20:44.0776485+00:00"}, "uKx1ZJF/3WuXmsXzMZ6VtTt0Y6vwrVHdXeeEB+7sE+w=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\l374mctbup-rzd6atqjts.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-30T22:20:44.0786485+00:00"}, "Mlq4XbcCq8a+qzSrgk748+sa8jW5bBwyLyA+wUY7QfQ=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\xojlgnqrx4-ee0r1s7dh0.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-30T22:20:44.0836491+00:00"}, "clqDnoHsgdQr0SyggytUVm4dK5vOX05frqEjwq8fhAU=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\ck0skx72mb-dxx9fxp4il.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-30T22:20:44.0856501+00:00"}, "TnEN1nwBo82xhGnQAKGv31R6CWxH1Q7jL9mUPHRyROI=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\7fxf9gq4db-jd9uben2k1.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-30T22:20:44.0886498+00:00"}, "sVV1XDfeYjl5N8jJ56xK5Qqh4Se5MgcHv6fkDDpuong=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\div0bo15tn-khv3u5hwcm.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-30T22:20:44.0926515+00:00"}, "0P8bOKfpiHRcbzMTdWsbRHbyWSFFJKV1L5mzY5Rgstw=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\1gmvp0o92a-r4e9w2rdcm.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-30T22:20:44.1016502+00:00"}, "IZfTKHa557aOgEygzqoziHBhnW2NG8xld/C66Tce0a4=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\rk4rh0gy3u-lcd1t2u6c8.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-30T22:20:44.03765+00:00"}, "+If4hFGUCgKnqk69Y1H4DjHENR/GuOB7J2512EgFSeA=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\4531t1ed1a-c2oey78nd0.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-30T22:20:44.0456486+00:00"}, "/gUR52fOo6yy26KqjbILube9pNItUUZmeD8POoiC880=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\8fkvxl9fzg-tdbxkamptv.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-30T22:20:44.0486487+00:00"}, "jzQyfihvvLt1ZFv0rw8yhGMZkP2u81dXv8t5Pq47ndA=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\bunabjyx51-j5mq2jizvt.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-30T22:20:44.0576501+00:00"}, "O5EW2JXDvAJaDvuMJZuTGUX21WRmPnh7C7pnhobMW3w=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\gflzxeyadh-06098lyss8.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-30T22:20:44.0606496+00:00"}, "uoDL587Mi9HYDFJLVVhi98+uUKAoIOWp8O7nZKaI8Yw=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\nffcc8z3bl-nvvlpmu67g.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-30T22:20:44.0656501+00:00"}, "Gou8sitcZVsmgGyNNW68qdM0T3VeU/7A7AjCQoOVyFI=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\6c8i22kdo7-s35ty4nyc5.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-30T22:20:44.0736512+00:00"}, "CXE3MO01ia2TFrW4DFPWTgVYt+kE9zSKG19xIaE8Hho=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\0kdkp2uamr-pj5nd1wqec.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-30T22:20:44.1116517+00:00"}, "eR8vSuuO12fBka1WLDFMEpZPiTrlXdVn1itHo4tWmsY=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\j7ijznnkyj-46ein0sx1k.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-30T22:20:44.09565+00:00"}, "HJv2cVfpgNQb2kv1veKVPUu8mz1Va0XoLml4Q1XUwBY=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\eqfboqlxop-v0zj4ognzu.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-30T22:20:44.1296487+00:00"}, "u/Lfb0rv76rz9v6tjS5kwp1uy141t9M9s+on5PZeOrs=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\6s5r4ni1tx-37tfw0ft22.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-30T22:20:44.2097412+00:00"}, "szlcqC9g/9oRP0pevexTMZwqohGvyHfhpcXbkGSfd1U=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\mtkil0aunx-hrwsygsryq.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-30T22:20:44.2167435+00:00"}, "g5pcyuUXrzXbZgAUqduZCerKT/qWCe24OH2Z10S/1rc=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\hkea5md6bs-pk9g2wxc8p.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-30T22:20:44.2410736+00:00"}, "O/AgF4+VXcuvP8ENccmjT70IKfJ2uTydXWl9mjFn6YQ=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\mub60lakkj-ft3s53vfgj.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-30T22:20:44.3120745+00:00"}, "CLA4y/Jkic8RCBhCpSHoXjxFXMCe1DDgcYXhnkSE1cQ=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\pprmefjzty-6cfz1n2cew.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-30T22:20:44.3403923+00:00"}, "4BVHx0f1lAnpOHVK02dDVzMaHXDLxq3OWhr/FF2Htis=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\60j4getjw3-6pdc2jztkx.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-30T22:20:44.3583914+00:00"}, "3+SrvFoIMq22rcs7L1szjHxeqFbW6y3BHcScyauKY2k=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\myuddrgfyq-493y06b0oq.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-30T22:20:44.1166476+00:00"}, "+WdX1WDSMGZkjA2r9EGuqNSuyvULa0rsdvqa28oixoo=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\kj8nmj45hc-iovd86k7lj.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-30T22:20:44.1376505+00:00"}, "VWE8ms5fNRKU77wpA6b3RP5kjdCmG1gA6dvZpFdaSUI=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\fctsmlqq45-vr1egmr9el.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-30T22:20:44.2810768+00:00"}, "Y5OevsJL3n9+3iH5St0vJTVp9FiWUiAOTo+WyAZzzrY=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\qdbyu6s272-kbrnm935zg.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-30T22:20:44.2950798+00:00"}, "GDDJ3weljSM3Mbod8XKM/PkNAwlJMaKDO02ia904Jo0=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\0fsk1lox6i-jj8uyg4cgr.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-30T22:20:44.2990768+00:00"}, "hlvzSIRioDyxYfnogSEIo04ZK/DMBmvsEP14VZTTW7Y=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\5kywyzdd5l-y7v9cxd14o.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-30T22:20:44.3090782+00:00"}, "tf+Z4ovVbLIHtcMM8BTcDECjrl9f+C4ihvQO0+5jP0g=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\wjsfn8esu0-notf2xhcfb.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-30T22:20:44.3353922+00:00"}, "fbVgYpzFqwZGslnUDgtp/AJwaGjrMowiU6Hpctq0nfM=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\j6hs8eumbu-h1s4sie4z3.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-30T22:20:44.3483923+00:00"}, "atRTT6g7LsV6vcl4Qb5Guwl34enu1WHbZo+AIgZ+O20=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\8p68r1h6fd-63fj8s7r0e.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-30T22:20:44.2027442+00:00"}, "YVChefNDF3al8DLz5ehblV6uVMQr3nJP/iHwJJSURqA=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\7yp80hgysb-0j3bgjxly4.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-30T22:20:44.2097412+00:00"}, "37T5ILX/8cdUKyQ/J/WkpYrldtra/EnbKku/+Q0v3ms=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\vllwsq6v5j-47otxtyo56.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-30T22:20:44.2470755+00:00"}, "LZL5CxT9imksNRZASFGzxwWFXbVJZrEUI/4GdJONwXs=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\nkg3np5xy1-4v8eqarkd7.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-30T22:20:44.2490755+00:00"}, "apy1fy/Z4/6oUF7MTNkk30k6Sk2flAJxrxp/QBoMvEg=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\3ww3g6sfpi-356vix0kms.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-30T22:20:44.2510762+00:00"}, "Jr0kssY3XVE4nMFWtj5BH+4wxOclzpgdHr8WpnvZQoc=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\73m3s604pp-83jwlth58m.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-30T22:20:44.2550765+00:00"}, "8fFeyFYlu6DAPpkoPNDJ1VaaorIIl65YAxBnT86LLVU=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\tinlqjcioi-mrlpezrjn3.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-30T22:20:44.258077+00:00"}, "MlWjA58t4mEjzFDVIUQK6/fOyTmIPJavCgq+eXeh6Ok=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\ehlfdiqdmp-lzl9nlhx6b.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-30T22:20:44.2620764+00:00"}, "tjTzN9F21iAZLHgshzLczkDw0qnuegiAuZPKDCb0WS8=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\26uayrvj4w-ag7o75518u.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-30T22:20:44.2380752+00:00"}, "fj7hXfMWCOLRP8RhuVy9wVlFarqq7NLxtq834VzzJBo=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\e07ebqpxxb-x0q3zqp4vz.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-30T22:20:44.2590779+00:00"}, "QAn6L/2iKnNKlH6FVQ0cFId4rxU0VrpbCOeTxKmQDk4=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\i6mp4gjtp9-mlv21k5csn.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-30T22:20:44.2630762+00:00"}, "pVkKt5lQMxj1aWrUkOZiEwgovuqvV3qUVgnMOLC9v/E=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\235p5f2gur-0i3buxo5is.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-30T22:20:44.2910759+00:00"}, "sXmvIf4vd4S1B/LlCfvh5fQGtIwoZxJC1tYkyLyhJh8=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\66iuflll45-o1o13a6vjx.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-30T22:20:44.2470755+00:00"}, "VVNE+RBP4m9WUUv96qxiQTCOOMj0pRgP1l6cnq4/WNg=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\obxvixxw6y-ttgo8qnofa.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-30T22:20:44.2550765+00:00"}, "hVF7lKzzb4LW4TNcoVLPLY8hRnrDkHJIG7YYqdGcIiU=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\96x22mxnmi-87fc7y1x7t.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-30T22:20:44.3160768+00:00"}, "wMywWiLzfrzUhAeMo0kKZIOixr4ALijgnkdIH5rGK0o=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\4mifu3tirp-muycvpuwrr.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-30T22:20:44.2710769+00:00"}, "fFfnaMvzwbTod5CDuW8j0SyOhydmpXXltZ3u45wiVxA=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\zb2o5i2eq0-rnr274xhvc.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "css/fithr-components#[.{fingerprint=rnr274xhvc}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\css\\fithr-components.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rp2ndm89f4", "Integrity": "gY3w4eWlog6v/P1znc26Skp21KtMzfmek3/vVPujctg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\css\\fithr-components.css", "FileLength": 1694, "LastWriteTime": "2025-07-31T09:31:52.2429079+00:00"}, "2QI53iFX+Rh7Qm5S18D6Yt5z6Gw3kXoLdn4lZ+jcgQ8=": {"Identity": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\5tfxq5ivmr-8g4cyvnxuq.gz", "SourceId": "FitHRPlus.Web", "SourceType": "Discovered", "ContentRoot": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/FitHRPlus.Web", "RelativePath": "js/fithr-common#[.{fingerprint=8g4cyvnxuq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\js\\fithr-common.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w0muiuaocc", "Integrity": "AAsl9rB7HFvu8mp6G4KJf4qUuUnxKNSK5MlCfP1uGbo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\wwwroot\\js\\fithr-common.js", "FileLength": 3593, "LastWriteTime": "2025-07-31T09:31:52.2806633+00:00"}}, "CachedCopyCandidates": {}}