using FitHRPlus.Domain.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Domain.Entities
{
    /// <summary>
    /// Leave request entity for employee leave applications
    /// كيان طلب الإجازة لطلبات إجازات الموظفين
    /// </summary>
    public class LeaveRequest : BaseEntity
    {
        [Required]
        public Guid EmployeeId { get; set; }
        
        [Required]
        public Guid LeaveTypeId { get; set; }
        
        [Required]
        public DateTime StartDate { get; set; }
        
        [Required]
        public DateTime EndDate { get; set; }
        
        public decimal TotalDays { get; set; }
        
        [MaxLength(1000)]
        public string? Reason { get; set; }
        
        [MaxLength(1000)]
        public string? ReasonAr { get; set; }
        
        [MaxLength(20)]
        public string Status { get; set; } = "Pending";
        
        public Guid? ApprovedBy { get; set; }
        public DateTime? ApprovedAt { get; set; }
        
        [MaxLength(500)]
        public string? RejectionReason { get; set; }
        
        [MaxLength(500)]
        public string? RejectionReasonAr { get; set; }
        
        public string? AttachedDocuments { get; set; }
        
        [MaxLength(200)]
        public string? EmergencyContact { get; set; }
        
        [MaxLength(20)]
        public string? EmergencyPhone { get; set; }

        // Navigation Properties
        public virtual Employee Employee { get; set; } = null!;
        public virtual LeaveType LeaveType { get; set; } = null!;
        public virtual Employee? ApprovedByEmployee { get; set; }
    }

    /// <summary>
    /// Leave balance entity for tracking employee leave balances
    /// كيان رصيد الإجازة لتتبع أرصدة إجازات الموظفين
    /// </summary>
    public class LeaveBalance : BaseEntity
    {
        [Required]
        public Guid EmployeeId { get; set; }
        
        [Required]
        public Guid LeaveTypeId { get; set; }
        
        [Required]
        public int Year { get; set; }
        
        public decimal EntitledDays { get; set; } = 0;
        public decimal UsedDays { get; set; } = 0;
        public decimal RemainingDays { get; set; } = 0;
        public decimal CarriedForwardDays { get; set; } = 0;

        // Navigation Properties
        public virtual Employee Employee { get; set; } = null!;
        public virtual LeaveType LeaveType { get; set; } = null!;
    }

    /// <summary>
    /// Biometric device entity for attendance tracking devices
    /// كيان جهاز البصمة لأجهزة تتبع الحضور
    /// </summary>
    public class BiometricDevice : BaseEntity
    {
        [Required]
        public Guid CompanyId { get; set; }
        
        [Required]
        [MaxLength(200)]
        public string DeviceName { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(50)]
        public string DeviceType { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string? Brand { get; set; }
        
        [MaxLength(100)]
        public string? Model { get; set; }
        
        [MaxLength(100)]
        public string? SerialNumber { get; set; }
        
        [MaxLength(50)]
        public string? IpAddress { get; set; }
        
        public int? Port { get; set; }
        
        [MaxLength(200)]
        public string? Location { get; set; }
        
        [MaxLength(200)]
        public string? LocationAr { get; set; }
        
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public int GeofenceRadius { get; set; } = 100;
        
        public DateTime? LastSync { get; set; }
        public int SyncInterval { get; set; } = 300;

        // Navigation Properties
        public virtual Company Company { get; set; } = null!;
        public virtual ICollection<AttendanceRecord> AttendanceRecords { get; set; } = new List<AttendanceRecord>();
    }

    /// <summary>
    /// Work shift entity for defining work schedules
    /// كيان وردية العمل لتعريف جداول العمل
    /// </summary>
    public class WorkShift : BaseEntity
    {
        [Required]
        public Guid CompanyId { get; set; }
        
        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(200)]
        public string NameAr { get; set; } = string.Empty;
        
        [Required]
        public TimeSpan StartTime { get; set; }
        
        [Required]
        public TimeSpan EndTime { get; set; }
        
        public int BreakDuration { get; set; } = 0;
        public int GracePeriod { get; set; } = 15;
        public bool IsFlexible { get; set; } = false;
        public int FlexibleMinutes { get; set; } = 0;
        
        [MaxLength(20)]
        public string WorkingDays { get; set; } = "1,2,3,4,5";

        // Navigation Properties
        public virtual Company Company { get; set; } = null!;
        public virtual ICollection<EmployeeShift> EmployeeShifts { get; set; } = new List<EmployeeShift>();
    }

    /// <summary>
    /// Employee shift assignment entity
    /// كيان تخصيص وردية الموظف
    /// </summary>
    public class EmployeeShift : BaseEntity
    {
        [Required]
        public Guid EmployeeId { get; set; }
        
        [Required]
        public Guid ShiftId { get; set; }
        
        [Required]
        public DateTime EffectiveDate { get; set; }
        
        public DateTime? EndDate { get; set; }

        // Navigation Properties
        public virtual Employee Employee { get; set; } = null!;
        public virtual WorkShift Shift { get; set; } = null!;
    }

    /// <summary>
    /// Holiday entity for company holidays
    /// كيان العطلة لعطل الشركة
    /// </summary>
    public class Holiday : BaseEntity
    {
        [Required]
        public Guid CompanyId { get; set; }
        
        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(200)]
        public string NameAr { get; set; } = string.Empty;
        
        [Required]
        public DateTime Date { get; set; }
        
        public bool IsRecurring { get; set; } = false;
        
        [MaxLength(20)]
        public string? RecurrenceType { get; set; }
        
        public bool IsOptional { get; set; } = false;
        
        [MaxLength(50)]
        public string AppliesTo { get; set; } = "All";

        // Navigation Properties
        public virtual Company Company { get; set; } = null!;
    }

    /// <summary>
    /// Salary component entity for defining salary elements
    /// كيان مكون الراتب لتعريف عناصر الراتب
    /// </summary>
    public class SalaryComponent : BaseEntity
    {
        [Required]
        public Guid CompanyId { get; set; }
        
        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(200)]
        public string NameAr { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string? Description { get; set; }
        
        [MaxLength(500)]
        public string? DescriptionAr { get; set; }
        
        [Required]
        [MaxLength(20)]
        public string Type { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string? Category { get; set; }
        
        public bool IsPercentage { get; set; } = false;
        public decimal? Amount { get; set; }
        public decimal? Percentage { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
        public bool IsTaxable { get; set; } = true;
        public bool IsInsurable { get; set; } = true;
        
        [MaxLength(1000)]
        public string? CalculationFormula { get; set; }

        // Navigation Properties
        public virtual Company Company { get; set; } = null!;
        public virtual ICollection<EmployeeSalary> EmployeeSalaries { get; set; } = new List<EmployeeSalary>();
    }

    /// <summary>
    /// Employee salary component assignment
    /// تخصيص مكون راتب الموظف
    /// </summary>
    public class EmployeeSalary : BaseEntity
    {
        [Required]
        public Guid EmployeeId { get; set; }
        
        [Required]
        public Guid SalaryComponentId { get; set; }
        
        [Required]
        public decimal Amount { get; set; }
        
        public decimal? Percentage { get; set; }
        
        [Required]
        public DateTime EffectiveDate { get; set; }
        
        public DateTime? EndDate { get; set; }
        
        [MaxLength(500)]
        public string? Notes { get; set; }

        // Navigation Properties
        public virtual Employee Employee { get; set; } = null!;
        public virtual SalaryComponent SalaryComponent { get; set; } = null!;
    }

    /// <summary>
    /// Payroll entity for monthly salary calculations
    /// كيان كشف الراتب للحسابات الشهرية للراتب
    /// </summary>
    public class Payroll : BaseEntity
    {
        [Required]
        public Guid CompanyId { get; set; }
        
        [Required]
        public Guid EmployeeId { get; set; }
        
        [Required]
        public int PayrollMonth { get; set; }
        
        [Required]
        public int PayrollYear { get; set; }
        
        public decimal BasicSalary { get; set; } = 0;
        public decimal TotalAllowances { get; set; } = 0;
        public decimal TotalDeductions { get; set; } = 0;
        public decimal GrossSalary { get; set; } = 0;
        public decimal IncomeTax { get; set; } = 0;
        public decimal SocialInsuranceEmployee { get; set; } = 0;
        public decimal SocialInsuranceEmployer { get; set; } = 0;
        public decimal MedicalInsurance { get; set; } = 0;
        public int WorkingDays { get; set; } = 0;
        public decimal ActualWorkingDays { get; set; } = 0;
        public decimal AbsentDays { get; set; } = 0;
        public decimal LeaveDays { get; set; } = 0;
        public decimal OvertimeHours { get; set; } = 0;
        public decimal OvertimeAmount { get; set; } = 0;
        public decimal LateDeduction { get; set; } = 0;
        public decimal NetSalary { get; set; } = 0;
        
        [MaxLength(20)]
        public string Status { get; set; } = "Draft";
        
        public DateTime? PaymentDate { get; set; }
        
        [MaxLength(50)]
        public string? PaymentMethod { get; set; }
        
        [MaxLength(100)]
        public string? BankTransferReference { get; set; }
        
        public Guid? ProcessedBy { get; set; }
        public DateTime? ProcessedAt { get; set; }

        public Guid? ApprovedBy { get; set; }
        public DateTime? ApprovedAt { get; set; }

        public Guid? PaidBy { get; set; }
        public DateTime? PaidAt { get; set; }
        
        [MaxLength(1000)]
        public string? Notes { get; set; }

        [MaxLength(1000)]
        public string? NotesAr { get; set; }
        
        public bool PayslipGenerated { get; set; } = false;
        
        [MaxLength(500)]
        public string? PayslipPath { get; set; }

        // Navigation Properties
        public virtual Company Company { get; set; } = null!;
        public virtual Employee Employee { get; set; } = null!;
        public virtual Employee? ProcessedByEmployee { get; set; }
        public virtual Employee? ApprovedByEmployee { get; set; }
        public virtual Employee? PaidByEmployee { get; set; }
    }

    /// <summary>
    /// Audit log entity for tracking system changes
    /// كيان سجل المراجعة لتتبع تغييرات النظام
    /// </summary>
    public class AuditLog : BaseEntity
    {
        [Required]
        public Guid UserId { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string Action { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(100)]
        public string EntityName { get; set; } = string.Empty;
        
        public Guid? EntityId { get; set; }
        
        public string? OldValues { get; set; }
        public string? NewValues { get; set; }
        
        [MaxLength(50)]
        public string? IpAddress { get; set; }
        
        [MaxLength(500)]
        public string? UserAgent { get; set; }

        // Navigation Properties
        public virtual User User { get; set; } = null!;
    }

    /// <summary>
    /// Notification entity for system notifications
    /// كيان الإشعار لإشعارات النظام
    /// </summary>
    public class Notification : BaseEntity
    {
        [Required]
        public Guid UserId { get; set; }
        
        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(200)]
        public string TitleAr { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(1000)]
        public string Message { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(1000)]
        public string MessageAr { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string Type { get; set; } = "Info";

        [MaxLength(50)]
        public string Category { get; set; } = "System";

        [MaxLength(50)]
        public string Priority { get; set; } = "Medium";

        public bool IsRead { get; set; } = false;
        public DateTime? ReadAt { get; set; }

        [MaxLength(500)]
        public string? ActionUrl { get; set; }

        [MaxLength(50)]
        public string? Icon { get; set; }

        public DateTime? ExpiresAt { get; set; }

        // Navigation Properties
        public virtual User User { get; set; } = null!;
    }

    /// <summary>
    /// Company settings entity
    /// كيان إعدادات الشركة
    /// </summary>
    public class CompanySettings : BaseEntity
    {
        [Required]
        public Guid CompanyId { get; set; }

        // Company information
        [MaxLength(200)]
        public string? CompanyName { get; set; }

        [MaxLength(200)]
        public string? CompanyNameAr { get; set; }

        [MaxLength(500)]
        public string? Address { get; set; }

        [MaxLength(500)]
        public string? AddressAr { get; set; }

        [MaxLength(20)]
        public string? Phone { get; set; }

        [MaxLength(100)]
        public string? Email { get; set; }

        [MaxLength(200)]
        public string? Website { get; set; }

        [MaxLength(50)]
        public string? TaxNumber { get; set; }

        [MaxLength(50)]
        public string? CommercialRegister { get; set; }

        // Working hours settings
        [Required]
        public TimeSpan WorkingHoursStart { get; set; } = new TimeSpan(8, 0, 0); // 8:00 AM

        [Required]
        public TimeSpan WorkingHoursEnd { get; set; } = new TimeSpan(17, 0, 0); // 5:00 PM

        [Required]
        public decimal WorkingHoursPerDay { get; set; } = 8.0m;

        [Required]
        public decimal WorkingDaysPerWeek { get; set; } = 5.0m;

        // Break time settings
        public TimeSpan? BreakTimeStart { get; set; }
        public TimeSpan? BreakTimeEnd { get; set; }
        public decimal BreakTimeMinutes { get; set; } = 60; // 1 hour

        // Weekend settings
        [Required]
        [MaxLength(20)]
        public string WeekendDays { get; set; } = "Friday,Saturday"; // Comma-separated

        // Attendance policies
        [Required]
        public int LateGracePeriodMinutes { get; set; } = 15;

        [Required]
        public int EarlyLeaveGracePeriodMinutes { get; set; } = 15;

        [Required]
        public decimal OvertimeThresholdHours { get; set; } = 8.0m;

        [Required]
        public decimal OvertimeMultiplier { get; set; } = 1.5m;

        // Leave policies
        [Required]
        public decimal AnnualLeaveEntitlement { get; set; } = 21.0m; // Days per year

        [Required]
        public decimal SickLeaveEntitlement { get; set; } = 30.0m; // Days per year

        [Required]
        public decimal MaternityLeaveEntitlement { get; set; } = 90.0m; // Days

        [Required]
        public decimal PaternityLeaveEntitlement { get; set; } = 3.0m; // Days

        // Payroll settings
        [Required]
        public int PayrollCutoffDay { get; set; } = 25; // Day of month

        [Required]
        public int PayrollPaymentDay { get; set; } = 30; // Day of month

        [Required]
        [MaxLength(10)]
        public string Currency { get; set; } = "EGP";

        [Required]
        [MaxLength(10)]
        public string CurrencySymbol { get; set; } = "ج.م";

        // Tax settings
        [Required]
        public decimal IncomeTaxRate { get; set; } = 0.14m; // 14%

        [Required]
        public decimal SocialInsuranceEmployeeRate { get; set; } = 0.11m; // 11%

        [Required]
        public decimal SocialInsuranceEmployerRate { get; set; } = 0.185m; // 18.5%

        [Required]
        public decimal MedicalInsuranceRate { get; set; } = 0.03m; // 3%

        // Notification settings
        public bool EnableEmailNotifications { get; set; } = true;
        public bool EnableSmsNotifications { get; set; } = false;
        public bool EnablePushNotifications { get; set; } = true;

        // System settings
        [Required]
        [MaxLength(10)]
        public string DefaultLanguage { get; set; } = "ar";

        [Required]
        [MaxLength(50)]
        public string TimeZone { get; set; } = "Africa/Cairo";

        [Required]
        [MaxLength(20)]
        public string DateFormat { get; set; } = "dd/MM/yyyy";

        [Required]
        [MaxLength(20)]
        public string TimeFormat { get; set; } = "HH:mm";

        // Navigation properties
        public virtual Company Company { get; set; } = null!;
    }

    /// <summary>
    /// Work schedule entity
    /// كيان جدول العمل
    /// </summary>
    public class WorkSchedule : BaseEntity
    {
        [Required]
        public Guid CompanyId { get; set; }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string NameAr { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? Description { get; set; }

        [MaxLength(500)]
        public string? DescriptionAr { get; set; }

        [Required]
        public bool IsDefault { get; set; } = false;

        // Navigation properties
        public virtual Company Company { get; set; } = null!;
        public virtual ICollection<WorkScheduleDay> WorkScheduleDays { get; set; } = new List<WorkScheduleDay>();
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
    }

    /// <summary>
    /// Work schedule day entity
    /// كيان يوم جدول العمل
    /// </summary>
    public class WorkScheduleDay : BaseEntity
    {
        [Required]
        public Guid WorkScheduleId { get; set; }

        [Required]
        public DayOfWeek DayOfWeek { get; set; }

        [Required]
        public bool IsWorkingDay { get; set; } = true;

        public TimeSpan? StartTime { get; set; }
        public TimeSpan? EndTime { get; set; }
        public TimeSpan? BreakStartTime { get; set; }
        public TimeSpan? BreakEndTime { get; set; }

        [Required]
        public decimal WorkingHours { get; set; } = 8.0m;

        // Navigation properties
        public virtual WorkSchedule WorkSchedule { get; set; } = null!;
    }
}