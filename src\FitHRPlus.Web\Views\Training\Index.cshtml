@model FitHRPlus.Web.Models.Training.TrainingIndexViewModel
@{
    ViewData["Title"] = "التدريب والتطوير";
}

<!-- Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-title">
            <h1><i class="bi bi-mortarboard"></i> التدريب والتطوير</h1>
            <p>إدارة برامج التدريب وتطوير مهارات الموظفين</p>
        </div>
        <div class="page-actions">
            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#newTrainingModal">
                <i class="bi bi-plus-circle"></i>
                برنامج تدريبي جديد
            </button>
            <button type="button" class="btn btn-primary" onclick="viewTrainingCalendar()">
                <i class="bi bi-calendar-event"></i>
                التقويم التدريبي
            </button>
            <div class="btn-group">
                <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="bi bi-download"></i>
                    تصدير
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="exportTraining('excel')">
                        <i class="bi bi-file-earmark-excel me-2"></i>Excel
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportTraining('pdf')">
                        <i class="bi bi-file-earmark-pdf me-2"></i>PDF
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-primary">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@(Model.TotalPrograms ?? 0)</h3>
                        <p>البرامج التدريبية</p>
                        <small>إجمالي البرامج المتاحة</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-book"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: 100%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-success">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@(Model.CompletedTrainings ?? 0)</h3>
                        <p>التدريبات المكتملة</p>
                        <small>التدريبات المنجزة بنجاح</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-check-circle"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: @(Model.TotalPrograms > 0 ? (Model.CompletedTrainings * 100 / Model.TotalPrograms) : 0)%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-warning">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@(Model.OngoingTrainings ?? 0)</h3>
                        <p>التدريبات الجارية</p>
                        <small>التدريبات قيد التنفيذ حالياً</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-play-circle"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: @(Model.TotalPrograms > 0 ? (Model.OngoingTrainings * 100 / Model.TotalPrograms) : 0)%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-info">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@(Model.TotalParticipants ?? 0)</h3>
                        <p>إجمالي المشاركين</p>
                        <small>عدد الموظفين المشاركين</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-people"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: @(Model.TotalParticipants * 2)%"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <div class="col-lg-8">
        <!-- Training Programs -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title">البرامج التدريبية</h5>
                    <div class="card-actions">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary active" onclick="filterTrainings('all')">
                                الكل
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="filterTrainings('completed')">
                                مكتملة
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="filterTrainings('ongoing')">
                                جارية
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="filterTrainings('upcoming')">
                                قادمة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                @if (Model.TrainingPrograms?.Any() == true)
                {
                    <div class="training-programs-list">
                        @foreach (var program in Model.TrainingPrograms)
                        {
                            <div class="training-program-item @(program.Status?.ToLower())" data-status="@program.Status?.ToLower()">
                                <div class="training-program-content">
                                    <div class="training-program-header">
                                        <div class="program-info">
                                            <div class="program-icon">
                                                <i class="bi bi-mortarboard"></i>
                                            </div>
                                            <div class="program-details">
                                                <h6>@program.Title</h6>
                                                <small>@program.Category</small>
                                            </div>
                                        </div>
                                        <div class="program-meta">
                                            <span class="program-status <EMAIL>?.ToLower()">
                                                @program.StatusAr
                                            </span>
                                            <span class="program-duration">
                                                <i class="bi bi-clock"></i>
                                                @program.Duration ساعة
                                            </span>
                                        </div>
                                    </div>
                                    <div class="training-program-body">
                                        <p class="program-description">@program.Description</p>
                                        <div class="program-details-grid">
                                            <div class="detail-item">
                                                <span class="detail-label">المدرب:</span>
                                                <span class="detail-value">@program.Instructor</span>
                                            </div>
                                            <div class="detail-item">
                                                <span class="detail-label">تاريخ البداية:</span>
                                                <span class="detail-value">@program.StartDate?.ToString("dd/MM/yyyy")</span>
                                            </div>
                                            <div class="detail-item">
                                                <span class="detail-label">تاريخ النهاية:</span>
                                                <span class="detail-value">@program.EndDate?.ToString("dd/MM/yyyy")</span>
                                            </div>
                                            <div class="detail-item">
                                                <span class="detail-label">المشاركين:</span>
                                                <span class="detail-value">@program.ParticipantsCount/@program.MaxParticipants</span>
                                            </div>
                                        </div>
                                        @if (program.Status == "Ongoing")
                                        {
                                            <div class="program-progress">
                                                <div class="progress-info">
                                                    <span>التقدم: @(program.ProgressPercentage ?? 0)%</span>
                                                </div>
                                                <div class="progress">
                                                    <div class="progress-bar" style="width: @(program.ProgressPercentage ?? 0)%"></div>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                    <div class="training-program-actions">
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewProgram('@program.Id')">
                                            <i class="bi bi-eye"></i>
                                            عرض
                                        </button>
                                        @if (program.Status == "Upcoming")
                                        {
                                            <button class="btn btn-sm btn-outline-success" onclick="startProgram('@program.Id')">
                                                <i class="bi bi-play"></i>
                                                بدء
                                            </button>
                                        }
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editProgram('@program.Id')">
                                            <i class="bi bi-pencil"></i>
                                            تعديل
                                        </button>
                                        <button class="btn btn-sm btn-outline-info" onclick="viewParticipants('@program.Id')">
                                            <i class="bi bi-people"></i>
                                            المشاركين
                                        </button>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="bi bi-book"></i>
                        </div>
                        <h5>لا توجد برامج تدريبية</h5>
                        <p>لم يتم العثور على أي برامج تدريبية</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newTrainingModal">
                            <i class="bi bi-plus-circle"></i>
                            إضافة برنامج تدريبي
                        </button>
                    </div>
                }
            </div>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Training Categories Chart -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">التدريب حسب الفئة</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="trainingCategoriesChart" style="height: 250px;"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Upcoming Trainings -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">التدريبات القادمة</h5>
            </div>
            <div class="card-body">
                <div class="upcoming-trainings-list">
                    @if (Model.UpcomingTrainings?.Any() == true)
                    {
                        @foreach (var training in Model.UpcomingTrainings.Take(5))
                        {
                            <div class="upcoming-training-item">
                                <div class="training-date">
                                    <span class="day">@training.StartDate?.Day</span>
                                    <span class="month">@training.StartDate?.ToString("MMM")</span>
                                </div>
                                <div class="training-info">
                                    <h6>@training.Title</h6>
                                    <small>@training.Instructor</small>
                                    <div class="training-time">
                                        <i class="bi bi-clock"></i>
                                        @training.StartTime - @training.EndTime
                                    </div>
                                </div>
                                <div class="training-participants">
                                    <span class="participants-count">@training.ParticipantsCount</span>
                                    <i class="bi bi-people"></i>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center text-muted">
                            <i class="bi bi-calendar-x"></i>
                            <p>لا توجد تدريبات قادمة</p>
                        </div>
                    }
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="quick-actions">
                    <button class="quick-action-btn" data-bs-toggle="modal" data-bs-target="#newTrainingModal">
                        <i class="bi bi-plus-circle"></i>
                        <span>برنامج جديد</span>
                    </button>
                    <button class="quick-action-btn" onclick="viewTrainingCalendar()">
                        <i class="bi bi-calendar-event"></i>
                        <span>التقويم التدريبي</span>
                    </button>
                    <button class="quick-action-btn" onclick="viewTrainingReports()">
                        <i class="bi bi-graph-up"></i>
                        <span>تقارير التدريب</span>
                    </button>
                    <button class="quick-action-btn" onclick="manageInstructors()">
                        <i class="bi bi-person-badge"></i>
                        <span>إدارة المدربين</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        $(document).ready(function() {
            loadTrainingCategoriesChart();
        });

        function loadTrainingCategoriesChart() {
            const ctx = document.getElementById('trainingCategoriesChart').getContext('2d');
            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['تقنية', 'إدارية', 'مهارات شخصية', 'السلامة', 'أخرى'],
                    datasets: [{
                        data: [
                            @(Model.TechnicalTrainings ?? 0),
                            @(Model.ManagementTrainings ?? 0),
                            @(Model.SoftSkillsTrainings ?? 0),
                            @(Model.SafetyTrainings ?? 0),
                            @(Model.OtherTrainings ?? 0)
                        ],
                        backgroundColor: [
                            '#1e3a8a',
                            '#22c55e',
                            '#f59e0b',
                            '#ef4444',
                            '#8b5cf6'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: {
                                    family: 'Tajawal'
                                },
                                padding: 15
                            }
                        }
                    }
                }
            });
        }

        function filterTrainings(status) {
            $('.btn-group .btn').removeClass('active');
            $(`button[onclick="filterTrainings('${status}')"]`).addClass('active');

            $('.training-program-item').each(function() {
                const itemStatus = $(this).data('status');
                const show = status === 'all' || itemStatus === status;
                $(this).toggle(show);
            });
        }

        function viewProgram(id) {
            window.location.href = `@Url.Action("Details", "Training")/${id}`;
        }

        function editProgram(id) {
            window.location.href = `@Url.Action("Edit", "Training")/${id}`;
        }

        function startProgram(id) {
            if (confirm('هل تريد بدء هذا البرنامج التدريبي؟')) {
                $.ajax({
                    url: '@Url.Action("Start", "Training")',
                    type: 'POST',
                    data: {
                        id: id,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            showToast('تم بدء البرنامج التدريبي بنجاح', 'success');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showToast('حدث خطأ: ' + result.message, 'error');
                        }
                    },
                    error: function() {
                        showToast('حدث خطأ أثناء بدء البرنامج التدريبي', 'error');
                    }
                });
            }
        }

        function viewParticipants(id) {
            window.location.href = `@Url.Action("Participants", "Training")/${id}`;
        }

        function viewTrainingCalendar() {
            window.location.href = '@Url.Action("Calendar", "Training")';
        }

        function viewTrainingReports() {
            window.location.href = '@Url.Action("Reports", "Training")';
        }

        function manageInstructors() {
            window.location.href = '@Url.Action("Instructors", "Training")';
        }

        function exportTraining(format) {
            showToast(`جاري تصدير البيانات بصيغة ${format.toUpperCase()}...`, 'info');
            setTimeout(() => {
                window.open(`@Url.Action("Export", "Training")?format=${format}`, '_blank');
                showToast('تم تصدير البيانات بنجاح', 'success');
            }, 2000);
        }

        function showToast(message, type = 'info') {
            const bgClass = type === 'success' ? 'bg-success' :
                           type === 'warning' ? 'bg-warning' :
                           type === 'error' ? 'bg-danger' : 'bg-info';

            const toast = $(`
                <div class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `);

            let container = $('.toast-container');
            if (container.length === 0) {
                container = $('<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
                $('body').append(container);
            }

            container.append(toast);
            const bsToast = new bootstrap.Toast(toast[0]);
            bsToast.show();

            toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    </script>
}
