/* Base Styles */
html {
  font-size: 14px;
  position: relative;
  min-height: 100%;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

body {
  margin-bottom: 60px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* RTL Support */
.rtl {
  direction: rtl;
  text-align: right;
}

.rtl .text-left {
  text-align: right !important;
}

.rtl .text-right {
  text-align: left !important;
}

/* Focus Styles */
.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

/* Admin Layout */
.wrapper {
  display: flex;
  width: 100%;
  align-items: stretch;
}

/* Sidebar */
.sidebar {
  min-width: 250px;
  max-width: 250px;
  background: #343a40;
  color: #fff;
  transition: all 0.3s;
  min-height: 100vh;
}

.sidebar.active {
  margin-left: -250px;
}

.sidebar-header {
  padding: 20px;
  background: #495057;
  text-align: center;
}

.sidebar-header h3 {
  color: #fff;
  margin: 0;
  font-size: 1.2rem;
}

.sidebar ul.components {
  padding: 20px 0;
}

.sidebar ul li {
  padding: 0;
}

.sidebar ul li a {
  padding: 15px 20px;
  font-size: 1rem;
  display: block;
  color: #adb5bd;
  text-decoration: none;
  transition: all 0.3s;
}

.sidebar ul li a:hover,
.sidebar ul li.active > a {
  color: #fff;
  background: #495057;
}

.sidebar ul li a i {
  margin-right: 10px;
  width: 20px;
}

/* Content */
#content {
  width: 100%;
  padding: 0;
  min-height: 100vh;
  transition: all 0.3s;
}

/* Cards */
.card {
  border: none;
  border-radius: 10px;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
  background-color: #f8f9fc;
  border-bottom: 1px solid #e3e6f0;
  border-radius: 10px 10px 0 0 !important;
}

/* Border Colors */
.border-left-primary {
  border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
  border-left: 0.25rem solid #1cc88a !important;
}

.border-left-warning {
  border-left: 0.25rem solid #f6c23e !important;
}

.border-left-info {
  border-left: 0.25rem solid #36b9cc !important;
}

/* Text Colors */
.text-gray-800 {
  color: #5a5c69 !important;
}

.text-gray-500 {
  color: #858796 !important;
}

.text-gray-300 {
  color: #dddfeb !important;
}

/* Icon Circle */
.icon-circle {
  height: 2.5rem;
  width: 2.5rem;
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    margin-left: -250px;
  }

  .sidebar.active {
    margin-left: 0;
  }

  #sidebarCollapse span {
    display: none;
  }
}

/* Login Page */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Buttons */
.btn-block {
  display: block;
  width: 100%;
  text-align: center;
  padding: 15px;
  border-radius: 8px;
  transition: all 0.3s;
}

.btn-block:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Footer */
.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}