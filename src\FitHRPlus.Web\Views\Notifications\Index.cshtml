@model FitHRPlus.Web.Models.Notifications.NotificationListViewModel
@{
    ViewData["Title"] = "الإشعارات";
}

<!-- Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-title">
            <h1><i class="bi bi-bell"></i> الإشعارات</h1>
            <p>إدارة ومتابعة جميع الإشعارات والتنبيهات</p>
        </div>
        <div class="page-actions">
            <button type="button" class="btn btn-success" onclick="markAllAsRead()">
                <i class="bi bi-check-all"></i>
                تسجيل الكل كمقروء
            </button>
            <button type="button" class="btn btn-danger" onclick="deleteAllRead()">
                <i class="bi bi-trash"></i>
                حذف المقروءة
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-primary">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@Model.TotalNotifications</h3>
                        <p>إجمالي الإشعارات</p>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-bell"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: 100%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-warning">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@Model.UnreadCount</h3>
                        <p>غير مقروءة</p>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-bell-fill"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: @(Model.TotalNotifications > 0 ? (Model.UnreadCount * 100 / Model.TotalNotifications) : 0)%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-success">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@Model.ReadCount</h3>
                        <p>مقروءة</p>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-check-circle"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: @(Model.TotalNotifications > 0 ? (Model.ReadCount * 100 / Model.TotalNotifications) : 0)%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-info">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@Model.TodayCount</h3>
                        <p>اليوم</p>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-calendar-day"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: @(Model.TotalNotifications > 0 ? (Model.TodayCount * 100 / Model.TotalNotifications) : 0)%"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <div class="col-lg-8">
        <!-- Notifications List Card -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">قائمة الإشعارات</h5>
                <div class="card-actions">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary active" onclick="filterNotifications('all')">
                            الكل
                        </button>
                        <button type="button" class="btn btn-outline-warning" onclick="filterNotifications('unread')">
                            غير مقروءة
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="filterNotifications('read')">
                            مقروءة
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">

                @if (Model?.Notifications?.Any() == true)
                {
                    <div class="notifications-list">
                        @foreach (var notification in Model.Notifications)
                        {
                            <div class="notification-item @(notification.IsRead ? "read" : "unread")"
                                 data-notification-id="@notification.Id"
                                 data-is-read="@notification.IsRead.ToString().ToLower()"
                                 onclick="markAsRead('@notification.Id')">
                                <div class="notification-content">
                                    <div class="notification-icon">
                                        @switch (notification.Type)
                                        {
                                            case "Info":
                                                <i class="bi bi-info-circle text-info"></i>
                                                break;
                                            case "Warning":
                                                <i class="bi bi-exclamation-triangle text-warning"></i>
                                                break;
                                            case "Error":
                                                <i class="bi bi-x-circle text-danger"></i>
                                                break;
                                            case "Success":
                                                <i class="bi bi-check-circle text-success"></i>
                                                break;
                                            default:
                                                <i class="bi bi-bell text-primary"></i>
                                                break;
                                        }
                                    </div>
                                    <div class="notification-body">
                                        <div class="notification-header">
                                            <h6 class="notification-title">@notification.Title</h6>
                                            <div class="notification-meta">
                                                @if (!notification.IsRead)
                                                {
                                                    <span class="badge badge-new">جديد</span>
                                                }
                                                <span class="notification-time">
                                                    <i class="bi bi-clock"></i>
                                                    @notification.TimeAgo
                                                </span>
                                            </div>
                                        </div>
                                        <p class="notification-message">@notification.Message</p>
                                        @if (!string.IsNullOrEmpty(notification.ActionUrl))
                                        {
                                            <div class="notification-actions">
                                                <a href="@notification.ActionUrl" class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation();">
                                                    <i class="bi bi-eye"></i>
                                                    عرض التفاصيل
                                                </a>
                                            </div>
                                        }
                                    </div>
                                    <div class="notification-menu">
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-ghost dropdown-toggle" type="button" data-bs-toggle="dropdown" onclick="event.stopPropagation();">
                                                <i class="bi bi-three-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                @if (!notification.IsRead)
                                                {
                                                    <li><a class="dropdown-item" href="#" onclick="markAsRead('@notification.Id'); event.stopPropagation();"><i class="bi bi-check me-2"></i>تسجيل كمقروء</a></li>
                                                }
                                                else
                                                {
                                                    <li><a class="dropdown-item" href="#" onclick="markAsUnread('@notification.Id'); event.stopPropagation();"><i class="bi bi-arrow-counterclockwise me-2"></i>تسجيل كغير مقروء</a></li>
                                                }
                                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteNotification('@notification.Id'); event.stopPropagation();"><i class="bi bi-trash me-2"></i>حذف</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="bi bi-bell-slash"></i>
                        </div>
                        <h5>لا توجد إشعارات</h5>
                        <p>لم يتم العثور على أي إشعارات حالياً</p>
                    </div>
                }
            </div>

            <!-- Pagination -->
            @if (Model.TotalPages > 1)
            {
                <div class="card-footer">
                    <nav aria-label="صفحات الإشعارات">
                        <ul class="pagination justify-content-center mb-0">
                            @if (Model.CurrentPage > 1)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="?page=@(Model.CurrentPage - 1)">
                                        <i class="bi bi-chevron-right"></i>
                                        السابق
                                    </a>
                                </li>
                            }

                            @for (int i = 1; i <= Model.TotalPages; i++)
                            {
                                <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                    <a class="page-link" href="?page=@i">@i</a>
                                </li>
                            }

                            @if (Model.CurrentPage < Model.TotalPages)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="?page=@(Model.CurrentPage + 1)">
                                        التالي
                                        <i class="bi bi-chevron-left"></i>
                                    </a>
                                </li>
                            }
                        </ul>
                    </nav>
                </div>
            }
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Notification Types Chart -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">توزيع الإشعارات</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="notificationTypesChart" style="height: 250px;"></canvas>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="markAllAsRead()">
                        <i class="bi bi-check-all"></i>
                        <span>تسجيل الكل كمقروء</span>
                    </button>
                    <button class="quick-action-btn" onclick="deleteAllRead()">
                        <i class="bi bi-trash"></i>
                        <span>حذف المقروءة</span>
                    </button>
                    <button class="quick-action-btn" onclick="refreshNotifications()">
                        <i class="bi bi-arrow-clockwise"></i>
                        <span>تحديث</span>
                    </button>
                    <button class="quick-action-btn" onclick="exportNotifications()">
                        <i class="bi bi-download"></i>
                        <span>تصدير</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Notification Settings -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">إعدادات الإشعارات</h5>
            </div>
            <div class="card-body">
                <div class="notification-settings">
                    <div class="setting-item">
                        <div class="setting-info">
                            <h6>الإشعارات الفورية</h6>
                            <p>تلقي إشعارات فورية للأحداث المهمة</p>
                        </div>
                        <div class="setting-control">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="instantNotifications" checked>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="setting-info">
                            <h6>إشعارات البريد الإلكتروني</h6>
                            <p>إرسال الإشعارات عبر البريد الإلكتروني</p>
                        </div>
                        <div class="setting-control">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="emailNotifications">
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="setting-info">
                            <h6>الأصوات</h6>
                            <p>تشغيل الأصوات عند وصول إشعار جديد</p>
                        </div>
                        <div class="setting-control">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="soundNotifications" checked>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        $(document).ready(function() {
            loadNotificationChart();
            initializeNotificationSettings();
        });

        function loadNotificationChart() {
            const ctx = document.getElementById('notificationTypesChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['معلومات', 'تحذيرات', 'أخطاء', 'نجاح'],
                    datasets: [{
                        data: [
                            @(Model.Notifications?.Count(n => n.Type == "Info") ?? 0),
                            @(Model.Notifications?.Count(n => n.Type == "Warning") ?? 0),
                            @(Model.Notifications?.Count(n => n.Type == "Error") ?? 0),
                            @(Model.Notifications?.Count(n => n.Type == "Success") ?? 0)
                        ],
                        backgroundColor: [
                            '#17a2b8',
                            '#ffc107',
                            '#dc3545',
                            '#28a745'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: {
                                    family: 'Tajawal'
                                },
                                padding: 20
                            }
                        }
                    }
                }
            });
        }

        function initializeNotificationSettings() {
            // Load saved settings
            const settings = JSON.parse(localStorage.getItem('notificationSettings') || '{}');

            $('#instantNotifications').prop('checked', settings.instant !== false);
            $('#emailNotifications').prop('checked', settings.email === true);
            $('#soundNotifications').prop('checked', settings.sound !== false);

            // Save settings on change
            $('.form-check-input').on('change', function() {
                const settings = {
                    instant: $('#instantNotifications').is(':checked'),
                    email: $('#emailNotifications').is(':checked'),
                    sound: $('#soundNotifications').is(':checked')
                };
                localStorage.setItem('notificationSettings', JSON.stringify(settings));
            });
        }

        function filterNotifications(type) {
            // Update active button
            $('.btn-group .btn').removeClass('active');
            $(`button[onclick="filterNotifications('${type}')"]`).addClass('active');

            // Filter notifications
            $('.notification-item').each(function() {
                var isRead = $(this).data('is-read');
                var show = false;

                switch(type) {
                    case 'all':
                        show = true;
                        break;
                    case 'read':
                        show = isRead === true;
                        break;
                    case 'unread':
                        show = isRead === false;
                        break;
                }

                $(this).toggle(show);
            });
        }

        function markAsRead(notificationId) {
            $.ajax({
                url: '@Url.Action("MarkAsRead", "Notifications")',
                type: 'POST',
                data: {
                    id: notificationId,
                    __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                },
                success: function(result) {
                    if (result.success) {
                        var item = $(`[data-notification-id="${notificationId}"]`);
                        item.removeClass('unread').addClass('read');
                        item.data('is-read', true);
                        item.find('.badge-new').remove();
                        item.find('.notification-title').removeClass('fw-bold');

                        // Update counters
                        updateNotificationCounters();

                        // Play sound if enabled
                        playNotificationSound('read');
                    }
                },
                error: function() {
                    showToast('خطأ في تسجيل الإشعار كمقروء', 'error');
                }
            });
        }

        function refreshNotifications() {
            location.reload();
        }

        function exportNotifications() {
            window.open('@Url.Action("Export", "Notifications")', '_blank');
        }

        function playNotificationSound(type) {
            const settings = JSON.parse(localStorage.getItem('notificationSettings') || '{}');
            if (settings.sound !== false) {
                // Create audio element and play sound
                const audio = new Audio('/sounds/notification-' + type + '.mp3');
                audio.volume = 0.3;
                audio.play().catch(() => {
                    // Ignore audio play errors
                });
            }
        }

        function showToast(message, type = 'info') {
            // Create toast notification
            const toast = $(`
                <div class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : 'success'} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `);

            // Add to toast container or create one
            let container = $('.toast-container');
            if (container.length === 0) {
                container = $('<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
                $('body').append(container);
            }

            container.append(toast);
            const bsToast = new bootstrap.Toast(toast[0]);
            bsToast.show();

            // Remove after hiding
            toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }

        function markAsUnread(notificationId) {
            $.ajax({
                url: '@Url.Action("MarkAsUnread", "Notifications")',
                type: 'POST',
                data: {
                    id: notificationId,
                    __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                },
                success: function(result) {
                    if (result.success) {
                        var item = $(`[data-notification-id="${notificationId}"]`);
                        item.removeClass('read').addClass('unread');
                        item.data('is-read', false);
                        item.find('.notification-title').addClass('fw-bold');

                        // Add new badge if not exists
                        if (item.find('.badge-new').length === 0) {
                            item.find('.notification-meta').prepend('<span class="badge badge-new">جديد</span>');
                        }

                        // Update counters
                        updateNotificationCounters();
                    }
                },
                error: function() {
                    showToast('خطأ في تسجيل الإشعار كغير مقروء', 'error');
                }
            });
        }

        function deleteNotification(notificationId) {
            if (confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
                $.ajax({
                    url: '@Url.Action("Delete", "Notifications")',
                    type: 'POST',
                    data: {
                        id: notificationId,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            $(`[data-notification-id="${notificationId}"]`).fadeOut(300, function() {
                                $(this).remove();
                                updateNotificationCounters();
                            });
                            showToast('تم حذف الإشعار بنجاح', 'success');
                        } else {
                            showToast('حدث خطأ: ' + result.message, 'error');
                        }
                    },
                    error: function() {
                        showToast('حدث خطأ أثناء حذف الإشعار', 'error');
                    }
                });
            }
        }

        function markAllAsRead() {
            if (confirm('هل أنت متأكد من تسجيل جميع الإشعارات كمقروءة؟')) {
                // Show loading
                const btn = $('button[onclick="markAllAsRead()"]');
                const originalText = btn.html();
                btn.html('<i class="bi bi-arrow-repeat spin"></i> جاري التحديث...');
                btn.prop('disabled', true);

                $.ajax({
                    url: '@Url.Action("MarkAllAsRead", "Notifications")',
                    type: 'POST',
                    data: {
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            showToast('تم تسجيل جميع الإشعارات كمقروءة', 'success');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showToast('حدث خطأ: ' + result.message, 'error');
                            btn.html(originalText);
                            btn.prop('disabled', false);
                        }
                    },
                    error: function() {
                        showToast('حدث خطأ أثناء تسجيل الإشعارات', 'error');
                        btn.html(originalText);
                        btn.prop('disabled', false);
                    }
                });
            }
        }

        function deleteAllRead() {
            if (confirm('هل أنت متأكد من حذف جميع الإشعارات المقروءة؟')) {
                // Show loading
                const btn = $('button[onclick="deleteAllRead()"]');
                const originalText = btn.html();
                btn.html('<i class="bi bi-arrow-repeat spin"></i> جاري الحذف...');
                btn.prop('disabled', true);

                $.ajax({
                    url: '@Url.Action("DeleteAllRead", "Notifications")',
                    type: 'POST',
                    data: {
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            showToast('تم حذف جميع الإشعارات المقروءة', 'success');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showToast('حدث خطأ: ' + result.message, 'error');
                            btn.html(originalText);
                            btn.prop('disabled', false);
                        }
                    },
                    error: function() {
                        showToast('حدث خطأ أثناء حذف الإشعارات', 'error');
                        btn.html(originalText);
                        btn.prop('disabled', false);
                    }
                });
            }
        }

        function updateNotificationCounters() {
            // Update counters without full page reload
            $.get('@Url.Action("GetStatistics", "Notifications")', function(data) {
                $('.stats-card-primary h3').text(data.totalNotifications);
                $('.stats-card-warning h3').text(data.unreadCount);
                $('.stats-card-success h3').text(data.readCount);
                $('.stats-card-info h3').text(data.todayCount);

                // Update progress bars
                const total = data.totalNotifications || 1;
                $('.stats-card-warning .progress-bar').css('width', (data.unreadCount * 100 / total) + '%');
                $('.stats-card-success .progress-bar').css('width', (data.readCount * 100 / total) + '%');
                $('.stats-card-info .progress-bar').css('width', (data.todayCount * 100 / total) + '%');
            });
        }

        // Auto-refresh notifications every 30 seconds
        setInterval(function() {
            updateNotificationCounters();
        }, 30000);
    </script>
}
