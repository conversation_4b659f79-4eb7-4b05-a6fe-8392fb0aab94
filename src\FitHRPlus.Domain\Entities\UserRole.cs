using FitHRPlus.Domain.Common;
using System;
using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Domain.Entities
{
    /// <summary>
    /// User role assignment entity
    /// كيان تخصيص الأدوار للمستخدمين
    /// </summary>
    public class UserRole : BaseEntity
    {
        /// <summary>
        /// User ID
        /// معرف المستخدم
        /// </summary>
        [Required]
        public Guid UserId { get; set; }

        /// <summary>
        /// Role ID
        /// معرف الدور
        /// </summary>
        [Required]
        public Guid RoleId { get; set; }

        /// <summary>
        /// Company ID - roles are company-specific
        /// معرف الشركة - الأدوار خاصة بكل شركة
        /// </summary>
        [Required]
        public Guid CompanyId { get; set; }

        /// <summary>
        /// Date when the role was assigned
        /// تاريخ تخصيص الدور
        /// </summary>
        public DateTime AssignedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// User who assigned this role
        /// المستخدم الذي خصص هذا الدور
        /// </summary>
        public Guid? AssignedBy { get; set; }

        // Navigation Properties - خصائص التنقل

        /// <summary>
        /// User entity
        /// كيان المستخدم
        /// </summary>
        public virtual User User { get; set; } = null!;

        /// <summary>
        /// Role entity
        /// كيان الدور
        /// </summary>
        public virtual Role Role { get; set; } = null!;

        /// <summary>
        /// Company entity
        /// كيان الشركة
        /// </summary>
        public virtual Company Company { get; set; } = null!;

        /// <summary>
        /// User who assigned this role
        /// المستخدم الذي خصص هذا الدور
        /// </summary>
        public virtual User? AssignedByUser { get; set; }
    }
}