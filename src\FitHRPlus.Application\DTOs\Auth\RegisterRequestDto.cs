using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Application.DTOs.Auth
{
    /// <summary>
    /// User registration request data transfer object
    /// كائنة نقل البيانات لطلب تسجيل المستخدم
    /// </summary>
    public class RegisterRequestDto
    {
        /// <summary>
        /// Username (unique)
        /// اسم المستخدم (فريد)
        /// </summary>
        [Required(ErrorMessage = "Username is required")]
        [StringLength(100, MinimumLength = 3, ErrorMessage = "Username must be between 3 and 100 characters")]
        [RegularExpression(@"^[a-zA-Z0-9_.-]+$", ErrorMessage = "Username can only contain letters, numbers, dots, hyphens, and underscores")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// Email address (unique)
        /// البريد الإلكتروني (فريد)
        /// </summary>
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Password
        /// كلمة المرور
        /// </summary>
        [Required(ErrorMessage = "Password is required")]
        [StringLength(100, MinimumLength = 8, ErrorMessage = "Password must be between 8 and 100 characters")]
        [RegularExpression(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]", 
            ErrorMessage = "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character")]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// Password confirmation
        /// تأكيد كلمة المرور
        /// </summary>
        [Required(ErrorMessage = "Password confirmation is required")]
        [Compare("Password", ErrorMessage = "Password and confirmation password do not match")]
        public string ConfirmPassword { get; set; } = string.Empty;

        /// <summary>
        /// First name
        /// الاسم الأول
        /// </summary>
        [Required(ErrorMessage = "First name is required")]
        [StringLength(100, ErrorMessage = "First name cannot exceed 100 characters")]
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// Last name
        /// الاسم الأخير
        /// </summary>
        [Required(ErrorMessage = "Last name is required")]
        [StringLength(100, ErrorMessage = "Last name cannot exceed 100 characters")]
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// Phone number (optional)
        /// رقم الهاتف (اختياري)
        /// </summary>
        [Phone(ErrorMessage = "Invalid phone number format")]
        [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
        public string? Phone { get; set; }

        /// <summary>
        /// Preferred language (ar/en)
        /// اللغة المفضلة (ar/en)
        /// </summary>
        [StringLength(5, ErrorMessage = "Language code cannot exceed 5 characters")]
        [RegularExpression(@"^(ar|en)$", ErrorMessage = "Language must be 'ar' or 'en'")]
        public string PreferredLanguage { get; set; } = "ar";

        /// <summary>
        /// Time zone
        /// المنطقة الزمنية
        /// </summary>
        [StringLength(50, ErrorMessage = "Time zone cannot exceed 50 characters")]
        public string TimeZone { get; set; } = "Africa/Cairo";

        /// <summary>
        /// Terms and conditions acceptance
        /// قبول الشروط والأحكام
        /// </summary>
        [Range(typeof(bool), "true", "true", ErrorMessage = "You must accept the terms and conditions")]
        public bool AcceptTerms { get; set; } = false;
    }
}
