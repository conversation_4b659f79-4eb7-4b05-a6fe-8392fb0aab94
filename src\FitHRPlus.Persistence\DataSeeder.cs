using FitHRPlus.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Text;

namespace FitHRPlus.Persistence
{
    /// <summary>
    /// Data seeder for initial system setup
    /// بذر البيانات للإعداد الأولي للنظام
    /// </summary>
    public static class DataSeeder
    {
        /// <summary>
        /// Seed initial data including default admin user
        /// بذر البيانات الأولية بما في ذلك المستخدم الإداري الافتراضي
        /// </summary>
        public static async Task SeedAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<FitHRContext>();
            var loggerFactory = scope.ServiceProvider.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger("DataSeeder");

            try
            {
                // Apply pending migrations
                // await context.Database.MigrateAsync();

                // Seed default roles
                await SeedRolesAsync(context, logger);

                // Seed default company
                var defaultCompany = await SeedDefaultCompanyAsync(context, logger);

                // Seed default admin user
                await SeedDefaultAdminUserAsync(context, defaultCompany, logger);

                await context.SaveChangesAsync();
                logger.LogInformation("Data seeding completed successfully");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred while seeding data");
                throw;
            }
        }

        private static async Task SeedRolesAsync(FitHRContext context, ILogger logger)
        {
            if (!await context.Roles.AnyAsync())
            {
                var roles = new[]
                {
                    new Role
                    {
                        Id = Guid.NewGuid(),
                        Name = "SuperAdmin",
                        NameAr = "مدير النظام",
                        Description = "System Super Administrator with full access",
                        DescriptionAr = "مدير النظام الفائق مع صلاحية كاملة",
                        IsSystemRole = true,
                        Permissions = "ALL",
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new Role
                    {
                        Id = Guid.NewGuid(),
                        Name = "Admin",
                        NameAr = "مدير",
                        Description = "Company Administrator",
                        DescriptionAr = "مدير الشركة",
                        IsSystemRole = false,
                        Permissions = "COMPANY_ADMIN,USER_MANAGEMENT,EMPLOYEE_MANAGEMENT,ATTENDANCE_MANAGEMENT",
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new Role
                    {
                        Id = Guid.NewGuid(),
                        Name = "HR",
                        NameAr = "موارد بشرية",
                        Description = "Human Resources Manager",
                        DescriptionAr = "مدير الموارد البشرية",
                        IsSystemRole = false,
                        Permissions = "EMPLOYEE_MANAGEMENT,ATTENDANCE_VIEW,LEAVE_MANAGEMENT",
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new Role
                    {
                        Id = Guid.NewGuid(),
                        Name = "Employee",
                        NameAr = "موظف",
                        Description = "Regular Employee",
                        DescriptionAr = "موظف عادي",
                        IsSystemRole = false,
                        Permissions = "ATTENDANCE_SELF,LEAVE_REQUEST",
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    }
                };

                await context.Roles.AddRangeAsync(roles);
                logger.LogInformation("Default roles seeded successfully");
            }
        }

        private static async Task<Company> SeedDefaultCompanyAsync(FitHRContext context, ILogger logger)
        {
            var existingCompany = await context.Companies.FirstOrDefaultAsync();
            if (existingCompany != null)
            {
                return existingCompany;
            }

            var defaultCompany = new Company
            {
                Id = Guid.NewGuid(),
                Name = "FIT HR Plus Demo Company",
                NameAr = "شركة FIT HR Plus التجريبية",
                TaxNumber = "*********",
                CommercialRegister = "CR*********",
                Address = "123 Business Street, Cairo, Egypt",
                AddressAr = "123 شارع الأعمال، القاهرة، مصر",
                Phone = "+***********",
                Email = "<EMAIL>",
                Website = "https://fithrplus-demo.com",
                Industry = "Technology",
                EmployeeCount = 0,
                EstablishedDate = DateTime.Today.AddYears(-5),
                TimeZone = "Africa/Cairo",
                Currency = "EGP",
                FiscalYearStart = 1,
                IsActive = true,
                SubscriptionPlan = "Premium",
                SubscriptionExpiry = DateTime.UtcNow.AddYears(1),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await context.Companies.AddAsync(defaultCompany);
            logger.LogInformation("Default company seeded successfully");
            return defaultCompany;
        }

        private static async Task SeedDefaultAdminUserAsync(
            FitHRContext context,
            Company defaultCompany,
            ILogger logger)
        {
            if (!await context.Users.AnyAsync())
            {
                // Create default admin user
                var defaultPassword = "Admin123!@#";
                var (hashedPassword, salt) = HashPassword(defaultPassword);

                var adminUser = new User
                {
                    Id = Guid.NewGuid(),
                    Username = "admin",
                    Email = "<EMAIL>",
                    PasswordHash = hashedPassword,
                    Salt = salt,
                    FirstName = "System",
                    LastName = "Administrator",
                    Phone = "+***********",
                    PreferredLanguage = "ar",
                    TimeZone = "Africa/Cairo",
                    IsActive = true,
                    IsEmailVerified = true,
                    IsPhoneVerified = false,
                    TwoFactorEnabled = false,
                    LastPasswordChangeAt = DateTime.UtcNow,
                    FailedLoginAttempts = 0,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                await context.Users.AddAsync(adminUser);

                // Assign SuperAdmin role to the default user
                var superAdminRole = await context.Roles.FirstOrDefaultAsync(r => r.Name == "SuperAdmin");
                if (superAdminRole != null)
                {
                    var userRole = new UserRole
                    {
                        Id = Guid.NewGuid(),
                        UserId = adminUser.Id,
                        RoleId = superAdminRole.Id,
                        CompanyId = defaultCompany.Id,
                        IsActive = true,
                        AssignedAt = DateTime.UtcNow,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    await context.UserRoles.AddAsync(userRole);
                }

                logger.LogInformation("Default admin user created successfully");
                logger.LogInformation("Default Login Credentials:");
                logger.LogInformation("Username: admin");
                logger.LogInformation("Password: Admin123!@#");
            }
        }

        /// <summary>
        /// Password hashing compatible with PasswordHashingService
        /// تشفير كلمة المرور متوافق مع خدمة تشفير كلمات المرور
        /// </summary>
        private static (string hashedPassword, string salt) HashPassword(string password)
        {
            // Generate salt (32 bytes)
            var saltBytes = new byte[32];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(saltBytes);
            }
            var salt = Convert.ToBase64String(saltBytes);

            // Hash password with PBKDF2 (same as PasswordHashingService)
            using (var pbkdf2 = new Rfc2898DeriveBytes(password, saltBytes, 100000, HashAlgorithmName.SHA256))
            {
                var hashBytes = pbkdf2.GetBytes(32);
                var hashedPassword = Convert.ToBase64String(hashBytes);
                return (hashedPassword, salt);
            }
        }
    }
}
