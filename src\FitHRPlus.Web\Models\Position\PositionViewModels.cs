using System.ComponentModel.DataAnnotations;
using FitHRPlus.Web.Models.Department;

namespace FitHRPlus.Web.Models.Position
{
    /// <summary>
    /// Position view model
    /// نموذج عرض المنصب
    /// </summary>
    public class PositionViewModel
    {
        public Guid Id { get; set; }

        [Required(ErrorMessage = "Company ID is required")]
        public Guid CompanyId { get; set; }

        [Required(ErrorMessage = "Department is required")]
        [Display(Name = "Department")]
        public Guid DepartmentId { get; set; }

        [Display(Name = "Department Name")]
        public string? DepartmentName { get; set; }

        [Required(ErrorMessage = "Position title is required")]
        [StringLength(100, ErrorMessage = "Position title cannot exceed 100 characters")]
        [Display(Name = "Position Title")]
        public string Title { get; set; } = string.Empty;

        [Required(ErrorMessage = "Arabic position title is required")]
        [StringLength(100, ErrorMessage = "Arabic position title cannot exceed 100 characters")]
        [Display(Name = "Position Title (Arabic)")]
        public string TitleAr { get; set; } = string.Empty;

        [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
        [Display(Name = "Description")]
        public string? Description { get; set; }

        [StringLength(1000, ErrorMessage = "Arabic description cannot exceed 1000 characters")]
        [Display(Name = "Description (Arabic)")]
        public string? DescriptionAr { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "Minimum salary must be a positive number")]
        [Display(Name = "Minimum Salary")]
        public decimal? MinSalary { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "Maximum salary must be a positive number")]
        [Display(Name = "Maximum Salary")]
        public decimal? MaxSalary { get; set; }

        [Display(Name = "Employee Count")]
        public int EmployeeCount { get; set; }

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "Created At")]
        public DateTime CreatedAt { get; set; }

        [Display(Name = "Updated At")]
        public DateTime UpdatedAt { get; set; }

        // Navigation properties for form dropdowns
        public List<DepartmentOptionViewModel> Departments { get; set; } = new();

        // Validation
        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (MinSalary.HasValue && MaxSalary.HasValue && MinSalary > MaxSalary)
            {
                yield return new ValidationResult(
                    "Minimum salary cannot be greater than maximum salary",
                    new[] { nameof(MinSalary), nameof(MaxSalary) });
            }
        }
    }

    /// <summary>
    /// Position list view model
    /// نموذج عرض قائمة المناصب
    /// </summary>
    public class PositionListViewModel
    {
        public List<PositionViewModel> Positions { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SearchTerm { get; set; }
        public Guid? DepartmentId { get; set; }

        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
    }

    /// <summary>
    /// Position option view model for dropdowns
    /// نموذج خيار المنصب للقوائم المنسدلة
    /// </summary>
    public class PositionOptionViewModel
    {
        public Guid Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string TitleAr { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string DisplayName => $"{Title} / {TitleAr} ({DepartmentName})";
    }
}
