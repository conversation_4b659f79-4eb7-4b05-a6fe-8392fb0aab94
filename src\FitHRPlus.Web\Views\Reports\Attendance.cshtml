@model FitHRPlus.Web.Models.Reports.AttendanceReportsViewModel
@{
    ViewData["Title"] = "تقارير الحضور";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        تقارير الحضور
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Filter Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-filter me-2"></i>
                                        المرشحات
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <form method="get" class="row g-3">
                                        <div class="col-md-3">
                                            <label for="fromDate" class="form-label">من تاريخ</label>
                                            <input type="date" class="form-control" id="fromDate" name="fromDate" value="@Model.FromDate.ToString("yyyy-MM-dd")" />
                                        </div>
                                        <div class="col-md-3">
                                            <label for="toDate" class="form-label">إلى تاريخ</label>
                                            <input type="date" class="form-control" id="toDate" name="toDate" value="@Model.ToDate.ToString("yyyy-MM-dd")" />
                                        </div>
                                        <div class="col-md-3">
                                            <label for="department" class="form-label">القسم</label>
                                            <select class="form-select" id="department" name="department">
                                                <option value="">جميع الأقسام</option>
                                                <option value="hr">الموارد البشرية</option>
                                                <option value="it">تكنولوجيا المعلومات</option>
                                                <option value="finance">المالية</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="reportType" class="form-label">نوع التقرير</label>
                                            <select class="form-select" id="reportType" name="reportType">
                                                <option value="daily">يومي</option>
                                                <option value="weekly">أسبوعي</option>
                                                <option value="monthly">شهري</option>
                                                <option value="summary">ملخص</option>
                                            </select>
                                        </div>
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-success">
                                                <i class="fas fa-search me-1"></i>
                                                إنشاء التقرير
                                            </button>
                                            <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                                                <i class="fas fa-undo me-1"></i>
                                                إعادة تعيين
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">معدل الحضور</h6>
                                            <h3 class="mb-0">95%</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-user-check fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">حالات التأخير</h6>
                                            <h3 class="mb-0">12</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-clock fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">حالات الغياب</h6>
                                            <h3 class="mb-0">5</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-user-times fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">ساعات العمل الإضافي</h6>
                                            <h3 class="mb-0">45</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-plus-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Attendance Data Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الموظف</th>
                                    <th>القسم</th>
                                    <th>التاريخ</th>
                                    <th>وقت الدخول</th>
                                    <th>وقت الخروج</th>
                                    <th>ساعات العمل</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="7" class="text-center text-muted py-4">
                                        <i class="fas fa-info-circle me-2"></i>
                                        لا توجد بيانات حضور متاحة للفترة المحددة
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Charts Section -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">اتجاه الحضور اليومي</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="attendanceTrendChart" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">حضور الأقسام</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="departmentAttendanceChart" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">روابط سريعة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <a href="@Url.Action("DailyReport", "Attendance")" class="btn btn-outline-primary w-100 mb-2">
                                                <i class="fas fa-calendar-day me-1"></i>
                                                التقرير اليومي
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="@Url.Action("MonthlyReport", "Attendance")" class="btn btn-outline-success w-100 mb-2">
                                                <i class="fas fa-calendar-alt me-1"></i>
                                                التقرير الشهري
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-outline-warning w-100 mb-2" onclick="generateOvertimeReport()">
                                                <i class="fas fa-clock me-1"></i>
                                                تقرير العمل الإضافي
                                            </button>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-outline-danger w-100 mb-2" onclick="generateAbsenceReport()">
                                                <i class="fas fa-user-times me-1"></i>
                                                تقرير الغياب
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button class="btn btn-success" onclick="exportToExcel()">
                                    <i class="fas fa-file-excel me-1"></i>
                                    تصدير إلى Excel
                                </button>
                                <button class="btn btn-danger" onclick="exportToPDF()">
                                    <i class="fas fa-file-pdf me-1"></i>
                                    تصدير إلى PDF
                                </button>
                                <button class="btn btn-primary" onclick="printReport()">
                                    <i class="fas fa-print me-1"></i>
                                    طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        function exportToExcel() {
            alert('سيتم تنفيذ وظيفة تصدير Excel');
        }

        function exportToPDF() {
            alert('سيتم تنفيذ وظيفة تصدير PDF');
        }

        function printReport() {
            window.print();
        }

        function resetFilters() {
            document.querySelector('form').reset();
        }

        function generateOvertimeReport() {
            alert('سيتم إنشاء تقرير العمل الإضافي');
        }

        function generateAbsenceReport() {
            alert('سيتم إنشاء تقرير الغياب');
        }

        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Placeholder for chart initialization
            console.log('Attendance charts will be initialized here');
        });
    </script>
}
