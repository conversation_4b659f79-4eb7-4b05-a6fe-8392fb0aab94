@model FitHRPlus.Web.Models.CompanySettings.CompanySettingsIndexViewModel
@{
    ViewData["Title"] = "إعدادات الشركة";
}

<!-- Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-title">
            <h1><i class="bi bi-gear"></i> إعدادات الشركة</h1>
            <p>إدارة وتكوين إعدادات الشركة وسياسات العمل</p>
        </div>
        <div class="page-actions">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#settingsModal">
                <i class="bi bi-pencil"></i>
                تعديل الإعدادات
            </button>
            <button type="button" class="btn btn-success" onclick="exportSettings()">
                <i class="bi bi-download"></i>
                تصدير الإعدادات
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-primary">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@(Model.Settings?.WorkingHoursPerDay ?? 8)</h3>
                        <p>ساعات العمل اليومية</p>
                        <small>@(Model.Settings?.WorkingHoursStart.ToString(@"hh\:mm") ?? "08:00") - @(Model.Settings?.WorkingHoursEnd.ToString(@"hh\:mm") ?? "17:00")</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-clock"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: @((Model.Settings?.WorkingHoursPerDay ?? 8) * 100 / 12)%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-success">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@Model.TotalWorkSchedules</h3>
                        <p>جداول العمل</p>
                        <small><a href="@Url.Action("WorkSchedules")" class="text-decoration-none">إدارة الجداول</a></small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-calendar-week"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: @(Model.TotalWorkSchedules * 10)%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-warning">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@Model.TotalHolidays</h3>
                        <p>العطل الرسمية</p>
                        <small><a href="@Url.Action("Holidays")" class="text-decoration-none">إدارة العطل</a></small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-calendar-event"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: @(Model.TotalHolidays * 5)%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-info">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@(Model.Settings?.Currency ?? "EGP")</h3>
                        <p>العملة الأساسية</p>
                        <small>@(Model.Settings?.CurrencySymbol ?? "ج.م")</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-currency-exchange"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: 100%"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Settings Tabs -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="settingsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="working-hours-tab" data-bs-toggle="tab" data-bs-target="#working-hours" type="button" role="tab">
                            <i class="bi bi-clock"></i>
                            ساعات العمل
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="attendance-tab" data-bs-toggle="tab" data-bs-target="#attendance" type="button" role="tab">
                            <i class="bi bi-person-check"></i>
                            سياسات الحضور
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="payroll-tab" data-bs-toggle="tab" data-bs-target="#payroll" type="button" role="tab">
                            <i class="bi bi-currency-dollar"></i>
                            الرواتب
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications" type="button" role="tab">
                            <i class="bi bi-bell"></i>
                            الإشعارات
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                            <i class="bi bi-shield-lock"></i>
                            الأمان
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="settingsTabContent">
                    <!-- Working Hours Tab -->
                    <div class="tab-pane fade show active" id="working-hours" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="settings-section">
                                    <h6 class="settings-section-title">أوقات العمل الأساسية</h6>
                                    @if (Model.HasSettings)
                                    {
                                        <div class="settings-item">
                                            <div class="settings-item-info">
                                                <label>وقت بداية العمل</label>
                                                <span class="settings-value">@Model.Settings.WorkingHoursStart.ToString(@"hh\:mm")</span>
                                            </div>
                                            <div class="settings-item-icon">
                                                <i class="bi bi-sunrise text-primary"></i>
                                            </div>
                                        </div>
                                        <div class="settings-item">
                                            <div class="settings-item-info">
                                                <label>وقت انتهاء العمل</label>
                                                <span class="settings-value">@Model.Settings.WorkingHoursEnd.ToString(@"hh\:mm")</span>
                                            </div>
                                            <div class="settings-item-icon">
                                                <i class="bi bi-sunset text-warning"></i>
                                            </div>
                                        </div>
                                        <div class="settings-item">
                                            <div class="settings-item-info">
                                                <label>ساعات العمل اليومية</label>
                                                <span class="settings-value">@Model.Settings.WorkingHoursPerDay ساعة</span>
                                            </div>
                                            <div class="settings-item-icon">
                                                <i class="bi bi-hourglass text-success"></i>
                                            </div>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="empty-state-small">
                                            <i class="bi bi-clock"></i>
                                            <p>لم يتم تكوين ساعات العمل بعد</p>
                                        </div>
                                    }
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="settings-section">
                                    <h6 class="settings-section-title">أيام العمل</h6>
                                    @if (Model.HasSettings)
                                    {
                                        <div class="settings-item">
                                            <div class="settings-item-info">
                                                <label>أيام العمل الأسبوعية</label>
                                                <span class="settings-value">@Model.Settings.WorkingDaysPerWeek أيام</span>
                                            </div>
                                            <div class="settings-item-icon">
                                                <i class="bi bi-calendar-week text-info"></i>
                                            </div>
                                        </div>
                                        <div class="settings-item">
                                            <div class="settings-item-info">
                                                <label>أيام العطلة الأسبوعية</label>
                                                <span class="settings-value">@string.Join("، ", Model.Settings.WeekendDaysList)</span>
                                            </div>
                                            <div class="settings-item-icon">
                                                <i class="bi bi-calendar-x text-danger"></i>
                                            </div>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="empty-state-small">
                                            <i class="bi bi-calendar"></i>
                                            <p>لم يتم تكوين أيام العمل بعد</p>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Attendance Policies Tab -->
                    <div class="tab-pane fade" id="attendance" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="settings-section">
                                    <h6 class="settings-section-title">سياسات التأخير والمغادرة</h6>
                                    @if (Model.HasSettings)
                                    {
                                        <div class="settings-item">
                                            <div class="settings-item-info">
                                                <label>فترة السماح للتأخير</label>
                                                <span class="settings-value">@Model.Settings.LateGracePeriodMinutes دقيقة</span>
                                            </div>
                                            <div class="settings-item-icon">
                                                <i class="bi bi-clock-history text-warning"></i>
                                            </div>
                                        </div>
                                        <div class="settings-item">
                                            <div class="settings-item-info">
                                                <label>فترة السماح للمغادرة المبكرة</label>
                                                <span class="settings-value">@Model.Settings.EarlyLeaveGracePeriodMinutes دقيقة</span>
                                            </div>
                                            <div class="settings-item-icon">
                                                <i class="bi bi-door-open text-info"></i>
                                            </div>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="empty-state-small">
                                            <i class="bi bi-person-check"></i>
                                            <p>لم يتم تكوين سياسات الحضور بعد</p>
                                        </div>
                                    }
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="settings-section">
                                    <h6 class="settings-section-title">الوقت الإضافي</h6>
                                    @if (Model.HasSettings)
                                    {
                                        <div class="settings-item">
                                            <div class="settings-item-info">
                                                <label>حد الوقت الإضافي</label>
                                                <span class="settings-value">@Model.Settings.OvertimeThresholdHours ساعة</span>
                                            </div>
                                            <div class="settings-item-icon">
                                                <i class="bi bi-stopwatch text-primary"></i>
                                            </div>
                                        </div>
                                        <div class="settings-item">
                                            <div class="settings-item-info">
                                                <label>مضاعف الوقت الإضافي</label>
                                                <span class="settings-value">@Model.Settings.OvertimeMultiplier x</span>
                                            </div>
                                            <div class="settings-item-icon">
                                                <i class="bi bi-calculator text-success"></i>
                                            </div>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="empty-state-small">
                                            <i class="bi bi-plus-circle"></i>
                                            <p>لم يتم تكوين الوقت الإضافي بعد</p>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payroll Tab -->
                    <div class="tab-pane fade" id="payroll" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="settings-section">
                                    <h6 class="settings-section-title">إعدادات العملة</h6>
                                    @if (Model.HasSettings)
                                    {
                                        <div class="settings-item">
                                            <div class="settings-item-info">
                                                <label>العملة الأساسية</label>
                                                <span class="settings-value">@(Model.Settings.Currency ?? "EGP")</span>
                                            </div>
                                            <div class="settings-item-icon">
                                                <i class="bi bi-currency-exchange text-primary"></i>
                                            </div>
                                        </div>
                                        <div class="settings-item">
                                            <div class="settings-item-info">
                                                <label>رمز العملة</label>
                                                <span class="settings-value">@(Model.Settings.CurrencySymbol ?? "ج.م")</span>
                                            </div>
                                            <div class="settings-item-icon">
                                                <i class="bi bi-currency-dollar text-success"></i>
                                            </div>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="empty-state-small">
                                            <i class="bi bi-currency-dollar"></i>
                                            <p>لم يتم تكوين العملة بعد</p>
                                        </div>
                                    }
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="settings-section">
                                    <h6 class="settings-section-title">دورة الرواتب</h6>
                                    <div class="settings-item">
                                        <div class="settings-item-info">
                                            <label>تكرار دفع الراتب</label>
                                            <span class="settings-value">شهرياً</span>
                                        </div>
                                        <div class="settings-item-icon">
                                            <i class="bi bi-calendar-month text-info"></i>
                                        </div>
                                    </div>
                                    <div class="settings-item">
                                        <div class="settings-item-info">
                                            <label>يوم دفع الراتب</label>
                                            <span class="settings-value">30 من كل شهر</span>
                                        </div>
                                        <div class="settings-item-icon">
                                            <i class="bi bi-calendar-date text-warning"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notifications Tab -->
                    <div class="tab-pane fade" id="notifications" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="settings-section">
                                    <h6 class="settings-section-title">إعدادات الإشعارات</h6>
                                    <div class="settings-toggle-item">
                                        <div class="settings-toggle-info">
                                            <label>إشعارات الحضور والانصراف</label>
                                            <p>إرسال إشعارات عند تسجيل الحضور والانصراف</p>
                                        </div>
                                        <div class="settings-toggle-control">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="attendanceNotifications" checked>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="settings-toggle-item">
                                        <div class="settings-toggle-info">
                                            <label>إشعارات طلبات الإجازة</label>
                                            <p>إرسال إشعارات عند تقديم أو الموافقة على الإجازات</p>
                                        </div>
                                        <div class="settings-toggle-control">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="leaveNotifications" checked>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="settings-section">
                                    <h6 class="settings-section-title">قنوات الإشعارات</h6>
                                    <div class="settings-toggle-item">
                                        <div class="settings-toggle-info">
                                            <label>البريد الإلكتروني</label>
                                            <p>إرسال الإشعارات عبر البريد الإلكتروني</p>
                                        </div>
                                        <div class="settings-toggle-control">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="settings-toggle-item">
                                        <div class="settings-toggle-info">
                                            <label>الرسائل النصية</label>
                                            <p>إرسال الإشعارات عبر الرسائل النصية</p>
                                        </div>
                                        <div class="settings-toggle-control">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="smsNotifications">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Security Tab -->
                    <div class="tab-pane fade" id="security" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="settings-section">
                                    <h6 class="settings-section-title">أمان كلمات المرور</h6>
                                    <div class="settings-toggle-item">
                                        <div class="settings-toggle-info">
                                            <label>فرض كلمات مرور قوية</label>
                                            <p>يجب أن تحتوي كلمة المرور على 8 أحرف على الأقل</p>
                                        </div>
                                        <div class="settings-toggle-control">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="strongPasswords" checked>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="settings-toggle-item">
                                        <div class="settings-toggle-info">
                                            <label>انتهاء صلاحية كلمة المرور</label>
                                            <p>فرض تغيير كلمة المرور كل 90 يوم</p>
                                        </div>
                                        <div class="settings-toggle-control">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="passwordExpiry">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="settings-section">
                                    <h6 class="settings-section-title">أمان الجلسات</h6>
                                    <div class="settings-item">
                                        <div class="settings-item-info">
                                            <label>مدة انتهاء الجلسة</label>
                                            <span class="settings-value">30 دقيقة</span>
                                        </div>
                                        <div class="settings-item-icon">
                                            <i class="bi bi-clock text-warning"></i>
                                        </div>
                                    </div>
                                    <div class="settings-toggle-item">
                                        <div class="settings-toggle-info">
                                            <label>تسجيل محاولات تسجيل الدخول</label>
                                            <p>تسجيل جميع محاولات تسجيل الدخول الناجحة والفاشلة</p>
                                        </div>
                                        <div class="settings-toggle-control">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="loginLogging" checked>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="quick-actions-grid">
                    <button class="quick-action-card" onclick="backupSettings()">
                        <div class="quick-action-icon">
                            <i class="bi bi-cloud-download"></i>
                        </div>
                        <div class="quick-action-content">
                            <h6>نسخ احتياطي</h6>
                            <p>إنشاء نسخة احتياطية من الإعدادات</p>
                        </div>
                    </button>
                    <button class="quick-action-card" onclick="restoreSettings()">
                        <div class="quick-action-icon">
                            <i class="bi bi-cloud-upload"></i>
                        </div>
                        <div class="quick-action-content">
                            <h6>استعادة</h6>
                            <p>استعادة الإعدادات من نسخة احتياطية</p>
                        </div>
                    </button>
                    <button class="quick-action-card" onclick="resetSettings()">
                        <div class="quick-action-icon">
                            <i class="bi bi-arrow-clockwise"></i>
                        </div>
                        <div class="quick-action-content">
                            <h6>إعادة تعيين</h6>
                            <p>إعادة الإعدادات للقيم الافتراضية</p>
                        </div>
                    </button>
                    <button class="quick-action-card" onclick="validateSettings()">
                        <div class="quick-action-icon">
                            <i class="bi bi-check-circle"></i>
                        </div>
                        <div class="quick-action-content">
                            <h6>التحقق</h6>
                            <p>التحقق من صحة الإعدادات</p>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            initializeSettingsToggles();
            loadSettingsFromStorage();
        });

        function initializeSettingsToggles() {
            // Handle toggle switches
            $('.form-check-input').on('change', function() {
                const settingId = $(this).attr('id');
                const isEnabled = $(this).is(':checked');

                // Save to localStorage
                localStorage.setItem('setting_' + settingId, isEnabled);

                // Show toast notification
                showToast(`تم ${isEnabled ? 'تفعيل' : 'إلغاء'} الإعداد بنجاح`, 'success');
            });
        }

        function loadSettingsFromStorage() {
            // Load saved settings from localStorage
            $('.form-check-input').each(function() {
                const settingId = $(this).attr('id');
                const savedValue = localStorage.getItem('setting_' + settingId);

                if (savedValue !== null) {
                    $(this).prop('checked', savedValue === 'true');
                }
            });
        }

        function exportSettings() {
            // Show loading
            const btn = $('button[onclick="exportSettings()"]');
            const originalText = btn.html();
            btn.html('<i class="bi bi-arrow-repeat spin"></i> جاري التصدير...');
            btn.prop('disabled', true);

            // Simulate export process
            setTimeout(() => {
                // Create and download settings file
                const settings = {
                    exportDate: new Date().toISOString(),
                    workingHours: {
                        start: '@(Model.Settings?.WorkingHoursStart.ToString(@"hh\:mm") ?? "08:00")',
                        end: '@(Model.Settings?.WorkingHoursEnd.ToString(@"hh\:mm") ?? "17:00")',
                        hoursPerDay: @(Model.Settings?.WorkingHoursPerDay ?? 8),
                        daysPerWeek: @(Model.Settings?.WorkingDaysPerWeek ?? 5)
                    },
                    currency: '@(Model.Settings?.Currency ?? "EGP")',
                    currencySymbol: '@(Model.Settings?.CurrencySymbol ?? "ج.م")'
                };

                const dataStr = JSON.stringify(settings, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);

                const link = document.createElement('a');
                link.href = url;
                link.download = 'company-settings-' + new Date().toISOString().split('T')[0] + '.json';
                link.click();

                URL.revokeObjectURL(url);

                btn.html(originalText);
                btn.prop('disabled', false);
                showToast('تم تصدير الإعدادات بنجاح', 'success');
            }, 2000);
        }

        function backupSettings() {
            if (confirm('هل تريد إنشاء نسخة احتياطية من الإعدادات الحالية؟')) {
                showToast('جاري إنشاء النسخة الاحتياطية...', 'info');

                // Simulate backup process
                setTimeout(() => {
                    const backupData = {
                        timestamp: new Date().toISOString(),
                        settings: 'backup-data-here'
                    };

                    localStorage.setItem('settings_backup', JSON.stringify(backupData));
                    showToast('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
                }, 1500);
            }
        }

        function restoreSettings() {
            const backup = localStorage.getItem('settings_backup');
            if (!backup) {
                showToast('لا توجد نسخة احتياطية متاحة', 'warning');
                return;
            }

            if (confirm('هل تريد استعادة الإعدادات من النسخة الاحتياطية؟ سيتم استبدال الإعدادات الحالية.')) {
                showToast('جاري استعادة الإعدادات...', 'info');

                setTimeout(() => {
                    // Simulate restore process
                    showToast('تم استعادة الإعدادات بنجاح', 'success');
                    setTimeout(() => location.reload(), 1000);
                }, 1500);
            }
        }

        function resetSettings() {
            if (confirm('هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟ لا يمكن التراجع عن هذا الإجراء.')) {
                showToast('جاري إعادة تعيين الإعدادات...', 'info');

                setTimeout(() => {
                    // Clear localStorage
                    Object.keys(localStorage).forEach(key => {
                        if (key.startsWith('setting_')) {
                            localStorage.removeItem(key);
                        }
                    });

                    showToast('تم إعادة تعيين الإعدادات بنجاح', 'success');
                    setTimeout(() => location.reload(), 1000);
                }, 1500);
            }
        }

        function validateSettings() {
            showToast('جاري التحقق من الإعدادات...', 'info');

            setTimeout(() => {
                const issues = [];

                // Simulate validation checks
                @if (!Model.HasSettings)
                {
                    <text>issues.push('لم يتم تكوين ساعات العمل');</text>
                }

                if (issues.length === 0) {
                    showToast('جميع الإعدادات صحيحة ✓', 'success');
                } else {
                    showToast(`تم العثور على ${issues.length} مشكلة في الإعدادات`, 'warning');
                }
            }, 1000);
        }

        function showToast(message, type = 'info') {
            const bgClass = type === 'success' ? 'bg-success' :
                           type === 'warning' ? 'bg-warning' :
                           type === 'error' ? 'bg-danger' : 'bg-info';

            const toast = $(`
                <div class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `);

            let container = $('.toast-container');
            if (container.length === 0) {
                container = $('<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
                $('body').append(container);
            }

            container.append(toast);
            const bsToast = new bootstrap.Toast(toast[0]);
            bsToast.show();

            toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }

        // Initialize tabs functionality
        $(document).ready(function() {
            console.log('Initializing Company Settings tabs...');

            // Ensure Bootstrap tabs are working
            if (typeof bootstrap !== 'undefined' && bootstrap.Tab) {
                console.log('✅ Bootstrap Tabs is available');

                // Initialize all tab triggers
                const tabTriggerList = [].slice.call(document.querySelectorAll('#settingsTabs button[data-bs-toggle="tab"]'));
                const tabList = tabTriggerList.map(function (tabTriggerEl) {
                    return new bootstrap.Tab(tabTriggerEl);
                });

                console.log('✅ Initialized', tabList.length, 'tabs');

                // Add event listeners for tab changes
                $('button[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
                    const targetTab = $(e.target).attr('data-bs-target');
                    const tabName = $(e.target).text().trim();
                    console.log('✅ Tab activated:', tabName, '→', targetTab);

                    // Optional: Add any specific logic for each tab
                    switch(targetTab) {
                        case '#working-hours':
                            console.log('Working hours tab activated');
                            break;
                        case '#attendance':
                            console.log('Attendance tab activated');
                            break;
                        case '#payroll':
                            console.log('Payroll tab activated');
                            break;
                        case '#notifications':
                            console.log('Notifications tab activated');
                            break;
                        case '#security':
                            console.log('Security tab activated');
                            break;
                    }
                });

                // Test initial active tab
                const activeTab = $('#settingsTabs .nav-link.active');
                if (activeTab.length > 0) {
                    console.log('✅ Initial active tab:', activeTab.text().trim());
                } else {
                    console.log('⚠️ No initial active tab found');
                }

            } else {
                console.error('❌ Bootstrap Tabs is not available');
                showToast('خطأ: Bootstrap Tabs غير متاح', 'error');
            }
        });
    </script>
}


<!-- Settings Modal -->
<div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="settingsModalLabel">
                    <i class="fas fa-cogs me-2"></i>
                    Company Settings / إعدادات الشركة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form asp-action="UpdateSettings" method="post">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Configure your company's working hours, policies, and preferences. These settings will apply to all employees unless overridden by individual work schedules.
                    </div>
                    
                    <!-- Working Hours Tab Content -->
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">Working Hours / ساعات العمل</h6>
                            <div class="mb-3">
                                <label class="form-label">Working Hours Start / بداية ساعات العمل</label>
                                <input type="time" class="form-control" name="WorkingHoursStart" value="@(Model.Settings?.WorkingHoursStart.ToString(@"hh\:mm") ?? "08:00")" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Working Hours End / نهاية ساعات العمل</label>
                                <input type="time" class="form-control" name="WorkingHoursEnd" value="@(Model.Settings?.WorkingHoursEnd.ToString(@"hh\:mm") ?? "17:00")" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Working Hours Per Day / ساعات العمل في اليوم</label>
                                <input type="number" class="form-control" name="WorkingHoursPerDay" value="@(Model.Settings?.WorkingHoursPerDay ?? 8)" min="1" max="24" step="0.5" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">Attendance Policies / سياسات الحضور</h6>
                            <div class="mb-3">
                                <label class="form-label">Late Grace Period (minutes) / فترة السماح للتأخير (دقائق)</label>
                                <input type="number" class="form-control" name="LateGracePeriodMinutes" value="@(Model.Settings?.LateGracePeriodMinutes ?? 15)" min="0" max="120" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Early Leave Grace Period (minutes) / فترة السماح للمغادرة المبكرة (دقائق)</label>
                                <input type="number" class="form-control" name="EarlyLeaveGracePeriodMinutes" value="@(Model.Settings?.EarlyLeaveGracePeriodMinutes ?? 15)" min="0" max="120" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Overtime Multiplier / مضاعف الوقت الإضافي</label>
                                <input type="number" class="form-control" name="OvertimeMultiplier" value="@(Model.Settings?.OvertimeMultiplier ?? 1.5m)" min="1.0" max="3.0" step="0.1" required>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        Cancel / إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>
                        Save Settings / حفظ الإعدادات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
