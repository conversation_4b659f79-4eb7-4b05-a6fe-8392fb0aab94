using FitHRPlus.Application.Common;
using FitHRPlus.Application.DTOs.Employees;
using FitHRPlus.Application.DTOs.Common;
using FitHRPlus.Application.Interfaces;
using FitHRPlus.Persistence;
using FitHRPlus.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FitHRPlus.Infrastructure.Services
{
    /// <summary>
    /// Department service implementation
    /// تنفيذ خدمة الأقسام
    /// </summary>
    public class DepartmentService : IDepartmentService
    {
        private readonly FitHRContext _context;
        private readonly ILogger<DepartmentService> _logger;

        public DepartmentService(FitHRContext context, ILogger<DepartmentService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<ServiceResult<List<DepartmentDto>>> GetDepartmentsAsync(Guid companyId, bool includeInactive = false)
        {
            try
            {
                var query = _context.Departments
                    .Where(d => d.CompanyId == companyId);

                if (!includeInactive)
                {
                    query = query.Where(d => d.IsActive);
                }

                var departments = await query
                    .OrderBy(d => d.Name)
                    .Select(d => new DepartmentDto
                    {
                        Id = d.Id,
                        CompanyId = d.CompanyId,
                        Name = d.Name,
                        NameAr = d.NameAr,
                        Description = d.Description,
                        DescriptionAr = d.DescriptionAr,
                        Budget = d.Budget,
                        EmployeeCount = d.Employees.Count,
                        IsActive = d.IsActive,
                        CreatedAt = d.CreatedAt,
                        UpdatedAt = d.UpdatedAt
                    })
                    .ToListAsync();

                return ServiceResult<List<DepartmentDto>>.Success(departments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving departments for company {CompanyId}", companyId);
                return ServiceResult<List<DepartmentDto>>.Failure(
                    "Failed to retrieve departments",
                    "فشل في استرجاع الأقسام",
                    "DEPARTMENTS_RETRIEVAL_FAILED");
            }
        }

        public async Task<ServiceResult<PaginatedResult<DepartmentDto>>> GetDepartmentsAsync(DepartmentListRequestDto request)
        {
            try
            {
                var query = _context.Departments
                    .Where(d => d.CompanyId == request.CompanyId);

                // Apply filters
                if (!string.IsNullOrWhiteSpace(request.SearchTerm))
                {
                    var searchTerm = request.SearchTerm.ToLower();
                    query = query.Where(d =>
                        d.Name.ToLower().Contains(searchTerm) ||
                        (d.NameAr != null && d.NameAr.ToLower().Contains(searchTerm)) ||
                        (d.Description != null && d.Description.ToLower().Contains(searchTerm)));
                }

                if (request.IsActive.HasValue)
                {
                    query = query.Where(d => d.IsActive == request.IsActive.Value);
                }

                // Get total count
                var totalCount = await query.CountAsync();

                // Apply pagination
                var departments = await query
                    .OrderBy(d => d.Name)
                    .Skip((request.PageNumber - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .Select(d => new DepartmentDto
                    {
                        Id = d.Id,
                        CompanyId = d.CompanyId,
                        Name = d.Name,
                        NameAr = d.NameAr,
                        Description = d.Description,
                        DescriptionAr = d.DescriptionAr,
                        Budget = d.Budget,
                        EmployeeCount = d.Employees.Count,
                        IsActive = d.IsActive,
                        CreatedAt = d.CreatedAt,
                        UpdatedAt = d.UpdatedAt
                    })
                    .ToListAsync();

                var result = PaginatedResult<DepartmentDto>.Create(
                    departments,
                    totalCount,
                    request.PageNumber,
                    request.PageSize);

                return ServiceResult<PaginatedResult<DepartmentDto>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving departments with pagination for company {CompanyId}", request.CompanyId);
                return ServiceResult<PaginatedResult<DepartmentDto>>.Failure(
                    "Failed to retrieve departments",
                    "فشل في استرجاع الأقسام",
                    "DEPARTMENTS_RETRIEVAL_FAILED");
            }
        }

        public async Task<ServiceResult<DepartmentDto>> GetDepartmentByIdAsync(Guid id)
        {
            try
            {
                var department = await _context.Departments
                    .Where(d => d.Id == id)
                    .Select(d => new DepartmentDto
                    {
                        Id = d.Id,
                        CompanyId = d.CompanyId,
                        Name = d.Name,
                        NameAr = d.NameAr,
                        Description = d.Description,
                        DescriptionAr = d.DescriptionAr,
                        Budget = d.Budget,
                        EmployeeCount = d.Employees.Count,
                        IsActive = d.IsActive,
                        CreatedAt = d.CreatedAt,
                        UpdatedAt = d.UpdatedAt
                    })
                    .FirstOrDefaultAsync();

                if (department == null)
                {
                    return ServiceResult<DepartmentDto>.Failure(
                        "Department not found",
                        "القسم غير موجود",
                        "DEPARTMENT_NOT_FOUND");
                }

                return ServiceResult<DepartmentDto>.Success(department);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving department {DepartmentId}", id);
                return ServiceResult<DepartmentDto>.Failure(
                    "Failed to retrieve department",
                    "فشل في استرجاع القسم",
                    "DEPARTMENT_RETRIEVAL_FAILED");
            }
        }

        public Task<ServiceResult<List<DepartmentDto>>> GetDepartmentHierarchyAsync(Guid companyId, bool includeInactive = false)
        {
            // TODO: Implement department hierarchy retrieval
            var departments = new List<DepartmentDto>();
            return Task.FromResult(ServiceResult<List<DepartmentDto>>.Success(departments));
        }

        public async Task<ServiceResult<DepartmentDto>> CreateDepartmentAsync(DepartmentDto departmentDto, Guid createdBy)
        {
            try
            {
                var department = new Department
                {
                    Id = Guid.NewGuid(),
                    CompanyId = departmentDto.CompanyId,
                    Name = departmentDto.Name,
                    NameAr = departmentDto.NameAr,
                    Description = departmentDto.Description,
                    DescriptionAr = departmentDto.DescriptionAr,
                    Budget = departmentDto.Budget,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = createdBy,
                    UpdatedAt = DateTime.UtcNow,
                    UpdatedBy = createdBy
                };

                _context.Departments.Add(department);
                await _context.SaveChangesAsync();

                var result = new DepartmentDto
                {
                    Id = department.Id,
                    CompanyId = department.CompanyId,
                    Name = department.Name,
                    NameAr = department.NameAr,
                    Description = department.Description,
                    DescriptionAr = department.DescriptionAr,
                    Budget = department.Budget,
                    EmployeeCount = 0,
                    IsActive = department.IsActive,
                    CreatedAt = department.CreatedAt,
                    UpdatedAt = department.UpdatedAt
                };

                return ServiceResult<DepartmentDto>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating department");
                return ServiceResult<DepartmentDto>.Failure(
                    "Failed to create department",
                    "فشل في إنشاء القسم",
                    "DEPARTMENT_CREATION_FAILED");
            }
        }

        public async Task<ServiceResult<DepartmentDto>> CreateDepartmentAsync(CreateDepartmentDto createDto)
        {
            try
            {
                var department = new Department
                {
                    Id = Guid.NewGuid(),
                    CompanyId = createDto.CompanyId,
                    Name = createDto.Name,
                    NameAr = createDto.NameAr,
                    Description = createDto.Description,
                    DescriptionAr = createDto.DescriptionAr,
                    Budget = createDto.Budget,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = createDto.CreatedBy,
                    UpdatedAt = DateTime.UtcNow,
                    UpdatedBy = createDto.CreatedBy
                };

                _context.Departments.Add(department);
                await _context.SaveChangesAsync();

                var result = new DepartmentDto
                {
                    Id = department.Id,
                    CompanyId = department.CompanyId,
                    Name = department.Name,
                    NameAr = department.NameAr,
                    Description = department.Description,
                    DescriptionAr = department.DescriptionAr,
                    Budget = department.Budget,
                    EmployeeCount = 0,
                    IsActive = department.IsActive,
                    CreatedAt = department.CreatedAt,
                    UpdatedAt = department.UpdatedAt
                };

                return ServiceResult<DepartmentDto>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating department");
                return ServiceResult<DepartmentDto>.Failure(
                    "Failed to create department",
                    "فشل في إنشاء القسم",
                    "DEPARTMENT_CREATION_FAILED");
            }
        }

        public async Task<ServiceResult<DepartmentDto>> UpdateDepartmentAsync(Guid id, DepartmentDto departmentDto, Guid updatedBy)
        {
            try
            {
                var department = await _context.Departments.FindAsync(id);
                if (department == null)
                {
                    return ServiceResult<DepartmentDto>.Failure(
                        "Department not found",
                        "القسم غير موجود",
                        "DEPARTMENT_NOT_FOUND");
                }

                department.Name = departmentDto.Name;
                department.NameAr = departmentDto.NameAr;
                department.Description = departmentDto.Description;
                department.DescriptionAr = departmentDto.DescriptionAr;
                department.Budget = departmentDto.Budget;
                department.IsActive = departmentDto.IsActive;
                department.UpdatedAt = DateTime.UtcNow;
                department.UpdatedBy = updatedBy;

                await _context.SaveChangesAsync();

                var result = new DepartmentDto
                {
                    Id = department.Id,
                    CompanyId = department.CompanyId,
                    Name = department.Name,
                    NameAr = department.NameAr,
                    Description = department.Description,
                    DescriptionAr = department.DescriptionAr,
                    Budget = department.Budget,
                    EmployeeCount = department.Employees?.Count ?? 0,
                    IsActive = department.IsActive,
                    CreatedAt = department.CreatedAt,
                    UpdatedAt = department.UpdatedAt
                };

                return ServiceResult<DepartmentDto>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating department {DepartmentId}", id);
                return ServiceResult<DepartmentDto>.Failure(
                    "Failed to update department",
                    "فشل في تحديث القسم",
                    "DEPARTMENT_UPDATE_FAILED");
            }
        }

        public async Task<ServiceResult<DepartmentDto>> UpdateDepartmentAsync(UpdateDepartmentDto updateDto)
        {
            try
            {
                var department = await _context.Departments.FindAsync(updateDto.Id);
                if (department == null)
                {
                    return ServiceResult<DepartmentDto>.Failure(
                        "Department not found",
                        "القسم غير موجود",
                        "DEPARTMENT_NOT_FOUND");
                }

                department.Name = updateDto.Name;
                department.NameAr = updateDto.NameAr;
                department.Description = updateDto.Description;
                department.DescriptionAr = updateDto.DescriptionAr;
                department.Budget = updateDto.Budget;
                department.IsActive = updateDto.IsActive;
                department.UpdatedAt = DateTime.UtcNow;
                department.UpdatedBy = updateDto.UpdatedBy;

                await _context.SaveChangesAsync();

                var result = new DepartmentDto
                {
                    Id = department.Id,
                    CompanyId = department.CompanyId,
                    Name = department.Name,
                    NameAr = department.NameAr,
                    Description = department.Description,
                    DescriptionAr = department.DescriptionAr,
                    Budget = department.Budget,
                    EmployeeCount = department.Employees?.Count ?? 0,
                    IsActive = department.IsActive,
                    CreatedAt = department.CreatedAt,
                    UpdatedAt = department.UpdatedAt
                };

                return ServiceResult<DepartmentDto>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating department {DepartmentId}", updateDto.Id);
                return ServiceResult<DepartmentDto>.Failure(
                    "Failed to update department",
                    "فشل في تحديث القسم",
                    "DEPARTMENT_UPDATE_FAILED");
            }
        }

        public async Task<ServiceResult<bool>> DeleteDepartmentAsync(Guid id, Guid deletedBy)
        {
            try
            {
                var department = await _context.Departments
                    .Include(d => d.Employees)
                    .FirstOrDefaultAsync(d => d.Id == id);

                if (department == null)
                {
                    return ServiceResult<bool>.Failure(
                        "Department not found",
                        "القسم غير موجود",
                        "DEPARTMENT_NOT_FOUND");
                }

                // Check if department has employees
                if (department.Employees.Any())
                {
                    return ServiceResult<bool>.Failure(
                        "Cannot delete department with employees",
                        "لا يمكن حذف قسم يحتوي على موظفين",
                        "DEPARTMENT_HAS_EMPLOYEES");
                }

                // Soft delete
                department.IsActive = false;
                department.UpdatedAt = DateTime.UtcNow;
                department.UpdatedBy = deletedBy;

                await _context.SaveChangesAsync();

                return ServiceResult<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting department {DepartmentId}", id);
                return ServiceResult<bool>.Failure(
                    "Failed to delete department",
                    "فشل في حذف القسم",
                    "DEPARTMENT_DELETION_FAILED");
            }
        }

        public async Task<ServiceResult<bool>> DeleteDepartmentAsync(Guid id)
        {
            try
            {
                var department = await _context.Departments
                    .Include(d => d.Employees)
                    .FirstOrDefaultAsync(d => d.Id == id);

                if (department == null)
                {
                    return ServiceResult<bool>.Failure(
                        "Department not found",
                        "القسم غير موجود",
                        "DEPARTMENT_NOT_FOUND");
                }

                // Check if department has employees
                if (department.Employees.Any())
                {
                    return ServiceResult<bool>.Failure(
                        "Cannot delete department with employees",
                        "لا يمكن حذف قسم يحتوي على موظفين",
                        "DEPARTMENT_HAS_EMPLOYEES");
                }

                // Soft delete
                department.IsActive = false;
                department.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return ServiceResult<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting department {DepartmentId}", id);
                return ServiceResult<bool>.Failure(
                    "Failed to delete department",
                    "فشل في حذف القسم",
                    "DEPARTMENT_DELETION_FAILED");
            }
        }

        public Task<ServiceResult<bool>> SetDepartmentActiveStatusAsync(Guid id, bool isActive, Guid updatedBy)
        {
            // TODO: Implement department status update
            return Task.FromResult(ServiceResult<bool>.Failure(
                "Department status update not implemented",
                "تحديث حالة القسم غير مطبق",
                "NOT_IMPLEMENTED"));
        }

        public Task<ServiceResult<List<DepartmentDto>>> GetSubDepartmentsAsync(Guid parentDepartmentId, bool includeInactive = false)
        {
            // TODO: Implement sub-departments retrieval
            var departments = new List<DepartmentDto>();
            return Task.FromResult(ServiceResult<List<DepartmentDto>>.Success(departments));
        }

        public Task<ServiceResult<bool>> MoveDepartmentAsync(Guid departmentId, Guid? newParentId, Guid updatedBy)
        {
            // TODO: Implement department move
            return Task.FromResult(ServiceResult<bool>.Failure(
                "Department move not implemented",
                "نقل القسم غير مطبق",
                "NOT_IMPLEMENTED"));
        }

        public Task<ServiceResult<bool>> ValidateDepartmentCodeAsync(string code, Guid companyId, Guid? excludeDepartmentId = null)
        {
            // TODO: Implement department code validation
            return Task.FromResult(ServiceResult<bool>.Success(true));
        }

        public Task<ServiceResult<List<DepartmentDto>>> SearchDepartmentsAsync(string searchTerm, Guid companyId, bool includeInactive = false)
        {
            // TODO: Implement department search
            var departments = new List<DepartmentDto>();
            return Task.FromResult(ServiceResult<List<DepartmentDto>>.Success(departments));
        }
    }

    /// <summary>
    /// Position service implementation
    /// تنفيذ خدمة المناصب
    /// </summary>
    public class PositionService : IPositionService
    {
        private readonly FitHRContext _context;
        private readonly ILogger<PositionService> _logger;

        public PositionService(FitHRContext context, ILogger<PositionService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<ServiceResult<List<PositionDto>>> GetPositionsAsync(Guid companyId, bool includeInactive = false)
        {
            try
            {
                var query = _context.Positions
                    .Include(p => p.Department)
                    .Where(p => p.CompanyId == companyId);

                if (!includeInactive)
                {
                    query = query.Where(p => p.IsActive);
                }

                var positions = await query
                    .OrderBy(p => p.Title)
                    .Select(p => new PositionDto
                    {
                        Id = p.Id,
                        CompanyId = p.CompanyId,
                        DepartmentId = p.DepartmentId,
                        DepartmentName = p.Department.Name,
                        Title = p.Title,
                        TitleAr = p.TitleAr,
                        Description = p.Description,
                        DescriptionAr = p.DescriptionAr,
                        MinSalary = p.MinSalary,
                        MaxSalary = p.MaxSalary,
                        EmployeeCount = p.Employees.Count,
                        IsActive = p.IsActive,
                        CreatedAt = p.CreatedAt,
                        UpdatedAt = p.UpdatedAt
                    })
                    .ToListAsync();

                return ServiceResult<List<PositionDto>>.Success(positions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving positions for company {CompanyId}", companyId);
                return ServiceResult<List<PositionDto>>.Failure(
                    "Failed to retrieve positions",
                    "فشل في استرجاع المناصب",
                    "POSITIONS_RETRIEVAL_FAILED");
            }
        }

        public async Task<ServiceResult<PaginatedResult<PositionDto>>> GetPositionsAsync(PositionListRequestDto request)
        {
            try
            {
                var query = _context.Positions
                    .Include(p => p.Department)
                    .Where(p => p.CompanyId == request.CompanyId);

                // Apply filters
                if (request.DepartmentId.HasValue)
                {
                    query = query.Where(p => p.DepartmentId == request.DepartmentId.Value);
                }

                if (!string.IsNullOrWhiteSpace(request.SearchTerm))
                {
                    var searchTerm = request.SearchTerm.ToLower();
                    query = query.Where(p =>
                        p.Title.ToLower().Contains(searchTerm) ||
                        (p.TitleAr != null && p.TitleAr.ToLower().Contains(searchTerm)) ||
                        (p.Description != null && p.Description.ToLower().Contains(searchTerm)));
                }

                if (request.IsActive.HasValue)
                {
                    query = query.Where(p => p.IsActive == request.IsActive.Value);
                }

                // Get total count
                var totalCount = await query.CountAsync();

                // Apply pagination
                var positions = await query
                    .OrderBy(p => p.Title)
                    .Skip((request.PageNumber - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .Select(p => new PositionDto
                    {
                        Id = p.Id,
                        CompanyId = p.CompanyId,
                        DepartmentId = p.DepartmentId,
                        DepartmentName = p.Department.Name,
                        Title = p.Title,
                        TitleAr = p.TitleAr,
                        Description = p.Description,
                        DescriptionAr = p.DescriptionAr,
                        MinSalary = p.MinSalary,
                        MaxSalary = p.MaxSalary,
                        EmployeeCount = p.Employees.Count,
                        IsActive = p.IsActive,
                        CreatedAt = p.CreatedAt,
                        UpdatedAt = p.UpdatedAt
                    })
                    .ToListAsync();

                var result = PaginatedResult<PositionDto>.Create(
                    positions,
                    totalCount,
                    request.PageNumber,
                    request.PageSize);

                return ServiceResult<PaginatedResult<PositionDto>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving positions with pagination for company {CompanyId}", request.CompanyId);
                return ServiceResult<PaginatedResult<PositionDto>>.Failure(
                    "Failed to retrieve positions",
                    "فشل في استرجاع المناصب",
                    "POSITIONS_RETRIEVAL_FAILED");
            }
        }

        public Task<ServiceResult<List<PositionDto>>> GetPositionsByDepartmentAsync(Guid departmentId, bool includeInactive = false)
        {
            // TODO: Implement position retrieval by department
            var positions = new List<PositionDto>();
            return Task.FromResult(ServiceResult<List<PositionDto>>.Success(positions));
        }

        public async Task<ServiceResult<PositionDto>> GetPositionByIdAsync(Guid id)
        {
            try
            {
                var position = await _context.Positions
                    .Include(p => p.Department)
                    .Where(p => p.Id == id)
                    .Select(p => new PositionDto
                    {
                        Id = p.Id,
                        CompanyId = p.CompanyId,
                        DepartmentId = p.DepartmentId,
                        DepartmentName = p.Department.Name,
                        Title = p.Title,
                        TitleAr = p.TitleAr,
                        Description = p.Description,
                        DescriptionAr = p.DescriptionAr,
                        MinSalary = p.MinSalary,
                        MaxSalary = p.MaxSalary,
                        EmployeeCount = p.Employees.Count,
                        IsActive = p.IsActive,
                        CreatedAt = p.CreatedAt,
                        UpdatedAt = p.UpdatedAt
                    })
                    .FirstOrDefaultAsync();

                if (position == null)
                {
                    return ServiceResult<PositionDto>.Failure(
                        "Position not found",
                        "المنصب غير موجود",
                        "POSITION_NOT_FOUND");
                }

                return ServiceResult<PositionDto>.Success(position);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving position {PositionId}", id);
                return ServiceResult<PositionDto>.Failure(
                    "Failed to retrieve position",
                    "فشل في استرجاع المنصب",
                    "POSITION_RETRIEVAL_FAILED");
            }
        }

        public async Task<ServiceResult<PositionDto>> CreatePositionAsync(PositionDto positionDto, Guid createdBy)
        {
            try
            {
                var position = new Position
                {
                    Id = Guid.NewGuid(),
                    CompanyId = positionDto.CompanyId,
                    DepartmentId = positionDto.DepartmentId,
                    Title = positionDto.Title,
                    TitleAr = positionDto.TitleAr,
                    Description = positionDto.Description,
                    DescriptionAr = positionDto.DescriptionAr,
                    MinSalary = positionDto.MinSalary,
                    MaxSalary = positionDto.MaxSalary,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = createdBy,
                    UpdatedAt = DateTime.UtcNow,
                    UpdatedBy = createdBy
                };

                _context.Positions.Add(position);
                await _context.SaveChangesAsync();

                var result = new PositionDto
                {
                    Id = position.Id,
                    CompanyId = position.CompanyId,
                    DepartmentId = position.DepartmentId,
                    DepartmentName = positionDto.DepartmentName,
                    Title = position.Title,
                    TitleAr = position.TitleAr,
                    Description = position.Description,
                    DescriptionAr = position.DescriptionAr,
                    MinSalary = position.MinSalary,
                    MaxSalary = position.MaxSalary,
                    EmployeeCount = 0,
                    IsActive = position.IsActive,
                    CreatedAt = position.CreatedAt,
                    UpdatedAt = position.UpdatedAt
                };

                return ServiceResult<PositionDto>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating position");
                return ServiceResult<PositionDto>.Failure(
                    "Failed to create position",
                    "فشل في إنشاء المنصب",
                    "POSITION_CREATION_FAILED");
            }
        }

        public async Task<ServiceResult<PositionDto>> CreatePositionAsync(CreatePositionDto createDto)
        {
            try
            {
                // Get department name for the result
                var department = await _context.Departments
                    .Where(d => d.Id == createDto.DepartmentId)
                    .Select(d => d.Name)
                    .FirstOrDefaultAsync();

                var position = new Position
                {
                    Id = Guid.NewGuid(),
                    CompanyId = createDto.CompanyId,
                    DepartmentId = createDto.DepartmentId,
                    Title = createDto.Title,
                    TitleAr = createDto.TitleAr,
                    Description = createDto.Description,
                    DescriptionAr = createDto.DescriptionAr,
                    MinSalary = createDto.MinSalary,
                    MaxSalary = createDto.MaxSalary,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = createDto.CreatedBy,
                    UpdatedAt = DateTime.UtcNow,
                    UpdatedBy = createDto.CreatedBy
                };

                _context.Positions.Add(position);
                await _context.SaveChangesAsync();

                var result = new PositionDto
                {
                    Id = position.Id,
                    CompanyId = position.CompanyId,
                    DepartmentId = position.DepartmentId,
                    DepartmentName = department ?? "Unknown Department",
                    Title = position.Title,
                    TitleAr = position.TitleAr,
                    Description = position.Description,
                    DescriptionAr = position.DescriptionAr,
                    MinSalary = position.MinSalary,
                    MaxSalary = position.MaxSalary,
                    EmployeeCount = 0,
                    IsActive = position.IsActive,
                    CreatedAt = position.CreatedAt,
                    UpdatedAt = position.UpdatedAt
                };

                return ServiceResult<PositionDto>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating position");
                return ServiceResult<PositionDto>.Failure(
                    "Failed to create position",
                    "فشل في إنشاء المنصب",
                    "POSITION_CREATION_FAILED");
            }
        }

        public async Task<ServiceResult<PositionDto>> UpdatePositionAsync(Guid id, PositionDto positionDto, Guid updatedBy)
        {
            try
            {
                var position = await _context.Positions.FindAsync(id);
                if (position == null)
                {
                    return ServiceResult<PositionDto>.Failure(
                        "Position not found",
                        "المنصب غير موجود",
                        "POSITION_NOT_FOUND");
                }

                position.DepartmentId = positionDto.DepartmentId;
                position.Title = positionDto.Title;
                position.TitleAr = positionDto.TitleAr;
                position.Description = positionDto.Description;
                position.DescriptionAr = positionDto.DescriptionAr;
                position.MinSalary = positionDto.MinSalary;
                position.MaxSalary = positionDto.MaxSalary;
                position.IsActive = positionDto.IsActive;
                position.UpdatedAt = DateTime.UtcNow;
                position.UpdatedBy = updatedBy;

                await _context.SaveChangesAsync();

                var result = new PositionDto
                {
                    Id = position.Id,
                    CompanyId = position.CompanyId,
                    DepartmentId = position.DepartmentId,
                    DepartmentName = positionDto.DepartmentName,
                    Title = position.Title,
                    TitleAr = position.TitleAr,
                    Description = position.Description,
                    DescriptionAr = position.DescriptionAr,
                    MinSalary = position.MinSalary,
                    MaxSalary = position.MaxSalary,
                    EmployeeCount = position.Employees?.Count ?? 0,
                    IsActive = position.IsActive,
                    CreatedAt = position.CreatedAt,
                    UpdatedAt = position.UpdatedAt
                };

                return ServiceResult<PositionDto>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating position {PositionId}", id);
                return ServiceResult<PositionDto>.Failure(
                    "Failed to update position",
                    "فشل في تحديث المنصب",
                    "POSITION_UPDATE_FAILED");
            }
        }

        public async Task<ServiceResult<PositionDto>> UpdatePositionAsync(UpdatePositionDto updateDto)
        {
            try
            {
                var position = await _context.Positions
                    .Include(p => p.Department)
                    .FirstOrDefaultAsync(p => p.Id == updateDto.Id);

                if (position == null)
                {
                    return ServiceResult<PositionDto>.Failure(
                        "Position not found",
                        "المنصب غير موجود",
                        "POSITION_NOT_FOUND");
                }

                position.DepartmentId = updateDto.DepartmentId;
                position.Title = updateDto.Title;
                position.TitleAr = updateDto.TitleAr;
                position.Description = updateDto.Description;
                position.DescriptionAr = updateDto.DescriptionAr;
                position.MinSalary = updateDto.MinSalary;
                position.MaxSalary = updateDto.MaxSalary;
                position.IsActive = updateDto.IsActive;
                position.UpdatedAt = DateTime.UtcNow;
                position.UpdatedBy = updateDto.UpdatedBy;

                await _context.SaveChangesAsync();

                var result = new PositionDto
                {
                    Id = position.Id,
                    CompanyId = position.CompanyId,
                    DepartmentId = position.DepartmentId,
                    DepartmentName = position.Department.Name,
                    Title = position.Title,
                    TitleAr = position.TitleAr,
                    Description = position.Description,
                    DescriptionAr = position.DescriptionAr,
                    MinSalary = position.MinSalary,
                    MaxSalary = position.MaxSalary,
                    EmployeeCount = position.Employees?.Count ?? 0,
                    IsActive = position.IsActive,
                    CreatedAt = position.CreatedAt,
                    UpdatedAt = position.UpdatedAt
                };

                return ServiceResult<PositionDto>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating position {PositionId}", updateDto.Id);
                return ServiceResult<PositionDto>.Failure(
                    "Failed to update position",
                    "فشل في تحديث المنصب",
                    "POSITION_UPDATE_FAILED");
            }
        }

        public async Task<ServiceResult<bool>> DeletePositionAsync(Guid id, Guid deletedBy)
        {
            try
            {
                var position = await _context.Positions
                    .Include(p => p.Employees)
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (position == null)
                {
                    return ServiceResult<bool>.Failure(
                        "Position not found",
                        "المنصب غير موجود",
                        "POSITION_NOT_FOUND");
                }

                // Check if position has employees
                if (position.Employees.Any())
                {
                    return ServiceResult<bool>.Failure(
                        "Cannot delete position with employees",
                        "لا يمكن حذف منصب يحتوي على موظفين",
                        "POSITION_HAS_EMPLOYEES");
                }

                // Soft delete
                position.IsActive = false;
                position.UpdatedAt = DateTime.UtcNow;
                position.UpdatedBy = deletedBy;

                await _context.SaveChangesAsync();

                return ServiceResult<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting position {PositionId}", id);
                return ServiceResult<bool>.Failure(
                    "Failed to delete position",
                    "فشل في حذف المنصب",
                    "POSITION_DELETION_FAILED");
            }
        }

        public Task<ServiceResult<bool>> SetPositionActiveStatusAsync(Guid id, bool isActive, Guid updatedBy)
        {
            // TODO: Implement position status update
            return Task.FromResult(ServiceResult<bool>.Failure(
                "Position status update not implemented",
                "تحديث حالة المنصب غير مطبق",
                "NOT_IMPLEMENTED"));
        }

        public Task<ServiceResult<bool>> ValidatePositionCodeAsync(string code, Guid companyId, Guid? excludePositionId = null)
        {
            // TODO: Implement position code validation
            return Task.FromResult(ServiceResult<bool>.Success(true));
        }

        public Task<ServiceResult<List<PositionDto>>> SearchPositionsAsync(string searchTerm, Guid companyId, Guid? departmentId = null, bool includeInactive = false)
        {
            // TODO: Implement position search
            var positions = new List<PositionDto>();
            return Task.FromResult(ServiceResult<List<PositionDto>>.Success(positions));
        }

        public Task<ServiceResult<PositionSalaryStatsDto>> GetPositionSalaryStatsAsync(Guid positionId)
        {
            // TODO: Implement position salary statistics
            var stats = new PositionSalaryStatsDto
            {
                PositionId = positionId,
                PositionTitle = "Unknown Position",
                EmployeeCount = 0
            };
            return Task.FromResult(ServiceResult<PositionSalaryStatsDto>.Success(stats));
        }
    }
}
