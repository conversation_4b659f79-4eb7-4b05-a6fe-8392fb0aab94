using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace FitHRPlus.Web.Models.Notifications
{
    /// <summary>
    /// Notification list view model
    /// نموذج عرض قائمة الإشعارات
    /// </summary>
    public class NotificationListViewModel
    {
        public List<NotificationViewModel> Notifications { get; set; } = new();
        
        // Filter properties
        public string? Type { get; set; }
        public string? Category { get; set; }
        public string? Priority { get; set; }
        public bool? IsRead { get; set; }
        public string? SearchTerm { get; set; }
        
        // Pagination
        public int CurrentPage { get; set; } = 1;
        public int TotalPages { get; set; }
        public int TotalCount { get; set; }
        public int PageSize { get; set; } = 20;

        // Statistics
        public int TotalNotifications { get; set; }
        public int UnreadCount { get; set; }
        public int ReadCount { get; set; }
        public int TodayCount { get; set; }
        
        // Filter options
        public List<SelectListItem> TypeOptions { get; set; } = new()
        {
            new SelectListItem { Value = "Info", Text = "Info / معلومات" },
            new SelectListItem { Value = "Warning", Text = "Warning / تحذير" },
            new SelectListItem { Value = "Error", Text = "Error / خطأ" },
            new SelectListItem { Value = "Success", Text = "Success / نجح" }
        };
        
        public List<SelectListItem> CategoryOptions { get; set; } = new()
        {
            new SelectListItem { Value = "Leave", Text = "Leave / الإجازات" },
            new SelectListItem { Value = "Payroll", Text = "Payroll / كشوف المرتبات" },
            new SelectListItem { Value = "Attendance", Text = "Attendance / الحضور" },
            new SelectListItem { Value = "System", Text = "System / النظام" }
        };
        
        public List<SelectListItem> PriorityOptions { get; set; } = new()
        {
            new SelectListItem { Value = "Low", Text = "Low / منخفض" },
            new SelectListItem { Value = "Medium", Text = "Medium / متوسط" },
            new SelectListItem { Value = "High", Text = "High / عالي" },
            new SelectListItem { Value = "Critical", Text = "Critical / حرج" }
        };
        
        // Statistics
        public NotificationStatisticsViewModel? Statistics { get; set; }
    }

    /// <summary>
    /// Notification view model
    /// نموذج عرض الإشعار
    /// </summary>
    public class NotificationViewModel
    {
        public Guid Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string TitleAr { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string MessageAr { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public bool IsRead { get; set; }
        public DateTime? ReadAt { get; set; }
        public string? ActionUrl { get; set; }
        public string? ActionText { get; set; }
        public string? ActionTextAr { get; set; }
        public string? Icon { get; set; }
        public DateTime CreatedAt { get; set; }
        public string TimeAgo { get; set; } = string.Empty;
        public string TypeBadgeClass { get; set; } = string.Empty;
        public string PriorityBadgeClass { get; set; } = string.Empty;
        public bool IsExpired { get; set; }

        // Display properties
        public string DisplayTitle => !string.IsNullOrEmpty(TitleAr) ? TitleAr : Title;
        public string DisplayMessage => !string.IsNullOrEmpty(MessageAr) ? MessageAr : Message;
        public string DisplayActionText => !string.IsNullOrEmpty(ActionTextAr) ? ActionTextAr : ActionText ?? "";
    }

    /// <summary>
    /// Notification details view model
    /// نموذج عرض تفاصيل الإشعار
    /// </summary>
    public class NotificationDetailsViewModel
    {
        public Guid Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string TitleAr { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string MessageAr { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public bool IsRead { get; set; }
        public DateTime? ReadAt { get; set; }
        public string? ActionUrl { get; set; }
        public string? ActionText { get; set; }
        public string? ActionTextAr { get; set; }
        public string? Icon { get; set; }
        public string? Data { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public string TimeAgo { get; set; } = string.Empty;
        public string TypeBadgeClass { get; set; } = string.Empty;
        public string PriorityBadgeClass { get; set; } = string.Empty;
        public bool IsExpired { get; set; }

        // Display properties
        public string DisplayTitle => !string.IsNullOrEmpty(TitleAr) ? TitleAr : Title;
        public string DisplayMessage => !string.IsNullOrEmpty(MessageAr) ? MessageAr : Message;
        public string DisplayActionText => !string.IsNullOrEmpty(ActionTextAr) ? ActionTextAr : ActionText ?? "";
    }

    /// <summary>
    /// Notification statistics view model
    /// نموذج عرض إحصائيات الإشعارات
    /// </summary>
    public class NotificationStatisticsViewModel
    {
        public int TotalNotifications { get; set; }
        public int UnreadNotifications { get; set; }
        public int ReadNotifications { get; set; }
        public int ExpiredNotifications { get; set; }

        public Dictionary<string, int> NotificationsByType { get; set; } = new();
        public Dictionary<string, int> NotificationsByCategory { get; set; } = new();
        public Dictionary<string, int> NotificationsByPriority { get; set; } = new();

        public decimal ReadPercentage { get; set; }

        // Calculated properties
        public string ReadPercentageDisplay => $"{ReadPercentage:F1}%";
        public decimal UnreadPercentage => TotalNotifications > 0 ? (decimal)UnreadNotifications / TotalNotifications * 100 : 0;
        public string UnreadPercentageDisplay => $"{UnreadPercentage:F1}%";
    }

    /// <summary>
    /// Notification preferences view model
    /// نموذج عرض تفضيلات الإشعارات
    /// </summary>
    public class NotificationPreferencesViewModel
    {
        [Display(Name = "Email Notifications / إشعارات البريد الإلكتروني")]
        public bool EmailNotifications { get; set; } = true;

        [Display(Name = "Push Notifications / الإشعارات الفورية")]
        public bool PushNotifications { get; set; } = true;

        [Display(Name = "SMS Notifications / إشعارات الرسائل النصية")]
        public bool SmsNotifications { get; set; } = false;

        [Display(Name = "Leave Notifications / إشعارات الإجازات")]
        public bool LeaveNotifications { get; set; } = true;

        [Display(Name = "Payroll Notifications / إشعارات كشوف المرتبات")]
        public bool PayrollNotifications { get; set; } = true;

        [Display(Name = "Attendance Notifications / إشعارات الحضور")]
        public bool AttendanceNotifications { get; set; } = true;

        [Display(Name = "System Notifications / إشعارات النظام")]
        public bool SystemNotifications { get; set; } = true;

        [Display(Name = "Quiet Hours Start / بداية ساعات الهدوء")]
        public string? QuietHoursStart { get; set; }

        [Display(Name = "Quiet Hours End / نهاية ساعات الهدوء")]
        public string? QuietHoursEnd { get; set; }

        public List<string> DisabledCategories { get; set; } = new();

        // Helper properties
        public List<SelectListItem> TimeOptions { get; set; } = new();
        public List<SelectListItem> CategoryOptions { get; set; } = new()
        {
            new SelectListItem { Value = "Leave", Text = "Leave / الإجازات" },
            new SelectListItem { Value = "Payroll", Text = "Payroll / كشوف المرتبات" },
            new SelectListItem { Value = "Attendance", Text = "Attendance / الحضور" },
            new SelectListItem { Value = "System", Text = "System / النظام" }
        };
    }

    /// <summary>
    /// Create notification view model
    /// نموذج عرض إنشاء إشعار
    /// </summary>
    public class CreateNotificationViewModel
    {
        [Required(ErrorMessage = "Title is required / العنوان مطلوب")]
        [MaxLength(200, ErrorMessage = "Title cannot exceed 200 characters")]
        [Display(Name = "Title / العنوان")]
        public string Title { get; set; } = string.Empty;

        [MaxLength(200, ErrorMessage = "Arabic title cannot exceed 200 characters")]
        [Display(Name = "Arabic Title / العنوان بالعربية")]
        public string? TitleAr { get; set; }

        [Required(ErrorMessage = "Message is required / الرسالة مطلوبة")]
        [MaxLength(1000, ErrorMessage = "Message cannot exceed 1000 characters")]
        [Display(Name = "Message / الرسالة")]
        public string Message { get; set; } = string.Empty;

        [MaxLength(1000, ErrorMessage = "Arabic message cannot exceed 1000 characters")]
        [Display(Name = "Arabic Message / الرسالة بالعربية")]
        public string? MessageAr { get; set; }

        [Required(ErrorMessage = "Type is required / النوع مطلوب")]
        [Display(Name = "Type / النوع")]
        public string Type { get; set; } = "Info";

        [Required(ErrorMessage = "Category is required / الفئة مطلوبة")]
        [Display(Name = "Category / الفئة")]
        public string Category { get; set; } = "System";

        [Display(Name = "Priority / الأولوية")]
        public string Priority { get; set; } = "Medium";

        [MaxLength(500, ErrorMessage = "Action URL cannot exceed 500 characters")]
        [Display(Name = "Action URL / رابط الإجراء")]
        public string? ActionUrl { get; set; }

        [MaxLength(100, ErrorMessage = "Action text cannot exceed 100 characters")]
        [Display(Name = "Action Text / نص الإجراء")]
        public string? ActionText { get; set; }

        [MaxLength(100, ErrorMessage = "Arabic action text cannot exceed 100 characters")]
        [Display(Name = "Arabic Action Text / نص الإجراء بالعربية")]
        public string? ActionTextAr { get; set; }

        [MaxLength(50, ErrorMessage = "Icon cannot exceed 50 characters")]
        [Display(Name = "Icon / الأيقونة")]
        public string? Icon { get; set; }

        [Display(Name = "Expires At / تنتهي في")]
        public DateTime? ExpiresAt { get; set; }

        // Filter options
        public List<SelectListItem> TypeOptions { get; set; } = new()
        {
            new SelectListItem { Value = "Info", Text = "Info / معلومات" },
            new SelectListItem { Value = "Warning", Text = "Warning / تحذير" },
            new SelectListItem { Value = "Error", Text = "Error / خطأ" },
            new SelectListItem { Value = "Success", Text = "Success / نجح" }
        };
        
        public List<SelectListItem> CategoryOptions { get; set; } = new()
        {
            new SelectListItem { Value = "Leave", Text = "Leave / الإجازات" },
            new SelectListItem { Value = "Payroll", Text = "Payroll / كشوف المرتبات" },
            new SelectListItem { Value = "Attendance", Text = "Attendance / الحضور" },
            new SelectListItem { Value = "System", Text = "System / النظام" }
        };
        
        public List<SelectListItem> PriorityOptions { get; set; } = new()
        {
            new SelectListItem { Value = "Low", Text = "Low / منخفض" },
            new SelectListItem { Value = "Medium", Text = "Medium / متوسط" },
            new SelectListItem { Value = "High", Text = "High / عالي" },
            new SelectListItem { Value = "Critical", Text = "Critical / حرج" }
        };
    }

    /// <summary>
    /// Notification template view model
    /// نموذج عرض قالب الإشعار
    /// </summary>
    public class NotificationTemplateViewModel
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string NameAr { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string DescriptionAr { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string TitleTemplate { get; set; } = string.Empty;
        public string TitleTemplateAr { get; set; } = string.Empty;
        public string MessageTemplate { get; set; } = string.Empty;
        public string MessageTemplateAr { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public string? Icon { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // Display properties
        public string DisplayName => !string.IsNullOrEmpty(NameAr) ? NameAr : Name;
        public string DisplayDescription => !string.IsNullOrEmpty(DescriptionAr) ? DescriptionAr : Description;
    }
}
