@{
    ViewData["Title"] = "Daily Attendance Report / التقرير اليومي للحضور";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-calendar-day me-2"></i>
                        Daily Attendance Report / التقرير اليومي للحضور
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Report Date Selection -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form method="get" class="d-flex align-items-end gap-3">
                                <div class="flex-grow-1">
                                    <label for="date" class="form-label">Report Date / تاريخ التقرير</label>
                                    <input type="date" class="form-control" id="date" name="date" 
                                           value="@ViewBag.ReportDate?.ToString("yyyy-MM-dd")" />
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>
                                    Generate / إنشاء
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Report Content -->
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Report Date:</strong> @ViewBag.ReportDate?.ToString("dddd, MMMM dd, yyyy")
                                <br>
                                <strong>تاريخ التقرير:</strong> @ViewBag.ReportDate?.ToString("dddd، dd MMMM yyyy", new System.Globalization.CultureInfo("ar-EG"))
                            </div>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Present / حاضر</h6>
                                            <h3 class="mb-0">0</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-user-check fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Absent / غائب</h6>
                                            <h3 class="mb-0">0</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-user-times fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Late / متأخر</h6>
                                            <h3 class="mb-0">0</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-clock fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">On Leave / في إجازة</h6>
                                            <h3 class="mb-0">0</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-calendar-alt fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Attendance Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Employee / الموظف</th>
                                    <th>Department / القسم</th>
                                    <th>Check In / وقت الدخول</th>
                                    <th>Check Out / وقت الخروج</th>
                                    <th>Working Hours / ساعات العمل</th>
                                    <th>Status / الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="6" class="text-center text-muted py-4">
                                        <i class="fas fa-info-circle me-2"></i>
                                        No attendance data available for this date / لا توجد بيانات حضور لهذا التاريخ
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button class="btn btn-success" onclick="exportToExcel()">
                                    <i class="fas fa-file-excel me-1"></i>
                                    Export to Excel / تصدير إلى Excel
                                </button>
                                <button class="btn btn-danger" onclick="exportToPDF()">
                                    <i class="fas fa-file-pdf me-1"></i>
                                    Export to PDF / تصدير إلى PDF
                                </button>
                                <button class="btn btn-primary" onclick="printReport()">
                                    <i class="fas fa-print me-1"></i>
                                    Print / طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function exportToExcel() {
        alert('Excel export functionality will be implemented / سيتم تنفيذ وظيفة تصدير Excel');
    }

    function exportToPDF() {
        alert('PDF export functionality will be implemented / سيتم تنفيذ وظيفة تصدير PDF');
    }

    function printReport() {
        window.print();
    }
</script>
