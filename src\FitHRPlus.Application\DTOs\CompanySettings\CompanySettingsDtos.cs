using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Application.DTOs.CompanySettings
{
    /// <summary>
    /// Company settings DTO
    /// نموذج بيانات إعدادات الشركة
    /// </summary>
    public class CompanySettingsDto
    {
        public Guid Id { get; set; }
        public Guid CompanyId { get; set; }

        // Company information
        public string? CompanyName { get; set; }
        public string? CompanyNameAr { get; set; }
        public string? Address { get; set; }
        public string? AddressAr { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Website { get; set; }
        public string? TaxNumber { get; set; }
        public string? CommercialRegister { get; set; }

        // Working hours settings
        public TimeSpan WorkingHoursStart { get; set; }
        public TimeSpan WorkingHoursEnd { get; set; }
        public decimal WorkingHoursPerDay { get; set; }
        public decimal WorkingDaysPerWeek { get; set; }

        // Break time settings
        public TimeSpan? BreakTimeStart { get; set; }
        public TimeSpan? BreakTimeEnd { get; set; }
        public decimal BreakTimeMinutes { get; set; }

        // Weekend settings
        public string WeekendDays { get; set; } = string.Empty;
        public List<string> WeekendDaysList => WeekendDays.Split(',').ToList();

        // Attendance policies
        public int LateGracePeriodMinutes { get; set; }
        public int EarlyLeaveGracePeriodMinutes { get; set; }
        public decimal OvertimeThresholdHours { get; set; }
        public decimal OvertimeMultiplier { get; set; }

        // Leave policies
        public decimal AnnualLeaveEntitlement { get; set; }
        public decimal SickLeaveEntitlement { get; set; }
        public decimal MaternityLeaveEntitlement { get; set; }
        public decimal PaternityLeaveEntitlement { get; set; }

        // Payroll settings
        public int PayrollCutoffDay { get; set; }
        public int PayrollPaymentDay { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string CurrencySymbol { get; set; } = string.Empty;

        // Tax settings
        public decimal IncomeTaxRate { get; set; }
        public decimal SocialInsuranceEmployeeRate { get; set; }
        public decimal SocialInsuranceEmployerRate { get; set; }
        public decimal MedicalInsuranceRate { get; set; }

        // Notification settings
        public bool EnableEmailNotifications { get; set; }
        public bool EnableSmsNotifications { get; set; }
        public bool EnablePushNotifications { get; set; }

        // System settings
        public string DefaultLanguage { get; set; } = string.Empty;
        public string TimeZone { get; set; } = string.Empty;
        public string DateFormat { get; set; } = string.Empty;
        public string TimeFormat { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// Update company settings DTO
    /// نموذج بيانات تحديث إعدادات الشركة
    /// </summary>
    public class UpdateCompanySettingsDto
    {
        public string? CompanyName { get; set; }
        public string? CompanyNameAr { get; set; }
        public string? Address { get; set; }
        public string? AddressAr { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Website { get; set; }
        public string? TaxNumber { get; set; }
        public string? CommercialRegister { get; set; }
        public TimeSpan WorkingHoursStart { get; set; }
        public TimeSpan WorkingHoursEnd { get; set; }
        public string WeekendDays { get; set; } = string.Empty;
        public string Currency { get; set; } = string.Empty;
        public string CurrencySymbol { get; set; } = string.Empty;
        public decimal IncomeTaxRate { get; set; }
        public decimal SocialInsuranceEmployeeRate { get; set; }
        public decimal SocialInsuranceEmployerRate { get; set; }
        public decimal MedicalInsuranceRate { get; set; }
        public bool EnableEmailNotifications { get; set; }
        public bool EnableSmsNotifications { get; set; }
        public bool EnablePushNotifications { get; set; }
        public string DefaultLanguage { get; set; } = string.Empty;
        public string TimeZone { get; set; } = string.Empty;
        public string DateFormat { get; set; } = string.Empty;
        public string TimeFormat { get; set; } = string.Empty;
    }

    /// <summary>
    /// Create/Update company settings DTO
    /// نموذج بيانات إنشاء/تحديث إعدادات الشركة
    /// </summary>
    public class CreateUpdateCompanySettingsDto
    {
        // Working hours settings
        [Required]
        public TimeSpan WorkingHoursStart { get; set; } = new TimeSpan(8, 0, 0);

        [Required]
        public TimeSpan WorkingHoursEnd { get; set; } = new TimeSpan(17, 0, 0);

        [Required]
        [Range(1, 24)]
        public decimal WorkingHoursPerDay { get; set; } = 8.0m;

        [Required]
        [Range(1, 7)]
        public decimal WorkingDaysPerWeek { get; set; } = 5.0m;

        // Break time settings
        public TimeSpan? BreakTimeStart { get; set; }
        public TimeSpan? BreakTimeEnd { get; set; }

        [Range(0, 480)]
        public decimal BreakTimeMinutes { get; set; } = 60;

        // Weekend settings
        [Required]
        [MaxLength(20)]
        public string WeekendDays { get; set; } = "Friday,Saturday";

        // Attendance policies
        [Range(0, 120)]
        public int LateGracePeriodMinutes { get; set; } = 15;

        [Range(0, 120)]
        public int EarlyLeaveGracePeriodMinutes { get; set; } = 15;

        [Range(1, 24)]
        public decimal OvertimeThresholdHours { get; set; } = 8.0m;

        [Range(1.0, 3.0)]
        public decimal OvertimeMultiplier { get; set; } = 1.5m;

        // Leave policies
        [Range(0, 365)]
        public decimal AnnualLeaveEntitlement { get; set; } = 21.0m;

        [Range(0, 365)]
        public decimal SickLeaveEntitlement { get; set; } = 30.0m;

        [Range(0, 365)]
        public decimal MaternityLeaveEntitlement { get; set; } = 90.0m;

        [Range(0, 365)]
        public decimal PaternityLeaveEntitlement { get; set; } = 3.0m;

        // Payroll settings
        [Range(1, 31)]
        public int PayrollCutoffDay { get; set; } = 25;

        [Range(1, 31)]
        public int PayrollPaymentDay { get; set; } = 30;

        [Required]
        [MaxLength(10)]
        public string Currency { get; set; } = "EGP";

        [Required]
        [MaxLength(10)]
        public string CurrencySymbol { get; set; } = "ج.م";

        // Tax settings
        [Range(0, 1)]
        public decimal IncomeTaxRate { get; set; } = 0.14m;

        [Range(0, 1)]
        public decimal SocialInsuranceEmployeeRate { get; set; } = 0.11m;

        [Range(0, 1)]
        public decimal SocialInsuranceEmployerRate { get; set; } = 0.185m;

        [Range(0, 1)]
        public decimal MedicalInsuranceRate { get; set; } = 0.03m;

        // Notification settings
        public bool EnableEmailNotifications { get; set; } = true;
        public bool EnableSmsNotifications { get; set; } = false;
        public bool EnablePushNotifications { get; set; } = true;

        // System settings
        [Required]
        [MaxLength(10)]
        public string DefaultLanguage { get; set; } = "ar";

        [Required]
        [MaxLength(50)]
        public string TimeZone { get; set; } = "Africa/Cairo";

        [Required]
        [MaxLength(20)]
        public string DateFormat { get; set; } = "dd/MM/yyyy";

        [Required]
        [MaxLength(20)]
        public string TimeFormat { get; set; } = "HH:mm";
    }

    /// <summary>
    /// Work schedule DTO
    /// نموذج بيانات جدول العمل
    /// </summary>
    public class WorkScheduleDto
    {
        public Guid Id { get; set; }
        public Guid CompanyId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string NameAr { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? DescriptionAr { get; set; }
        public bool IsDefault { get; set; }
        public List<WorkScheduleDayDto> WorkScheduleDays { get; set; } = new();
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// Work schedule day DTO
    /// نموذج بيانات يوم جدول العمل
    /// </summary>
    public class WorkScheduleDayDto
    {
        public Guid Id { get; set; }
        public Guid WorkScheduleId { get; set; }
        public DayOfWeek DayOfWeek { get; set; }
        public bool IsWorkingDay { get; set; }
        public TimeSpan? StartTime { get; set; }
        public TimeSpan? EndTime { get; set; }
        public TimeSpan? BreakStartTime { get; set; }
        public TimeSpan? BreakEndTime { get; set; }
        public decimal WorkingHours { get; set; }
        public string DayName => DayOfWeek.ToString();
        public string DayNameAr => GetArabicDayName(DayOfWeek);

        private static string GetArabicDayName(DayOfWeek dayOfWeek)
        {
            return dayOfWeek switch
            {
                DayOfWeek.Sunday => "الأحد",
                DayOfWeek.Monday => "الاثنين",
                DayOfWeek.Tuesday => "الثلاثاء",
                DayOfWeek.Wednesday => "الأربعاء",
                DayOfWeek.Thursday => "الخميس",
                DayOfWeek.Friday => "الجمعة",
                DayOfWeek.Saturday => "السبت",
                _ => dayOfWeek.ToString()
            };
        }
    }

    /// <summary>
    /// Create work schedule DTO
    /// نموذج بيانات إنشاء جدول العمل
    /// </summary>
    public class CreateWorkScheduleDto
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string NameAr { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? Description { get; set; }

        [MaxLength(500)]
        public string? DescriptionAr { get; set; }

        public bool IsDefault { get; set; } = false;

        [Required]
        public List<CreateWorkScheduleDayDto> WorkScheduleDays { get; set; } = new();
    }

    /// <summary>
    /// Update work schedule DTO
    /// نموذج بيانات تحديث جدول العمل
    /// </summary>
    public class UpdateWorkScheduleDto
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string NameAr { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? Description { get; set; }

        [MaxLength(500)]
        public string? DescriptionAr { get; set; }

        public bool IsDefault { get; set; } = false;

        [Required]
        public List<UpdateWorkScheduleDayDto> WorkScheduleDays { get; set; } = new();
    }

    /// <summary>
    /// Create work schedule day DTO
    /// نموذج بيانات إنشاء يوم جدول العمل
    /// </summary>
    public class CreateWorkScheduleDayDto
    {
        [Required]
        public DayOfWeek DayOfWeek { get; set; }

        public bool IsWorkingDay { get; set; } = true;

        public TimeSpan? StartTime { get; set; }
        public TimeSpan? EndTime { get; set; }
        public TimeSpan? BreakStartTime { get; set; }
        public TimeSpan? BreakEndTime { get; set; }

        [Range(0, 24)]
        public decimal WorkingHours { get; set; } = 8.0m;
    }

    /// <summary>
    /// Update work schedule day DTO
    /// نموذج بيانات تحديث يوم جدول العمل
    /// </summary>
    public class UpdateWorkScheduleDayDto
    {
        public Guid? Id { get; set; }

        [Required]
        public DayOfWeek DayOfWeek { get; set; }

        public bool IsWorkingDay { get; set; } = true;

        public TimeSpan? StartTime { get; set; }
        public TimeSpan? EndTime { get; set; }
        public TimeSpan? BreakStartTime { get; set; }
        public TimeSpan? BreakEndTime { get; set; }

        [Range(0, 24)]
        public decimal WorkingHours { get; set; } = 8.0m;
    }

    /// <summary>
    /// Holiday DTO
    /// نموذج بيانات العطلة
    /// </summary>
    public class HolidayDto
    {
        public Guid Id { get; set; }
        public Guid CompanyId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string NameAr { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public bool IsRecurring { get; set; }
        public string? RecurrenceType { get; set; }
        public bool IsOptional { get; set; }
        public string AppliesTo { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// Create holiday DTO
    /// نموذج بيانات إنشاء العطلة
    /// </summary>
    public class CreateHolidayDto
    {
        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [Required]
        public DateTime Date { get; set; }

        public bool IsRecurring { get; set; } = false;

        [MaxLength(20)]
        public string? RecurrenceType { get; set; }

        public bool IsOptional { get; set; } = false;

        [MaxLength(50)]
        public string AppliesTo { get; set; } = "All";
    }

    /// <summary>
    /// Update holiday DTO
    /// نموذج بيانات تحديث العطلة
    /// </summary>
    public class UpdateHolidayDto
    {
        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [Required]
        public DateTime Date { get; set; }

        public bool IsRecurring { get; set; } = false;

        [MaxLength(20)]
        public string? RecurrenceType { get; set; }

        public bool IsOptional { get; set; } = false;

        [MaxLength(50)]
        public string AppliesTo { get; set; } = "All";
    }

    /// <summary>
    /// Tax and insurance rates DTO
    /// نموذج بيانات معدلات الضرائب والتأمينات
    /// </summary>
    public class TaxInsuranceRatesDto
    {
        public decimal IncomeTaxRate { get; set; }
        public decimal SocialInsuranceEmployeeRate { get; set; }
        public decimal SocialInsuranceEmployerRate { get; set; }
        public decimal MedicalInsuranceRate { get; set; }
        public decimal TotalEmployeeDeductionRate => SocialInsuranceEmployeeRate + MedicalInsuranceRate;
        public decimal TotalEmployerContributionRate => SocialInsuranceEmployerRate + MedicalInsuranceRate;
    }
}
