using FitHRPlus.Domain.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Domain.Entities
{
    /// <summary>
    /// Department entity representing organizational units
    /// كيان القسم الذي يمثل الوحدات التنظيمية
    /// </summary>
    public class Department : BaseEntity
    {
        /// <summary>
        /// Company ID that owns this department
        /// معرف الشركة التي تملك هذا القسم
        /// </summary>
        [Required]
        public Guid CompanyId { get; set; }

        /// <summary>
        /// Department name in English
        /// اسم القسم بالإنجليزية
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Department name in Arabic
        /// اسم القسم بالعربية
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// Department description in English
        /// وصف القسم بالإنجليزية
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Department description in Arabic
        /// وصف القسم بالعربية
        /// </summary>
        [MaxLength(500)]
        public string? DescriptionAr { get; set; }

        /// <summary>
        /// Department code (unique within company)
        /// رمز القسم (فريد داخل الشركة)
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Department manager employee ID
        /// معرف موظف مدير القسم
        /// </summary>
        public Guid? ManagerId { get; set; }

        /// <summary>
        /// Parent department ID for hierarchical structure
        /// معرف القسم الأب للهيكل الهرمي
        /// </summary>
        public Guid? ParentDepartmentId { get; set; }

        /// <summary>
        /// Cost center code for accounting
        /// رمز مركز التكلفة للمحاسبة
        /// </summary>
        [MaxLength(50)]
        public string? CostCenter { get; set; }

        /// <summary>
        /// Department budget
        /// ميزانية القسم
        /// </summary>
        public decimal? Budget { get; set; }

        /// <summary>
        /// Department location in English
        /// موقع القسم بالإنجليزية
        /// </summary>
        [MaxLength(200)]
        public string? Location { get; set; }

        /// <summary>
        /// Department location in Arabic
        /// موقع القسم بالعربية
        /// </summary>
        [MaxLength(200)]
        public string? LocationAr { get; set; }

        // Navigation Properties - خصائص التنقل

        /// <summary>
        /// Company that owns this department
        /// الشركة التي تملك هذا القسم
        /// </summary>
        public virtual Company Company { get; set; } = null!;

        /// <summary>
        /// Department manager
        /// مدير القسم
        /// </summary>
        public virtual Employee? Manager { get; set; }

        /// <summary>
        /// Parent department
        /// القسم الأب
        /// </summary>
        public virtual Department? ParentDepartment { get; set; }

        /// <summary>
        /// Child departments
        /// الأقسام الفرعية
        /// </summary>
        public virtual ICollection<Department> ChildDepartments { get; set; } = new List<Department>();

        /// <summary>
        /// Employees in this department
        /// الموظفون في هذا القسم
        /// </summary>
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();

        /// <summary>
        /// Positions in this department
        /// المناصب في هذا القسم
        /// </summary>
        public virtual ICollection<Position> Positions { get; set; } = new List<Position>();
    }
}