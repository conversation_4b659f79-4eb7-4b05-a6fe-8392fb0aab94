# FitHR Plus - نظام إدارة الموارد البشرية المتقدم

<div align="center">

![FitHR Plus Logo](docs/images/logo.png)

**نظام شامل لإدارة الموارد البشرية مع واجهة عصرية ومميزات متقدمة**

[![.NET](https://img.shields.io/badge/.NET-8.0-blue.svg)](https://dotnet.microsoft.com/)
[![Bootstrap](https://img.shields.io/badge/Bootstrap-5.3-purple.svg)](https://getbootstrap.com/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Arabic](https://img.shields.io/badge/Language-Arabic-red.svg)](README.ar.md)

</div>

## 📋 نظرة عامة

FitHR Plus هو نظام إدارة موارد بشرية شامل ومتطور، مصمم خصيصاً للشركات العربية مع دعم كامل للغة العربية واتجاه النص من اليمين إلى اليسار (RTL). يوفر النظام جميع الأدوات اللازمة لإدارة الموظفين والرواتب والحضور والإجازات والتقارير.

## ✨ المميزات الرئيسية

### 👥 إدارة الموظفين
- **ملفات شخصية شاملة** للموظفين مع جميع البيانات الضرورية
- **هيكل تنظيمي** مرن للأقسام والوظائف
- **إدارة الصلاحيات** والأدوار المختلفة
- **تتبع تاريخ العمل** والترقيات

### 💰 إدارة الرواتب
- **حاسبة راتب تفاعلية** مع حسابات دقيقة
- **كشوف مرتبات** مفصلة وقابلة للتخصيص
- **إدارة البدلات والخصومات** بمرونة عالية
- **تقارير مالية** شاملة

### ⏰ نظام الحضور والانصراف
- **تتبع الحضور** في الوقت الفعلي
- **إدارة الورديات** والجداول المرنة
- **حساب العمل الإضافي** تلقائياً
- **تقارير الحضور** التفصيلية

### 🏖️ إدارة الإجازات
- **طلبات الإجازات** الإلكترونية
- **أرصدة الإجازات** وتتبع الاستخدام
- **موافقات متدرجة** حسب الهيكل التنظيمي
- **تقويم الإجازات** التفاعلي

### 📊 التقارير والتحليلات
- **لوحة تحكم تفاعلية** مع رسوم بيانية
- **تقارير مخصصة** قابلة للتصدير
- **تحليلات متقدمة** للأداء والإنتاجية
- **منشئ التقارير** السريع

### 🎯 مميزات إضافية
- **تقييم الأداء** الشامل للموظفين
- **التدريب والتطوير** وإدارة البرامج التدريبية
- **إدارة المهام** والمشاريع
- **الإشعارات الفورية** مع SignalR
- **البحث الذكي** والفلاتر المتقدمة

## 🛠️ التقنيات المستخدمة

### Backend
- **ASP.NET Core 8.0** - إطار العمل الرئيسي
- **Entity Framework Core** - ORM لقاعدة البيانات
- **SQL Server** - قاعدة البيانات
- **SignalR** - الإشعارات الفورية
- **AutoMapper** - تحويل البيانات

### Frontend
- **Bootstrap 5.3** - إطار العمل للتصميم
- **jQuery** - مكتبة JavaScript
- **Chart.js** - الرسوم البيانية
- **Bootstrap Icons** - الأيقونات
- **RTL Support** - دعم اللغة العربية

### أدوات التطوير
- **Visual Studio 2022** - بيئة التطوير
- **Git** - نظام إدارة الإصدارات
- **Docker** - الحاويات (اختياري)

## 🚀 التثبيت والإعداد

### المتطلبات الأساسية
- .NET 8.0 SDK
- SQL Server 2019 أو أحدث
- Visual Studio 2022 أو VS Code
- Node.js (للأدوات الإضافية)

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/fithr-plus.git
cd fithr-plus
```

2. **إعداد قاعدة البيانات**
```bash
# تحديث سلسلة الاتصال في appsettings.json
dotnet ef database update
```

3. **تثبيت الحزم**
```bash
dotnet restore
npm install
```

4. **تشغيل التطبيق**
```bash
dotnet run --project src/FitHRPlus.Web
```

5. **الوصول للتطبيق**
افتح المتصفح وانتقل إلى: `https://localhost:5001`

## 📁 هيكل المشروع

```
FitHRPlus/
├── src/
│   ├── FitHRPlus.Core/           # الطبقة الأساسية
│   ├── FitHRPlus.Infrastructure/ # طبقة البيانات
│   ├── FitHRPlus.Application/    # منطق الأعمال
│   └── FitHRPlus.Web/           # واجهة الويب
├── tests/                       # الاختبارات
├── docs/                        # الوثائق
└── scripts/                     # سكريبتات مساعدة
```

## 🎨 لقطات الشاشة

### لوحة التحكم الرئيسية
![Dashboard](docs/screenshots/dashboard.png)

### إدارة الموظفين
![Employees](docs/screenshots/employees.png)

### كشوف المرتبات
![Payroll](docs/screenshots/payroll.png)

### التقارير والتحليلات
![Reports](docs/screenshots/reports.png)

## 📖 الوثائق

- [دليل المستخدم](docs/user-guide.md)
- [دليل المطور](docs/developer-guide.md)
- [API Documentation](docs/api-documentation.md)
- [دليل النشر](docs/deployment-guide.md)

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

### خطوات المساهمة
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 👨‍💻 الفريق

- **المطور الرئيسي** - [اسم المطور](https://github.com/username)
- **مصمم UI/UX** - [اسم المصمم](https://github.com/username)

## 📞 الدعم والتواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع الرسمي**: [www.fithrplus.com](https://www.fithrplus.com)
- **التوثيق**: [docs.fithrplus.com](https://docs.fithrplus.com)

## 🔄 التحديثات الأخيرة

### الإصدار 2.0.0 (2024-01-15)
- ✨ إضافة نظام تقييم الأداء
- 🎓 إضافة إدارة التدريب والتطوير
- 📋 إضافة إدارة المهام والمشاريع
- 🔔 تحسين نظام الإشعارات
- 🎨 تحديث التصميم والواجهة

### الإصدار 1.5.0 (2023-12-01)
- 🔍 إضافة البحث الذكي
- 📊 تحسين التقارير والرسوم البيانية
- 🌐 تحسين دعم اللغة العربية
- 🚀 تحسينات في الأداء

## ⭐ إذا أعجبك المشروع

إذا كان هذا المشروع مفيداً لك، لا تنس إعطاؤه نجمة ⭐ على GitHub!

---

<div align="center">

**صُنع بـ ❤️ للمجتمع العربي**

[الموقع الرسمي](https://www.fithrplus.com) • [الوثائق](https://docs.fithrplus.com) • [الدعم](mailto:<EMAIL>)

</div>
