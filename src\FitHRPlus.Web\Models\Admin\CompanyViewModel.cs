using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Web.Models.Admin
{
    /// <summary>
    /// Company view model for administration
    /// نموذج عرض الشركة للإدارة
    /// </summary>
    public class CompanyViewModel
    {
        /// <summary>
        /// Company ID
        /// معرف الشركة
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Company name in English
        /// اسم الشركة بالإنجليزية
        /// </summary>
        [Required(ErrorMessage = "Company name is required")]
        [StringLength(200, ErrorMessage = "Company name cannot exceed 200 characters")]
        [Display(Name = "Company Name (English)")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Company name in Arabic
        /// اسم الشركة بالعربية
        /// </summary>
        [Required(ErrorMessage = "Company name in Arabic is required")]
        [StringLength(200, ErrorMessage = "Company name in Arabic cannot exceed 200 characters")]
        [Display(Name = "Company Name (Arabic)")]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// Company description in English
        /// وصف الشركة بالإنجليزية
        /// </summary>
        [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
        [Display(Name = "Description (English)")]
        public string? Description { get; set; }

        /// <summary>
        /// Company description in Arabic
        /// وصف الشركة بالعربية
        /// </summary>
        [StringLength(1000, ErrorMessage = "Description in Arabic cannot exceed 1000 characters")]
        [Display(Name = "Description (Arabic)")]
        public string? DescriptionAr { get; set; }

        /// <summary>
        /// Company email
        /// البريد الإلكتروني للشركة
        /// </summary>
        [EmailAddress(ErrorMessage = "Invalid email format")]
        [Display(Name = "Email")]
        public string? Email { get; set; }

        /// <summary>
        /// Company phone
        /// هاتف الشركة
        /// </summary>
        [Phone(ErrorMessage = "Invalid phone number format")]
        [Display(Name = "Phone")]
        public string? Phone { get; set; }

        /// <summary>
        /// Company website
        /// موقع الشركة الإلكتروني
        /// </summary>
        [Url(ErrorMessage = "Invalid website URL")]
        [Display(Name = "Website")]
        public string? Website { get; set; }

        /// <summary>
        /// Company address
        /// عنوان الشركة
        /// </summary>
        [StringLength(500, ErrorMessage = "Address cannot exceed 500 characters")]
        [Display(Name = "Address")]
        public string? Address { get; set; }

        /// <summary>
        /// Company city
        /// مدينة الشركة
        /// </summary>
        [StringLength(100, ErrorMessage = "City cannot exceed 100 characters")]
        [Display(Name = "City")]
        public string? City { get; set; }

        /// <summary>
        /// Company country
        /// دولة الشركة
        /// </summary>
        [StringLength(100, ErrorMessage = "Country cannot exceed 100 characters")]
        [Display(Name = "Country")]
        public string? Country { get; set; } = "Egypt";

        /// <summary>
        /// Company logo URL
        /// رابط شعار الشركة
        /// </summary>
        [Display(Name = "Logo")]
        public string? Logo { get; set; }

        /// <summary>
        /// Tax registration number
        /// رقم التسجيل الضريبي
        /// </summary>
        [StringLength(50, ErrorMessage = "Tax number cannot exceed 50 characters")]
        [Display(Name = "Tax Registration Number")]
        public string? TaxNumber { get; set; }

        /// <summary>
        /// Commercial registration number
        /// رقم السجل التجاري
        /// </summary>
        [StringLength(50, ErrorMessage = "Commercial registration cannot exceed 50 characters")]
        [Display(Name = "Commercial Registration")]
        public string? CommercialRegistration { get; set; }

        /// <summary>
        /// Company settings as JSON
        /// إعدادات الشركة كـ JSON
        /// </summary>
        public string? Settings { get; set; }

        /// <summary>
        /// Whether the company is active
        /// ما إذا كانت الشركة نشطة
        /// </summary>
        [Display(Name = "Active")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Creation date
        /// تاريخ الإنشاء
        /// </summary>
        [Display(Name = "Created At")]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Last update date
        /// تاريخ آخر تحديث
        /// </summary>
        [Display(Name = "Updated At")]
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Number of employees
        /// عدد الموظفين
        /// </summary>
        [Display(Name = "Employees Count")]
        public int EmployeesCount { get; set; }

        /// <summary>
        /// Number of users
        /// عدد المستخدمين
        /// </summary>
        [Display(Name = "Users Count")]
        public int UsersCount { get; set; }
    }

    /// <summary>
    /// Company list view model
    /// نموذج عرض قائمة الشركات
    /// </summary>
    public class CompanyListViewModel
    {
        /// <summary>
        /// List of companies
        /// قائمة الشركات
        /// </summary>
        public List<CompanyViewModel> Companies { get; set; } = new();

        /// <summary>
        /// Search term
        /// مصطلح البحث
        /// </summary>
        [Display(Name = "Search")]
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Filter by active status
        /// تصفية حسب الحالة النشطة
        /// </summary>
        [Display(Name = "Status")]
        public bool? IsActive { get; set; }

        /// <summary>
        /// Current page number
        /// رقم الصفحة الحالية
        /// </summary>
        public int CurrentPage { get; set; } = 1;

        /// <summary>
        /// Total number of pages
        /// العدد الإجمالي للصفحات
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// Total number of companies
        /// العدد الإجمالي للشركات
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Page size
        /// حجم الصفحة
        /// </summary>
        public int PageSize { get; set; } = 10;
    }
}
