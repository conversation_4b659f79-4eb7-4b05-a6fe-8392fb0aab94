using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Application.DTOs.Payroll
{
    /// <summary>
    /// Payroll DTO
    /// DTO كشف المرتبات
    /// </summary>
    public class PayrollDto
    {
        public Guid Id { get; set; }
        public Guid EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string EmployeeNameAr { get; set; } = string.Empty;
        public string EmployeeCode { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string DepartmentNameAr { get; set; } = string.Empty;
        public string? PositionTitle { get; set; }
        public string? PositionTitleAr { get; set; }

        public int PayrollMonth { get; set; }
        public int PayrollYear { get; set; }
        public string PayrollPeriod => $"{PayrollYear}-{PayrollMonth:D2}";

        public decimal BasicSalary { get; set; }
        public decimal TotalAllowances { get; set; }
        public decimal GrossSalary { get; set; }

        public decimal TotalDeductions { get; set; }
        public decimal IncomeTax { get; set; }
        public decimal SocialInsuranceEmployee { get; set; }
        public decimal SocialInsuranceEmployer { get; set; }
        public decimal MedicalInsurance { get; set; }
        public decimal LateDeduction { get; set; }

        public decimal OvertimeHours { get; set; }
        public decimal OvertimeAmount { get; set; }

        public decimal ActualWorkingDays { get; set; }
        public decimal AbsentDays { get; set; }
        public decimal LeaveDays { get; set; }

        public decimal NetSalary { get; set; }

        public string Status { get; set; } = "Draft";
        public string StatusAr { get; set; } = "مسودة";

        public DateTime? ProcessedAt { get; set; }
        public Guid? ProcessedBy { get; set; }
        public string? ProcessedByName { get; set; }

        public DateTime? ApprovedAt { get; set; }
        public Guid? ApprovedBy { get; set; }
        public string? ApprovedByName { get; set; }

        public DateTime? PaidAt { get; set; }
        public Guid? PaidBy { get; set; }
        public string? PaidByName { get; set; }

        public string? Notes { get; set; }
        public string? NotesAr { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // Calculated properties
        public bool CanEdit => Status == "Draft";
        public bool CanProcess => Status == "Draft";
        public bool CanApprove => Status == "Processed";
        public bool CanPay => Status == "Approved";
        public bool CanReject => Status == "Processed" || Status == "Approved";

        // Allowances and deductions details
        public List<PayrollAllowanceDto> Allowances { get; set; } = new();
        public List<PayrollDeductionDto> Deductions { get; set; } = new();
    }

    /// <summary>
    /// Create payroll DTO
    /// DTO إنشاء كشف المرتبات
    /// </summary>
    public class CreatePayrollDto
    {
        [Required(ErrorMessage = "Employee is required")]
        public Guid EmployeeId { get; set; }

        [Required(ErrorMessage = "Payroll month is required")]
        [Range(1, 12, ErrorMessage = "Month must be between 1 and 12")]
        public int PayrollMonth { get; set; }

        [Required(ErrorMessage = "Payroll year is required")]
        [Range(2020, 2050, ErrorMessage = "Year must be between 2020 and 2050")]
        public int PayrollYear { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "Basic salary must be positive")]
        public decimal BasicSalary { get; set; }

        [Range(0, 31, ErrorMessage = "Working days must be between 0 and 31")]
        public decimal ActualWorkingDays { get; set; }

        [Range(0, 31, ErrorMessage = "Absent days must be between 0 and 31")]
        public decimal AbsentDays { get; set; }

        [Range(0, 31, ErrorMessage = "Leave days must be between 0 and 31")]
        public decimal LeaveDays { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "Overtime hours must be positive")]
        public decimal OvertimeHours { get; set; }

        [MaxLength(1000, ErrorMessage = "Notes cannot exceed 1000 characters")]
        public string? Notes { get; set; }

        [MaxLength(1000, ErrorMessage = "Arabic notes cannot exceed 1000 characters")]
        public string? NotesAr { get; set; }

        public List<CreatePayrollAllowanceDto> Allowances { get; set; } = new();
        public List<CreatePayrollDeductionDto> Deductions { get; set; } = new();
    }

    /// <summary>
    /// Update payroll DTO
    /// DTO تحديث كشف المرتبات
    /// </summary>
    public class UpdatePayrollDto : CreatePayrollDto
    {
        [Required]
        public Guid Id { get; set; }
    }

    /// <summary>
    /// Payroll allowance DTO
    /// DTO بدل كشف المرتبات
    /// </summary>
    public class PayrollAllowanceDto
    {
        public Guid Id { get; set; }
        public Guid PayrollId { get; set; }
        public Guid SalaryComponentId { get; set; }
        public string ComponentName { get; set; } = string.Empty;
        public string ComponentNameAr { get; set; } = string.Empty;
        public string ComponentType { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public decimal? Percentage { get; set; }
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Create payroll allowance DTO
    /// DTO إنشاء بدل كشف المرتبات
    /// </summary>
    public class CreatePayrollAllowanceDto
    {
        [Required]
        public Guid SalaryComponentId { get; set; }

        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "Amount must be positive")]
        public decimal Amount { get; set; }

        [Range(0, 100, ErrorMessage = "Percentage must be between 0 and 100")]
        public decimal? Percentage { get; set; }

        [MaxLength(500, ErrorMessage = "Notes cannot exceed 500 characters")]
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Payroll deduction DTO
    /// DTO خصم كشف المرتبات
    /// </summary>
    public class PayrollDeductionDto
    {
        public Guid Id { get; set; }
        public Guid PayrollId { get; set; }
        public Guid SalaryComponentId { get; set; }
        public string ComponentName { get; set; } = string.Empty;
        public string ComponentNameAr { get; set; } = string.Empty;
        public string ComponentType { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public decimal? Percentage { get; set; }
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Create payroll deduction DTO
    /// DTO إنشاء خصم كشف المرتبات
    /// </summary>
    public class CreatePayrollDeductionDto
    {
        [Required]
        public Guid SalaryComponentId { get; set; }

        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "Amount must be positive")]
        public decimal Amount { get; set; }

        [Range(0, 100, ErrorMessage = "Percentage must be between 0 and 100")]
        public decimal? Percentage { get; set; }

        [MaxLength(500, ErrorMessage = "Notes cannot exceed 500 characters")]
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Payroll list request DTO
    /// DTO طلب قائمة كشوف المرتبات
    /// </summary>
    public class PayrollListDto
    {
        public Guid? CompanyId { get; set; }
        public Guid? EmployeeId { get; set; }
        public Guid? DepartmentId { get; set; }
        public int? PayrollMonth { get; set; }
        public int? PayrollYear { get; set; }
        public string? Status { get; set; }
        public string? SearchTerm { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? SortBy { get; set; } = "PayrollYear,PayrollMonth";
        public string? SortDirection { get; set; } = "desc";
    }

    /// <summary>
    /// Payroll statistics DTO
    /// DTO إحصائيات كشوف المرتبات
    /// </summary>
    public class PayrollStatisticsDto
    {
        public int TotalPayrolls { get; set; }
        public int DraftPayrolls { get; set; }
        public int ProcessedPayrolls { get; set; }
        public int ApprovedPayrolls { get; set; }
        public int PaidPayrolls { get; set; }

        public decimal TotalGrossSalary { get; set; }
        public decimal TotalNetSalary { get; set; }
        public decimal TotalDeductions { get; set; }
        public decimal TotalAllowances { get; set; }

        public Dictionary<string, decimal> SalaryByDepartment { get; set; } = new();
        public Dictionary<string, int> PayrollsByMonth { get; set; } = new();
        public Dictionary<string, decimal> DeductionsByType { get; set; } = new();
        public Dictionary<string, decimal> AllowancesByType { get; set; } = new();
    }

    /// <summary>
    /// Payroll process request DTO
    /// DTO طلب معالجة كشف المرتبات
    /// </summary>
    public class ProcessPayrollDto
    {
        [Required]
        public Guid PayrollId { get; set; }

        [MaxLength(500, ErrorMessage = "Notes cannot exceed 500 characters")]
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Payroll approve request DTO
    /// DTO طلب الموافقة على كشف المرتبات
    /// </summary>
    public class ApprovePayrollDto
    {
        [Required]
        public Guid PayrollId { get; set; }

        [MaxLength(500, ErrorMessage = "Notes cannot exceed 500 characters")]
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Payroll payment request DTO
    /// DTO طلب دفع كشف المرتبات
    /// </summary>
    public class PayPayrollDto
    {
        [Required]
        public Guid PayrollId { get; set; }

        [MaxLength(500, ErrorMessage = "Notes cannot exceed 500 characters")]
        public string? Notes { get; set; }

        [MaxLength(100, ErrorMessage = "Payment method cannot exceed 100 characters")]
        public string? PaymentMethod { get; set; }

        [MaxLength(200, ErrorMessage = "Payment reference cannot exceed 200 characters")]
        public string? PaymentReference { get; set; }
    }
}
