<!-- Real-time Notifications Component -->
<div class="notifications-container">
    <!-- Notification Bell -->
    <div class="notification-bell" id="notificationBell">
        <button type="button" class="btn btn-ghost notification-btn" data-bs-toggle="dropdown">
            <i class="bi bi-bell"></i>
            <span class="notification-badge" id="notificationBadge">0</span>
        </button>
        
        <!-- Notifications Dropdown -->
        <div class="dropdown-menu dropdown-menu-end notifications-dropdown">
            <div class="notifications-header">
                <h6>الإشعارات</h6>
                <div class="notifications-actions">
                    <button type="button" class="btn btn-sm btn-ghost" onclick="markAllAsRead()">
                        <i class="bi bi-check-all"></i>
                        قراءة الكل
                    </button>
                    <button type="button" class="btn btn-sm btn-ghost" onclick="clearAllNotifications()">
                        <i class="bi bi-trash"></i>
                        مسح الكل
                    </button>
                </div>
            </div>
            
            <div class="notifications-content" id="notificationsContent">
                <!-- Notifications will be loaded here -->
            </div>
            
            <div class="notifications-footer">
                <a href="/notifications" class="btn btn-sm btn-primary w-100">
                    عرض جميع الإشعارات
                </a>
            </div>
        </div>
    </div>
    
    <!-- Toast Notifications Container -->
    <div class="toast-notifications-container" id="toastContainer">
        <!-- Toast notifications will appear here -->
    </div>
</div>

<style>
.notifications-container {
    position: relative;
}

.notification-bell {
    position: relative;
}

.notification-btn {
    position: relative;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.notification-btn:hover {
    background: rgba(0, 0, 0, 0.05);
}

.notification-btn i {
    font-size: 18px;
    color: #6c757d;
}

.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0);
    transition: transform 0.2s ease;
}

.notification-badge.show {
    transform: scale(1);
}

.notification-badge.pulse {
    animation: pulse 2s infinite;
}

@@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.notifications-dropdown {
    width: 380px;
    max-height: 500px;
    border: none;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    padding: 0;
    overflow: hidden;
}

.notifications-header {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.notifications-header h6 {
    margin: 0;
    font-weight: 600;
    color: #212529;
}

.notifications-actions {
    display: flex;
    gap: 8px;
}

.notifications-content {
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px 20px;
    border-bottom: 1px solid #f1f3f4;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.notification-item:hover {
    background: #f8f9fa;
}

.notification-item.unread {
    background: #f0f8ff;
    border-left: 3px solid #0d6efd;
}

.notification-item.unread::before {
    content: '';
    position: absolute;
    top: 20px;
    right: 20px;
    width: 8px;
    height: 8px;
    background: #0d6efd;
    border-radius: 50%;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
}

.notification-icon.success {
    background: #d1edff;
    color: #28a745;
}

.notification-icon.warning {
    background: #fff3cd;
    color: #ffc107;
}

.notification-icon.danger {
    background: #f8d7da;
    color: #dc3545;
}

.notification-icon.info {
    background: #d1ecf1;
    color: #17a2b8;
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-size: 14px;
    font-weight: 500;
    color: #212529;
    margin-bottom: 4px;
    line-height: 1.4;
}

.notification-message {
    font-size: 13px;
    color: #6c757d;
    line-height: 1.4;
    margin-bottom: 6px;
}

.notification-time {
    font-size: 11px;
    color: #adb5bd;
}

.notification-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

.notification-actions .btn {
    font-size: 11px;
    padding: 4px 8px;
}

.notifications-footer {
    padding: 12px 20px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

.notifications-empty {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.notifications-empty i {
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.5;
}

.toast-notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1060;
    max-width: 350px;
}

.toast-notification {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 12px;
    overflow: hidden;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.toast-notification.show {
    transform: translateX(0);
}

.toast-notification.hide {
    transform: translateX(100%);
    opacity: 0;
}

.toast-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.toast-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.toast-title {
    flex: 1;
    font-size: 13px;
    font-weight: 600;
    color: #212529;
}

.toast-close {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 16px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toast-body {
    padding: 12px 16px;
    font-size: 13px;
    color: #495057;
    line-height: 1.4;
}

.toast-progress {
    height: 3px;
    background: #e9ecef;
    overflow: hidden;
}

.toast-progress-bar {
    height: 100%;
    background: #0d6efd;
    transition: width 0.1s linear;
}

.toast-notification.success .toast-icon {
    background: #d1edff;
    color: #28a745;
}

.toast-notification.success .toast-progress-bar {
    background: #28a745;
}

.toast-notification.warning .toast-icon {
    background: #fff3cd;
    color: #ffc107;
}

.toast-notification.warning .toast-progress-bar {
    background: #ffc107;
}

.toast-notification.danger .toast-icon {
    background: #f8d7da;
    color: #dc3545;
}

.toast-notification.danger .toast-progress-bar {
    background: #dc3545;
}

.toast-notification.info .toast-icon {
    background: #d1ecf1;
    color: #17a2b8;
}

.toast-notification.info .toast-progress-bar {
    background: #17a2b8;
}

@@media (max-width: 768px) {
    .notifications-dropdown {
        width: 320px;
        left: -280px !important;
    }

    .toast-notifications-container {
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .toast-notification {
        margin-bottom: 8px;
    }
}
</style>

<script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/6.0.1/signalr.min.js"></script>
<script>
$(document).ready(function() {
    initializeNotifications();
    connectToSignalR();
});

let notificationConnection;
let notifications = [];
let unreadCount = 0;

function initializeNotifications() {
    loadNotifications();

    // Click handler for notification items
    $(document).on('click', '.notification-item', function() {
        const notificationId = $(this).data('id');
        const url = $(this).data('url');

        markAsRead(notificationId);

        if (url) {
            window.location.href = url;
        }
    });

    // Auto-refresh notifications every 30 seconds
    setInterval(loadNotifications, 30000);
}

function connectToSignalR() {
    notificationConnection = new signalR.HubConnectionBuilder()
        .withUrl("/notificationHub")
        .build();

    notificationConnection.start().then(function () {
        console.log("SignalR Connected");

        // Join user group
        const userId = getCurrentUserId();
        if (userId) {
            notificationConnection.invoke("JoinUserGroup", userId);
        }

    }).catch(function (err) {
        console.error("SignalR Connection Error: ", err);
        // Fallback to polling if SignalR fails
        setInterval(loadNotifications, 10000);
    });

    // Handle incoming notifications
    notificationConnection.on("ReceiveNotification", function (notification) {
        addNotification(notification);
        showToastNotification(notification);
        updateNotificationBadge();
    });

    // Handle notification updates
    notificationConnection.on("NotificationRead", function (notificationId) {
        markNotificationAsRead(notificationId);
        updateNotificationBadge();
    });

    // Handle bulk updates
    notificationConnection.on("AllNotificationsRead", function () {
        markAllNotificationsAsRead();
        updateNotificationBadge();
    });
}

function loadNotifications() {
    $.ajax({
        url: '/api/notifications',
        method: 'GET',
        success: function(data) {
            notifications = data.notifications || [];
            unreadCount = data.unreadCount || 0;

            renderNotifications();
            updateNotificationBadge();
        },
        error: function() {
            console.error('Failed to load notifications');
        }
    });
}

function renderNotifications() {
    const container = $('#notificationsContent');
    container.empty();

    if (notifications.length === 0) {
        container.html(`
            <div class="notifications-empty">
                <i class="bi bi-bell"></i>
                <h6>لا توجد إشعارات</h6>
                <p>ستظهر الإشعارات الجديدة هنا</p>
            </div>
        `);
        return;
    }

    notifications.slice(0, 10).forEach(notification => {
        const notificationHtml = createNotificationHtml(notification);
        container.append(notificationHtml);
    });
}

function createNotificationHtml(notification) {
    const timeAgo = getTimeAgo(notification.createdAt);
    const iconClass = getNotificationIcon(notification.type);
    const iconType = getNotificationIconType(notification.type);

    return `
        <div class="notification-item ${notification.isRead ? '' : 'unread'}"
             data-id="${notification.id}"
             data-url="${notification.url || ''}">
            <div class="notification-icon ${iconType}">
                <i class="${iconClass}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">${notification.title}</div>
                <div class="notification-message">${notification.message}</div>
                <div class="notification-time">${timeAgo}</div>
                ${notification.actions ? createNotificationActions(notification.actions) : ''}
            </div>
        </div>
    `;
}

function createNotificationActions(actions) {
    let actionsHtml = '<div class="notification-actions">';

    actions.forEach(action => {
        actionsHtml += `
            <button class="btn btn-sm btn-outline-primary"
                    onclick="handleNotificationAction('${action.type}', '${action.data}')">
                ${action.label}
            </button>
        `;
    });

    actionsHtml += '</div>';
    return actionsHtml;
}

function getNotificationIcon(type) {
    const icons = {
        'leave_request': 'bi bi-calendar-event',
        'payroll': 'bi bi-currency-dollar',
        'attendance': 'bi bi-clock',
        'performance': 'bi bi-star',
        'training': 'bi bi-mortarboard',
        'task': 'bi bi-list-task',
        'system': 'bi bi-gear',
        'default': 'bi bi-bell'
    };

    return icons[type] || icons.default;
}

function getNotificationIconType(type) {
    const types = {
        'leave_request': 'info',
        'payroll': 'success',
        'attendance': 'warning',
        'performance': 'info',
        'training': 'info',
        'task': 'warning',
        'system': 'info',
        'default': 'info'
    };

    return types[type] || types.default;
}

function getTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) {
        return 'الآن';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `منذ ${minutes} دقيقة`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `منذ ${hours} ساعة`;
    } else {
        const days = Math.floor(diffInSeconds / 86400);
        return `منذ ${days} يوم`;
    }
}

function updateNotificationBadge() {
    const badge = $('#notificationBadge');

    if (unreadCount > 0) {
        badge.text(unreadCount > 99 ? '99+' : unreadCount);
        badge.addClass('show pulse');
    } else {
        badge.removeClass('show pulse');
    }
}

function addNotification(notification) {
    notifications.unshift(notification);
    unreadCount++;

    // Keep only last 50 notifications
    if (notifications.length > 50) {
        notifications = notifications.slice(0, 50);
    }

    renderNotifications();
}

function markAsRead(notificationId) {
    $.ajax({
        url: `/api/notifications/${notificationId}/read`,
        method: 'POST',
        success: function() {
            markNotificationAsRead(notificationId);
            updateNotificationBadge();
        }
    });
}

function markNotificationAsRead(notificationId) {
    const notification = notifications.find(n => n.id === notificationId);
    if (notification && !notification.isRead) {
        notification.isRead = true;
        unreadCount = Math.max(0, unreadCount - 1);

        $(`.notification-item[data-id="${notificationId}"]`).removeClass('unread');
    }
}

function markAllAsRead() {
    $.ajax({
        url: '/api/notifications/read-all',
        method: 'POST',
        success: function() {
            markAllNotificationsAsRead();
            updateNotificationBadge();
        }
    });
}

function markAllNotificationsAsRead() {
    notifications.forEach(notification => {
        notification.isRead = true;
    });

    unreadCount = 0;
    $('.notification-item').removeClass('unread');
}

function clearAllNotifications() {
    if (confirm('هل تريد مسح جميع الإشعارات؟')) {
        $.ajax({
            url: '/api/notifications/clear-all',
            method: 'DELETE',
            success: function() {
                notifications = [];
                unreadCount = 0;
                renderNotifications();
                updateNotificationBadge();
            }
        });
    }
}

function showToastNotification(notification) {
    const toast = createToastNotification(notification);
    $('#toastContainer').append(toast);

    // Show toast
    setTimeout(() => {
        toast.addClass('show');
    }, 100);

    // Auto-hide after 5 seconds
    const duration = 5000;
    const progressBar = toast.find('.toast-progress-bar');

    let progress = 100;
    const interval = setInterval(() => {
        progress -= (100 / (duration / 100));
        progressBar.css('width', progress + '%');

        if (progress <= 0) {
            clearInterval(interval);
            hideToastNotification(toast);
        }
    }, 100);

    // Click to close
    toast.find('.toast-close').on('click', () => {
        clearInterval(interval);
        hideToastNotification(toast);
    });

    // Click to navigate
    toast.on('click', (e) => {
        if (!$(e.target).hasClass('toast-close') && notification.url) {
            window.location.href = notification.url;
        }
    });
}

function createToastNotification(notification) {
    const iconClass = getNotificationIcon(notification.type);
    const iconType = getNotificationIconType(notification.type);

    return $(`
        <div class="toast-notification ${iconType}">
            <div class="toast-header">
                <div class="toast-icon">
                    <i class="${iconClass}"></i>
                </div>
                <div class="toast-title">${notification.title}</div>
                <button class="toast-close">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            <div class="toast-body">
                ${notification.message}
            </div>
            <div class="toast-progress">
                <div class="toast-progress-bar" style="width: 100%"></div>
            </div>
        </div>
    `);
}

function hideToastNotification(toast) {
    toast.addClass('hide');
    setTimeout(() => {
        toast.remove();
    }, 300);
}

function handleNotificationAction(actionType, actionData) {
    switch(actionType) {
        case 'approve':
            // Handle approval action
            break;
        case 'reject':
            // Handle rejection action
            break;
        case 'view':
            window.location.href = actionData;
            break;
    }
}

function getCurrentUserId() {
    // Get current user ID from meta tag or global variable
    return $('meta[name="user-id"]').attr('content') || window.currentUserId;
}

// Expose functions globally
window.showToastNotification = showToastNotification;
window.markAsRead = markAsRead;
window.markAllAsRead = markAllAsRead;
window.clearAllNotifications = clearAllNotifications;
window.handleNotificationAction = handleNotificationAction;
</script>
