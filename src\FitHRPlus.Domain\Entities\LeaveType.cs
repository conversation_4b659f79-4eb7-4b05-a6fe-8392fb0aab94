using FitHRPlus.Domain.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Domain.Entities
{
    /// <summary>
    /// Leave type entity for defining different types of leaves
    /// كيان نوع الإجازة لتعريف أنواع الإجازات المختلفة
    /// </summary>
    public class LeaveType : BaseEntity
    {
        /// <summary>
        /// Company ID that owns this leave type
        /// معرف الشركة التي تملك نوع الإجازة هذا
        /// </summary>
        [Required]
        public Guid CompanyId { get; set; }

        /// <summary>
        /// Leave type name in English
        /// اسم نوع الإجازة بالإنجليزية
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Leave type name in Arabic
        /// اسم نوع الإجازة بالعربية
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// Description in English
        /// الوصف بالإنجليزية
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Description in Arabic
        /// الوصف بالعربية
        /// </summary>
        [MaxLength(500)]
        public string? DescriptionAr { get; set; }

        /// <summary>
        /// Maximum days per year
        /// الحد الأقصى للأيام في السنة
        /// </summary>
        public int? MaxDaysPerYear { get; set; }

        /// <summary>
        /// Maximum consecutive days
        /// الحد الأقصى للأيام المتتالية
        /// </summary>
        public int? MaxConsecutiveDays { get; set; }

        /// <summary>
        /// Minimum days notice required
        /// الحد الأدنى لأيام الإشعار المطلوبة
        /// </summary>
        public int MinDaysNotice { get; set; } = 0;

        /// <summary>
        /// Is this leave type paid
        /// هل نوع الإجازة هذا مدفوع الأجر
        /// </summary>
        public bool IsPaid { get; set; } = true;

        /// <summary>
        /// Requires approval
        /// يتطلب موافقة
        /// </summary>
        public bool RequiresApproval { get; set; } = true;

        /// <summary>
        /// Requires supporting document
        /// يتطلب مستند داعم
        /// </summary>
        public bool RequiresDocument { get; set; } = false;

        /// <summary>
        /// Can be carried forward to next year
        /// يمكن ترحيله للسنة القادمة
        /// </summary>
        public bool CarryForward { get; set; } = false;

        /// <summary>
        /// Maximum days that can be carried forward
        /// الحد الأقصى للأيام التي يمكن ترحيلها
        /// </summary>
        public int CarryForwardLimit { get; set; } = 0;

        /// <summary>
        /// Gender restriction (null for both, 'Male', 'Female')
        /// قيود الجنس
        /// </summary>
        [MaxLength(10)]
        public string? Gender { get; set; }

        // Navigation Properties - خصائص التنقل

        /// <summary>
        /// Company that owns this leave type
        /// الشركة التي تملك نوع الإجازة هذا
        /// </summary>
        public virtual Company Company { get; set; } = null!;

        /// <summary>
        /// Leave requests of this type
        /// طلبات الإجازات من هذا النوع
        /// </summary>
        public virtual ICollection<LeaveRequest> LeaveRequests { get; set; } = new List<LeaveRequest>();

        /// <summary>
        /// Leave balances for this type
        /// أرصدة الإجازات لهذا النوع
        /// </summary>
        public virtual ICollection<LeaveBalance> LeaveBalances { get; set; } = new List<LeaveBalance>();
    }
}