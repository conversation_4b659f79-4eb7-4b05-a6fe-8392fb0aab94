using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Web.Models.Shared
{
    public class AdvancedFiltersViewModel
    {
        public bool ShowDateFilter { get; set; } = true;
        public bool ShowDepartmentFilter { get; set; } = true;
        public bool ShowEmployeeFilter { get; set; } = true;
        public bool ShowStatusFilter { get; set; } = true;
        public bool ShowPriorityFilter { get; set; } = false;
        public bool ShowAmountFilter { get; set; } = false;
        
        public List<DepartmentOption>? Departments { get; set; }
        public List<EmployeeOption>? Employees { get; set; }
        public List<StatusOption>? StatusOptions { get; set; }
        public List<CustomFilter>? CustomFilters { get; set; }
    }

    public class DepartmentOption
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
    }

    public class EmployeeOption
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
    }

    public class StatusOption
    {
        public string Value { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
    }

    public class CustomFilter
    {
        public string Id { get; set; } = string.Empty;
        public string Label { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // select, text, number, checkbox
        public string? Placeholder { get; set; }
        public bool Multiple { get; set; } = false;
        public List<FilterOption>? Options { get; set; }
    }

    public class FilterOption
    {
        public string Value { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
    }
}
