using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Application.DTOs.Attendance
{
    /// <summary>
    /// Attendance list request DTO for filtering and pagination
    /// كائنة نقل بيانات طلب قائمة الحضور للتصفية والترقيم
    /// </summary>
    public class AttendanceListRequestDto
    {
        /// <summary>
        /// Employee ID filter
        /// تصفية معرف الموظف
        /// </summary>
        public Guid? EmployeeId { get; set; }

        /// <summary>
        /// Company ID filter
        /// تصفية معرف الشركة
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// Department ID filter
        /// تصفية معرف القسم
        /// </summary>
        public Guid? DepartmentId { get; set; }

        /// <summary>
        /// Position ID filter
        /// تصفية معرف المنصب
        /// </summary>
        public Guid? PositionId { get; set; }

        /// <summary>
        /// Date from filter
        /// تصفية التاريخ من
        /// </summary>
        public DateTime? DateFrom { get; set; }

        /// <summary>
        /// Date to filter
        /// تصفية التاريخ إلى
        /// </summary>
        public DateTime? DateTo { get; set; }

        /// <summary>
        /// Attendance status filter
        /// تصفية حالة الحضور
        /// </summary>
        public string? AttendanceStatus { get; set; }

        /// <summary>
        /// Show only late arrivals
        /// عرض المتأخرين فقط
        /// </summary>
        public bool? ShowLateOnly { get; set; }

        /// <summary>
        /// Show only early departures
        /// عرض المغادرين مبكراً فقط
        /// </summary>
        public bool? ShowEarlyDepartureOnly { get; set; }

        /// <summary>
        /// Show only overtime
        /// عرض العمل الإضافي فقط
        /// </summary>
        public bool? ShowOvertimeOnly { get; set; }

        /// <summary>
        /// Show only unapproved records
        /// عرض السجلات غير المعتمدة فقط
        /// </summary>
        public bool? ShowUnapprovedOnly { get; set; }

        /// <summary>
        /// Search term (employee name, number, etc.)
        /// مصطلح البحث (اسم الموظف، الرقم، إلخ)
        /// </summary>
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Sort field
        /// حقل الترتيب
        /// </summary>
        public string? SortBy { get; set; } = "Date";

        /// <summary>
        /// Sort direction (asc/desc)
        /// اتجاه الترتيب (تصاعدي/تنازلي)
        /// </summary>
        public string? SortDirection { get; set; } = "desc";

        /// <summary>
        /// Page number (1-based)
        /// رقم الصفحة (يبدأ من 1)
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "Page number must be greater than 0")]
        public int Page { get; set; } = 1;

        /// <summary>
        /// Page size
        /// حجم الصفحة
        /// </summary>
        [Range(1, 100, ErrorMessage = "Page size must be between 1 and 100")]
        public int PageSize { get; set; } = 10;
    }

    /// <summary>
    /// Attendance list response DTO
    /// كائنة نقل بيانات استجابة قائمة الحضور
    /// </summary>
    public class AttendanceListResponseDto
    {
        /// <summary>
        /// List of attendance records
        /// قائمة سجلات الحضور
        /// </summary>
        public List<AttendanceDto> Attendances { get; set; } = new();

        /// <summary>
        /// Total number of records (before pagination)
        /// العدد الإجمالي للسجلات (قبل الترقيم)
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Current page number
        /// رقم الصفحة الحالية
        /// </summary>
        public int CurrentPage { get; set; }

        /// <summary>
        /// Page size
        /// حجم الصفحة
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// Total number of pages
        /// العدد الإجمالي للصفحات
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// Whether there is a previous page
        /// ما إذا كانت هناك صفحة سابقة
        /// </summary>
        public bool HasPreviousPage { get; set; }

        /// <summary>
        /// Whether there is a next page
        /// ما إذا كانت هناك صفحة تالية
        /// </summary>
        public bool HasNextPage { get; set; }

        /// <summary>
        /// Summary statistics for the filtered data
        /// إحصائيات ملخصة للبيانات المصفاة
        /// </summary>
        public AttendanceStatisticsDto Statistics { get; set; } = new();
    }

    /// <summary>
    /// Attendance statistics DTO
    /// كائنة نقل بيانات إحصائيات الحضور
    /// </summary>
    public class AttendanceStatisticsDto
    {
        /// <summary>
        /// Total attendance records
        /// إجمالي سجلات الحضور
        /// </summary>
        public int TotalRecords { get; set; }

        /// <summary>
        /// Total present days
        /// إجمالي أيام الحضور
        /// </summary>
        public int TotalPresentDays { get; set; }

        /// <summary>
        /// Total absent days
        /// إجمالي أيام الغياب
        /// </summary>
        public int TotalAbsentDays { get; set; }

        /// <summary>
        /// Total late arrivals
        /// إجمالي حالات التأخير
        /// </summary>
        public int TotalLateArrivals { get; set; }

        /// <summary>
        /// Total early departures
        /// إجمالي حالات المغادرة المبكرة
        /// </summary>
        public int TotalEarlyDepartures { get; set; }

        /// <summary>
        /// Total overtime records
        /// إجمالي سجلات العمل الإضافي
        /// </summary>
        public int TotalOvertimeRecords { get; set; }

        /// <summary>
        /// Average attendance percentage
        /// متوسط نسبة الحضور
        /// </summary>
        public double AverageAttendancePercentage { get; set; }

        /// <summary>
        /// Total working hours
        /// إجمالي ساعات العمل
        /// </summary>
        public TimeSpan TotalWorkingHours { get; set; }

        /// <summary>
        /// Total overtime hours
        /// إجمالي ساعات العمل الإضافي
        /// </summary>
        public TimeSpan TotalOvertimeHours { get; set; }

        /// <summary>
        /// Average working hours per day
        /// متوسط ساعات العمل يومياً
        /// </summary>
        public TimeSpan AverageWorkingHours { get; set; }

        /// <summary>
        /// Department breakdown
        /// تفصيل الأقسام
        /// </summary>
        public List<DepartmentAttendanceStatsDto> DepartmentBreakdown { get; set; } = new();
    }

    /// <summary>
    /// Department attendance statistics DTO
    /// كائنة نقل بيانات إحصائيات حضور القسم
    /// </summary>
    public class DepartmentAttendanceStatsDto
    {
        /// <summary>
        /// Department ID
        /// معرف القسم
        /// </summary>
        public Guid DepartmentId { get; set; }

        /// <summary>
        /// Department name
        /// اسم القسم
        /// </summary>
        public string DepartmentName { get; set; } = string.Empty;

        /// <summary>
        /// Total employees in department
        /// إجمالي الموظفين في القسم
        /// </summary>
        public int TotalEmployees { get; set; }

        /// <summary>
        /// Present employees
        /// الموظفين الحاضرين
        /// </summary>
        public int PresentEmployees { get; set; }

        /// <summary>
        /// Absent employees
        /// الموظفين الغائبين
        /// </summary>
        public int AbsentEmployees { get; set; }

        /// <summary>
        /// Late employees
        /// الموظفين المتأخرين
        /// </summary>
        public int LateEmployees { get; set; }

        /// <summary>
        /// Attendance percentage for department
        /// نسبة الحضور للقسم
        /// </summary>
        public double AttendancePercentage => TotalEmployees > 0 ? 
            (double)PresentEmployees / TotalEmployees * 100 : 0;
    }

    /// <summary>
    /// Daily attendance report DTO
    /// كائنة نقل بيانات تقرير الحضور اليومي
    /// </summary>
    public class DailyAttendanceReportDto
    {
        /// <summary>
        /// Report date
        /// تاريخ التقرير
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// Total employees
        /// إجمالي الموظفين
        /// </summary>
        public int TotalEmployees { get; set; }

        /// <summary>
        /// Present employees
        /// الموظفين الحاضرين
        /// </summary>
        public int PresentEmployees { get; set; }

        /// <summary>
        /// Absent employees
        /// الموظفين الغائبين
        /// </summary>
        public int AbsentEmployees { get; set; }

        /// <summary>
        /// Late employees
        /// الموظفين المتأخرين
        /// </summary>
        public int LateEmployees { get; set; }

        /// <summary>
        /// Early departure employees
        /// الموظفين المغادرين مبكراً
        /// </summary>
        public int EarlyDepartureEmployees { get; set; }

        /// <summary>
        /// Overtime employees
        /// الموظفين في العمل الإضافي
        /// </summary>
        public int OvertimeEmployees { get; set; }

        /// <summary>
        /// Attendance percentage
        /// نسبة الحضور
        /// </summary>
        public double AttendancePercentage => TotalEmployees > 0 ? 
            (double)PresentEmployees / TotalEmployees * 100 : 0;

        /// <summary>
        /// Department breakdown for the day
        /// تفصيل الأقسام لليوم
        /// </summary>
        public List<DepartmentAttendanceStatsDto> DepartmentBreakdown { get; set; } = new();

        /// <summary>
        /// Detailed attendance records
        /// سجلات الحضور التفصيلية
        /// </summary>
        public List<AttendanceDto> AttendanceRecords { get; set; } = new();
    }

    /// <summary>
    /// Check-in/Check-out request DTO
    /// كائنة نقل بيانات طلب الحضور/الانصراف
    /// </summary>
    public class CheckInOutRequestDto
    {
        /// <summary>
        /// Employee ID
        /// معرف الموظف
        /// </summary>
        [Required(ErrorMessage = "Employee ID is required")]
        public Guid EmployeeId { get; set; }

        /// <summary>
        /// Check-in or check-out time
        /// وقت الحضور أو الانصراف
        /// </summary>
        public DateTime? Time { get; set; }

        /// <summary>
        /// Location (GPS coordinates or location name)
        /// الموقع (إحداثيات GPS أو اسم الموقع)
        /// </summary>
        public string? Location { get; set; }

        /// <summary>
        /// Device/method used for check-in/out
        /// الجهاز/الطريقة المستخدمة للحضور/الانصراف
        /// </summary>
        public string? Device { get; set; }

        /// <summary>
        /// Notes or comments
        /// ملاحظات أو تعليقات
        /// </summary>
        public string? Notes { get; set; }
    }
}
