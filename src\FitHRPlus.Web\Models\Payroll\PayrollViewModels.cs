using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace FitHRPlus.Web.Models.Payroll
{
    /// <summary>
    /// Payroll list view model
    /// نموذج عرض قائمة كشوف المرتبات
    /// </summary>
    public class PayrollListViewModel
    {
        public List<PayrollViewModel> Payrolls { get; set; } = new();
        
        // Filter properties
        public int? PayrollMonth { get; set; }
        public int? PayrollYear { get; set; }
        public string? Status { get; set; }
        public Guid? EmployeeId { get; set; }
        public Guid? DepartmentId { get; set; }
        public string? SearchTerm { get; set; }
        
        // Pagination
        public int CurrentPage { get; set; } = 1;
        public int TotalPages { get; set; }
        public int TotalCount { get; set; }
        public int PageSize { get; set; } = 20;
        
        // Filter options
        public List<SelectListItem> Employees { get; set; } = new();
        public List<SelectListItem> Departments { get; set; } = new();
        public List<SelectListItem> StatusOptions { get; set; } = new();
        public List<SelectListItem> MonthOptions { get; set; } = new();
        public List<SelectListItem> YearOptions { get; set; } = new();
        
        // Statistics
        public PayrollStatisticsViewModel? Statistics { get; set; }
    }

    /// <summary>
    /// Payroll view model
    /// نموذج عرض كشف المرتبات
    /// </summary>
    public class PayrollViewModel
    {
        public Guid Id { get; set; }
        public Guid EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string EmployeeNameAr { get; set; } = string.Empty;
        public string EmployeeCode { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string DepartmentNameAr { get; set; } = string.Empty;
        public string? PositionTitle { get; set; }
        public string? PositionTitleAr { get; set; }

        public int PayrollMonth { get; set; }
        public int PayrollYear { get; set; }
        public string PayrollPeriod => $"{PayrollYear}-{PayrollMonth:D2}";
        public string PayrollPeriodDisplay => $"{GetMonthName(PayrollMonth)} {PayrollYear}";

        public decimal BasicSalary { get; set; }
        public decimal GrossSalary { get; set; }
        public decimal NetSalary { get; set; }

        public string Status { get; set; } = "Draft";
        public string StatusAr { get; set; } = "مسودة";
        public string StatusBadgeClass { get; set; } = "bg-secondary";

        public DateTime CreatedAt { get; set; }

        public bool CanEdit { get; set; }
        public bool CanProcess { get; set; }
        public bool CanApprove { get; set; }
        public bool CanPay { get; set; }

        private static string GetMonthName(int month)
        {
            return month switch
            {
                1 => "January / يناير",
                2 => "February / فبراير",
                3 => "March / مارس",
                4 => "April / أبريل",
                5 => "May / مايو",
                6 => "June / يونيو",
                7 => "July / يوليو",
                8 => "August / أغسطس",
                9 => "September / سبتمبر",
                10 => "October / أكتوبر",
                11 => "November / نوفمبر",
                12 => "December / ديسمبر",
                _ => month.ToString()
            };
        }
    }

    /// <summary>
    /// Payroll details view model
    /// نموذج عرض تفاصيل كشف المرتبات
    /// </summary>
    public class PayrollDetailsViewModel
    {
        public Guid Id { get; set; }
        public Guid EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string EmployeeNameAr { get; set; } = string.Empty;
        public string EmployeeCode { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string DepartmentNameAr { get; set; } = string.Empty;
        public string? PositionTitle { get; set; }
        public string? PositionTitleAr { get; set; }

        public int PayrollMonth { get; set; }
        public int PayrollYear { get; set; }
        public string PayrollPeriod => $"{PayrollYear}-{PayrollMonth:D2}";

        // Salary components
        public decimal BasicSalary { get; set; }
        public decimal TotalAllowances { get; set; }
        public decimal GrossSalary { get; set; }
        public decimal TotalDeductions { get; set; }
        public decimal NetSalary { get; set; }

        // Detailed deductions
        public decimal IncomeTax { get; set; }
        public decimal SocialInsuranceEmployee { get; set; }
        public decimal SocialInsuranceEmployer { get; set; }
        public decimal MedicalInsurance { get; set; }
        public decimal LateDeduction { get; set; }

        // Overtime
        public decimal OvertimeHours { get; set; }
        public decimal OvertimeAmount { get; set; }

        // Working days
        public decimal ActualWorkingDays { get; set; }
        public decimal AbsentDays { get; set; }
        public decimal LeaveDays { get; set; }

        public string Status { get; set; } = "Draft";
        public string StatusAr { get; set; } = "مسودة";
        public string StatusBadgeClass { get; set; } = "bg-secondary";

        // Workflow information
        public DateTime? ProcessedAt { get; set; }
        public string? ProcessedByName { get; set; }
        public DateTime? ApprovedAt { get; set; }
        public string? ApprovedByName { get; set; }
        public DateTime? PaidAt { get; set; }
        public string? PaidByName { get; set; }

        public string? Notes { get; set; }
        public string? NotesAr { get; set; }

        public DateTime CreatedAt { get; set; }

        public bool CanEdit { get; set; }
        public bool CanProcess { get; set; }
        public bool CanApprove { get; set; }
        public bool CanPay { get; set; }
        public bool CanReject { get; set; }

        // Allowances and deductions breakdown
        public List<PayrollAllowanceViewModel> Allowances { get; set; } = new();
        public List<PayrollDeductionViewModel> Deductions { get; set; } = new();

        // Additional properties for compatibility
        public string EmployeeNumber { get; set; } = string.Empty;
        public string PositionName { get; set; } = string.Empty;
        public DateTime HireDate { get; set; }
        public decimal TransportationAllowance { get; set; }
        public decimal FoodAllowance { get; set; }
        public decimal HousingAllowance { get; set; }
        public decimal BonusAmount { get; set; }
        public decimal TotalEarnings { get; set; }
        public decimal SocialInsurance { get; set; }
        public decimal AbsenceDeduction { get; set; }
        public decimal AdvanceDeduction { get; set; }
        public decimal OtherDeductions { get; set; }
        public int WorkingDays { get; set; }
        public int PresentDays { get; set; }
        public string? PaymentMethod { get; set; }
        public string? PaymentReference { get; set; }
    }

    /// <summary>
    /// Generate payroll view model
    /// نموذج عرض إنشاء كشوف المرتبات
    /// </summary>
    public class GeneratePayrollViewModel
    {
        [Required(ErrorMessage = "الشهر مطلوب")]
        [Range(1, 12, ErrorMessage = "الشهر يجب أن يكون بين 1 و 12")]
        [Display(Name = "Payroll Month / شهر كشف المرتبات")]
        public int PayrollMonth { get; set; }

        [Required(ErrorMessage = "السنة مطلوبة")]
        [Range(2020, 2050, ErrorMessage = "السنة يجب أن تكون بين 2020 و 2050")]
        [Display(Name = "Payroll Year / سنة كشف المرتبات")]
        public int PayrollYear { get; set; }

        [Display(Name = "Include Inactive Employees / تضمين الموظفين غير النشطين")]
        public bool IncludeInactiveEmployees { get; set; } = false;

        [Display(Name = "Overwrite Existing / استبدال الموجود")]
        public bool OverwriteExisting { get; set; } = false;

        public List<SelectListItem> MonthOptions { get; set; } = new()
        {
            new SelectListItem { Value = "1", Text = "January / يناير" },
            new SelectListItem { Value = "2", Text = "February / فبراير" },
            new SelectListItem { Value = "3", Text = "March / مارس" },
            new SelectListItem { Value = "4", Text = "April / أبريل" },
            new SelectListItem { Value = "5", Text = "May / مايو" },
            new SelectListItem { Value = "6", Text = "June / يونيو" },
            new SelectListItem { Value = "7", Text = "July / يوليو" },
            new SelectListItem { Value = "8", Text = "August / أغسطس" },
            new SelectListItem { Value = "9", Text = "September / سبتمبر" },
            new SelectListItem { Value = "10", Text = "October / أكتوبر" },
            new SelectListItem { Value = "11", Text = "November / نوفمبر" },
            new SelectListItem { Value = "12", Text = "December / ديسمبر" }
        };

        public List<SelectListItem> YearOptions { get; set; } = new();

        // Additional properties for compatibility
        public int? DepartmentId { get; set; }
        public int? PositionId { get; set; }
        public bool IncludeActiveOnly { get; set; } = true;
        public bool IncludeNewHires { get; set; } = true;
        public bool CalculateOvertime { get; set; } = true;
        public bool CalculateDeductions { get; set; } = true;
        public bool CalculateTax { get; set; } = true;
        public bool CalculateInsurance { get; set; } = true;
        public bool CalculateBonuses { get; set; } = true;
        public bool CalculateAllowances { get; set; } = true;
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Payroll allowance view model
    /// نموذج عرض بدل كشف المرتبات
    /// </summary>
    public class PayrollAllowanceViewModel
    {
        public Guid Id { get; set; }
        public string ComponentName { get; set; } = string.Empty;
        public string ComponentNameAr { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public decimal? Percentage { get; set; }
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Payroll deduction view model
    /// نموذج عرض خصم كشف المرتبات
    /// </summary>
    public class PayrollDeductionViewModel
    {
        public Guid Id { get; set; }
        public string ComponentName { get; set; } = string.Empty;
        public string ComponentNameAr { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public decimal? Percentage { get; set; }
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Payroll statistics view model
    /// نموذج عرض إحصائيات كشوف المرتبات
    /// </summary>
    public class PayrollStatisticsViewModel
    {
        public int TotalPayrolls { get; set; }
        public int DraftPayrolls { get; set; }
        public int ProcessedPayrolls { get; set; }
        public int ApprovedPayrolls { get; set; }
        public int PaidPayrolls { get; set; }

        public decimal TotalGrossSalary { get; set; }
        public decimal TotalNetSalary { get; set; }
        public decimal TotalDeductions { get; set; }
        public decimal TotalAllowances { get; set; }

        public decimal AverageGrossSalary => TotalPayrolls > 0 ? TotalGrossSalary / TotalPayrolls : 0;
        public decimal AverageNetSalary => TotalPayrolls > 0 ? TotalNetSalary / TotalPayrolls : 0;
        public decimal DeductionPercentage => TotalGrossSalary > 0 ? (TotalDeductions / TotalGrossSalary) * 100 : 0;
    }

    /// <summary>
    /// Bulk payroll action view model
    /// نموذج عرض الإجراءات المجمعة لكشوف المرتبات
    /// </summary>
    public class BulkPayrollActionViewModel
    {
        public List<Guid> PayrollIds { get; set; } = new();
        public string Action { get; set; } = string.Empty; // Approve, Pay, Reject
        public string? Notes { get; set; }
        public string? PaymentMethod { get; set; }
    }

    /// <summary>
    /// Payroll management view model with tabs
    /// نموذج عرض إدارة كشوف المرتبات مع التبويبات
    /// </summary>
    public class PayrollManagementViewModel
    {
        public int CurrentMonth { get; set; }
        public int CurrentYear { get; set; }
        public List<PayrollItemViewModel> ActivePayrolls { get; set; } = new();
        public List<PayrollItemViewModel> PendingPayrolls { get; set; } = new();
        public List<PayrollItemViewModel> ProcessedPayrolls { get; set; } = new();
        public List<PayrollItemViewModel> PaidPayrolls { get; set; } = new();

        public string CurrentMonthName => GetMonthName(CurrentMonth);

        private string GetMonthName(int month)
        {
            return month switch
            {
                1 => "يناير",
                2 => "فبراير",
                3 => "مارس",
                4 => "أبريل",
                5 => "مايو",
                6 => "يونيو",
                7 => "يوليو",
                8 => "أغسطس",
                9 => "سبتمبر",
                10 => "أكتوبر",
                11 => "نوفمبر",
                12 => "ديسمبر",
                _ => "غير محدد"
            };
        }
    }

    /// <summary>
    /// Payroll item view model for tabs
    /// نموذج عنصر كشف المرتبات للتبويبات
    /// </summary>
    public class PayrollItemViewModel
    {
        public Guid Id { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string EmployeeCode { get; set; } = string.Empty;
        public string Department { get; set; } = string.Empty;
        public decimal BasicSalary { get; set; }
        public decimal Allowances { get; set; }
        public decimal Deductions { get; set; }
        public decimal NetSalary { get; set; }
        public string Status { get; set; } = string.Empty;
        public string PayrollPeriod { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
    }
}
