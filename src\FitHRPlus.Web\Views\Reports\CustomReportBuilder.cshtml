@{
    ViewData["Title"] = "منشئ التقارير المخصصة";
}

<!-- Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-title">
            <h1><i class="bi bi-tools"></i> منشئ التقارير المخصصة</h1>
            <p>إنشاء تقارير مخصصة حسب احتياجاتك</p>
        </div>
        <div class="page-actions">
            <a href="@Url.Action("Index")" class="btn btn-secondary">
                <i class="bi bi-arrow-right"></i>
                العودة للتقارير
            </a>
            <button type="button" class="btn btn-success" onclick="generateReport()">
                <i class="bi bi-play-circle"></i>
                إنشاء التقرير
            </button>
        </div>
    </div>
</div>

<!-- Report Builder -->
<div class="row">
    <div class="col-lg-3">
        <!-- Data Sources -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title">مصادر البيانات</h6>
            </div>
            <div class="card-body">
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="employees" checked>
                    <label class="form-check-label" for="employees">
                        بيانات الموظفين
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="attendance">
                    <label class="form-check-label" for="attendance">
                        بيانات الحضور
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="payroll">
                    <label class="form-check-label" for="payroll">
                        بيانات الرواتب
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="leaves">
                    <label class="form-check-label" for="leaves">
                        بيانات الإجازات
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="departments">
                    <label class="form-check-label" for="departments">
                        بيانات الأقسام
                    </label>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title">المرشحات</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">الفترة الزمنية</label>
                    <select class="form-select form-select-sm">
                        <option>آخر 30 يوم</option>
                        <option>هذا الشهر</option>
                        <option>الشهر الماضي</option>
                        <option>هذا العام</option>
                        <option>مخصص</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">القسم</label>
                    <select class="form-select form-select-sm">
                        <option>جميع الأقسام</option>
                        <option>الموارد البشرية</option>
                        <option>المحاسبة</option>
                        <option>التسويق</option>
                        <option>تقنية المعلومات</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">حالة الموظف</label>
                    <select class="form-select form-select-sm">
                        <option>جميع الحالات</option>
                        <option>نشط</option>
                        <option>غير نشط</option>
                        <option>في إجازة</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <!-- Report Canvas -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title">تصميم التقرير</h6>
            </div>
            <div class="card-body">
                <div class="report-canvas" id="reportCanvas">
                    <div class="canvas-placeholder">
                        <i class="bi bi-file-earmark-text display-1 text-muted"></i>
                        <p class="text-muted">اسحب العناصر من الجانب لبناء تقريرك</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Preview -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title">معاينة التقرير</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>اسم الموظف</th>
                                <th>القسم</th>
                                <th>المنصب</th>
                                <th>الراتب</th>
                                <th>تاريخ التوظيف</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>أحمد محمد</td>
                                <td>تقنية المعلومات</td>
                                <td>مطور برمجيات</td>
                                <td>8,000 ج.م</td>
                                <td>2023-01-15</td>
                            </tr>
                            <tr>
                                <td>فاطمة علي</td>
                                <td>الموارد البشرية</td>
                                <td>أخصائي موارد بشرية</td>
                                <td>6,500 ج.م</td>
                                <td>2023-03-10</td>
                            </tr>
                            <tr>
                                <td>محمد حسن</td>
                                <td>المحاسبة</td>
                                <td>محاسب</td>
                                <td>7,000 ج.م</td>
                                <td>2023-02-20</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3">
        <!-- Report Elements -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title">عناصر التقرير</h6>
            </div>
            <div class="card-body">
                <div class="report-element" draggable="true" data-element="table">
                    <i class="bi bi-table"></i>
                    <span>جدول</span>
                </div>
                <div class="report-element" draggable="true" data-element="chart">
                    <i class="bi bi-bar-chart"></i>
                    <span>رسم بياني</span>
                </div>
                <div class="report-element" draggable="true" data-element="text">
                    <i class="bi bi-text-paragraph"></i>
                    <span>نص</span>
                </div>
                <div class="report-element" draggable="true" data-element="image">
                    <i class="bi bi-image"></i>
                    <span>صورة</span>
                </div>
                <div class="report-element" draggable="true" data-element="summary">
                    <i class="bi bi-card-text"></i>
                    <span>ملخص</span>
                </div>
            </div>
        </div>

        <!-- Export Options -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title">خيارات التصدير</h6>
            </div>
            <div class="card-body">
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="exportPdf" checked>
                    <label class="form-check-label" for="exportPdf">
                        PDF
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="exportExcel">
                    <label class="form-check-label" for="exportExcel">
                        Excel
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="exportCsv">
                    <label class="form-check-label" for="exportCsv">
                        CSV
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="exportWord">
                    <label class="form-check-label" for="exportWord">
                        Word
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
<script>
    function generateReport() {
        showToast('جاري إنشاء التقرير...', 'info');
        
        // Simulate report generation
        setTimeout(() => {
            showToast('تم إنشاء التقرير بنجاح', 'success');
        }, 2000);
    }

    function showToast(message, type) {
        // Simple toast implementation
        const toast = $(`
            <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `);
        
        $('.toast-container').append(toast);
        toast.toast('show');
        
        setTimeout(() => toast.remove(), 5000);
    }

    // Drag and drop functionality
    $(document).ready(function() {
        $('.report-element').on('dragstart', function(e) {
            e.originalEvent.dataTransfer.setData('text/plain', $(this).data('element'));
        });

        $('#reportCanvas').on('dragover', function(e) {
            e.preventDefault();
        });

        $('#reportCanvas').on('drop', function(e) {
            e.preventDefault();
            const elementType = e.originalEvent.dataTransfer.getData('text/plain');
            addElementToCanvas(elementType);
        });
    });

    function addElementToCanvas(elementType) {
        const canvas = $('#reportCanvas');
        const placeholder = canvas.find('.canvas-placeholder');
        
        if (placeholder.length) {
            placeholder.remove();
        }

        const element = $(`
            <div class="report-canvas-element" data-type="${elementType}">
                <div class="element-header">
                    <span>${getElementName(elementType)}</span>
                    <button class="btn btn-sm btn-danger" onclick="removeElement(this)">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
                <div class="element-content">
                    ${getElementContent(elementType)}
                </div>
            </div>
        `);

        canvas.append(element);
    }

    function getElementName(type) {
        const names = {
            'table': 'جدول',
            'chart': 'رسم بياني',
            'text': 'نص',
            'image': 'صورة',
            'summary': 'ملخص'
        };
        return names[type] || type;
    }

    function getElementContent(type) {
        switch(type) {
            case 'table':
                return '<small>جدول البيانات سيظهر هنا</small>';
            case 'chart':
                return '<small>الرسم البياني سيظهر هنا</small>';
            case 'text':
                return '<small>النص سيظهر هنا</small>';
            case 'image':
                return '<small>الصورة ستظهر هنا</small>';
            case 'summary':
                return '<small>الملخص سيظهر هنا</small>';
            default:
                return '<small>المحتوى سيظهر هنا</small>';
        }
    }

    function removeElement(button) {
        $(button).closest('.report-canvas-element').remove();
        
        // If no elements left, show placeholder
        if ($('#reportCanvas .report-canvas-element').length === 0) {
            $('#reportCanvas').html(`
                <div class="canvas-placeholder">
                    <i class="bi bi-file-earmark-text display-1 text-muted"></i>
                    <p class="text-muted">اسحب العناصر من الجانب لبناء تقريرك</p>
                </div>
            `);
        }
    }
</script>

<style>
.report-canvas {
    min-height: 400px;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 20px;
}

.canvas-placeholder {
    text-align: center;
    padding: 60px 20px;
}

.report-element {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    margin-bottom: 8px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    cursor: grab;
    transition: all 0.2s;
}

.report-element:hover {
    background: #e9ecef;
    transform: translateX(-2px);
}

.report-element:active {
    cursor: grabbing;
}

.report-canvas-element {
    margin-bottom: 15px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
}

.element-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 500;
}

.element-content {
    padding: 15px;
    text-align: center;
    color: #6c757d;
}
</style>
}
