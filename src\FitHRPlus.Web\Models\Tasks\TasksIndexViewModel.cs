using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Web.Models.Tasks
{
    public class TasksIndexViewModel
    {
        public int? TotalTasks { get; set; }
        public int? CompletedTasks { get; set; }
        public int? InProgressTasks { get; set; }
        public int? PendingTasks { get; set; }
        public int? OverdueTasks { get; set; }
        
        public List<TaskViewModel>? Tasks { get; set; }
        public List<TaskViewModel>? MyTasks { get; set; }
    }

    public class TaskViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? ProjectName { get; set; }
        public string? Status { get; set; }
        public string? StatusAr { get; set; }
        public string? Priority { get; set; }
        public string? PriorityAr { get; set; }
        public string? AssigneeName { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? DueDate { get; set; }
        public decimal? ProgressPercentage { get; set; }
        public bool IsOverdue { get; set; }
    }
}
