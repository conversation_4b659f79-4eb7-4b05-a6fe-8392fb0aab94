﻿@model FitHRPlus.Web.Models.Dashboard.DashboardViewModel
@{
    ViewData["Title"] = "Dashboard / لوحة التحكم";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-tachometer-alt text-primary me-2"></i>
                        Dashboard / لوحة التحكم
                    </h2>
                    <p class="text-muted mb-0">Welcome to FIT HR Plus - Your comprehensive HR management system</p>
                </div>
                <div>
                    <span class="badge bg-success fs-6">
                        <i class="fas fa-circle me-1"></i>
                        System Online
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.TotalEmployees</h4>
                            <p class="mb-0">Total Employees / إجمالي الموظفين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-primary bg-opacity-75">
                    <small>@Model.ActiveEmployees Active / نشط</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.TodayAttendance</h4>
                            <p class="mb-0">Today's Attendance / حضور اليوم</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-success bg-opacity-75">
                    <small>@Model.AttendanceRateDisplay Rate / معدل</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.PendingLeaveRequests</h4>
                            <p class="mb-0">Pending Leaves / الإجازات المعلقة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-warning bg-opacity-75">
                    <small><a href="@Url.Action("Index", "Leave")" class="text-white">View All / عرض الكل</a></small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.PendingPayrolls</h4>
                            <p class="mb-0">Pending Payrolls / كشوف المرتبات المعلقة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-info bg-opacity-75">
                    <small><a href="@Url.Action("Index", "Payroll")" class="text-white">View All / عرض الكل</a></small>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions / إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <a href="@Url.Action("Create", "Employees")" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-user-plus fa-2x mb-2"></i>
                                <span>Add Employee</span>
                                <small>إضافة موظف</small>
                            </a>
                        </div>
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <a href="@Url.Action("CheckIn", "Attendance")" class="btn btn-outline-success w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-sign-in-alt fa-2x mb-2"></i>
                                <span>Check In</span>
                                <small>تسجيل حضور</small>
                            </a>
                        </div>
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <a href="@Url.Action("Create", "Leave")" class="btn btn-outline-warning w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-calendar-plus fa-2x mb-2"></i>
                                <span>Request Leave</span>
                                <small>طلب إجازة</small>
                            </a>
                        </div>
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <a href="@Url.Action("Generate", "Payroll")" class="btn btn-outline-info w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-calculator fa-2x mb-2"></i>
                                <span>Generate Payroll</span>
                                <small>إنشاء كشف مرتبات</small>
                            </a>
                        </div>
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <a href="@Url.Action("Index", "Reports")" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                <span>View Reports</span>
                                <small>عرض التقارير</small>
                            </a>
                        </div>
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <a href="@Url.Action("Index", "Notifications")" class="btn btn-outline-dark w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-bell fa-2x mb-2"></i>
                                <span>Notifications</span>
                                <small>الإشعارات</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
