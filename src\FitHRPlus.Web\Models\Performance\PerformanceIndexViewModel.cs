using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Web.Models.Performance
{
    public class PerformanceIndexViewModel
    {
        public int? TotalEvaluations { get; set; }
        public int? ExcellentPerformers { get; set; }
        public int? VeryGoodPerformers { get; set; }
        public int? GoodPerformers { get; set; }
        public int? SatisfactoryPerformers { get; set; }
        public int? PoorPerformers { get; set; }
        public int? PendingEvaluations { get; set; }
        public decimal? AverageScore { get; set; }
        
        public List<PerformanceEvaluationViewModel>? Evaluations { get; set; }
        public List<TopPerformerViewModel>? TopPerformers { get; set; }
    }

    public class PerformanceEvaluationViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string EmployeeName { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string EvaluationPeriod { get; set; } = string.Empty;
        public DateTime? EvaluationDate { get; set; }
        public decimal? QualityScore { get; set; }
        public decimal? ProductivityScore { get; set; }
        public decimal? TeamworkScore { get; set; }
        public string? OverallRating { get; set; }
        public string? Status { get; set; }
        public string? StatusAr { get; set; }
        public bool CanProcess { get; set; }
        public bool CanApprove { get; set; }
    }

    public class TopPerformerViewModel
    {
        public string? Name { get; set; }
        public string? Department { get; set; }
        public decimal? Score { get; set; }
    }
}
