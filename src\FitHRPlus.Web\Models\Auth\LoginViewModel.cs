using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Web.Models.Auth
{
    /// <summary>
    /// Login view model for web interface
    /// نموذج عرض تسجيل الدخول للواجهة الويب
    /// </summary>
    public class LoginViewModel
    {
        /// <summary>
        /// Username or email address
        /// اسم المستخدم أو البريد الإلكتروني
        /// </summary>
        [Required(ErrorMessage = "Username or email is required")]
        [Display(Name = "Username or Email")]
        public string UsernameOrEmail { get; set; } = string.Empty;

        /// <summary>
        /// User password
        /// كلمة مرور المستخدم
        /// </summary>
        [Required(ErrorMessage = "Password is required")]
        [DataType(DataType.Password)]
        [Display(Name = "Password")]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// Remember me option
        /// خيار تذكرني
        /// </summary>
        [Display(Name = "Remember me")]
        public bool RememberMe { get; set; } = false;

        /// <summary>
        /// Two-factor authentication code (if enabled)
        /// رمز المصادقة الثنائية (إذا كان مفعلاً)
        /// </summary>
        [Display(Name = "Two-Factor Code")]
        public string? TwoFactorCode { get; set; }

        /// <summary>
        /// Return URL after successful login
        /// رابط العودة بعد تسجيل الدخول بنجاح
        /// </summary>
        public string? ReturnUrl { get; set; }

        /// <summary>
        /// Error message to display
        /// رسالة الخطأ للعرض
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Success message to display
        /// رسالة النجاح للعرض
        /// </summary>
        public string? SuccessMessage { get; set; }

        /// <summary>
        /// Whether two-factor authentication is required
        /// ما إذا كانت المصادقة الثنائية مطلوبة
        /// </summary>
        public bool RequiresTwoFactor { get; set; } = false;

        /// <summary>
        /// Two-factor token for verification
        /// رمز المصادقة الثنائية للتحقق
        /// </summary>
        public string? TwoFactorToken { get; set; }
    }

    /// <summary>
    /// Register view model for web interface
    /// نموذج عرض التسجيل للواجهة الويب
    /// </summary>
    public class RegisterViewModel
    {
        /// <summary>
        /// Username (unique)
        /// اسم المستخدم (فريد)
        /// </summary>
        [Required(ErrorMessage = "Username is required")]
        [StringLength(100, MinimumLength = 3, ErrorMessage = "Username must be between 3 and 100 characters")]
        [Display(Name = "Username")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// Email address (unique)
        /// البريد الإلكتروني (فريد)
        /// </summary>
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        [Display(Name = "Email")]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Password
        /// كلمة المرور
        /// </summary>
        [Required(ErrorMessage = "Password is required")]
        [StringLength(100, MinimumLength = 8, ErrorMessage = "Password must be between 8 and 100 characters")]
        [DataType(DataType.Password)]
        [Display(Name = "Password")]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// Password confirmation
        /// تأكيد كلمة المرور
        /// </summary>
        [Required(ErrorMessage = "Password confirmation is required")]
        [DataType(DataType.Password)]
        [Display(Name = "Confirm Password")]
        [Compare("Password", ErrorMessage = "Password and confirmation password do not match")]
        public string ConfirmPassword { get; set; } = string.Empty;

        /// <summary>
        /// First name
        /// الاسم الأول
        /// </summary>
        [Required(ErrorMessage = "First name is required")]
        [StringLength(100, ErrorMessage = "First name cannot exceed 100 characters")]
        [Display(Name = "First Name")]
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// Last name
        /// الاسم الأخير
        /// </summary>
        [Required(ErrorMessage = "Last name is required")]
        [StringLength(100, ErrorMessage = "Last name cannot exceed 100 characters")]
        [Display(Name = "Last Name")]
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// Phone number (optional)
        /// رقم الهاتف (اختياري)
        /// </summary>
        [Phone(ErrorMessage = "Invalid phone number format")]
        [Display(Name = "Phone Number")]
        public string? Phone { get; set; }

        /// <summary>
        /// Preferred language (ar/en)
        /// اللغة المفضلة (ar/en)
        /// </summary>
        [Display(Name = "Preferred Language")]
        public string PreferredLanguage { get; set; } = "ar";

        /// <summary>
        /// Terms and conditions acceptance
        /// قبول الشروط والأحكام
        /// </summary>
        [Required(ErrorMessage = "You must accept the terms and conditions")]
        [Display(Name = "I accept the terms and conditions")]
        public bool AcceptTerms { get; set; } = false;

        /// <summary>
        /// Error message to display
        /// رسالة الخطأ للعرض
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Success message to display
        /// رسالة النجاح للعرض
        /// </summary>
        public string? SuccessMessage { get; set; }
    }
}
