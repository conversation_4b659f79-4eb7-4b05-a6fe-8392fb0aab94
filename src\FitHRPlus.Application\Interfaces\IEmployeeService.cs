using FitHRPlus.Application.Common;
using FitHRPlus.Application.DTOs.Employees;

namespace FitHRPlus.Application.Interfaces
{
    /// <summary>
    /// Employee service interface
    /// واجهة خدمة الموظفين
    /// </summary>
    public interface IEmployeeService
    {
        /// <summary>
        /// Get employee list with filtering and pagination
        /// الحصول على قائمة الموظفين مع التصفية والترقيم
        /// </summary>
        /// <param name="request">List request parameters</param>
        /// <returns>Paginated employee list</returns>
        Task<ServiceResult<EmployeeListResponseDto>> GetEmployeesAsync(EmployeeListRequestDto request);

        /// <summary>
        /// Get employee by ID
        /// الحصول على الموظف بالمعرف
        /// </summary>
        /// <param name="id">Employee ID</param>
        /// <returns>Employee details</returns>
        Task<ServiceResult<EmployeeDto>> GetEmployeeByIdAsync(Guid id);

        /// <summary>
        /// Get employee by employee number
        /// الحصول على الموظف برقم الموظف
        /// </summary>
        /// <param name="employeeNumber">Employee number</param>
        /// <param name="companyId">Company ID</param>
        /// <returns>Employee details</returns>
        Task<ServiceResult<EmployeeDto>> GetEmployeeByNumberAsync(string employeeNumber, Guid companyId);

        /// <summary>
        /// Create new employee
        /// إنشاء موظف جديد
        /// </summary>
        /// <param name="employeeDto">Employee data</param>
        /// <param name="createdBy">User ID who created the employee</param>
        /// <returns>Created employee</returns>
        Task<ServiceResult<EmployeeDto>> CreateEmployeeAsync(EmployeeDto employeeDto, Guid createdBy);

        /// <summary>
        /// Update existing employee
        /// تحديث موظف موجود
        /// </summary>
        /// <param name="id">Employee ID</param>
        /// <param name="employeeDto">Updated employee data</param>
        /// <param name="updatedBy">User ID who updated the employee</param>
        /// <returns>Updated employee</returns>
        Task<ServiceResult<EmployeeDto>> UpdateEmployeeAsync(Guid id, EmployeeDto employeeDto, Guid updatedBy);

        /// <summary>
        /// Delete employee (soft delete)
        /// حذف الموظف (حذف ناعم)
        /// </summary>
        /// <param name="id">Employee ID</param>
        /// <param name="deletedBy">User ID who deleted the employee</param>
        /// <returns>Deletion result</returns>
        Task<ServiceResult<bool>> DeleteEmployeeAsync(Guid id, Guid deletedBy);

        /// <summary>
        /// Activate/Deactivate employee
        /// تفعيل/إلغاء تفعيل الموظف
        /// </summary>
        /// <param name="id">Employee ID</param>
        /// <param name="isActive">Active status</param>
        /// <param name="updatedBy">User ID who updated the status</param>
        /// <returns>Update result</returns>
        Task<ServiceResult<bool>> SetEmployeeActiveStatusAsync(Guid id, bool isActive, Guid updatedBy);

        /// <summary>
        /// Terminate employee
        /// إنهاء خدمة الموظف
        /// </summary>
        /// <param name="id">Employee ID</param>
        /// <param name="terminationDate">Termination date</param>
        /// <param name="reason">Termination reason</param>
        /// <param name="updatedBy">User ID who terminated the employee</param>
        /// <returns>Termination result</returns>
        Task<ServiceResult<bool>> TerminateEmployeeAsync(Guid id, DateTime terminationDate, string reason, Guid updatedBy);

        /// <summary>
        /// Get employees by department
        /// الحصول على الموظفين حسب القسم
        /// </summary>
        /// <param name="departmentId">Department ID</param>
        /// <param name="includeInactive">Include inactive employees</param>
        /// <returns>List of employees</returns>
        Task<ServiceResult<List<EmployeeSummaryDto>>> GetEmployeesByDepartmentAsync(Guid departmentId, bool includeInactive = false);

        /// <summary>
        /// Get employees by manager
        /// الحصول على الموظفين حسب المدير
        /// </summary>
        /// <param name="managerId">Manager ID</param>
        /// <param name="includeInactive">Include inactive employees</param>
        /// <returns>List of employees</returns>
        Task<ServiceResult<List<EmployeeSummaryDto>>> GetEmployeesByManagerAsync(Guid managerId, bool includeInactive = false);

        /// <summary>
        /// Get employees by position
        /// الحصول على الموظفين حسب المنصب
        /// </summary>
        /// <param name="positionId">Position ID</param>
        /// <param name="includeInactive">Include inactive employees</param>
        /// <returns>List of employees</returns>
        Task<ServiceResult<List<EmployeeSummaryDto>>> GetEmployeesByPositionAsync(Guid positionId, bool includeInactive = false);

        /// <summary>
        /// Get employee statistics
        /// الحصول على إحصائيات الموظفين
        /// </summary>
        /// <param name="companyId">Company ID (optional)</param>
        /// <param name="departmentId">Department ID (optional)</param>
        /// <returns>Employee statistics</returns>
        Task<ServiceResult<EmployeeStatisticsDto>> GetEmployeeStatisticsAsync(Guid? companyId = null, Guid? departmentId = null);

        /// <summary>
        /// Search employees
        /// البحث عن الموظفين
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <param name="companyId">Company ID (optional)</param>
        /// <param name="limit">Maximum number of results</param>
        /// <returns>List of matching employees</returns>
        Task<ServiceResult<List<EmployeeSummaryDto>>> SearchEmployeesAsync(string searchTerm, Guid? companyId = null, int limit = 10);

        /// <summary>
        /// Validate employee number uniqueness
        /// التحقق من فرادة رقم الموظف
        /// </summary>
        /// <param name="employeeNumber">Employee number</param>
        /// <param name="companyId">Company ID</param>
        /// <param name="excludeEmployeeId">Employee ID to exclude from check</param>
        /// <returns>Validation result</returns>
        Task<ServiceResult<bool>> ValidateEmployeeNumberAsync(string employeeNumber, Guid companyId, Guid? excludeEmployeeId = null);

        /// <summary>
        /// Generate next employee number
        /// إنشاء رقم الموظف التالي
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="departmentId">Department ID (optional)</param>
        /// <returns>Generated employee number</returns>
        Task<ServiceResult<string>> GenerateEmployeeNumberAsync(Guid companyId, Guid? departmentId = null);

        /// <summary>
        /// Import employees from file
        /// استيراد الموظفين من ملف
        /// </summary>
        /// <param name="fileData">File data</param>
        /// <param name="companyId">Company ID</param>
        /// <param name="importedBy">User ID who imported</param>
        /// <returns>Import result</returns>
        Task<ServiceResult<EmployeeImportResultDto>> ImportEmployeesAsync(byte[] fileData, Guid companyId, Guid importedBy);

        /// <summary>
        /// Export employees to file
        /// تصدير الموظفين إلى ملف
        /// </summary>
        /// <param name="request">Export request parameters</param>
        /// <returns>Export file data</returns>
        Task<ServiceResult<EmployeeExportResultDto>> ExportEmployeesAsync(EmployeeListRequestDto request);
    }

    /// <summary>
    /// Employee statistics DTO
    /// كائنة نقل بيانات إحصائيات الموظفين
    /// </summary>
    public class EmployeeStatisticsDto
    {
        /// <summary>
        /// Total number of employees
        /// العدد الإجمالي للموظفين
        /// </summary>
        public int TotalEmployees { get; set; }

        /// <summary>
        /// Number of active employees
        /// عدد الموظفين النشطين
        /// </summary>
        public int ActiveEmployees { get; set; }

        /// <summary>
        /// Number of inactive employees
        /// عدد الموظفين غير النشطين
        /// </summary>
        public int InactiveEmployees { get; set; }

        /// <summary>
        /// Number of terminated employees
        /// عدد الموظفين المنتهية خدمتهم
        /// </summary>
        public int TerminatedEmployees { get; set; }

        /// <summary>
        /// Number of male employees
        /// عدد الموظفين الذكور
        /// </summary>
        public int MaleEmployees { get; set; }

        /// <summary>
        /// Number of female employees
        /// عدد الموظفات الإناث
        /// </summary>
        public int FemaleEmployees { get; set; }

        /// <summary>
        /// Average age
        /// متوسط العمر
        /// </summary>
        public double AverageAge { get; set; }

        /// <summary>
        /// Average years of service
        /// متوسط سنوات الخدمة
        /// </summary>
        public double AverageYearsOfService { get; set; }

        /// <summary>
        /// New hires this month
        /// التوظيفات الجديدة هذا الشهر
        /// </summary>
        public int NewHiresThisMonth { get; set; }

        /// <summary>
        /// Terminations this month
        /// إنهاء الخدمات هذا الشهر
        /// </summary>
        public int TerminationsThisMonth { get; set; }

        /// <summary>
        /// Turnover rate (percentage)
        /// معدل دوران الموظفين (نسبة مئوية)
        /// </summary>
        public double TurnoverRate { get; set; }

        /// <summary>
        /// Department breakdown
        /// تفصيل الأقسام
        /// </summary>
        public List<DepartmentStatisticsDto> DepartmentBreakdown { get; set; } = new();

        /// <summary>
        /// Position breakdown
        /// تفصيل المناصب
        /// </summary>
        public List<PositionStatisticsDto> PositionBreakdown { get; set; } = new();
    }

    /// <summary>
    /// Department statistics DTO
    /// كائنة نقل بيانات إحصائيات القسم
    /// </summary>
    public class DepartmentStatisticsDto
    {
        public Guid DepartmentId { get; set; }
        public string DepartmentName { get; set; } = string.Empty;
        public int EmployeeCount { get; set; }
        public double Percentage { get; set; }
    }

    /// <summary>
    /// Position statistics DTO
    /// كائنة نقل بيانات إحصائيات المنصب
    /// </summary>
    public class PositionStatisticsDto
    {
        public Guid PositionId { get; set; }
        public string PositionTitle { get; set; } = string.Empty;
        public int EmployeeCount { get; set; }
        public double Percentage { get; set; }
    }

    /// <summary>
    /// Employee import result DTO
    /// كائنة نقل بيانات نتيجة استيراد الموظفين
    /// </summary>
    public class EmployeeImportResultDto
    {
        public int TotalRecords { get; set; }
        public int SuccessfulImports { get; set; }
        public int FailedImports { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<EmployeeDto> ImportedEmployees { get; set; } = new();
    }

    /// <summary>
    /// Employee export result DTO
    /// كائنة نقل بيانات نتيجة تصدير الموظفين
    /// </summary>
    public class EmployeeExportResultDto
    {
        public byte[] FileData { get; set; } = Array.Empty<byte>();
        public string FileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public int RecordCount { get; set; }
    }
}
