// Admin Panel JavaScript Functions
// وظائف JavaScript للوحة الإدارة

$(document).ready(function () {
    // Initialize sidebar toggle
    initializeSidebar();
    
    // Initialize tooltips
    initializeTooltips();
    
    // Initialize data tables
    initializeDataTables();
    
    // Initialize form validation
    initializeFormValidation();
    
    // Initialize language switcher
    initializeLanguageSwitcher();
});

// Sidebar Functions
// وظائف الشريط الجانبي
function initializeSidebar() {
    $('#sidebarCollapse').on('click', function () {
        $('#sidebar').toggleClass('active');
        $('#content').toggleClass('active');
    });
    
    // Close sidebar on mobile when clicking outside
    $(document).on('click', function (e) {
        if ($(window).width() <= 768) {
            if (!$(e.target).closest('#sidebar, #sidebarCollapse').length) {
                $('#sidebar').removeClass('active');
            }
        }
    });
}

// Tooltips Initialization
// تهيئة التلميحات
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Data Tables Initialization
// تهيئة جداول البيانات
function initializeDataTables() {
    if ($.fn.DataTable) {
        $('.data-table').DataTable({
            responsive: true,
            pageLength: 10,
            language: {
                url: getCurrentLanguage() === 'ar' ? 
                    'https://cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json' : 
                    'https://cdn.datatables.net/plug-ins/1.13.4/i18n/en-GB.json'
            },
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                 '<"row"<"col-sm-12"tr>>' +
                 '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
            columnDefs: [
                {
                    targets: 'no-sort',
                    orderable: false
                }
            ]
        });
    }
}

// Form Validation
// التحقق من صحة النماذج
function initializeFormValidation() {
    // Bootstrap validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
    
    // Custom validation rules
    $.validator.addMethod("arabicText", function(value, element) {
        return this.optional(element) || /^[\u0600-\u06FF\s]+$/.test(value);
    }, "يرجى إدخال نص عربي فقط");
    
    $.validator.addMethod("englishText", function(value, element) {
        return this.optional(element) || /^[a-zA-Z\s]+$/.test(value);
    }, "Please enter English text only");
}

// Language Switcher
// مبدل اللغة
function initializeLanguageSwitcher() {
    $('.language-switcher').on('click', function(e) {
        e.preventDefault();
        var language = $(this).data('lang');
        changeLanguage(language);
    });
}

function changeLanguage(language) {
    $.post('/Dashboard/ChangeLanguage', { language: language })
        .done(function(result) {
            if (result.success) {
                location.reload();
            }
        })
        .fail(function() {
            showNotification('خطأ في تغيير اللغة', 'Error changing language', 'error');
        });
}

function getCurrentLanguage() {
    return document.cookie.replace(/(?:(?:^|.*;\s*)PreferredLanguage\s*\=\s*([^;]*).*$)|^.*$/, "$1") || 'ar';
}

// Notification Functions
// وظائف الإشعارات
function showNotification(messageAr, messageEn, type = 'info', duration = 5000) {
    var message = getCurrentLanguage() === 'ar' ? messageAr : messageEn;
    var alertClass = 'alert-' + (type === 'error' ? 'danger' : type);
    var icon = getNotificationIcon(type);
    
    var notification = $(`
        <div class="alert ${alertClass} alert-dismissible fade show notification-alert" role="alert">
            <i class="${icon}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    $('#notification-container').append(notification);
    
    // Auto-hide after duration
    setTimeout(function() {
        notification.alert('close');
    }, duration);
}

function getNotificationIcon(type) {
    switch(type) {
        case 'success': return 'fas fa-check-circle';
        case 'error': return 'fas fa-exclamation-triangle';
        case 'warning': return 'fas fa-exclamation-circle';
        case 'info': return 'fas fa-info-circle';
        default: return 'fas fa-info-circle';
    }
}

// AJAX Functions
// وظائف AJAX
function makeAjaxRequest(url, method = 'GET', data = null, successCallback = null, errorCallback = null) {
    $.ajax({
        url: url,
        method: method,
        data: data,
        headers: {
            'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        beforeSend: function() {
            showLoader();
        },
        success: function(response) {
            hideLoader();
            if (successCallback) {
                successCallback(response);
            }
        },
        error: function(xhr, status, error) {
            hideLoader();
            if (errorCallback) {
                errorCallback(xhr, status, error);
            } else {
                showNotification(
                    'حدث خطأ أثناء معالجة الطلب',
                    'An error occurred while processing the request',
                    'error'
                );
            }
        }
    });
}

// Loader Functions
// وظائف التحميل
function showLoader() {
    if ($('#global-loader').length === 0) {
        $('body').append(`
            <div id="global-loader" class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" style="background: rgba(0,0,0,0.5); z-index: 9999;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        `);
    }
}

function hideLoader() {
    $('#global-loader').remove();
}

// Confirmation Dialog
// مربع حوار التأكيد
function showConfirmDialog(titleAr, titleEn, messageAr, messageEn, callback) {
    var title = getCurrentLanguage() === 'ar' ? titleAr : titleEn;
    var message = getCurrentLanguage() === 'ar' ? messageAr : messageEn;
    var confirmText = getCurrentLanguage() === 'ar' ? 'تأكيد' : 'Confirm';
    var cancelText = getCurrentLanguage() === 'ar' ? 'إلغاء' : 'Cancel';
    
    var modal = $(`
        <div class="modal fade" id="confirmModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${message}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">${cancelText}</button>
                        <button type="button" class="btn btn-danger" id="confirmButton">${confirmText}</button>
                    </div>
                </div>
            </div>
        </div>
    `);
    
    $('body').append(modal);
    
    $('#confirmButton').on('click', function() {
        $('#confirmModal').modal('hide');
        if (callback) callback();
    });
    
    $('#confirmModal').on('hidden.bs.modal', function() {
        $(this).remove();
    });
    
    $('#confirmModal').modal('show');
}

// Utility Functions
// وظائف مساعدة
function formatDate(date, format = 'DD/MM/YYYY') {
    if (moment) {
        return moment(date).format(format);
    }
    return new Date(date).toLocaleDateString();
}

function formatCurrency(amount, currency = 'EGP') {
    return new Intl.NumberFormat(getCurrentLanguage() === 'ar' ? 'ar-EG' : 'en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

function debounce(func, wait, immediate) {
    var timeout;
    return function() {
        var context = this, args = arguments;
        var later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        var callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}

// Export functions for global use
window.FitHR = {
    showNotification: showNotification,
    showConfirmDialog: showConfirmDialog,
    makeAjaxRequest: makeAjaxRequest,
    changeLanguage: changeLanguage,
    getCurrentLanguage: getCurrentLanguage,
    formatDate: formatDate,
    formatCurrency: formatCurrency,
    showLoader: showLoader,
    hideLoader: hideLoader
};
