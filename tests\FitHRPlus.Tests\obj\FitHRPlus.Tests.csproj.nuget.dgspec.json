{"format": 1, "restore": {"D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\tests\\FitHRPlus.Tests\\FitHRPlus.Tests.csproj": {}}, "projects": {"D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Application\\FitHRPlus.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Application\\FitHRPlus.Application.csproj", "projectName": "FitHRPlus.Application", "projectPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Application\\FitHRPlus.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Domain\\FitHRPlus.Domain.csproj": {"projectPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Domain\\FitHRPlus.Domain.csproj"}, "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Persistence\\FitHRPlus.Persistence.csproj": {"projectPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Persistence\\FitHRPlus.Persistence.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Domain\\FitHRPlus.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Domain\\FitHRPlus.Domain.csproj", "projectName": "FitHRPlus.Domain", "projectPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Domain\\FitHRPlus.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Infrastructure\\FitHRPlus.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Infrastructure\\FitHRPlus.Infrastructure.csproj", "projectName": "FitHRPlus.Infrastructure", "projectPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Infrastructure\\FitHRPlus.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Application\\FitHRPlus.Application.csproj": {"projectPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Application\\FitHRPlus.Application.csproj"}, "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Persistence\\FitHRPlus.Persistence.csproj": {"projectPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Persistence\\FitHRPlus.Persistence.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[8.2.1, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.2.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Persistence\\FitHRPlus.Persistence.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Persistence\\FitHRPlus.Persistence.csproj", "projectName": "FitHRPlus.Persistence", "projectPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Persistence\\FitHRPlus.Persistence.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Persistence\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Domain\\FitHRPlus.Domain.csproj": {"projectPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Domain\\FitHRPlus.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\FitHRPlus.Web.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\FitHRPlus.Web.csproj", "projectName": "FitHRPlus.Web", "projectPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\FitHRPlus.Web.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Application\\FitHRPlus.Application.csproj": {"projectPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Application\\FitHRPlus.Application.csproj"}, "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Infrastructure\\FitHRPlus.Infrastructure.csproj": {"projectPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Infrastructure\\FitHRPlus.Infrastructure.csproj"}, "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Persistence\\FitHRPlus.Persistence.csproj": {"projectPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Persistence\\FitHRPlus.Persistence.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\tests\\FitHRPlus.Tests\\FitHRPlus.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\tests\\FitHRPlus.Tests\\FitHRPlus.Tests.csproj", "projectName": "FitHRPlus.Tests", "projectPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\tests\\FitHRPlus.Tests\\FitHRPlus.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\tests\\FitHRPlus.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Application\\FitHRPlus.Application.csproj": {"projectPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Application\\FitHRPlus.Application.csproj"}, "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\FitHRPlus.Web.csproj": {"projectPath": "D:\\YASSER ALSOUSI\\Software Development AI\\Programs New AI\\FIT HR\\FIT HR Pluse\\FIT HR Pluse\\src\\FitHRPlus.Web\\FitHRPlus.Web.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.12.0, )"}, "Moq": {"target": "Package", "version": "[4.20.72, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.2, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.8.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}}}