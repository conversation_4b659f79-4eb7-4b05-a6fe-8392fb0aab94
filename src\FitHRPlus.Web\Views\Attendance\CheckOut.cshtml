@model FitHRPlus.Web.Models.Admin.CheckInOutViewModel
@{
    ViewData["Title"] = "Check Out / تسجيل الانصراف";
    ViewData["PageTitle"] = "Check Out";
    ViewData["PageTitleAr"] = "تسجيل الانصراف";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">@ViewData["PageTitle"] / @ViewData["PageTitleAr"]</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="/">Home</a></li>
                        <li class="breadcrumb-item"><a href="@Url.Action("Index")">Attendance</a></li>
                        <li class="breadcrumb-item active">Check Out</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-xl-8 col-lg-10">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div class="avatar-sm">
                                <div class="avatar-title rounded-circle bg-soft-warning text-warning">
                                    <i class="fas fa-sign-out-alt font-size-16"></i>
                                </div>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h4 class="card-title mb-1">@Model.PageTitle</h4>
                            <p class="text-muted mb-0">Record employee check-out time and location</p>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if (ViewData.ModelState.ErrorCount > 0)
                    {
                        <div class="alert alert-danger" role="alert">
                            <h6 class="alert-heading">Please correct the following errors:</h6>
                            <ul class="mb-0">
                                @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                                {
                                    <li>@error.ErrorMessage</li>
                                }
                            </ul>
                        </div>
                    }

                    <form asp-action="CheckOut" method="post" id="checkOutForm">
                        @Html.AntiForgeryToken()
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="EmployeeId" class="form-label required">Employee / الموظف</label>
                                    <select asp-for="EmployeeId" class="form-select" required>
                                        <option value="">Select Employee / اختر الموظف</option>
                                        @foreach (var employee in Model.AvailableEmployees)
                                        {
                                            <option value="@employee.Id">
                                                @employee.FullName (@employee.EmployeeNumber)
                                                @if (!string.IsNullOrEmpty(employee.DepartmentName))
                                                {
                                                    <text> - @employee.DepartmentName</text>
                                                }
                                            </option>
                                        }
                                    </select>
                                    <span asp-validation-for="EmployeeId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Time" class="form-label">Check-out Time / وقت الانصراف</label>
                                    <input asp-for="Time" type="datetime-local" class="form-control" />
                                    <span asp-validation-for="Time" class="text-danger"></span>
                                    <div class="form-text">Leave empty to use current time / اتركه فارغاً لاستخدام الوقت الحالي</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Location" class="form-label">Location / الموقع</label>
                                    <div class="input-group">
                                        <input asp-for="Location" type="text" class="form-control" placeholder="Enter location or use GPS">
                                        <button type="button" class="btn btn-outline-secondary" onclick="getCurrentLocation()">
                                            <i class="fas fa-map-marker-alt"></i> GPS
                                        </button>
                                    </div>
                                    <span asp-validation-for="Location" class="text-danger"></span>
                                    <div class="form-text">Optional: Enter location manually or use GPS / اختياري: أدخل الموقع يدوياً أو استخدم GPS</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Device" class="form-label">Device / الجهاز</label>
                                    <select asp-for="Device" class="form-select">
                                        <option value="">Select Device / اختر الجهاز</option>
                                        @foreach (var device in Model.AvailableDevices)
                                        {
                                            <option value="@device" selected="@(device == "Web Portal")">@device</option>
                                        }
                                    </select>
                                    <span asp-validation-for="Device" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Notes" class="form-label">Notes / ملاحظات</label>
                            <textarea asp-for="Notes" class="form-control" rows="3" placeholder="Enter any additional notes..."></textarea>
                            <span asp-validation-for="Notes" class="text-danger"></span>
                        </div>

                        <div class="d-flex gap-2 justify-content-end">
                            <a href="@Url.Action("Index")" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>
                                Cancel / إلغاء
                            </a>
                            <button type="submit" class="btn btn-warning" id="submitBtn">
                                <i class="fas fa-sign-out-alt me-1"></i>
                                @Model.SubmitButtonText
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Current Time Display -->
            <div class="card mt-4">
                <div class="card-body text-center">
                    <h5 class="card-title">Current Time / الوقت الحالي</h5>
                    <div class="display-6 text-warning" id="currentTime"></div>
                    <p class="text-muted mb-0" id="currentDate"></p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Update current time display
        function updateCurrentTime() {
            const now = new Date();
            const timeOptions = { 
                hour: '2-digit', 
                minute: '2-digit', 
                second: '2-digit',
                hour12: false 
            };
            const dateOptions = { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            };

            document.getElementById('currentTime').textContent = now.toLocaleTimeString('en-US', timeOptions);
            document.getElementById('currentDate').textContent = now.toLocaleDateString('en-US', dateOptions);
        }

        // Update time every second
        setInterval(updateCurrentTime, 1000);
        updateCurrentTime(); // Initial call

        // Set current time as default if time field is empty
        document.addEventListener('DOMContentLoaded', function() {
            const timeInput = document.querySelector('input[name="Time"]');
            if (!timeInput.value) {
                const now = new Date();
                // Format for datetime-local input
                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                
                timeInput.value = `${year}-${month}-${day}T${hours}:${minutes}`;
            }
        });

        // Get current location using GPS
        function getCurrentLocation() {
            const locationInput = document.querySelector('input[name="Location"]');
            const gpsButton = document.querySelector('button[onclick="getCurrentLocation()"]');
            
            if (navigator.geolocation) {
                gpsButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Getting...';
                gpsButton.disabled = true;

                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        const lat = position.coords.latitude.toFixed(6);
                        const lng = position.coords.longitude.toFixed(6);
                        locationInput.value = `${lat}, ${lng}`;
                        
                        gpsButton.innerHTML = '<i class="fas fa-check text-success"></i> GPS';
                        setTimeout(() => {
                            gpsButton.innerHTML = '<i class="fas fa-map-marker-alt"></i> GPS';
                            gpsButton.disabled = false;
                        }, 2000);
                    },
                    function(error) {
                        console.error('Error getting location:', error);
                        alert('Unable to get your location. Please enter manually.');
                        gpsButton.innerHTML = '<i class="fas fa-map-marker-alt"></i> GPS';
                        gpsButton.disabled = false;
                    },
                    {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 60000
                    }
                );
            } else {
                alert('Geolocation is not supported by this browser.');
            }
        }

        // Form submission handling
        document.getElementById('checkOutForm').addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
            submitBtn.disabled = true;
        });

        // Employee selection change handler
        document.querySelector('select[name="EmployeeId"]').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                // You can add additional logic here when an employee is selected
                console.log('Selected employee:', selectedOption.text);
            }
        });
    </script>
}
