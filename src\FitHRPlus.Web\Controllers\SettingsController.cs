using FitHRPlus.Application.DTOs.CompanySettings;
using FitHRPlus.Application.Interfaces;
using FitHRPlus.Web.Models.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace FitHRPlus.Web.Controllers
{
    /// <summary>
    /// Settings management controller
    /// وحدة تحكم إدارة الإعدادات
    /// </summary>
    [Authorize]
    public class SettingsController : Controller
    {
        private readonly ICompanySettingsService _companySettingsService;
        private readonly ILogger<SettingsController> _logger;

        public SettingsController(
            ICompanySettingsService companySettingsService,
            ILogger<SettingsController> logger)
        {
            _companySettingsService = companySettingsService;
            _logger = logger;
        }

        /// <summary>
        /// Settings main page
        /// الصفحة الرئيسية للإعدادات
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Index()
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "معلومات الشركة غير موجودة";
                    return RedirectToAction("Index", "Dashboard");
                }

                var result = await _companySettingsService.GetCompanySettingsAsync(companyId);

                if (result != null)
                {
                    var viewModel = new SettingsViewModel
                    {
                        CompanyId = companyId,
                        CompanySettings = MapToCompanySettingsViewModel(result)
                    };

                    return View(viewModel);
                }

                // If no settings exist, create default ones
                var defaultResult = await _companySettingsService.InitializeDefaultSettingsAsync(companyId);
                if (defaultResult != null)
                {
                    var viewModel = new SettingsViewModel
                    {
                        CompanyId = companyId,
                        CompanySettings = MapToCompanySettingsViewModel(defaultResult)
                    };

                    return View(viewModel);
                }

                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل الإعدادات";
                return View(new SettingsViewModel { CompanyId = companyId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading settings");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل الإعدادات";
                return View(new SettingsViewModel());
            }
        }

        /// <summary>
        /// Company settings page
        /// صفحة إعدادات الشركة
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Company()
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "معلومات الشركة غير موجودة";
                    return RedirectToAction("Index", "Dashboard");
                }

                var result = await _companySettingsService.GetCompanySettingsAsync(companyId);

                if (result != null)
                {
                    var viewModel = MapToCompanySettingsViewModel(result);
                    return View(viewModel);
                }

                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل إعدادات الشركة";
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading company settings");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل إعدادات الشركة";
                return RedirectToAction("Index");
            }
        }

        /// <summary>
        /// Update company settings
        /// تحديث إعدادات الشركة
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Company(CompanySettingsViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return View(model);
                }

                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "معلومات الشركة غير موجودة";
                    return RedirectToAction("Index", "Dashboard");
                }

                var updateDto = new UpdateCompanySettingsDto
                {
                    CompanyName = model.CompanyName,
                    CompanyNameAr = model.CompanyNameAr,
                    Address = model.Address,
                    AddressAr = model.AddressAr,
                    Phone = model.Phone,
                    Email = model.Email,
                    Website = model.Website,
                    TaxNumber = model.TaxNumber,
                    CommercialRegister = model.CommercialRegister,
                    WorkingHoursStart = model.WorkingHoursStart,
                    WorkingHoursEnd = model.WorkingHoursEnd,
                    WeekendDays = model.WeekendDays,
                    Currency = model.Currency,
                    CurrencySymbol = model.CurrencySymbol,
                    IncomeTaxRate = model.IncomeTaxRate,
                    SocialInsuranceEmployeeRate = model.SocialInsuranceEmployeeRate,
                    SocialInsuranceEmployerRate = model.SocialInsuranceEmployerRate,
                    MedicalInsuranceRate = model.MedicalInsuranceRate,
                    EnableEmailNotifications = model.EnableEmailNotifications,
                    EnableSmsNotifications = model.EnableSmsNotifications,
                    EnablePushNotifications = model.EnablePushNotifications,
                    DefaultLanguage = model.DefaultLanguage,
                    TimeZone = model.TimeZone,
                    DateFormat = model.DateFormat,
                    TimeFormat = model.TimeFormat
                };

                var result = await _companySettingsService.UpdateCompanySettingsAsync(companyId, updateDto);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = "تم تحديث إعدادات الشركة بنجاح";
                    return RedirectToAction("Company");
                }

                TempData["ErrorMessage"] = result.ErrorMessage ?? "حدث خطأ أثناء تحديث الإعدادات";
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating company settings");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحديث إعدادات الشركة";
                return View(model);
            }
        }

        /// <summary>
        /// System settings page
        /// صفحة إعدادات النظام
        /// </summary>
        [HttpGet]
        public IActionResult System()
        {
            var viewModel = new SystemSettingsViewModel
            {
                // Add system settings here
                MaintenanceMode = false,
                AllowRegistration = true,
                RequireEmailVerification = true,
                SessionTimeout = 30,
                MaxLoginAttempts = 5,
                PasswordMinLength = 8,
                RequireSpecialCharacters = true,
                RequireNumbers = true,
                RequireUppercase = true,
                RequireLowercase = true
            };

            return View(viewModel);
        }

        /// <summary>
        /// Update system settings
        /// تحديث إعدادات النظام
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult System(SystemSettingsViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return View(model);
                }

                // TODO: Implement system settings update
                TempData["SuccessMessage"] = "تم تحديث إعدادات النظام بنجاح";
                return RedirectToAction("System");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating system settings");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحديث إعدادات النظام";
                return View(model);
            }
        }

        /// <summary>
        /// Backup and restore page
        /// صفحة النسخ الاحتياطي والاستعادة
        /// </summary>
        [HttpGet]
        public IActionResult Backup()
        {
            var viewModel = new BackupSettingsViewModel
            {
                AutoBackupEnabled = true,
                BackupFrequency = "Daily",
                BackupRetentionDays = 30,
                LastBackupDate = DateTime.Now.AddDays(-1)
            };

            return View(viewModel);
        }

        /// <summary>
        /// Create backup
        /// إنشاء نسخة احتياطية
        /// </summary>
        [HttpPost]
        public IActionResult CreateBackup()
        {
            try
            {
                // TODO: Implement backup creation
                TempData["SuccessMessage"] = "تم إنشاء النسخة الاحتياطية بنجاح";
                return RedirectToAction("Backup");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating backup");
                TempData["ErrorMessage"] = "حدث خطأ أثناء إنشاء النسخة الاحتياطية";
                return RedirectToAction("Backup");
            }
        }

        /// <summary>
        /// Map CompanySettingsDto to CompanySettingsViewModel
        /// تحويل CompanySettingsDto إلى CompanySettingsViewModel
        /// </summary>
        private static CompanySettingsViewModel MapToCompanySettingsViewModel(CompanySettingsDto dto)
        {
            return new CompanySettingsViewModel
            {
                Id = dto.Id,
                CompanyId = dto.CompanyId,
                CompanyName = dto.CompanyName,
                CompanyNameAr = dto.CompanyNameAr,
                Address = dto.Address,
                AddressAr = dto.AddressAr,
                Phone = dto.Phone,
                Email = dto.Email,
                Website = dto.Website,
                TaxNumber = dto.TaxNumber,
                CommercialRegister = dto.CommercialRegister,
                WorkingHoursStart = dto.WorkingHoursStart,
                WorkingHoursEnd = dto.WorkingHoursEnd,
                WeekendDays = dto.WeekendDays,
                Currency = dto.Currency,
                CurrencySymbol = dto.CurrencySymbol,
                IncomeTaxRate = dto.IncomeTaxRate,
                SocialInsuranceEmployeeRate = dto.SocialInsuranceEmployeeRate,
                SocialInsuranceEmployerRate = dto.SocialInsuranceEmployerRate,
                MedicalInsuranceRate = dto.MedicalInsuranceRate,
                EnableEmailNotifications = dto.EnableEmailNotifications,
                EnableSmsNotifications = dto.EnableSmsNotifications,
                EnablePushNotifications = dto.EnablePushNotifications,
                DefaultLanguage = dto.DefaultLanguage,
                TimeZone = dto.TimeZone,
                DateFormat = dto.DateFormat,
                TimeFormat = dto.TimeFormat
            };
        }
    }
}
