using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace FitHRPlus.Web.Models.Attendance
{
    /// <summary>
    /// Attendance list view model
    /// نموذج عرض قائمة الحضور
    /// </summary>
    public class AttendanceListViewModel
    {
        public List<AttendanceViewModel> AttendanceRecords { get; set; } = new();
        
        // Filter properties
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public Guid? EmployeeId { get; set; }
        public Guid? DepartmentId { get; set; }
        public string? SearchTerm { get; set; }
        public string? Status { get; set; }
        
        // Pagination
        public int CurrentPage { get; set; } = 1;
        public int TotalPages { get; set; }
        public int TotalCount { get; set; }
        public int PageSize { get; set; } = 20;
        
        // Filter options
        public List<SelectListItem> Employees { get; set; } = new();
        public List<SelectListItem> Departments { get; set; } = new();
        public List<SelectListItem> StatusOptions { get; set; } = new()
        {
            new SelectListItem { Value = "Present", Text = "Present / حاضر" },
            new SelectListItem { Value = "Absent", Text = "Absent / غائب" },
            new SelectListItem { Value = "Late", Text = "Late / متأخر" },
            new SelectListItem { Value = "Early Leave", Text = "Early Leave / انصراف مبكر" },
            new SelectListItem { Value = "Overtime", Text = "Overtime / وقت إضافي" }
        };
        
        // Statistics
        public AttendanceStatisticsViewModel? Statistics { get; set; }
    }

    /// <summary>
    /// Attendance view model
    /// نموذج عرض الحضور
    /// </summary>
    public class AttendanceViewModel
    {
        public Guid Id { get; set; }
        public Guid EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string EmployeeNameAr { get; set; } = string.Empty;
        public string EmployeeCode { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string DepartmentNameAr { get; set; } = string.Empty;

        public DateTime Date { get; set; }
        public DateTime? CheckInTime { get; set; }
        public DateTime? CheckOutTime { get; set; }

        public decimal WorkingHours { get; set; }
        public decimal OvertimeHours { get; set; }
        public decimal BreakHours { get; set; }

        public string Status { get; set; } = string.Empty;
        public string StatusAr { get; set; } = string.Empty;
        public string StatusBadgeClass { get; set; } = "bg-secondary";

        public bool IsLate { get; set; }
        public bool IsEarlyLeave { get; set; }

        public string? Notes { get; set; }

        // Display properties
        public string CheckInTimeDisplay => CheckInTime?.ToString("HH:mm") ?? "-";
        public string CheckOutTimeDisplay => CheckOutTime?.ToString("HH:mm") ?? "-";
        public string WorkingHoursDisplay => $"{WorkingHours:F1}h";
        public string DateDisplay => Date.ToString("dd/MM/yyyy");
    }

    /// <summary>
    /// Attendance details view model
    /// نموذج عرض تفاصيل الحضور
    /// </summary>
    public class AttendanceDetailsViewModel
    {
        public Guid Id { get; set; }
        public Guid EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string EmployeeNameAr { get; set; } = string.Empty;
        public string EmployeeCode { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string DepartmentNameAr { get; set; } = string.Empty;

        public DateTime Date { get; set; }
        public DateTime? CheckInTime { get; set; }
        public DateTime? CheckOutTime { get; set; }

        public decimal WorkingHours { get; set; }
        public decimal OvertimeHours { get; set; }
        public decimal BreakHours { get; set; }

        public string Status { get; set; } = string.Empty;
        public string StatusAr { get; set; } = string.Empty;
        public string StatusBadgeClass { get; set; } = "bg-secondary";

        public bool IsLate { get; set; }
        public bool IsEarlyLeave { get; set; }

        // Location data
        public decimal? CheckInLatitude { get; set; }
        public decimal? CheckInLongitude { get; set; }
        public decimal? CheckOutLatitude { get; set; }
        public decimal? CheckOutLongitude { get; set; }

        public string? Notes { get; set; }
        public DateTime CreatedAt { get; set; }

        // Calculated properties
        public bool HasLocation => CheckInLatitude.HasValue && CheckInLongitude.HasValue;
        public string LocationDisplay => HasLocation ? $"{CheckInLatitude:F6}, {CheckInLongitude:F6}" : "No location data";
    }

    /// <summary>
    /// Check-in view model
    /// نموذج عرض تسجيل الحضور
    /// </summary>
    public class CheckInViewModel
    {
        [Required(ErrorMessage = "Check-in time is required / وقت الحضور مطلوب")]
        [Display(Name = "Check-in Time / وقت الحضور")]
        public DateTime CheckInTime { get; set; } = DateTime.Now;

        [Display(Name = "Latitude / خط العرض")]
        public decimal? Latitude { get; set; }

        [Display(Name = "Longitude / خط الطول")]
        public decimal? Longitude { get; set; }

        [MaxLength(500, ErrorMessage = "Notes cannot exceed 500 characters")]
        [Display(Name = "Notes / ملاحظات")]
        public string? Notes { get; set; }

        [Display(Name = "Use Current Location / استخدام الموقع الحالي")]
        public bool UseCurrentLocation { get; set; } = true;
    }

    /// <summary>
    /// Check-out view model
    /// نموذج عرض تسجيل الانصراف
    /// </summary>
    public class CheckOutViewModel
    {
        [Required(ErrorMessage = "Check-out time is required / وقت الانصراف مطلوب")]
        [Display(Name = "Check-out Time / وقت الانصراف")]
        public DateTime CheckOutTime { get; set; } = DateTime.Now;

        [Display(Name = "Latitude / خط العرض")]
        public decimal? Latitude { get; set; }

        [Display(Name = "Longitude / خط الطول")]
        public decimal? Longitude { get; set; }

        [MaxLength(500, ErrorMessage = "Notes cannot exceed 500 characters")]
        [Display(Name = "Notes / ملاحظات")]
        public string? Notes { get; set; }

        [Display(Name = "Use Current Location / استخدام الموقع الحالي")]
        public bool UseCurrentLocation { get; set; } = true;
    }

    /// <summary>
    /// Attendance statistics view model
    /// نموذج عرض إحصائيات الحضور
    /// </summary>
    public class AttendanceStatisticsViewModel
    {
        public int TotalRecords { get; set; }
        public int PresentDays { get; set; }
        public int AbsentDays { get; set; }
        public int LateDays { get; set; }
        public int EarlyLeaveDays { get; set; }
        public int OvertimeDays { get; set; }

        public decimal TotalWorkingHours { get; set; }
        public decimal TotalOvertimeHours { get; set; }
        public decimal AverageWorkingHours { get; set; }

        public decimal AttendanceRate { get; set; }
        public decimal PunctualityRate { get; set; }

        // Calculated properties
        public string AttendanceRateDisplay => $"{AttendanceRate:F1}%";
        public string PunctualityRateDisplay => $"{PunctualityRate:F1}%";
    }

    /// <summary>
    /// Attendance reports view model
    /// نموذج عرض تقارير الحضور
    /// </summary>
    public class AttendanceReportsViewModel
    {
        [Required(ErrorMessage = "From date is required")]
        [Display(Name = "From Date / من تاريخ")]
        public DateTime FromDate { get; set; } = DateTime.Today.AddDays(-30);

        [Required(ErrorMessage = "To date is required")]
        [Display(Name = "To Date / إلى تاريخ")]
        public DateTime ToDate { get; set; } = DateTime.Today;

        [Display(Name = "Employee / الموظف")]
        public Guid? EmployeeId { get; set; }

        [Display(Name = "Department / القسم")]
        public Guid? DepartmentId { get; set; }

        [Display(Name = "Report Type / نوع التقرير")]
        public string ReportType { get; set; } = "Summary";

        // Filter options
        public List<SelectListItem> Employees { get; set; } = new();
        public List<SelectListItem> Departments { get; set; } = new();
        public List<SelectListItem> ReportTypes { get; set; } = new()
        {
            new SelectListItem { Value = "Summary", Text = "Summary Report / تقرير ملخص" },
            new SelectListItem { Value = "Detailed", Text = "Detailed Report / تقرير مفصل" },
            new SelectListItem { Value = "Overtime", Text = "Overtime Report / تقرير الوقت الإضافي" },
            new SelectListItem { Value = "Attendance", Text = "Attendance Rate / معدل الحضور" },
            new SelectListItem { Value = "Punctuality", Text = "Punctuality Report / تقرير الالتزام بالمواعيد" }
        };

        // Report data
        public AttendanceStatisticsViewModel? Statistics { get; set; }
        public List<AttendanceViewModel> Records { get; set; } = new();
        public Dictionary<string, decimal> DepartmentStats { get; set; } = new();
        public Dictionary<string, int> MonthlyStats { get; set; } = new();
    }

    /// <summary>
    /// Bulk attendance action view model
    /// نموذج عرض الإجراءات المجمعة للحضور
    /// </summary>
    public class BulkAttendanceActionViewModel
    {
        public List<Guid> AttendanceIds { get; set; } = new();
        public string Action { get; set; } = string.Empty; // Approve, Reject, Update
        public string? Notes { get; set; }
        public string? Status { get; set; }
    }

    /// <summary>
    /// Employee attendance summary view model
    /// نموذج عرض ملخص حضور الموظف
    /// </summary>
    public class EmployeeAttendanceSummaryViewModel
    {
        public Guid EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string EmployeeCode { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;

        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }

        public int TotalWorkingDays { get; set; }
        public int PresentDays { get; set; }
        public int AbsentDays { get; set; }
        public int LateDays { get; set; }
        public int EarlyLeaveDays { get; set; }

        public decimal TotalWorkingHours { get; set; }
        public decimal TotalOvertimeHours { get; set; }
        public decimal AverageWorkingHours { get; set; }

        public decimal AttendanceRate => TotalWorkingDays > 0 ? (decimal)PresentDays / TotalWorkingDays * 100 : 0;
        public decimal PunctualityRate => PresentDays > 0 ? (decimal)(PresentDays - LateDays) / PresentDays * 100 : 0;

        public List<AttendanceViewModel> RecentRecords { get; set; } = new();
    }
}
