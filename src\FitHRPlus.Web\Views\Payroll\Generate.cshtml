@model FitHRPlus.Web.Models.Payroll.GeneratePayrollViewModel
@{
    ViewData["Title"] = "إنشاء كشوف المرتبات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-plus-circle me-2"></i>
                        إنشاء كشوف المرتبات
                    </h4>
                </div>
                <div class="card-body">
                    <form asp-action="Generate" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <!-- Payroll Period -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-calendar me-2"></i>
                                    فترة كشف المرتبات
                                </h5>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label asp-for="PayrollMonth" class="form-label">الشهر *</label>
                                <select asp-for="PayrollMonth" class="form-select">
                                    <option value="">اختر الشهر</option>
                                    <option value="1">يناير</option>
                                    <option value="2">فبراير</option>
                                    <option value="3">مارس</option>
                                    <option value="4">أبريل</option>
                                    <option value="5">مايو</option>
                                    <option value="6">يونيو</option>
                                    <option value="7">يوليو</option>
                                    <option value="8">أغسطس</option>
                                    <option value="9">سبتمبر</option>
                                    <option value="10">أكتوبر</option>
                                    <option value="11">نوفمبر</option>
                                    <option value="12">ديسمبر</option>
                                </select>
                                <span asp-validation-for="PayrollMonth" class="text-danger"></span>
                            </div>
                            <div class="col-md-4">
                                <label asp-for="PayrollYear" class="form-label">السنة *</label>
                                <select asp-for="PayrollYear" class="form-select">
                                    <option value="">اختر السنة</option>
                                    @for (int year = DateTime.Today.Year; year >= DateTime.Today.Year - 5; year--)
                                    {
                                        <option value="@year">@year</option>
                                    }
                                </select>
                                <span asp-validation-for="PayrollYear" class="text-danger"></span>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">فترة الراتب</label>
                                <p class="form-control-plaintext" id="payrollPeriod">اختر الشهر والسنة</p>
                            </div>
                        </div>

                        <!-- Employee Selection -->
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-users me-2"></i>
                                    اختيار الموظفين
                                </h5>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label asp-for="DepartmentId" class="form-label">القسم</label>
                                <select asp-for="DepartmentId" class="form-select" asp-items="ViewBag.Departments">
                                    <option value="">جميع الأقسام</option>
                                </select>
                                <span asp-validation-for="DepartmentId" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label asp-for="PositionId" class="form-label">المنصب</label>
                                <select asp-for="PositionId" class="form-select" asp-items="ViewBag.Positions">
                                    <option value="">جميع المناصب</option>
                                </select>
                                <span asp-validation-for="PositionId" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input asp-for="IncludeActiveOnly" class="form-check-input" type="checkbox" checked />
                                    <label asp-for="IncludeActiveOnly" class="form-check-label">
                                        الموظفين النشطين فقط
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input asp-for="IncludeNewHires" class="form-check-input" type="checkbox" checked />
                                    <label asp-for="IncludeNewHires" class="form-check-label">
                                        تضمين الموظفين الجدد
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Payroll Settings -->
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-cogs me-2"></i>
                                    إعدادات كشف المرتبات
                                </h5>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input asp-for="CalculateOvertime" class="form-check-input" type="checkbox" checked />
                                    <label asp-for="CalculateOvertime" class="form-check-label">
                                        حساب الساعات الإضافية
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input asp-for="CalculateDeductions" class="form-check-input" type="checkbox" checked />
                                    <label asp-for="CalculateDeductions" class="form-check-label">
                                        حساب الخصومات
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input asp-for="CalculateTax" class="form-check-input" type="checkbox" checked />
                                    <label asp-for="CalculateTax" class="form-check-label">
                                        حساب الضرائب
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input asp-for="CalculateInsurance" class="form-check-input" type="checkbox" checked />
                                    <label asp-for="CalculateInsurance" class="form-check-label">
                                        حساب التأمينات
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input asp-for="CalculateBonuses" class="form-check-input" type="checkbox" checked />
                                    <label asp-for="CalculateBonuses" class="form-check-label">
                                        حساب المكافآت
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input asp-for="CalculateAllowances" class="form-check-input" type="checkbox" checked />
                                    <label asp-for="CalculateAllowances" class="form-check-label">
                                        حساب البدلات
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Settings -->
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label asp-for="Notes" class="form-label">ملاحظات</label>
                                <textarea asp-for="Notes" class="form-control" rows="3" placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                                <span asp-validation-for="Notes" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- Preview Section -->
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-eye me-2"></i>
                                    معاينة الموظفين المختارين
                                </h5>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-12">
                                <button type="button" class="btn btn-outline-info" onclick="previewEmployees()">
                                    <i class="fas fa-search me-1"></i>
                                    معاينة الموظفين
                                </button>
                                <div id="employeePreview" class="mt-3" style="display: none;">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>الاسم</th>
                                                    <th>رقم الموظف</th>
                                                    <th>القسم</th>
                                                    <th>المنصب</th>
                                                    <th>الراتب الأساسي</th>
                                                </tr>
                                            </thead>
                                            <tbody id="employeePreviewBody">
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Summary -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>ملخص العملية</h6>
                                    <ul class="mb-0">
                                        <li>سيتم إنشاء كشوف مرتبات لجميع الموظفين المختارين</li>
                                        <li>سيتم حساب الرواتب بناءً على بيانات الحضور والغياب</li>
                                        <li>سيتم تطبيق جميع البدلات والخصومات المحددة</li>
                                        <li>يمكن مراجعة وتعديل كشوف المرتبات قبل الموافقة عليها</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-plus-circle me-1"></i>
                                    إنشاء كشوف المرتبات
                                </button>
                                <a href="@Url.Action("Index")" class="btn btn-secondary btn-lg">
                                    <i class="fas fa-times me-1"></i>
                                    إلغاء
                                </a>
                                <button type="button" class="btn btn-info btn-lg" onclick="saveAsDraft()">
                                    <i class="fas fa-save me-1"></i>
                                    حفظ كمسودة
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            updatePayrollPeriod();
            
            $('#PayrollMonth, #PayrollYear').change(function() {
                updatePayrollPeriod();
            });

            // Department change handler
            $('#DepartmentId').change(function() {
                var departmentId = $(this).val();
                if (departmentId) {
                    $.get('@Url.Action("GetPositionsByDepartment", "Payroll")', { departmentId: departmentId }, function(data) {
                        var positionSelect = $('#PositionId');
                        positionSelect.empty();
                        positionSelect.append('<option value="">جميع المناصب</option>');
                        $.each(data, function(index, position) {
                            positionSelect.append('<option value="' + position.value + '">' + position.text + '</option>');
                        });
                    });
                } else {
                    $('#PositionId').empty().append('<option value="">جميع المناصب</option>');
                }
            });
        });

        function updatePayrollPeriod() {
            var month = $('#PayrollMonth').val();
            var year = $('#PayrollYear').val();
            
            if (month && year) {
                var monthNames = ['', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 
                                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
                $('#payrollPeriod').text(monthNames[month] + ' ' + year);
            } else {
                $('#payrollPeriod').text('اختر الشهر والسنة');
            }
        }

        function previewEmployees() {
            var formData = {
                PayrollMonth: $('#PayrollMonth').val(),
                PayrollYear: $('#PayrollYear').val(),
                DepartmentId: $('#DepartmentId').val(),
                PositionId: $('#PositionId').val(),
                IncludeActiveOnly: $('#IncludeActiveOnly').is(':checked'),
                IncludeNewHires: $('#IncludeNewHires').is(':checked')
            };

            if (!formData.PayrollMonth || !formData.PayrollYear) {
                alert('يرجى اختيار الشهر والسنة أولاً');
                return;
            }

            $.post('@Url.Action("PreviewEmployees", "Payroll")', formData, function(data) {
                var tbody = $('#employeePreviewBody');
                tbody.empty();
                
                if (data.length > 0) {
                    $.each(data, function(index, employee) {
                        tbody.append(`
                            <tr>
                                <td>${employee.fullName}</td>
                                <td>${employee.employeeNumber}</td>
                                <td>${employee.departmentName}</td>
                                <td>${employee.positionName}</td>
                                <td>${employee.basicSalary.toLocaleString()} ج.م</td>
                            </tr>
                        `);
                    });
                    $('#employeePreview').show();
                } else {
                    tbody.append('<tr><td colspan="5" class="text-center text-muted">لا توجد موظفين مطابقين للمعايير المحددة</td></tr>');
                    $('#employeePreview').show();
                }
            }).fail(function() {
                alert('حدث خطأ أثناء معاينة الموظفين');
            });
        }

        function saveAsDraft() {
            // Add draft functionality here
            alert('سيتم تنفيذ وظيفة حفظ المسودة');
        }
    </script>
}
