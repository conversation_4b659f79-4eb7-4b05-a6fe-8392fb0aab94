using FitHRPlus.Domain.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Domain.Entities
{
    /// <summary>
    /// Company entity representing organizations using the HRMS
    /// كيان الشركة الذي يمثل المؤسسات التي تستخدم نظام إدارة الموارد البشرية
    /// </summary>
    public class Company : BaseEntity
    {
        /// <summary>
        /// Company name in English
        /// اسم الشركة بالإنجليزية
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Company name in Arabic
        /// اسم الشركة بالعربية
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// Tax identification number
        /// الرقم الضريبي
        /// </summary>
        [MaxLength(50)]
        public string? TaxNumber { get; set; }

        /// <summary>
        /// Commercial registration number
        /// رقم السجل التجاري
        /// </summary>
        [MaxLength(50)]
        public string? CommercialRegister { get; set; }

        /// <summary>
        /// Company address in English
        /// عنوان الشركة بالإنجليزية
        /// </summary>
        [MaxLength(500)]
        public string? Address { get; set; }

        /// <summary>
        /// Company address in Arabic
        /// عنوان الشركة بالعربية
        /// </summary>
        [MaxLength(500)]
        public string? AddressAr { get; set; }

        /// <summary>
        /// Company phone number
        /// رقم هاتف الشركة
        /// </summary>
        [MaxLength(20)]
        public string? Phone { get; set; }

        /// <summary>
        /// Company email address
        /// البريد الإلكتروني للشركة
        /// </summary>
        [MaxLength(100)]
        [EmailAddress]
        public string? Email { get; set; }

        /// <summary>
        /// Company website URL
        /// موقع الشركة الإلكتروني
        /// </summary>
        [MaxLength(200)]
        public string? Website { get; set; }

        /// <summary>
        /// Company logo file path
        /// مسار ملف شعار الشركة
        /// </summary>
        [MaxLength(500)]
        public string? Logo { get; set; }

        /// <summary>
        /// Industry sector
        /// القطاع الصناعي
        /// </summary>
        [MaxLength(100)]
        public string? Industry { get; set; }

        /// <summary>
        /// Number of employees
        /// عدد الموظفين
        /// </summary>
        public int EmployeeCount { get; set; } = 0;

        /// <summary>
        /// Company establishment date
        /// تاريخ تأسيس الشركة
        /// </summary>
        public DateTime? EstablishedDate { get; set; }

        /// <summary>
        /// Company timezone
        /// المنطقة الزمنية للشركة
        /// </summary>
        [MaxLength(50)]
        public string TimeZone { get; set; } = "Africa/Cairo";

        /// <summary>
        /// Default currency code (ISO 4217)
        /// رمز العملة الافتراضي
        /// </summary>
        [MaxLength(3)]
        public string Currency { get; set; } = "EGP";

        /// <summary>
        /// Fiscal year start month (1-12)
        /// شهر بداية السنة المالية
        /// </summary>
        public int FiscalYearStart { get; set; } = 1;

        /// <summary>
        /// Subscription plan type
        /// نوع خطة الاشتراك
        /// </summary>
        [MaxLength(50)]
        public string SubscriptionPlan { get; set; } = "Basic";

        /// <summary>
        /// Subscription expiry date
        /// تاريخ انتهاء الاشتراك
        /// </summary>
        public DateTime? SubscriptionExpiry { get; set; }

        // Navigation Properties - خصائص التنقل

        /// <summary>
        /// Company departments
        /// أقسام الشركة
        /// </summary>
        public virtual ICollection<Department> Departments { get; set; } = new List<Department>();

        /// <summary>
        /// Company employees
        /// موظفو الشركة
        /// </summary>
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();

        /// <summary>
        /// Company leave types
        /// أنواع الإجازات في الشركة
        /// </summary>
        public virtual ICollection<LeaveType> LeaveTypes { get; set; } = new List<LeaveType>();

        /// <summary>
        /// Company salary components
        /// مكونات الراتب في الشركة
        /// </summary>
        public virtual ICollection<SalaryComponent> SalaryComponents { get; set; } = new List<SalaryComponent>();

        /// <summary>
        /// Company biometric devices
        /// أجهزة البصمة في الشركة
        /// </summary>
        public virtual ICollection<BiometricDevice> BiometricDevices { get; set; } = new List<BiometricDevice>();

        /// <summary>
        /// Company holidays
        /// عطل الشركة
        /// </summary>
        public virtual ICollection<Holiday> Holidays { get; set; } = new List<Holiday>();

        /// <summary>
        /// Company work shifts
        /// ورديات العمل في الشركة
        /// </summary>
        public virtual ICollection<WorkShift> WorkShifts { get; set; } = new List<WorkShift>();
    }
}