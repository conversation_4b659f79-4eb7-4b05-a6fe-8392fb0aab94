@model FitHRPlus.Web.Models.Reports.ReportsDashboardViewModel
@{
    ViewData["Title"] = "التقارير والتحليلات";
}

<!-- Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-title">
            <h1><i class="bi bi-graph-up"></i> التقارير والتحليلات</h1>
            <p>تقارير شاملة وذكاء الأعمال لاتخاذ قرارات مدروسة</p>
        </div>
        <div class="page-actions">
            <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="bi bi-download"></i>
                    تصدير سريع
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="exportAllReports('excel')">
                        <i class="bi bi-file-earmark-excel me-2"></i>جميع التقارير (Excel)
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportAllReports('pdf')">
                        <i class="bi bi-file-earmark-pdf me-2"></i>جميع التقارير (PDF)
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" onclick="scheduleReports()">
                        <i class="bi bi-calendar-event me-2"></i>جدولة التقارير
                    </a></li>
                </ul>
            </div>
            <button type="button" class="btn btn-success" onclick="generateCustomReport()">
                <i class="bi bi-plus-circle"></i>
                تقرير مخصص
            </button>
            <button type="button" class="btn btn-info" onclick="openReportBuilder()">
                <i class="bi bi-tools"></i>
                منشئ التقارير
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-primary">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@Model.TotalEmployees</h3>
                        <p>إجمالي الموظفين</p>
                        <small>جميع الموظفين المسجلين في النظام</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-people"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: 100%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-success">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@Model.ActiveEmployees</h3>
                        <p>الموظفين النشطين</p>
                        <small>الموظفين العاملين حالياً</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-person-check"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: @(Model.TotalEmployees > 0 ? (Model.ActiveEmployees * 100 / Model.TotalEmployees) : 0)%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-info">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@Model.TotalDepartments</h3>
                        <p>الأقسام</p>
                        <small>عدد الأقسام في الشركة</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-building"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: @(Model.TotalDepartments * 10)%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-warning">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@Model.TotalSalaries.ToString("C")</h3>
                        <p>إجمالي المرتبات</p>
                        <small>مجموع جميع المرتبات الشهرية</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-currency-dollar"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: 100%"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Dashboard Overview -->
<div class="row mb-4">
    <div class="col-lg-4">
        <!-- Today's Activity -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">نشاط اليوم</h5>
            </div>
            <div class="card-body">
                <div class="activity-list">
                    <div class="activity-item">
                        <div class="activity-icon bg-success">
                            <i class="bi bi-person-check"></i>
                        </div>
                        <div class="activity-content">
                            <h6>حضور اليوم</h6>
                            <span class="activity-value">@Model.TodayAttendance</span>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon bg-warning">
                            <i class="bi bi-calendar-x"></i>
                        </div>
                        <div class="activity-content">
                            <h6>الإجازات المعلقة</h6>
                            <span class="activity-value">@Model.PendingLeaveRequests</span>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon bg-info">
                            <i class="bi bi-file-earmark-text"></i>
                        </div>
                        <div class="activity-content">
                            <h6>كشوف المرتبات المعلقة</h6>
                            <span class="activity-value">@Model.PendingPayrolls</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Report Generator -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">منشئ التقارير السريع</h5>
            </div>
            <div class="card-body">
                <div class="quick-report-form">
                    <div class="mb-3">
                        <label class="form-label">نوع التقرير</label>
                        <select class="form-select" id="reportType">
                            <option value="attendance">تقرير الحضور</option>
                            <option value="payroll">تقرير الرواتب</option>
                            <option value="employees">تقرير الموظفين</option>
                            <option value="leaves">تقرير الإجازات</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الفترة الزمنية</label>
                        <select class="form-select" id="reportPeriod">
                            <option value="today">اليوم</option>
                            <option value="week">هذا الأسبوع</option>
                            <option value="month">هذا الشهر</option>
                            <option value="quarter">هذا الربع</option>
                            <option value="year">هذا العام</option>
                        </select>
                    </div>
                    <button class="btn btn-primary w-100" onclick="generateQuickReport()">
                        <i class="bi bi-lightning"></i>
                        إنشاء التقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-8">
        <!-- Interactive Charts -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title">الموظفين حسب القسم</h5>
                    <div class="chart-controls">
                        <button class="btn btn-sm btn-outline-primary" onclick="changeChartType('pie')">دائري</button>
                        <button class="btn btn-sm btn-outline-primary" onclick="changeChartType('bar')">أعمدة</button>
                        <button class="btn btn-sm btn-outline-primary" onclick="changeChartType('doughnut')">حلقي</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="departmentChart" style="height: 300px;"></canvas>
                </div>
            </div>
        </div>

        <!-- Attendance Trend -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">اتجاه الحضور الشهري</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="attendanceTrendChart" style="height: 250px;"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Available Reports -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">التقارير المتاحة</h5>
            </div>
            <div class="card-body">
                <div class="reports-grid">
                    <!-- Employee Reports -->
                    <div class="report-category">
                        <div class="report-category-header">
                            <div class="report-category-icon bg-primary">
                                <i class="bi bi-people"></i>
                            </div>
                            <div class="report-category-info">
                                <h6>تقارير الموظفين</h6>
                                <p>بيانات شاملة للموظفين والديموغرافيا وتقارير الأداء</p>
                            </div>
                        </div>
                        <div class="report-category-actions">
                            <a href="@Url.Action("Employees")" class="btn btn-primary">
                                <i class="bi bi-graph-up"></i>
                                عرض التقارير
                            </a>
                        </div>
                    </div>

                    <!-- Attendance Reports -->
                    <div class="report-category">
                        <div class="report-category-header">
                            <div class="report-category-icon bg-success">
                                <i class="bi bi-clock"></i>
                            </div>
                            <div class="report-category-info">
                                <h6>تقارير الحضور</h6>
                                <p>تتبع الحضور والانضباط والعمل الإضافي وتحليل ساعات العمل</p>
                            </div>
                        </div>
                        <div class="report-category-actions">
                            <a href="@Url.Action("Attendance")" class="btn btn-success">
                                <i class="bi bi-graph-up-arrow"></i>
                                عرض التقارير
                            </a>
                        </div>
                    </div>

                    <!-- Leave Reports -->
                    <div class="report-category">
                        <div class="report-category-header">
                            <div class="report-category-icon bg-warning">
                                <i class="bi bi-calendar-event"></i>
                            </div>
                            <div class="report-category-info">
                                <h6>تقارير الإجازات</h6>
                                <p>أرصدة الإجازات وأنماط الاستخدام وتحليلات إدارة الإجازات</p>
                            </div>
                        </div>
                        <div class="report-category-actions">
                            <a href="@Url.Action("Leave")" class="btn btn-warning">
                                <i class="bi bi-pie-chart"></i>
                                عرض التقارير
                            </a>
                        </div>
                    <!-- Payroll Reports -->
                    <div class="report-category">
                        <div class="report-category-header">
                            <div class="report-category-icon bg-info">
                                <i class="bi bi-currency-dollar"></i>
                            </div>
                            <div class="report-category-info">
                                <h6>تقارير كشوف المرتبات</h6>
                                <p>تحليل الرواتب والخصومات والبدلات وملخصات كشوف المرتبات</p>
                            </div>
                        </div>
                        <div class="report-category-actions">
                            <a href="@Url.Action("Payroll")" class="btn btn-info">
                                <i class="bi bi-pie-chart-fill"></i>
                                عرض التقارير
                            </a>
                        </div>
                    </div>

                    <!-- Analytics Dashboard -->
                    <div class="report-category">
                        <div class="report-category-header">
                            <div class="report-category-icon bg-secondary">
                                <i class="bi bi-graph-down-arrow"></i>
                            </div>
                            <div class="report-category-info">
                                <h6>لوحة التحليلات</h6>
                                <p>تحليلات متقدمة واتجاهات وتوقعات ورؤى الأعمال</p>
                            </div>
                        </div>
                        <div class="report-category-actions">
                            <a href="@Url.Action("Analytics")" class="btn btn-secondary">
                                <i class="bi bi-lightbulb"></i>
                                عرض التحليلات
                            </a>
                        </div>
                    </div>

                    <!-- Custom Reports -->
                    <div class="report-category">
                        <div class="report-category-header">
                            <div class="report-category-icon bg-dark">
                                <i class="bi bi-gear"></i>
                            </div>
                            <div class="report-category-info">
                                <h6>التقارير المخصصة</h6>
                                <p>إنشاء تقارير مخصصة بمرشحات مرنة وتركيبات بيانات متنوعة</p>
                            </div>
                        </div>
                        <div class="report-category-actions">
                            <button class="btn btn-dark" onclick="createCustomReport()">
                                <i class="bi bi-plus-circle"></i>
                                إنشاء تقرير
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let departmentChart;
        let attendanceTrendChart;

        $(document).ready(function() {
            loadDepartmentChart();
            loadAttendanceTrendChart();
        });

        function loadDepartmentChart() {
            const ctx = document.getElementById('departmentChart').getContext('2d');
            departmentChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: @Html.Raw(Json.Serialize(Model.EmployeesByDepartment?.Keys.ToList() ?? new List<string>())),
                    datasets: [{
                        data: @Html.Raw(Json.Serialize(Model.EmployeesByDepartment?.Values.ToList() ?? new List<int>())),
                        backgroundColor: [
                            '#1e3a8a',
                            '#22c55e',
                            '#f59e0b',
                            '#ef4444',
                            '#8b5cf6',
                            '#06b6d4'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: {
                                    family: 'Tajawal'
                                },
                                padding: 20
                            }
                        }
                    }
                }
            });
        }

        function loadAttendanceTrendChart() {
            const ctx = document.getElementById('attendanceTrendChart').getContext('2d');
            attendanceTrendChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'معدل الحضور %',
                        data: [85, 88, 92, 89, 94, 91],
                        borderColor: '#1e3a8a',
                        backgroundColor: 'rgba(30, 58, 138, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                font: {
                                    family: 'Tajawal'
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        function changeChartType(type) {
            if (departmentChart) {
                departmentChart.destroy();
            }

            const ctx = document.getElementById('departmentChart').getContext('2d');
            departmentChart = new Chart(ctx, {
                type: type,
                data: {
                    labels: @Html.Raw(Json.Serialize(Model.EmployeesByDepartment?.Keys.ToList() ?? new List<string>())),
                    datasets: [{
                        data: @Html.Raw(Json.Serialize(Model.EmployeesByDepartment?.Values.ToList() ?? new List<int>())),
                        backgroundColor: [
                            '#1e3a8a',
                            '#22c55e',
                            '#f59e0b',
                            '#ef4444',
                            '#8b5cf6',
                            '#06b6d4'
                        ],
                        borderWidth: 0,
                        borderRadius: type === 'bar' ? 8 : 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: {
                                    family: 'Tajawal'
                                },
                                padding: 20
                            }
                        }
                    },
                    scales: type === 'bar' ? {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    } : {}
                }
            });

            // Update active button
            $('.chart-controls .btn').removeClass('btn-primary').addClass('btn-outline-primary');
            $(event.target).removeClass('btn-outline-primary').addClass('btn-primary');
        }

        function generateQuickReport() {
            const reportType = $('#reportType').val();
            const reportPeriod = $('#reportPeriod').val();

            showToast('جاري إنشاء التقرير...', 'info');

            // Simulate report generation
            setTimeout(() => {
                const reportUrl = `@Url.Action("Generate", "Reports")?type=${reportType}&period=${reportPeriod}`;
                window.open(reportUrl, '_blank');
                showToast('تم إنشاء التقرير بنجاح', 'success');
            }, 2000);
        }

        function exportAllReports(format) {
            showToast(`جاري تصدير جميع التقارير بصيغة ${format.toUpperCase()}...`, 'info');

            setTimeout(() => {
                window.open(`@Url.Action("ExportAll", "Reports")?format=${format}`, '_blank');
                showToast('تم تصدير التقارير بنجاح', 'success');
            }, 3000);
        }

        function scheduleReports() {
            // Open scheduling modal or redirect to scheduling page
            showToast('فتح صفحة جدولة التقارير...', 'info');
            setTimeout(() => {
                window.location.href = '@Url.Action("Schedule", "Reports")';
            }, 1000);
        }

        function generateCustomReport() {
            showToast('فتح منشئ التقارير المخصصة...', 'info');
            setTimeout(() => {
                window.location.href = '@Url.Action("CustomReportBuilder", "Reports")';
            }, 1000);
        }

        function openReportBuilder() {
            showToast('فتح منشئ التقارير المتقدم...', 'info');
            setTimeout(() => {
                window.location.href = '@Url.Action("ReportBuilder", "Reports")';
            }, 1000);
        }

        function createCustomReport() {
            generateCustomReport();
        }

        function showToast(message, type = 'info') {
            const bgClass = type === 'success' ? 'bg-success' :
                           type === 'warning' ? 'bg-warning' :
                           type === 'error' ? 'bg-danger' : 'bg-info';

            const toast = $(`
                <div class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `);

            let container = $('.toast-container');
            if (container.length === 0) {
                container = $('<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
                $('body').append(container);
            }

            container.append(toast);
            const bsToast = new bootstrap.Toast(toast[0]);
            bsToast.show();

            toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    </script>
}
