/**
 * FitHR Plus Common JavaScript Functions
 * وظائف JavaScript المشتركة لنظام FitHR Plus
 */

// Global variables
let currentUser = null;
let systemSettings = {};

// Initialize common functionality
$(document).ready(function() {
    initializeCommonFeatures();
    loadUserSettings();
    setupGlobalEventHandlers();
});

/**
 * Initialize common features
 * تهيئة المميزات المشتركة
 */
function initializeCommonFeatures() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Setup CSRF token for AJAX requests
    $.ajaxSetup({
        beforeSend: function(xhr, settings) {
            if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                xhr.setRequestHeader("RequestVerificationToken", $('input[name="__RequestVerificationToken"]').val());
            }
        }
    });

    // Initialize date pickers
    $('.datepicker').each(function() {
        $(this).attr('type', 'date');
    });

    // Initialize number formatting
    $('.number-format').each(function() {
        formatNumber(this);
    });
}

/**
 * Load user settings
 * تحميل إعدادات المستخدم
 */
function loadUserSettings() {
    const savedSettings = localStorage.getItem('userSettings');
    if (savedSettings) {
        try {
            systemSettings = JSON.parse(savedSettings);
            applyUserSettings();
        } catch (e) {
            console.error('Error loading user settings:', e);
        }
    }
}

/**
 * Apply user settings
 * تطبيق إعدادات المستخدم
 */
function applyUserSettings() {
    // Apply theme
    if (systemSettings.theme) {
        document.body.setAttribute('data-theme', systemSettings.theme);
    }

    // Apply language
    if (systemSettings.language) {
        document.documentElement.setAttribute('lang', systemSettings.language);
        if (systemSettings.language === 'ar') {
            document.documentElement.setAttribute('dir', 'rtl');
        }
    }
}

/**
 * Setup global event handlers
 * إعداد معالجات الأحداث العامة
 */
function setupGlobalEventHandlers() {
    // Handle form submissions
    $('form[data-ajax="true"]').on('submit', function(e) {
        e.preventDefault();
        handleAjaxForm(this);
    });

    // Handle confirmation dialogs
    $('[data-confirm]').on('click', function(e) {
        const message = $(this).data('confirm');
        if (!confirm(message)) {
            e.preventDefault();
            return false;
        }
    });

    // Handle loading states
    $(document).on('ajaxStart', function() {
        showGlobalLoading();
    }).on('ajaxStop', function() {
        hideGlobalLoading();
    });

    // Handle number inputs
    $('.number-input').on('input', function() {
        formatNumber(this);
    });

    // Handle search inputs with debounce
    $('.search-input').on('input', debounce(function() {
        const searchTerm = $(this).val();
        const target = $(this).data('target');
        if (target) {
            performSearch(searchTerm, target);
        }
    }, 300));
}

/**
 * Handle AJAX form submissions
 * معالجة إرسال النماذج عبر AJAX
 */
function handleAjaxForm(form) {
    const $form = $(form);
    const url = $form.attr('action');
    const method = $form.attr('method') || 'POST';
    const data = new FormData(form);

    showFormLoading($form);

    $.ajax({
        url: url,
        type: method,
        data: data,
        processData: false,
        contentType: false,
        success: function(response) {
            hideFormLoading($form);
            
            if (response.success) {
                showToast(response.message || 'تم الحفظ بنجاح', 'success');
                
                if (response.redirectUrl) {
                    window.location.href = response.redirectUrl;
                } else if (response.reload) {
                    location.reload();
                }
            } else {
                showToast(response.message || 'حدث خطأ أثناء الحفظ', 'error');
                displayFormErrors($form, response.errors);
            }
        },
        error: function(xhr) {
            hideFormLoading($form);
            showToast('حدث خطأ في الاتصال', 'error');
            console.error('AJAX Error:', xhr);
        }
    });
}

/**
 * Display form validation errors
 * عرض أخطاء التحقق من النماذج
 */
function displayFormErrors($form, errors) {
    // Clear previous errors
    $form.find('.is-invalid').removeClass('is-invalid');
    $form.find('.invalid-feedback').remove();

    if (errors) {
        Object.keys(errors).forEach(function(field) {
            const $field = $form.find(`[name="${field}"]`);
            if ($field.length) {
                $field.addClass('is-invalid');
                $field.after(`<div class="invalid-feedback">${errors[field][0]}</div>`);
            }
        });
    }
}

/**
 * Show form loading state
 * عرض حالة التحميل للنموذج
 */
function showFormLoading($form) {
    $form.find('button[type="submit"]').prop('disabled', true).append(' <span class="spinner-border spinner-border-sm" role="status"></span>');
}

/**
 * Hide form loading state
 * إخفاء حالة التحميل للنموذج
 */
function hideFormLoading($form) {
    $form.find('button[type="submit"]').prop('disabled', false).find('.spinner-border').remove();
}

/**
 * Show global loading indicator
 * عرض مؤشر التحميل العام
 */
function showGlobalLoading() {
    if (!$('#globalLoading').length) {
        $('body').append(`
            <div id="globalLoading" class="global-loading">
                <div class="loading-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                </div>
            </div>
        `);
    }
    $('#globalLoading').fadeIn(200);
}

/**
 * Hide global loading indicator
 * إخفاء مؤشر التحميل العام
 */
function hideGlobalLoading() {
    $('#globalLoading').fadeOut(200);
}

/**
 * Format number input
 * تنسيق إدخال الأرقام
 */
function formatNumber(input) {
    const value = $(input).val().replace(/[^\d.-]/g, '');
    const number = parseFloat(value);
    
    if (!isNaN(number)) {
        $(input).val(number.toLocaleString('ar-SA'));
    }
}

/**
 * Perform search with debounce
 * تنفيذ البحث مع التأخير
 */
function performSearch(searchTerm, target) {
    // Implementation depends on the specific search target
    console.log('Searching for:', searchTerm, 'in:', target);
}

/**
 * Debounce function
 * وظيفة التأخير
 */
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction() {
        const context = this;
        const args = arguments;
        const later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}

/**
 * Show toast notification
 * عرض إشعار منبثق
 */
function showToast(message, type = 'info', duration = 5000) {
    const bgClass = type === 'success' ? 'bg-success' : 
                   type === 'warning' ? 'bg-warning' : 
                   type === 'error' ? 'bg-danger' : 'bg-info';
    
    const toast = $(`
        <div class="toast align-items-center text-white ${bgClass} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `);
    
    let container = $('.toast-container');
    if (container.length === 0) {
        container = $('<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;"></div>');
        $('body').append(container);
    }
    
    container.append(toast);
    const bsToast = new bootstrap.Toast(toast[0], { delay: duration });
    bsToast.show();
    
    toast.on('hidden.bs.toast', function() {
        $(this).remove();
    });
}

/**
 * Confirm dialog
 * مربع حوار التأكيد
 */
function confirmDialog(message, callback) {
    if (confirm(message)) {
        if (typeof callback === 'function') {
            callback();
        }
        return true;
    }
    return false;
}

/**
 * Format currency
 * تنسيق العملة
 */
function formatCurrency(amount, currency = 'SAR') {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

/**
 * Format date
 * تنسيق التاريخ
 */
function formatDate(date, format = 'dd/MM/yyyy') {
    if (!date) return '';
    
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    
    return format.replace('dd', day).replace('MM', month).replace('yyyy', year);
}

/**
 * Get time ago string
 * الحصول على نص "منذ"
 */
function getTimeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - new Date(date)) / 1000);
    
    if (diffInSeconds < 60) {
        return 'الآن';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `منذ ${minutes} دقيقة`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `منذ ${hours} ساعة`;
    } else {
        const days = Math.floor(diffInSeconds / 86400);
        return `منذ ${days} يوم`;
    }
}

// Export functions for global use
window.FitHR = {
    showToast,
    confirmDialog,
    formatCurrency,
    formatDate,
    getTimeAgo,
    showGlobalLoading,
    hideGlobalLoading
};
