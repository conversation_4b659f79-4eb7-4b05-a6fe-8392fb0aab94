using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Web.Models.Admin
{
    /// <summary>
    /// Attendance view model for web forms
    /// نموذج عرض الحضور لنماذج الويب
    /// </summary>
    public class AttendanceViewModel
    {
        /// <summary>
        /// Attendance record ID
        /// معرف سجل الحضور
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Employee ID
        /// معرف الموظف
        /// </summary>
        [Required(ErrorMessage = "Employee is required")]
        [Display(Name = "Employee")]
        public Guid EmployeeId { get; set; }

        /// <summary>
        /// Employee name
        /// اسم الموظف
        /// </summary>
        [Display(Name = "Employee Name")]
        public string EmployeeName { get; set; } = string.Empty;

        /// <summary>
        /// Employee number
        /// رقم الموظف
        /// </summary>
        [Display(Name = "Employee Number")]
        public string EmployeeNumber { get; set; } = string.Empty;

        /// <summary>
        /// Department name
        /// اسم القسم
        /// </summary>
        [Display(Name = "Department")]
        public string? DepartmentName { get; set; }

        /// <summary>
        /// Position title
        /// مسمى المنصب
        /// </summary>
        [Display(Name = "Position")]
        public string? PositionTitle { get; set; }

        /// <summary>
        /// Attendance date
        /// تاريخ الحضور
        /// </summary>
        [Required(ErrorMessage = "Date is required")]
        [DataType(DataType.Date)]
        [Display(Name = "Date")]
        public DateTime Date { get; set; }

        /// <summary>
        /// Check-in time
        /// وقت الحضور
        /// </summary>
        [DataType(DataType.Time)]
        [Display(Name = "Check-in Time")]
        public DateTime? CheckInTime { get; set; }

        /// <summary>
        /// Check-out time
        /// وقت الانصراف
        /// </summary>
        [DataType(DataType.Time)]
        [Display(Name = "Check-out Time")]
        public DateTime? CheckOutTime { get; set; }

        /// <summary>
        /// Break start time
        /// وقت بداية الاستراحة
        /// </summary>
        [DataType(DataType.Time)]
        [Display(Name = "Break Start")]
        public DateTime? BreakStartTime { get; set; }

        /// <summary>
        /// Break end time
        /// وقت نهاية الاستراحة
        /// </summary>
        [DataType(DataType.Time)]
        [Display(Name = "Break End")]
        public DateTime? BreakEndTime { get; set; }

        /// <summary>
        /// Total working hours
        /// إجمالي ساعات العمل
        /// </summary>
        [Display(Name = "Working Hours")]
        public TimeSpan? WorkingHours { get; set; }

        /// <summary>
        /// Total break duration
        /// إجمالي مدة الاستراحة
        /// </summary>
        [Display(Name = "Break Duration")]
        public TimeSpan? BreakDuration { get; set; }

        /// <summary>
        /// Overtime hours
        /// ساعات العمل الإضافي
        /// </summary>
        [Display(Name = "Overtime Hours")]
        public TimeSpan? OvertimeHours { get; set; }

        /// <summary>
        /// Late arrival minutes
        /// دقائق التأخير
        /// </summary>
        [Display(Name = "Late Minutes")]
        public int LateMinutes { get; set; }

        /// <summary>
        /// Early departure minutes
        /// دقائق المغادرة المبكرة
        /// </summary>
        [Display(Name = "Early Departure Minutes")]
        public int EarlyDepartureMinutes { get; set; }

        /// <summary>
        /// Attendance status
        /// حالة الحضور
        /// </summary>
        [Display(Name = "Status")]
        public string AttendanceStatus { get; set; } = "Present";

        /// <summary>
        /// Check-in location
        /// موقع الحضور
        /// </summary>
        [Display(Name = "Check-in Location")]
        public string? CheckInLocation { get; set; }

        /// <summary>
        /// Check-out location
        /// موقع الانصراف
        /// </summary>
        [Display(Name = "Check-out Location")]
        public string? CheckOutLocation { get; set; }

        /// <summary>
        /// Check-in device/method
        /// جهاز/طريقة الحضور
        /// </summary>
        [Display(Name = "Check-in Device")]
        public string? CheckInDevice { get; set; }

        /// <summary>
        /// Check-out device/method
        /// جهاز/طريقة الانصراف
        /// </summary>
        [Display(Name = "Check-out Device")]
        public string? CheckOutDevice { get; set; }

        /// <summary>
        /// Notes or comments
        /// ملاحظات أو تعليقات
        /// </summary>
        [StringLength(1000, ErrorMessage = "Notes cannot exceed 1000 characters")]
        [Display(Name = "Notes")]
        [DataType(DataType.MultilineText)]
        public string? Notes { get; set; }

        /// <summary>
        /// Whether the record is approved
        /// ما إذا كان السجل معتمد
        /// </summary>
        [Display(Name = "Approved")]
        public bool IsApproved { get; set; }

        // Computed properties
        /// <summary>
        /// Whether employee is present
        /// ما إذا كان الموظف حاضر
        /// </summary>
        public bool IsPresent => AttendanceStatus == "Present" && CheckInTime.HasValue;

        /// <summary>
        /// Whether employee is late
        /// ما إذا كان الموظف متأخر
        /// </summary>
        public bool IsLate => LateMinutes > 0;

        /// <summary>
        /// Whether employee left early
        /// ما إذا كان الموظف غادر مبكراً
        /// </summary>
        public bool IsEarlyDeparture => EarlyDepartureMinutes > 0;

        /// <summary>
        /// Whether employee has overtime
        /// ما إذا كان الموظف لديه عمل إضافي
        /// </summary>
        public bool HasOvertime => OvertimeHours.HasValue && OvertimeHours.Value.TotalMinutes > 0;

        /// <summary>
        /// Working hours as formatted string
        /// ساعات العمل كنص منسق
        /// </summary>
        public string WorkingHoursFormatted => WorkingHours?.ToString(@"hh\:mm") ?? "00:00";

        /// <summary>
        /// Overtime hours as formatted string
        /// ساعات العمل الإضافي كنص منسق
        /// </summary>
        public string OvertimeHoursFormatted => OvertimeHours?.ToString(@"hh\:mm") ?? "00:00";

        /// <summary>
        /// Break duration as formatted string
        /// مدة الاستراحة كنص منسق
        /// </summary>
        public string BreakDurationFormatted => BreakDuration?.ToString(@"hh\:mm") ?? "00:00";

        /// <summary>
        /// Check-in time formatted
        /// وقت الحضور منسق
        /// </summary>
        public string CheckInTimeFormatted => CheckInTime?.ToString("HH:mm") ?? "--:--";

        /// <summary>
        /// Check-out time formatted
        /// وقت الانصراف منسق
        /// </summary>
        public string CheckOutTimeFormatted => CheckOutTime?.ToString("HH:mm") ?? "--:--";

        /// <summary>
        /// Status badge CSS class
        /// فئة CSS لشارة الحالة
        /// </summary>
        public string StatusBadgeClass => AttendanceStatus switch
        {
            "Present" => "badge bg-success",
            "Absent" => "badge bg-danger",
            "Late" => "badge bg-warning",
            "OnLeave" => "badge bg-info",
            _ => "badge bg-secondary"
        };

        /// <summary>
        /// Status display text
        /// نص عرض الحالة
        /// </summary>
        public string StatusDisplayText => AttendanceStatus switch
        {
            "Present" => "Present / حاضر",
            "Absent" => "Absent / غائب",
            "Late" => "Late / متأخر",
            "OnLeave" => "On Leave / في إجازة",
            _ => AttendanceStatus
        };
    }

    /// <summary>
    /// Check-in/Check-out view model
    /// نموذج عرض الحضور/الانصراف
    /// </summary>
    public class CheckInOutViewModel
    {
        /// <summary>
        /// Employee ID
        /// معرف الموظف
        /// </summary>
        [Required(ErrorMessage = "Employee is required")]
        [Display(Name = "Employee")]
        public Guid EmployeeId { get; set; }

        /// <summary>
        /// Check-in or check-out time
        /// وقت الحضور أو الانصراف
        /// </summary>
        [Display(Name = "Time")]
        public DateTime? Time { get; set; } = DateTime.Now;

        /// <summary>
        /// Location
        /// الموقع
        /// </summary>
        [StringLength(200, ErrorMessage = "Location cannot exceed 200 characters")]
        [Display(Name = "Location", Prompt = "Enter location")]
        public string? Location { get; set; }

        /// <summary>
        /// Device/method used
        /// الجهاز/الطريقة المستخدمة
        /// </summary>
        [StringLength(100, ErrorMessage = "Device cannot exceed 100 characters")]
        [Display(Name = "Device", Prompt = "Enter device or method")]
        public string? Device { get; set; }

        /// <summary>
        /// Notes or comments
        /// ملاحظات أو تعليقات
        /// </summary>
        [StringLength(500, ErrorMessage = "Notes cannot exceed 500 characters")]
        [Display(Name = "Notes", Prompt = "Enter any notes")]
        [DataType(DataType.MultilineText)]
        public string? Notes { get; set; }

        /// <summary>
        /// Whether this is a check-in (true) or check-out (false)
        /// ما إذا كان هذا حضور (true) أو انصراف (false)
        /// </summary>
        public bool IsCheckIn { get; set; } = true;

        /// <summary>
        /// Available employees for selection
        /// الموظفين المتاحين للاختيار
        /// </summary>
        public List<EmployeeViewModel> AvailableEmployees { get; set; } = new();

        /// <summary>
        /// Available devices for selection
        /// الأجهزة المتاحة للاختيار
        /// </summary>
        public List<string> AvailableDevices { get; set; } = new()
        {
            "Web Portal",
            "Mobile App",
            "Biometric Scanner",
            "RFID Card",
            "Manual Entry"
        };

        /// <summary>
        /// Page title based on operation
        /// عنوان الصفحة حسب العملية
        /// </summary>
        public string PageTitle => IsCheckIn ? "Check In / تسجيل الحضور" : "Check Out / تسجيل الانصراف";

        /// <summary>
        /// Submit button text
        /// نص زر الإرسال
        /// </summary>
        public string SubmitButtonText => IsCheckIn ? "Check In / حضور" : "Check Out / انصراف";
    }
}
