@model FitHRPlus.Web.Models.Payroll.PayrollDetailsViewModel
@{
    ViewData["Title"] = "تفاصيل كشف المرتبات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        تفاصيل كشف المرتبات
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Payroll Header -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <h5>كشف مرتبات رقم: @Model.Id</h5>
                            <p class="text-muted">فترة الراتب: @Model.PayrollMonth/@Model.PayrollYear</p>
                        </div>
                        <div class="col-md-4 text-end">
                            @switch (Model.Status)
                            {
                                case "Draft":
                                    <span class="badge bg-secondary fs-6">مسودة</span>
                                    break;
                                case "Processed":
                                    <span class="badge bg-warning fs-6">معالج</span>
                                    break;
                                case "Approved":
                                    <span class="badge bg-success fs-6">موافق عليه</span>
                                    break;
                                case "Paid":
                                    <span class="badge bg-primary fs-6">مدفوع</span>
                                    break;
                                default:
                                    <span class="badge bg-light text-dark fs-6">@Model.Status</span>
                                    break;
                            }
                        </div>
                    </div>

                    <!-- Employee Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-user me-2"></i>
                                معلومات الموظف
                            </h5>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>اسم الموظف:</strong>
                            <p>@Model.EmployeeName</p>
                        </div>
                        <div class="col-md-4">
                            <strong>رقم الموظف:</strong>
                            <p>@Model.EmployeeNumber</p>
                        </div>
                        <div class="col-md-4">
                            <strong>القسم:</strong>
                            <p>@Model.DepartmentName</p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>المنصب:</strong>
                            <p>@Model.PositionName</p>
                        </div>
                        <div class="col-md-4">
                            <strong>تاريخ التوظيف:</strong>
                            <p>@Model.HireDate.ToString("dd/MM/yyyy")</p>
                        </div>
                        <div class="col-md-4">
                            <strong>الراتب الأساسي:</strong>
                            <p>@Model.BasicSalary.ToString("N2") ج.م</p>
                        </div>
                    </div>

                    <!-- Salary Breakdown -->
                    <div class="row mb-4 mt-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-calculator me-2"></i>
                                تفصيل الراتب
                            </h5>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <!-- Earnings -->
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">الاستحقاقات</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr>
                                            <td>الراتب الأساسي</td>
                                            <td class="text-end">@Model.BasicSalary.ToString("N2") ج.م</td>
                                        </tr>
                                        <tr>
                                            <td>بدل مواصلات</td>
                                            <td class="text-end">@Model.TransportationAllowance.ToString("N2") ج.م</td>
                                        </tr>
                                        <tr>
                                            <td>بدل طعام</td>
                                            <td class="text-end">@Model.FoodAllowance.ToString("N2") ج.م</td>
                                        </tr>
                                        <tr>
                                            <td>بدل سكن</td>
                                            <td class="text-end">@Model.HousingAllowance.ToString("N2") ج.م</td>
                                        </tr>
                                        <tr>
                                            <td>ساعات إضافية</td>
                                            <td class="text-end">@Model.OvertimeAmount.ToString("N2") ج.م</td>
                                        </tr>
                                        <tr>
                                            <td>مكافآت</td>
                                            <td class="text-end">@Model.BonusAmount.ToString("N2") ج.م</td>
                                        </tr>
                                        <tr class="table-success">
                                            <td><strong>إجمالي الاستحقاقات</strong></td>
                                            <td class="text-end"><strong>@Model.TotalEarnings.ToString("N2") ج.م</strong></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Deductions -->
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">الخصومات</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr>
                                            <td>التأمينات الاجتماعية</td>
                                            <td class="text-end">@Model.SocialInsurance.ToString("N2") ج.م</td>
                                        </tr>
                                        <tr>
                                            <td>ضريبة الدخل</td>
                                            <td class="text-end">@Model.IncomeTax.ToString("N2") ج.م</td>
                                        </tr>
                                        <tr>
                                            <td>خصم غياب</td>
                                            <td class="text-end">@Model.AbsenceDeduction.ToString("N2") ج.م</td>
                                        </tr>
                                        <tr>
                                            <td>خصم تأخير</td>
                                            <td class="text-end">@Model.LateDeduction.ToString("N2") ج.م</td>
                                        </tr>
                                        <tr>
                                            <td>سلف</td>
                                            <td class="text-end">@Model.AdvanceDeduction.ToString("N2") ج.م</td>
                                        </tr>
                                        <tr>
                                            <td>خصومات أخرى</td>
                                            <td class="text-end">@Model.OtherDeductions.ToString("N2") ج.م</td>
                                        </tr>
                                        <tr class="table-danger">
                                            <td><strong>إجمالي الخصومات</strong></td>
                                            <td class="text-end"><strong>@Model.TotalDeductions.ToString("N2") ج.م</strong></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Net Salary -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0 text-center">صافي الراتب</h5>
                                </div>
                                <div class="card-body text-center">
                                    <h2 class="text-primary">@Model.NetSalary.ToString("N2") ج.م</h2>
                                    <p class="text-muted">صافي الراتب المستحق للموظف</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Attendance Summary -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-clock me-2"></i>
                                ملخص الحضور
                            </h5>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h6>أيام العمل</h6>
                                    <h4>@Model.WorkingDays</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h6>أيام الحضور</h6>
                                    <h4>@Model.PresentDays</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h6>أيام الغياب</h6>
                                    <h4>@Model.AbsentDays</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h6>ساعات إضافية</h6>
                                    <h4>@Model.OvertimeHours</h4>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Information -->
                    @if (Model.Status == "Paid")
                    {
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-credit-card me-2"></i>
                                    معلومات الدفع
                                </h5>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <strong>تاريخ الدفع:</strong>
                                <p>@Model.PaidAt?.ToString("dd/MM/yyyy HH:mm")</p>
                            </div>
                            <div class="col-md-4">
                                <strong>طريقة الدفع:</strong>
                                <p>@Model.PaymentMethod</p>
                            </div>
                            <div class="col-md-4">
                                <strong>مرجع الدفع:</strong>
                                <p>@Model.PaymentReference</p>
                            </div>
                        </div>
                    }

                    <!-- Notes -->
                    @if (!string.IsNullOrEmpty(Model.Notes))
                    {
                        <div class="row mb-3">
                            <div class="col-12">
                                <strong>ملاحظات:</strong>
                                <p class="border p-3 bg-light">@Model.Notes</p>
                            </div>
                        </div>
                    }

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            @if (Model.Status == "Draft")
                            {
                                <button type="button" class="btn btn-warning" onclick="processPayroll('@Model.Id')">
                                    <i class="fas fa-cogs me-1"></i>
                                    معالجة كشف المرتبات
                                </button>
                            }
                            
                            @if (Model.Status == "Processed")
                            {
                                <button type="button" class="btn btn-success" onclick="approvePayroll('@Model.Id')">
                                    <i class="fas fa-check me-1"></i>
                                    الموافقة على كشف المرتبات
                                </button>
                            }
                            
                            @if (Model.Status == "Approved")
                            {
                                <button type="button" class="btn btn-primary" onclick="markAsPaid('@Model.Id')">
                                    <i class="fas fa-money-bill me-1"></i>
                                    تسجيل كمدفوع
                                </button>
                            }
                            
                            <a href="@Url.Action("Index")" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>
                                العودة للقائمة
                            </a>
                            
                            <button type="button" class="btn btn-info" onclick="printPayroll()">
                                <i class="fas fa-print me-1"></i>
                                طباعة
                            </button>

                            <button type="button" class="btn btn-outline-primary" onclick="downloadPayslip('@Model.Id')">
                                <i class="fas fa-download me-1"></i>
                                تحميل قسيمة الراتب
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function processPayroll(payrollId) {
            if (confirm('هل أنت متأكد من معالجة كشف المرتبات؟')) {
                $.ajax({
                    url: '@Url.Action("Process")',
                    type: 'POST',
                    data: {
                        id: payrollId,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            location.reload();
                        } else {
                            alert('حدث خطأ: ' + result.message);
                        }
                    },
                    error: function() {
                        alert('حدث خطأ أثناء معالجة كشف المرتبات');
                    }
                });
            }
        }

        function approvePayroll(payrollId) {
            if (confirm('هل أنت متأكد من الموافقة على كشف المرتبات؟')) {
                $.ajax({
                    url: '@Url.Action("Approve")',
                    type: 'POST',
                    data: {
                        id: payrollId,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            location.reload();
                        } else {
                            alert('حدث خطأ: ' + result.message);
                        }
                    },
                    error: function() {
                        alert('حدث خطأ أثناء الموافقة على كشف المرتبات');
                    }
                });
            }
        }

        function markAsPaid(payrollId) {
            if (confirm('هل أنت متأكد من تسجيل كشف المرتبات كمدفوع؟')) {
                $.ajax({
                    url: '@Url.Action("Pay")',
                    type: 'POST',
                    data: {
                        id: payrollId,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            location.reload();
                        } else {
                            alert('حدث خطأ: ' + result.message);
                        }
                    },
                    error: function() {
                        alert('حدث خطأ أثناء تسجيل الدفع');
                    }
                });
            }
        }

        function printPayroll() {
            window.print();
        }

        function downloadPayslip(payrollId) {
            window.open('@Url.Action("DownloadPayslip")?id=' + payrollId, '_blank');
        }
    </script>
}
