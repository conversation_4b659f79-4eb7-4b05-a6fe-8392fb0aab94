using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Application.DTOs.Employees
{
    /// <summary>
    /// Department data transfer object
    /// كائنة نقل بيانات القسم
    /// </summary>
    public class DepartmentDto
    {
        /// <summary>
        /// Department ID
        /// معرف القسم
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Department name
        /// اسم القسم
        /// </summary>
        [Required(ErrorMessage = "Department name is required")]
        [StringLength(200, ErrorMessage = "Department name cannot exceed 200 characters")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Department name in Arabic
        /// اسم القسم بالعربية
        /// </summary>
        [StringLength(200, ErrorMessage = "Arabic department name cannot exceed 200 characters")]
        public string? NameAr { get; set; }

        /// <summary>
        /// Department description
        /// وصف القسم
        /// </summary>
        [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
        public string? Description { get; set; }

        /// <summary>
        /// Department description in Arabic
        /// وصف القسم بالعربية
        /// </summary>
        [StringLength(1000, ErrorMessage = "Arabic description cannot exceed 1000 characters")]
        public string? DescriptionAr { get; set; }

        /// <summary>
        /// Department code (unique within company)
        /// رمز القسم (فريد داخل الشركة)
        /// </summary>
        [StringLength(20, ErrorMessage = "Department code cannot exceed 20 characters")]
        public string? Code { get; set; }

        /// <summary>
        /// Company ID
        /// معرف الشركة
        /// </summary>
        [Required(ErrorMessage = "Company is required")]
        public Guid CompanyId { get; set; }

        /// <summary>
        /// Company name
        /// اسم الشركة
        /// </summary>
        public string CompanyName { get; set; } = string.Empty;

        /// <summary>
        /// Parent department ID (for hierarchical structure)
        /// معرف القسم الأب (للهيكل الهرمي)
        /// </summary>
        public Guid? ParentDepartmentId { get; set; }

        /// <summary>
        /// Parent department name
        /// اسم القسم الأب
        /// </summary>
        public string? ParentDepartmentName { get; set; }

        /// <summary>
        /// Department manager ID
        /// معرف مدير القسم
        /// </summary>
        public Guid? ManagerId { get; set; }

        /// <summary>
        /// Department manager name
        /// اسم مدير القسم
        /// </summary>
        public string? ManagerName { get; set; }

        /// <summary>
        /// Budget allocated to the department
        /// الميزانية المخصصة للقسم
        /// </summary>
        public decimal? Budget { get; set; }

        /// <summary>
        /// Location or floor
        /// الموقع أو الطابق
        /// </summary>
        [StringLength(200, ErrorMessage = "Location cannot exceed 200 characters")]
        public string? Location { get; set; }

        /// <summary>
        /// Department phone extension
        /// رقم هاتف القسم الداخلي
        /// </summary>
        [StringLength(20, ErrorMessage = "Phone extension cannot exceed 20 characters")]
        public string? PhoneExtension { get; set; }

        /// <summary>
        /// Department email
        /// البريد الإلكتروني للقسم
        /// </summary>
        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
        public string? Email { get; set; }

        /// <summary>
        /// Whether the department is active
        /// ما إذا كان القسم نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Creation date
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Last update date
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Number of employees in the department
        /// عدد الموظفين في القسم
        /// </summary>
        public int EmployeeCount { get; set; }

        /// <summary>
        /// Number of positions in the department
        /// عدد المناصب في القسم
        /// </summary>
        public int PositionCount { get; set; }

        /// <summary>
        /// Sub-departments
        /// الأقسام الفرعية
        /// </summary>
        public List<DepartmentDto> SubDepartments { get; set; } = new();

        /// <summary>
        /// Positions in the department
        /// المناصب في القسم
        /// </summary>
        public List<PositionDto> Positions { get; set; } = new();
    }

    /// <summary>
    /// Department list request DTO
    /// كائنة طلب قائمة الأقسام
    /// </summary>
    public class DepartmentListRequestDto
    {
        /// <summary>
        /// Company ID
        /// معرف الشركة
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// Search term
        /// مصطلح البحث
        /// </summary>
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Include inactive departments
        /// تضمين الأقسام غير النشطة
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// Page number
        /// رقم الصفحة
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Page size
        /// حجم الصفحة
        /// </summary>
        public int PageSize { get; set; } = 10;
    }

    /// <summary>
    /// Create department DTO
    /// كائنة إنشاء قسم
    /// </summary>
    public class CreateDepartmentDto
    {
        /// <summary>
        /// Company ID
        /// معرف الشركة
        /// </summary>
        [Required]
        public Guid CompanyId { get; set; }

        /// <summary>
        /// Department name
        /// اسم القسم
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Department name in Arabic
        /// اسم القسم بالعربية
        /// </summary>
        [StringLength(200)]
        public string? NameAr { get; set; }

        /// <summary>
        /// Department description
        /// وصف القسم
        /// </summary>
        [StringLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// Department description in Arabic
        /// وصف القسم بالعربية
        /// </summary>
        [StringLength(1000)]
        public string? DescriptionAr { get; set; }

        /// <summary>
        /// Department budget
        /// ميزانية القسم
        /// </summary>
        public decimal? Budget { get; set; }

        /// <summary>
        /// Created by user ID
        /// معرف المستخدم المنشئ
        /// </summary>
        [Required]
        public Guid CreatedBy { get; set; }
    }

    /// <summary>
    /// Update department DTO
    /// كائنة تحديث قسم
    /// </summary>
    public class UpdateDepartmentDto
    {
        /// <summary>
        /// Department ID
        /// معرف القسم
        /// </summary>
        [Required]
        public Guid Id { get; set; }

        /// <summary>
        /// Department name
        /// اسم القسم
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Department name in Arabic
        /// اسم القسم بالعربية
        /// </summary>
        [StringLength(200)]
        public string? NameAr { get; set; }

        /// <summary>
        /// Department description
        /// وصف القسم
        /// </summary>
        [StringLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// Department description in Arabic
        /// وصف القسم بالعربية
        /// </summary>
        [StringLength(1000)]
        public string? DescriptionAr { get; set; }

        /// <summary>
        /// Department budget
        /// ميزانية القسم
        /// </summary>
        public decimal? Budget { get; set; }

        /// <summary>
        /// Is active
        /// نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Updated by user ID
        /// معرف المستخدم المحدث
        /// </summary>
        [Required]
        public Guid UpdatedBy { get; set; }
    }

    /// <summary>
    /// Position list request DTO
    /// كائنة طلب قائمة المناصب
    /// </summary>
    public class PositionListRequestDto
    {
        /// <summary>
        /// Company ID
        /// معرف الشركة
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// Department ID filter
        /// فلتر معرف القسم
        /// </summary>
        public Guid? DepartmentId { get; set; }

        /// <summary>
        /// Search term
        /// مصطلح البحث
        /// </summary>
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Include inactive positions
        /// تضمين المناصب غير النشطة
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// Page number
        /// رقم الصفحة
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Page size
        /// حجم الصفحة
        /// </summary>
        public int PageSize { get; set; } = 10;
    }

    /// <summary>
    /// Create position DTO
    /// كائنة إنشاء منصب
    /// </summary>
    public class CreatePositionDto
    {
        /// <summary>
        /// Company ID
        /// معرف الشركة
        /// </summary>
        [Required]
        public Guid CompanyId { get; set; }

        /// <summary>
        /// Department ID
        /// معرف القسم
        /// </summary>
        [Required]
        public Guid DepartmentId { get; set; }

        /// <summary>
        /// Position title
        /// مسمى المنصب
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Position title in Arabic
        /// مسمى المنصب بالعربية
        /// </summary>
        [StringLength(200)]
        public string? TitleAr { get; set; }

        /// <summary>
        /// Position description
        /// وصف المنصب
        /// </summary>
        [StringLength(2000)]
        public string? Description { get; set; }

        /// <summary>
        /// Position description in Arabic
        /// وصف المنصب بالعربية
        /// </summary>
        [StringLength(2000)]
        public string? DescriptionAr { get; set; }

        /// <summary>
        /// Minimum salary
        /// الحد الأدنى للراتب
        /// </summary>
        public decimal? MinSalary { get; set; }

        /// <summary>
        /// Maximum salary
        /// الحد الأقصى للراتب
        /// </summary>
        public decimal? MaxSalary { get; set; }

        /// <summary>
        /// Created by user ID
        /// معرف المستخدم المنشئ
        /// </summary>
        [Required]
        public Guid CreatedBy { get; set; }
    }

    /// <summary>
    /// Update position DTO
    /// كائنة تحديث منصب
    /// </summary>
    public class UpdatePositionDto
    {
        /// <summary>
        /// Position ID
        /// معرف المنصب
        /// </summary>
        [Required]
        public Guid Id { get; set; }

        /// <summary>
        /// Department ID
        /// معرف القسم
        /// </summary>
        [Required]
        public Guid DepartmentId { get; set; }

        /// <summary>
        /// Position title
        /// مسمى المنصب
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Position title in Arabic
        /// مسمى المنصب بالعربية
        /// </summary>
        [StringLength(200)]
        public string? TitleAr { get; set; }

        /// <summary>
        /// Position description
        /// وصف المنصب
        /// </summary>
        [StringLength(2000)]
        public string? Description { get; set; }

        /// <summary>
        /// Position description in Arabic
        /// وصف المنصب بالعربية
        /// </summary>
        [StringLength(2000)]
        public string? DescriptionAr { get; set; }

        /// <summary>
        /// Minimum salary
        /// الحد الأدنى للراتب
        /// </summary>
        public decimal? MinSalary { get; set; }

        /// <summary>
        /// Maximum salary
        /// الحد الأقصى للراتب
        /// </summary>
        public decimal? MaxSalary { get; set; }

        /// <summary>
        /// Is active
        /// نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Updated by user ID
        /// معرف المستخدم المحدث
        /// </summary>
        [Required]
        public Guid UpdatedBy { get; set; }
    }

    /// <summary>
    /// Position data transfer object
    /// كائنة نقل بيانات المنصب
    /// </summary>
    public class PositionDto
    {
        /// <summary>
        /// Position ID
        /// معرف المنصب
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Position title
        /// مسمى المنصب
        /// </summary>
        [Required(ErrorMessage = "Position title is required")]
        [StringLength(200, ErrorMessage = "Position title cannot exceed 200 characters")]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Position title in Arabic
        /// مسمى المنصب بالعربية
        /// </summary>
        [StringLength(200, ErrorMessage = "Arabic position title cannot exceed 200 characters")]
        public string? TitleAr { get; set; }

        /// <summary>
        /// Position description
        /// وصف المنصب
        /// </summary>
        [StringLength(2000, ErrorMessage = "Description cannot exceed 2000 characters")]
        public string? Description { get; set; }

        /// <summary>
        /// Position description in Arabic
        /// وصف المنصب بالعربية
        /// </summary>
        [StringLength(2000, ErrorMessage = "Arabic description cannot exceed 2000 characters")]
        public string? DescriptionAr { get; set; }

        /// <summary>
        /// Position code (unique within company)
        /// رمز المنصب (فريد داخل الشركة)
        /// </summary>
        [StringLength(20, ErrorMessage = "Position code cannot exceed 20 characters")]
        public string? Code { get; set; }

        /// <summary>
        /// Company ID
        /// معرف الشركة
        /// </summary>
        [Required(ErrorMessage = "Company is required")]
        public Guid CompanyId { get; set; }

        /// <summary>
        /// Company name
        /// اسم الشركة
        /// </summary>
        public string CompanyName { get; set; } = string.Empty;

        /// <summary>
        /// Department ID
        /// معرف القسم
        /// </summary>
        [Required(ErrorMessage = "Department is required")]
        public Guid DepartmentId { get; set; }

        /// <summary>
        /// Department name
        /// اسم القسم
        /// </summary>
        public string DepartmentName { get; set; } = string.Empty;

        /// <summary>
        /// Position level (Junior, Senior, Manager, etc.)
        /// مستوى المنصب (مبتدئ، خبير، مدير، إلخ)
        /// </summary>
        [StringLength(50, ErrorMessage = "Position level cannot exceed 50 characters")]
        public string? Level { get; set; }

        /// <summary>
        /// Minimum salary for the position
        /// الحد الأدنى للراتب للمنصب
        /// </summary>
        public decimal? MinSalary { get; set; }

        /// <summary>
        /// Maximum salary for the position
        /// الحد الأقصى للراتب للمنصب
        /// </summary>
        public decimal? MaxSalary { get; set; }

        /// <summary>
        /// Required qualifications
        /// المؤهلات المطلوبة
        /// </summary>
        [StringLength(1000, ErrorMessage = "Qualifications cannot exceed 1000 characters")]
        public string? RequiredQualifications { get; set; }

        /// <summary>
        /// Required skills
        /// المهارات المطلوبة
        /// </summary>
        [StringLength(1000, ErrorMessage = "Required skills cannot exceed 1000 characters")]
        public string? RequiredSkills { get; set; }

        /// <summary>
        /// Years of experience required
        /// سنوات الخبرة المطلوبة
        /// </summary>
        public int? RequiredExperience { get; set; }

        /// <summary>
        /// Whether the position is active
        /// ما إذا كان المنصب نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Creation date
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Last update date
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Number of employees in this position
        /// عدد الموظفين في هذا المنصب
        /// </summary>
        public int EmployeeCount { get; set; }
    }
}
