using FitHRPlus.Application.Interfaces;
using FitHRPlus.Web.Models.Reports;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace FitHRPlus.Web.Controllers
{
    /// <summary>
    /// Reports and analytics controller
    /// وحدة تحكم التقارير والتحليلات
    /// </summary>
    [Authorize]
    public class ReportsController : Controller
    {
        private readonly IEmployeeService _employeeService;
        private readonly IAttendanceService _attendanceService;
        private readonly ILeaveService _leaveService;
        private readonly IPayrollService _payrollService;
        private readonly ILogger<ReportsController> _logger;

        public ReportsController(
            IEmployeeService employeeService,
            IAttendanceService attendanceService,
            ILeaveService leaveService,
            IPayrollService payrollService,
            ILogger<ReportsController> logger)
        {
            _employeeService = employeeService;
            _attendanceService = attendanceService;
            _leaveService = leaveService;
            _payrollService = payrollService;
            _logger = logger;
        }

        /// <summary>
        /// Reports dashboard
        /// لوحة تحكم التقارير
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Index()
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "Company information not found";
                    return RedirectToAction("Index", "Home");
                }

                var viewModel = new ReportsDashboardViewModel
                {
                    CompanyId = companyId
                };

                // Load dashboard statistics
                await LoadDashboardStatisticsAsync(viewModel, companyId);

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading reports dashboard");
                TempData["ErrorMessage"] = "An error occurred while loading reports dashboard";
                return View(new ReportsDashboardViewModel());
            }
        }

        /// <summary>
        /// Employee reports
        /// تقارير الموظفين
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Employees()
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "Company information not found";
                    return RedirectToAction("Index", "Home");
                }

                var viewModel = new EmployeeReportsViewModel
                {
                    CompanyId = companyId,
                    FromDate = DateTime.Today.AddMonths(-1),
                    ToDate = DateTime.Today
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading employee reports");
                TempData["ErrorMessage"] = "An error occurred while loading employee reports";
                return View(new EmployeeReportsViewModel());
            }
        }

        /// <summary>
        /// Attendance reports
        /// تقارير الحضور
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Attendance()
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "Company information not found";
                    return RedirectToAction("Index", "Home");
                }

                var viewModel = new AttendanceReportsViewModel
                {
                    CompanyId = companyId,
                    FromDate = DateTime.Today.AddDays(-30),
                    ToDate = DateTime.Today
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading attendance reports");
                TempData["ErrorMessage"] = "An error occurred while loading attendance reports";
                return View(new AttendanceReportsViewModel());
            }
        }

        /// <summary>
        /// Leave reports
        /// تقارير الإجازات
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Leave()
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "Company information not found";
                    return RedirectToAction("Index", "Home");
                }

                var viewModel = new LeaveReportsViewModel
                {
                    CompanyId = companyId,
                    FromDate = DateTime.Today.AddMonths(-3),
                    ToDate = DateTime.Today
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading leave reports");
                TempData["ErrorMessage"] = "An error occurred while loading leave reports";
                return View(new LeaveReportsViewModel());
            }
        }

        /// <summary>
        /// Payroll reports
        /// تقارير كشوف المرتبات
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Payroll()
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "Company information not found";
                    return RedirectToAction("Index", "Home");
                }

                var viewModel = new PayrollReportsViewModel
                {
                    CompanyId = companyId,
                    FromMonth = DateTime.Today.Month,
                    FromYear = DateTime.Today.Year,
                    ToMonth = DateTime.Today.Month,
                    ToYear = DateTime.Today.Year
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading payroll reports");
                TempData["ErrorMessage"] = "An error occurred while loading payroll reports";
                return View(new PayrollReportsViewModel());
            }
        }

        /// <summary>
        /// Analytics dashboard
        /// لوحة تحكم التحليلات
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Analytics()
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "Company information not found";
                    return RedirectToAction("Index", "Home");
                }

                var viewModel = new AnalyticsViewModel
                {
                    CompanyId = companyId
                };

                // Load analytics data
                await LoadAnalyticsDataAsync(viewModel, companyId);

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading analytics");
                TempData["ErrorMessage"] = "An error occurred while loading analytics";
                return View(new AnalyticsViewModel());
            }
        }

        /// <summary>
        /// Export report to Excel
        /// تصدير التقرير إلى Excel
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> ExportToExcel(string reportType, string parameters)
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    return BadRequest("Company information not found");
                }

                // Implementation for Excel export based on report type
                byte[] fileData = await GenerateExcelReportAsync(reportType, parameters, companyId);
                
                var fileName = $"{reportType}_Report_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                return File(fileData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting report to Excel");
                return BadRequest("An error occurred while exporting the report");
            }
        }

        /// <summary>
        /// Export report to PDF
        /// تصدير التقرير إلى PDF
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> ExportToPDF(string reportType, string parameters)
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    return BadRequest("Company information not found");
                }

                // Implementation for PDF export based on report type
                byte[] fileData = await GeneratePDFReportAsync(reportType, parameters, companyId);
                
                var fileName = $"{reportType}_Report_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";
                return File(fileData, "application/pdf", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting report to PDF");
                return BadRequest("An error occurred while exporting the report");
            }
        }

        // Helper methods
        private async Task LoadDashboardStatisticsAsync(ReportsDashboardViewModel viewModel, Guid companyId)
        {
            // Load statistics from various services
            // Implementation will be added based on service methods
        }

        private async Task LoadAnalyticsDataAsync(AnalyticsViewModel viewModel, Guid companyId)
        {
            // Load analytics data from various services
            // Implementation will be added based on service methods
        }

        private async Task<byte[]> GenerateExcelReportAsync(string reportType, string parameters, Guid companyId)
        {
            // Implementation for Excel report generation
            // This would use a library like EPPlus or ClosedXML
            return new byte[0]; // Placeholder
        }

        private async Task<byte[]> GeneratePDFReportAsync(string reportType, string parameters, Guid companyId)
        {
            // Implementation for PDF report generation
            // This would use a library like iTextSharp or PdfSharp
            return new byte[0]; // Placeholder
        }

        /// <summary>
        /// Payroll reports
        /// تقارير كشوف المرتبات
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> PayrollReports(int? fromMonth, int? fromYear, string? department, string? reportType)
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "Company information not found";
                    return RedirectToAction("Index", "Home");
                }

                var viewModel = new PayrollReportsViewModel
                {
                    FromMonth = fromMonth ?? DateTime.Today.Month,
                    FromYear = fromYear ?? DateTime.Today.Year,
                    Department = department,
                    ReportType = reportType ?? "summary"
                };

                // Get payroll statistics
                // This would be implemented in the service layer
                viewModel.TotalSalaries = 150000m; // Placeholder
                viewModel.TotalAllowances = 25000m; // Placeholder
                viewModel.TotalDeductions = 15000m; // Placeholder
                viewModel.NetSalaries = 160000m; // Placeholder

                // Sample chart data
                viewModel.SalariesByDepartment = new Dictionary<string, decimal>
                {
                    { "الموارد البشرية", 45000m },
                    { "تقنية المعلومات", 65000m },
                    { "المحاسبة", 40000m }
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading payroll reports");
                TempData["ErrorMessage"] = "Error loading payroll reports.";
                return RedirectToAction("Index");
            }
        }

        /// <summary>
        /// Schedule reports page
        /// صفحة جدولة التقارير
        /// </summary>
        [HttpGet]
        public IActionResult Schedule()
        {
            // For now, return a simple view
            return View();
        }

        /// <summary>
        /// Custom report builder page
        /// صفحة منشئ التقارير المخصصة
        /// </summary>
        [HttpGet]
        public IActionResult CustomReportBuilder()
        {
            // For now, return a simple view
            return View();
        }

        /// <summary>
        /// Advanced report builder page
        /// صفحة منشئ التقارير المتقدم
        /// </summary>
        [HttpGet]
        public IActionResult ReportBuilder()
        {
            // For now, return a simple view
            return View();
        }
    }
}
