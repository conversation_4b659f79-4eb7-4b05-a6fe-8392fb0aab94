using FitHRPlus.Application.DTOs.Auth;
using FitHRPlus.Application.Interfaces;
using FitHRPlus.Web.Models.Auth;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace FitHRPlus.Web.Controllers
{
    /// <summary>
    /// Account controller for web authentication
    /// وحدة تحكم الحساب للمصادقة عبر الويب
    /// </summary>
    public class AccountController : Controller
    {
        private readonly IAuthService _authService;
        private readonly ILogger<AccountController> _logger;

        public AccountController(IAuthService authService, ILogger<AccountController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// Display login page
        /// عرض صفحة تسجيل الدخول
        /// </summary>
        /// <param name="returnUrl">Return URL after login</param>
        /// <returns>Login view</returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult Login(string? returnUrl = null)
        {
            if (User.Identity?.IsAuthenticated == true)
            {
                return RedirectToAction("Index", "Dashboard");
            }

            var model = new LoginViewModel
            {
                ReturnUrl = returnUrl
            };

            return View(model);
        }

        /// <summary>
        /// Process login form submission
        /// معالجة إرسال نموذج تسجيل الدخول
        /// </summary>
        /// <param name="model">Login view model</param>
        /// <returns>Redirect or login view with errors</returns>
        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Login(LoginViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var loginRequest = new LoginRequestDto
                {
                    UsernameOrEmail = model.UsernameOrEmail,
                    Password = model.Password,
                    RememberMe = model.RememberMe,
                    TwoFactorCode = model.TwoFactorCode
                };

                var result = await _authService.LoginAsync(loginRequest);

                if (result.IsSuccess && result.Data != null)
                {
                    // Check if two-factor authentication is required
                    if (result.Data.RequiresTwoFactor)
                    {
                        model.RequiresTwoFactor = true;
                        model.TwoFactorToken = result.Data.TwoFactorToken;
                        model.ErrorMessage = null;
                        ViewBag.ShowTwoFactorForm = true;
                        return View(model);
                    }

                    // Create claims for the user
                    var claims = new List<Claim>
                    {
                        new(ClaimTypes.NameIdentifier, result.Data.User.Id.ToString()),
                        new(ClaimTypes.Name, result.Data.User.Username),
                        new(ClaimTypes.Email, result.Data.User.Email),
                        new("FullName", $"{result.Data.User.FirstName} {result.Data.User.LastName}"),
                        new("PreferredLanguage", result.Data.User.PreferredLanguage)
                    };

                    // Add roles and permissions
                    foreach (var company in result.Data.Companies)
                    {
                        claims.Add(new Claim(ClaimTypes.Role, company.RoleName));
                        claims.Add(new Claim("CompanyId", company.CompanyId.ToString()));
                        claims.Add(new Claim("CompanyName", company.CompanyName));
                        
                        foreach (var permission in company.Permissions)
                        {
                            claims.Add(new Claim("Permission", permission));
                        }
                    }

                    var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                    var authProperties = new AuthenticationProperties
                    {
                        IsPersistent = model.RememberMe,
                        ExpiresUtc = DateTimeOffset.UtcNow.AddDays(model.RememberMe ? 30 : 1)
                    };

                    await HttpContext.SignInAsync(
                        CookieAuthenticationDefaults.AuthenticationScheme,
                        new ClaimsPrincipal(claimsIdentity),
                        authProperties);

                    _logger.LogInformation("User {Username} logged in successfully", model.UsernameOrEmail);

                    // Redirect to return URL or dashboard
                    if (!string.IsNullOrEmpty(model.ReturnUrl) && Url.IsLocalUrl(model.ReturnUrl))
                    {
                        return Redirect(model.ReturnUrl);
                    }

                    return RedirectToAction("Index", "Dashboard");
                }

                // Login failed
                model.ErrorMessage = result.ErrorMessage ?? "Login failed. Please try again.";
                model.Password = string.Empty; // Clear password for security
                
                _logger.LogWarning("Failed login attempt for user: {Username}", model.UsernameOrEmail);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for user: {Username}", model.UsernameOrEmail);
                model.ErrorMessage = "An unexpected error occurred. Please try again.";
                model.Password = string.Empty;
            }

            return View(model);
        }

        /// <summary>
        /// Display registration page
        /// عرض صفحة التسجيل
        /// </summary>
        /// <returns>Registration view</returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult Register()
        {
            if (User.Identity?.IsAuthenticated == true)
            {
                return RedirectToAction("Index", "Dashboard");
            }

            return View(new RegisterViewModel());
        }

        /// <summary>
        /// Process registration form submission
        /// معالجة إرسال نموذج التسجيل
        /// </summary>
        /// <param name="model">Registration view model</param>
        /// <returns>Redirect or registration view with errors</returns>
        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Register(RegisterViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var registerRequest = new RegisterRequestDto
                {
                    Username = model.Username,
                    Email = model.Email,
                    Password = model.Password,
                    ConfirmPassword = model.ConfirmPassword,
                    FirstName = model.FirstName,
                    LastName = model.LastName,
                    Phone = model.Phone,
                    PreferredLanguage = model.PreferredLanguage,
                    AcceptTerms = model.AcceptTerms
                };

                var result = await _authService.RegisterAsync(registerRequest);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("User {Username} registered successfully", model.Username);
                    
                    // Auto-login after successful registration
                    var loginModel = new LoginViewModel
                    {
                        UsernameOrEmail = model.Username,
                        Password = model.Password,
                        RememberMe = false
                    };

                    return await Login(loginModel);
                }

                // Registration failed
                if (result.ValidationErrors != null)
                {
                    foreach (var error in result.ValidationErrors)
                    {
                        foreach (var errorMessage in error.Value)
                        {
                            ModelState.AddModelError(error.Key, errorMessage);
                        }
                    }
                }
                else
                {
                    model.ErrorMessage = result.ErrorMessage ?? "Registration failed. Please try again.";
                }

                model.Password = string.Empty;
                model.ConfirmPassword = string.Empty;
                
                _logger.LogWarning("Failed registration attempt for user: {Username}", model.Username);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during registration for user: {Username}", model.Username);
                model.ErrorMessage = "An unexpected error occurred. Please try again.";
                model.Password = string.Empty;
                model.ConfirmPassword = string.Empty;
            }

            return View(model);
        }

        /// <summary>
        /// Logout user
        /// تسجيل خروج المستخدم
        /// </summary>
        /// <returns>Redirect to login page</returns>
        [HttpPost]
        [Authorize]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Logout()
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            
            _logger.LogInformation("User {UserId} logged out", userId);
            
            return RedirectToAction("Login");
        }

        /// <summary>
        /// Access denied page
        /// صفحة رفض الوصول
        /// </summary>
        /// <returns>Access denied view</returns>
        [HttpGet]
        public IActionResult AccessDenied()
        {
            return View();
        }

        /// <summary>
        /// User profile page
        /// صفحة ملف المستخدم
        /// </summary>
        /// <returns>Profile view</returns>
        [HttpGet]
        [Authorize]
        public IActionResult Profile()
        {
            var userInfo = new
            {
                Id = User.FindFirst(ClaimTypes.NameIdentifier)?.Value,
                Username = User.FindFirst(ClaimTypes.Name)?.Value,
                Email = User.FindFirst(ClaimTypes.Email)?.Value,
                FullName = User.FindFirst("FullName")?.Value,
                PreferredLanguage = User.FindFirst("PreferredLanguage")?.Value,
                Roles = User.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList(),
                Companies = User.FindAll("CompanyName").Select(c => c.Value).ToList()
            };

            return View(userInfo);
        }
    }
}
