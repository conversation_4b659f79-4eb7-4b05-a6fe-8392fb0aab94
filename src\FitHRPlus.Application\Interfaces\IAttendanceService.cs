using FitHRPlus.Application.Common;
using FitHRPlus.Application.DTOs.Attendance;

namespace FitHRPlus.Application.Interfaces
{
    /// <summary>
    /// Attendance service interface
    /// واجهة خدمة الحضور والانصراف
    /// </summary>
    public interface IAttendanceService
    {
        /// <summary>
        /// Get attendance records with filtering and pagination
        /// الحصول على سجلات الحضور مع التصفية والترقيم
        /// </summary>
        /// <param name="request">List request parameters</param>
        /// <returns>Paginated attendance list</returns>
        Task<ServiceResult<AttendanceListResponseDto>> GetAttendanceRecordsAsync(AttendanceListRequestDto request);

        /// <summary>
        /// Get attendance record by ID
        /// الحصول على سجل الحضور بالمعرف
        /// </summary>
        /// <param name="id">Attendance record ID</param>
        /// <returns>Attendance record details</returns>
        Task<ServiceResult<AttendanceDto>> GetAttendanceByIdAsync(Guid id);

        /// <summary>
        /// Get employee attendance for a specific date
        /// الحصول على حضور الموظف لتاريخ محدد
        /// </summary>
        /// <param name="employeeId">Employee ID</param>
        /// <param name="date">Date</param>
        /// <returns>Attendance record</returns>
        Task<ServiceResult<AttendanceDto?>> GetEmployeeAttendanceAsync(Guid employeeId, DateTime date);

        /// <summary>
        /// Employee check-in
        /// حضور الموظف
        /// </summary>
        /// <param name="request">Check-in request</param>
        /// <param name="recordedBy">User ID who recorded the check-in</param>
        /// <returns>Updated attendance record</returns>
        Task<ServiceResult<AttendanceDto>> CheckInAsync(CheckInOutRequestDto request, Guid recordedBy);

        /// <summary>
        /// Employee check-out
        /// انصراف الموظف
        /// </summary>
        /// <param name="request">Check-out request</param>
        /// <param name="recordedBy">User ID who recorded the check-out</param>
        /// <returns>Updated attendance record</returns>
        Task<ServiceResult<AttendanceDto>> CheckOutAsync(CheckInOutRequestDto request, Guid recordedBy);

        /// <summary>
        /// Start break
        /// بداية الاستراحة
        /// </summary>
        /// <param name="employeeId">Employee ID</param>
        /// <param name="time">Break start time</param>
        /// <param name="recordedBy">User ID who recorded the break</param>
        /// <returns>Updated attendance record</returns>
        Task<ServiceResult<AttendanceDto>> StartBreakAsync(Guid employeeId, DateTime? time, Guid recordedBy);

        /// <summary>
        /// End break
        /// نهاية الاستراحة
        /// </summary>
        /// <param name="employeeId">Employee ID</param>
        /// <param name="time">Break end time</param>
        /// <param name="recordedBy">User ID who recorded the break end</param>
        /// <returns>Updated attendance record</returns>
        Task<ServiceResult<AttendanceDto>> EndBreakAsync(Guid employeeId, DateTime? time, Guid recordedBy);

        /// <summary>
        /// Create or update attendance record manually
        /// إنشاء أو تحديث سجل الحضور يدوياً
        /// </summary>
        /// <param name="attendanceDto">Attendance data</param>
        /// <param name="createdBy">User ID who created/updated the record</param>
        /// <returns>Created/updated attendance record</returns>
        Task<ServiceResult<AttendanceDto>> CreateOrUpdateAttendanceAsync(AttendanceDto attendanceDto, Guid createdBy);

        /// <summary>
        /// Delete attendance record
        /// حذف سجل الحضور
        /// </summary>
        /// <param name="id">Attendance record ID</param>
        /// <param name="deletedBy">User ID who deleted the record</param>
        /// <returns>Deletion result</returns>
        Task<ServiceResult<bool>> DeleteAttendanceAsync(Guid id, Guid deletedBy);

        /// <summary>
        /// Approve attendance record
        /// اعتماد سجل الحضور
        /// </summary>
        /// <param name="id">Attendance record ID</param>
        /// <param name="approvedBy">User ID who approved the record</param>
        /// <returns>Approval result</returns>
        Task<ServiceResult<bool>> ApproveAttendanceAsync(Guid id, Guid approvedBy);

        /// <summary>
        /// Bulk approve attendance records
        /// اعتماد سجلات الحضور بالجملة
        /// </summary>
        /// <param name="ids">List of attendance record IDs</param>
        /// <param name="approvedBy">User ID who approved the records</param>
        /// <returns>Approval result</returns>
        Task<ServiceResult<int>> BulkApproveAttendanceAsync(List<Guid> ids, Guid approvedBy);

        /// <summary>
        /// Get employee attendance summary for a period
        /// الحصول على ملخص حضور الموظف لفترة
        /// </summary>
        /// <param name="employeeId">Employee ID</param>
        /// <param name="dateFrom">Start date</param>
        /// <param name="dateTo">End date</param>
        /// <returns>Attendance summary</returns>
        Task<ServiceResult<AttendanceSummaryDto>> GetEmployeeAttendanceSummaryAsync(Guid employeeId, DateTime dateFrom, DateTime dateTo);

        /// <summary>
        /// Get daily attendance report
        /// الحصول على تقرير الحضور اليومي
        /// </summary>
        /// <param name="date">Report date</param>
        /// <param name="companyId">Company ID (optional)</param>
        /// <param name="departmentId">Department ID (optional)</param>
        /// <returns>Daily attendance report</returns>
        Task<ServiceResult<DailyAttendanceReportDto>> GetDailyAttendanceReportAsync(DateTime date, Guid? companyId = null, Guid? departmentId = null);

        /// <summary>
        /// Get attendance statistics for a period
        /// الحصول على إحصائيات الحضور لفترة
        /// </summary>
        /// <param name="dateFrom">Start date</param>
        /// <param name="dateTo">End date</param>
        /// <param name="companyId">Company ID (optional)</param>
        /// <param name="departmentId">Department ID (optional)</param>
        /// <returns>Attendance statistics</returns>
        Task<ServiceResult<AttendanceStatisticsDto>> GetAttendanceStatisticsAsync(DateTime dateFrom, DateTime dateTo, Guid? companyId = null, Guid? departmentId = null);

        /// <summary>
        /// Generate attendance records for employees (for a specific date)
        /// إنشاء سجلات الحضور للموظفين (لتاريخ محدد)
        /// </summary>
        /// <param name="date">Date to generate records for</param>
        /// <param name="companyId">Company ID</param>
        /// <param name="departmentId">Department ID (optional)</param>
        /// <param name="createdBy">User ID who generated the records</param>
        /// <returns>Number of records generated</returns>
        Task<ServiceResult<int>> GenerateAttendanceRecordsAsync(DateTime date, Guid companyId, Guid? departmentId, Guid createdBy);

        /// <summary>
        /// Import attendance records from external system
        /// استيراد سجلات الحضور من نظام خارجي
        /// </summary>
        /// <param name="fileData">File data</param>
        /// <param name="companyId">Company ID</param>
        /// <param name="importedBy">User ID who imported</param>
        /// <returns>Import result</returns>
        Task<ServiceResult<AttendanceImportResultDto>> ImportAttendanceRecordsAsync(byte[] fileData, Guid companyId, Guid importedBy);

        /// <summary>
        /// Export attendance records to file
        /// تصدير سجلات الحضور إلى ملف
        /// </summary>
        /// <param name="request">Export request parameters</param>
        /// <returns>Export file data</returns>
        Task<ServiceResult<AttendanceExportResultDto>> ExportAttendanceRecordsAsync(AttendanceListRequestDto request);

        /// <summary>
        /// Calculate working hours for attendance record
        /// حساب ساعات العمل لسجل الحضور
        /// </summary>
        /// <param name="checkInTime">Check-in time</param>
        /// <param name="checkOutTime">Check-out time</param>
        /// <param name="breakDuration">Break duration</param>
        /// <param name="employeeId">Employee ID for work schedule</param>
        /// <returns>Working hours calculation result</returns>
        Task<ServiceResult<WorkingHoursCalculationDto>> CalculateWorkingHoursAsync(DateTime checkInTime, DateTime checkOutTime, TimeSpan? breakDuration, Guid employeeId);

        /// <summary>
        /// Get employees currently checked in
        /// الحصول على الموظفين الحاضرين حالياً
        /// </summary>
        /// <param name="companyId">Company ID (optional)</param>
        /// <param name="departmentId">Department ID (optional)</param>
        /// <returns>List of employees currently checked in</returns>
        Task<ServiceResult<List<AttendanceDto>>> GetCurrentlyCheckedInEmployeesAsync(Guid? companyId = null, Guid? departmentId = null);

        /// <summary>
        /// Get attendance trends for dashboard
        /// الحصول على اتجاهات الحضور للوحة التحكم
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="days">Number of days to analyze</param>
        /// <returns>Attendance trends data</returns>
        Task<ServiceResult<AttendanceTrendsDto>> GetAttendanceTrendsAsync(Guid companyId, int days = 30);
    }

    /// <summary>
    /// Working hours calculation result DTO
    /// كائنة نقل بيانات نتيجة حساب ساعات العمل
    /// </summary>
    public class WorkingHoursCalculationDto
    {
        public TimeSpan WorkingHours { get; set; }
        public TimeSpan OvertimeHours { get; set; }
        public int LateMinutes { get; set; }
        public int EarlyDepartureMinutes { get; set; }
        public TimeSpan BreakDuration { get; set; }
        public TimeSpan NetWorkingHours { get; set; }
    }

    /// <summary>
    /// Attendance import result DTO
    /// كائنة نقل بيانات نتيجة استيراد الحضور
    /// </summary>
    public class AttendanceImportResultDto
    {
        public int TotalRecords { get; set; }
        public int SuccessfulImports { get; set; }
        public int FailedImports { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<AttendanceDto> ImportedRecords { get; set; } = new();
    }

    /// <summary>
    /// Attendance export result DTO
    /// كائنة نقل بيانات نتيجة تصدير الحضور
    /// </summary>
    public class AttendanceExportResultDto
    {
        public byte[] FileData { get; set; } = Array.Empty<byte>();
        public string FileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public int RecordCount { get; set; }
    }

    /// <summary>
    /// Attendance trends DTO for dashboard
    /// كائنة نقل بيانات اتجاهات الحضور للوحة التحكم
    /// </summary>
    public class AttendanceTrendsDto
    {
        public List<DailyTrendDto> DailyTrends { get; set; } = new();
        public double AverageAttendanceRate { get; set; }
        public double AverageLateRate { get; set; }
        public double AverageOvertimeRate { get; set; }
        public TimeSpan AverageWorkingHours { get; set; }
    }

    /// <summary>
    /// Daily trend DTO
    /// كائنة نقل بيانات الاتجاه اليومي
    /// </summary>
    public class DailyTrendDto
    {
        public DateTime Date { get; set; }
        public int TotalEmployees { get; set; }
        public int PresentEmployees { get; set; }
        public int LateEmployees { get; set; }
        public int OvertimeEmployees { get; set; }
        public double AttendanceRate { get; set; }
        public TimeSpan AverageWorkingHours { get; set; }
    }
}
