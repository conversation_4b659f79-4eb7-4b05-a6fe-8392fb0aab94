using System.Security.Claims;

namespace FitHRPlus.Application.Interfaces
{
    /// <summary>
    /// JWT token service interface
    /// واجهة خدمة رموز JWT
    /// </summary>
    public interface IJwtService
    {
        /// <summary>
        /// Generate access token for user
        /// إنشاء رمز الوصول للمستخدم
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="username">Username</param>
        /// <param name="email">Email</param>
        /// <param name="roles">User roles</param>
        /// <param name="permissions">User permissions</param>
        /// <param name="companyId">Current company ID</param>
        /// <returns>JWT access token</returns>
        string GenerateAccessToken(
            Guid userId,
            string username,
            string email,
            IEnumerable<string> roles,
            IEnumerable<string> permissions,
            Guid? companyId = null);

        /// <summary>
        /// Generate refresh token
        /// إنشاء رمز التحديث
        /// </summary>
        /// <returns>Refresh token</returns>
        string GenerateRefreshToken();

        /// <summary>
        /// Validate and parse JWT token
        /// التحقق من وتحليل رمز JWT
        /// </summary>
        /// <param name="token">JWT token</param>
        /// <returns>Claims principal if valid, null if invalid</returns>
        ClaimsPrincipal? ValidateToken(string token);

        /// <summary>
        /// Get user ID from token
        /// الحصول على معرف المستخدم من الرمز
        /// </summary>
        /// <param name="token">JWT token</param>
        /// <returns>User ID if valid, null if invalid</returns>
        Guid? GetUserIdFromToken(string token);

        /// <summary>
        /// Get token expiration time
        /// الحصول على وقت انتهاء صلاحية الرمز
        /// </summary>
        /// <param name="token">JWT token</param>
        /// <returns>Expiration time if valid, null if invalid</returns>
        DateTime? GetTokenExpiration(string token);

        /// <summary>
        /// Check if token is expired
        /// التحقق من انتهاء صلاحية الرمز
        /// </summary>
        /// <param name="token">JWT token</param>
        /// <returns>True if expired, false if still valid</returns>
        bool IsTokenExpired(string token);

        /// <summary>
        /// Get all claims from token
        /// الحصول على جميع المطالبات من الرمز
        /// </summary>
        /// <param name="token">JWT token</param>
        /// <returns>Dictionary of claims</returns>
        Dictionary<string, string> GetClaimsFromToken(string token);
    }

    /// <summary>
    /// JWT configuration options
    /// خيارات تكوين JWT
    /// </summary>
    public class JwtOptions
    {
        /// <summary>
        /// Secret key for signing tokens
        /// المفتاح السري لتوقيع الرموز
        /// </summary>
        public string SecretKey { get; set; } = string.Empty;

        /// <summary>
        /// Token issuer
        /// مُصدر الرمز
        /// </summary>
        public string Issuer { get; set; } = string.Empty;

        /// <summary>
        /// Token audience
        /// جمهور الرمز
        /// </summary>
        public string Audience { get; set; } = string.Empty;

        /// <summary>
        /// Access token expiration time in minutes
        /// وقت انتهاء صلاحية رمز الوصول بالدقائق
        /// </summary>
        public int AccessTokenExpirationMinutes { get; set; } = 60;

        /// <summary>
        /// Refresh token expiration time in days
        /// وقت انتهاء صلاحية رمز التحديث بالأيام
        /// </summary>
        public int RefreshTokenExpirationDays { get; set; } = 30;

        /// <summary>
        /// Whether to validate token lifetime
        /// ما إذا كان سيتم التحقق من صلاحية الرمز
        /// </summary>
        public bool ValidateLifetime { get; set; } = true;

        /// <summary>
        /// Whether to validate token issuer
        /// ما إذا كان سيتم التحقق من مُصدر الرمز
        /// </summary>
        public bool ValidateIssuer { get; set; } = true;

        /// <summary>
        /// Whether to validate token audience
        /// ما إذا كان سيتم التحقق من جمهور الرمز
        /// </summary>
        public bool ValidateAudience { get; set; } = true;

        /// <summary>
        /// Whether to validate issuer signing key
        /// ما إذا كان سيتم التحقق من مفتاح توقيع المُصدر
        /// </summary>
        public bool ValidateIssuerSigningKey { get; set; } = true;

        /// <summary>
        /// Clock skew tolerance in minutes
        /// تسامح انحراف الساعة بالدقائق
        /// </summary>
        public int ClockSkewMinutes { get; set; } = 5;
    }
}
