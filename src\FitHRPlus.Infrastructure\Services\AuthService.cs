using FitHRPlus.Application.Common;
using FitHRPlus.Application.DTOs.Auth;
using FitHRPlus.Application.Interfaces;
using FitHRPlus.Domain.Entities;
using FitHRPlus.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FitHRPlus.Infrastructure.Services
{
    /// <summary>
    /// Authentication service implementation
    /// تنفيذ خدمة المصادقة
    /// </summary>
    public class AuthService : IAuthService
    {
        private readonly FitHRContext _context;
        private readonly IJwtService _jwtService;
        private readonly IPasswordHashingService _passwordHashingService;
        private readonly ILogger<AuthService> _logger;

        public AuthService(
            FitHRContext context,
            IJwtService jwtService,
            IPasswordHashingService passwordHashingService,
            ILogger<AuthService> logger)
        {
            _context = context;
            _jwtService = jwtService;
            _passwordHashingService = passwordHashingService;
            _logger = logger;
        }

        public async Task<ServiceResult<LoginResponseDto>> LoginAsync(LoginRequestDto request)
        {
            try
            {
                // Find user by username or email
                var user = await _context.Users
                    .Include(u => u.UserRoles)
                        .ThenInclude(ur => ur.Role)
                    .Include(u => u.UserRoles)
                        .ThenInclude(ur => ur.Company)
                    .FirstOrDefaultAsync(u => 
                        (u.Username == request.UsernameOrEmail || u.Email == request.UsernameOrEmail) 
                        && u.IsActive);

                if (user == null)
                {
                    _logger.LogWarning("Login attempt with invalid username/email: {UsernameOrEmail}", request.UsernameOrEmail);
                    return ServiceResult<LoginResponseDto>.Failure(
                        "Invalid username/email or password",
                        "اسم المستخدم/البريد الإلكتروني أو كلمة المرور غير صحيحة",
                        "INVALID_CREDENTIALS");
                }

                // Check if account is locked
                if (user.LockedUntil.HasValue && user.LockedUntil.Value > DateTime.UtcNow)
                {
                    _logger.LogWarning("Login attempt on locked account: {UserId}", user.Id);
                    return ServiceResult<LoginResponseDto>.Failure(
                        "Account is temporarily locked. Please try again later.",
                        "الحساب مقفل مؤقتاً. يرجى المحاولة مرة أخرى لاحقاً.",
                        "ACCOUNT_LOCKED");
                }

                // Verify password
                if (!_passwordHashingService.VerifyPassword(request.Password, user.PasswordHash, user.Salt))
                {
                    // Increment failed login attempts
                    user.FailedLoginAttempts++;
                    
                    // Lock account after 5 failed attempts for 30 minutes
                    if (user.FailedLoginAttempts >= 5)
                    {
                        user.LockedUntil = DateTime.UtcNow.AddMinutes(30);
                        _logger.LogWarning("Account locked due to multiple failed login attempts: {UserId}", user.Id);
                    }

                    await _context.SaveChangesAsync();

                    _logger.LogWarning("Failed login attempt for user: {UserId}", user.Id);
                    return ServiceResult<LoginResponseDto>.Failure(
                        "Invalid username/email or password",
                        "اسم المستخدم/البريد الإلكتروني أو كلمة المرور غير صحيحة",
                        "INVALID_CREDENTIALS");
                }

                // Check if two-factor authentication is required
                if (user.TwoFactorEnabled && string.IsNullOrEmpty(request.TwoFactorCode))
                {
                    // Generate temporary token for two-factor authentication
                    var twoFactorToken = Guid.NewGuid().ToString();
                    // Store this token temporarily (you might want to use a cache or database)
                    
                    return ServiceResult<LoginResponseDto>.Success(new LoginResponseDto
                    {
                        RequiresTwoFactor = true,
                        TwoFactorToken = twoFactorToken,
                        User = MapToUserInfoDto(user)
                    });
                }

                // TODO: Verify two-factor code if provided
                if (user.TwoFactorEnabled && !string.IsNullOrEmpty(request.TwoFactorCode))
                {
                    // Implement two-factor verification logic here
                    // For now, we'll skip this implementation
                }

                // Reset failed login attempts on successful login
                user.FailedLoginAttempts = 0;
                user.LockedUntil = null;
                user.LastLoginAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                // Generate tokens
                var activeRoles = user.UserRoles.Where(ur => ur.IsActive).ToList();
                var roles = activeRoles.Select(ur => ur.Role.Name).ToList();
                var permissions = activeRoles.SelectMany(ur =>
                    string.IsNullOrEmpty(ur.Role.Permissions)
                        ? new List<string>()
                        : ur.Role.Permissions.Split(',', StringSplitOptions.RemoveEmptyEntries).AsEnumerable())
                    .Distinct()
                    .ToList();

                var accessToken = _jwtService.GenerateAccessToken(
                    user.Id,
                    user.Username,
                    user.Email,
                    roles,
                    permissions);

                var refreshToken = _jwtService.GenerateRefreshToken();

                // TODO: Store refresh token in database for validation

                var response = new LoginResponseDto
                {
                    AccessToken = accessToken,
                    RefreshToken = refreshToken,
                    ExpiresAt = DateTime.UtcNow.AddHours(1), // TODO: Get from JWT options
                    User = MapToUserInfoDto(user),
                    Companies = activeRoles.Select(ur => new UserCompanyRoleDto
                    {
                        CompanyId = ur.CompanyId,
                        CompanyName = ur.Company.Name,
                        CompanyNameAr = ur.Company.NameAr,
                        CompanyLogo = ur.Company.Logo,
                        RoleId = ur.RoleId,
                        RoleName = ur.Role.Name,
                        RoleNameAr = ur.Role.NameAr,
                        Permissions = string.IsNullOrEmpty(ur.Role.Permissions) 
                            ? new List<string>() 
                            : ur.Role.Permissions.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList(),
                        AssignedAt = ur.AssignedAt,
                        IsActive = ur.IsActive
                    }).ToList()
                };

                _logger.LogInformation("Successful login for user: {UserId}", user.Id);
                return ServiceResult<LoginResponseDto>.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for username/email: {UsernameOrEmail}", request.UsernameOrEmail);
                return ServiceResult<LoginResponseDto>.Failure(
                    "An error occurred during login. Please try again.",
                    "حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.",
                    "LOGIN_ERROR");
            }
        }

        public async Task<ServiceResult<LoginResponseDto>> RegisterAsync(RegisterRequestDto request)
        {
            try
            {
                // Check if username already exists
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == request.Username || u.Email == request.Email);

                if (existingUser != null)
                {
                    var errors = new Dictionary<string, List<string>>();
                    
                    if (existingUser.Username == request.Username)
                    {
                        errors.Add("Username", new List<string> { "Username is already taken" });
                    }
                    
                    if (existingUser.Email == request.Email)
                    {
                        errors.Add("Email", new List<string> { "Email is already registered" });
                    }

                    return ServiceResult<LoginResponseDto>.ValidationError(errors);
                }

                // Validate password strength
                var passwordValidation = _passwordHashingService.ValidatePassword(request.Password);
                if (!passwordValidation.IsValid)
                {
                    var errors = new Dictionary<string, List<string>>
                    {
                        { "Password", passwordValidation.Errors }
                    };
                    return ServiceResult<LoginResponseDto>.ValidationError(errors);
                }

                // Hash password
                var (hashedPassword, salt) = _passwordHashingService.HashPassword(request.Password);

                // Create new user
                var user = new User
                {
                    Id = Guid.NewGuid(),
                    Username = request.Username,
                    Email = request.Email,
                    PasswordHash = hashedPassword,
                    Salt = salt,
                    FirstName = request.FirstName,
                    LastName = request.LastName,
                    Phone = request.Phone,
                    PreferredLanguage = request.PreferredLanguage,
                    TimeZone = request.TimeZone,
                    IsEmailVerified = false,
                    IsPhoneVerified = false,
                    TwoFactorEnabled = false,
                    LastPasswordChangeAt = DateTime.UtcNow,
                    FailedLoginAttempts = 0,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    IsActive = true
                };

                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                _logger.LogInformation("New user registered: {UserId}", user.Id);

                // Auto-login after registration
                var loginRequest = new LoginRequestDto
                {
                    UsernameOrEmail = request.Username,
                    Password = request.Password
                };

                return await LoginAsync(loginRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during user registration for username: {Username}", request.Username);
                return ServiceResult<LoginResponseDto>.Failure(
                    "An error occurred during registration. Please try again.",
                    "حدث خطأ أثناء التسجيل. يرجى المحاولة مرة أخرى.",
                    "REGISTRATION_ERROR");
            }
        }

        // TODO: Implement other methods (RefreshTokenAsync, LogoutAsync, etc.)
        public Task<ServiceResult<LoginResponseDto>> RefreshTokenAsync(string refreshToken)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<bool>> LogoutAsync(Guid userId, string refreshToken)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<bool>> ChangePasswordAsync(Guid userId, string currentPassword, string newPassword)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<bool>> ForgotPasswordAsync(string email)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<bool>> ResetPasswordAsync(string email, string resetToken, string newPassword)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<bool>> VerifyEmailAsync(Guid userId, string verificationToken)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<bool>> SendEmailVerificationAsync(Guid userId)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<TwoFactorSetupDto>> EnableTwoFactorAsync(Guid userId)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<bool>> ConfirmTwoFactorAsync(Guid userId, string verificationCode)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<bool>> DisableTwoFactorAsync(Guid userId, string password)
        {
            throw new NotImplementedException();
        }

        private static UserInfoDto MapToUserInfoDto(User user)
        {
            return new UserInfoDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Phone = user.Phone,
                ProfilePicture = user.ProfilePicture,
                PreferredLanguage = user.PreferredLanguage,
                TimeZone = user.TimeZone,
                IsEmailVerified = user.IsEmailVerified,
                IsPhoneVerified = user.IsPhoneVerified,
                TwoFactorEnabled = user.TwoFactorEnabled,
                LastLoginAt = user.LastLoginAt
            };
        }
    }
}
