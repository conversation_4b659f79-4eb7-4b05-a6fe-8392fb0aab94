<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التبويبات - FitHR Plus</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
        }
        
        .nav-tabs .nav-link {
            border: none;
            border-bottom: 3px solid transparent;
            color: #6c757d;
            font-weight: 500;
            padding: 12px 20px;
        }

        .nav-tabs .nav-link.active {
            background: none;
            border-bottom-color: #0d6efd;
            color: #0d6efd;
        }

        .nav-tabs .nav-link:hover {
            border-bottom-color: #dee2e6;
            color: #495057;
        }

        .badge {
            font-size: 10px;
            padding: 4px 6px;
        }

        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .employee-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .employee-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">
            <i class="bi bi-currency-dollar"></i>
            اختبار تبويبات معالجة كشوف المرتبات
        </h1>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card">
                    <h3 id="activeCount">5</h3>
                    <p>كشوف نشطة</p>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card">
                    <h3 id="pendingCount">3</h3>
                    <p>قيد الانتظار</p>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card">
                    <h3 id="processedCount">8</h3>
                    <p>معالجة</p>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card">
                    <h3 id="paidCount">12</h3>
                    <p>مدفوعة</p>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="payrollTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="active-tab" data-bs-toggle="tab" data-bs-target="#active" type="button" role="tab">
                            <i class="bi bi-file-earmark-text"></i>
                            كشوف نشطة
                            <span class="badge bg-primary ms-2">5</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" type="button" role="tab">
                            <i class="bi bi-clock"></i>
                            قيد الانتظار
                            <span class="badge bg-warning ms-2">3</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="processed-tab" data-bs-toggle="tab" data-bs-target="#processed" type="button" role="tab">
                            <i class="bi bi-gear"></i>
                            معالجة
                            <span class="badge bg-info ms-2">8</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="paid-tab" data-bs-toggle="tab" data-bs-target="#paid" type="button" role="tab">
                            <i class="bi bi-check-circle"></i>
                            مدفوعة
                            <span class="badge bg-success ms-2">12</span>
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="payrollTabContent">
                    <!-- Active Tab -->
                    <div class="tab-pane fade show active" id="active" role="tabpanel">
                        <h6>كشوف المرتبات النشطة</h6>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الموظف</th>
                                        <th>القسم</th>
                                        <th>الراتب الأساسي</th>
                                        <th>صافي الراتب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div class="employee-info">
                                                <div class="employee-avatar">أ</div>
                                                <div>
                                                    <h6 class="mb-0">أحمد محمد</h6>
                                                    <small class="text-muted">EMP1001</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>تقنية المعلومات</td>
                                        <td>8,000 ج.م</td>
                                        <td><strong>7,500 ج.م</strong></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-warning">
                                                <i class="bi bi-gear"></i> معالجة
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="employee-info">
                                                <div class="employee-avatar">ف</div>
                                                <div>
                                                    <h6 class="mb-0">فاطمة علي</h6>
                                                    <small class="text-muted">EMP1002</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>الموارد البشرية</td>
                                        <td>6,500 ج.م</td>
                                        <td><strong>6,200 ج.م</strong></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-warning">
                                                <i class="bi bi-gear"></i> معالجة
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Pending Tab -->
                    <div class="tab-pane fade" id="pending" role="tabpanel">
                        <h6>كشوف المرتبات قيد الانتظار</h6>
                        <div class="alert alert-warning">
                            <i class="bi bi-clock"></i>
                            هذه الكشوف تحتاج إلى مراجعة قبل المعالجة
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الموظف</th>
                                        <th>القسم</th>
                                        <th>الراتب الأساسي</th>
                                        <th>صافي الراتب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div class="employee-info">
                                                <div class="employee-avatar">م</div>
                                                <div>
                                                    <h6 class="mb-0">محمد حسن</h6>
                                                    <small class="text-muted">EMP1003</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>المحاسبة</td>
                                        <td>7,000 ج.م</td>
                                        <td><strong>6,800 ج.م</strong></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-success">
                                                <i class="bi bi-check-circle"></i> موافقة
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Processed Tab -->
                    <div class="tab-pane fade" id="processed" role="tabpanel">
                        <h6>كشوف المرتبات المعالجة</h6>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            هذه الكشوف جاهزة للدفع
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الموظف</th>
                                        <th>القسم</th>
                                        <th>الراتب الأساسي</th>
                                        <th>صافي الراتب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div class="employee-info">
                                                <div class="employee-avatar">س</div>
                                                <div>
                                                    <h6 class="mb-0">سارة أحمد</h6>
                                                    <small class="text-muted">EMP1004</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>التسويق</td>
                                        <td>6,500 ج.م</td>
                                        <td><strong>6,300 ج.م</strong></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-success">
                                                <i class="bi bi-currency-dollar"></i> دفع
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Paid Tab -->
                    <div class="tab-pane fade" id="paid" role="tabpanel">
                        <h6>كشوف المرتبات المدفوعة</h6>
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle"></i>
                            هذه الكشوف تم دفعها بنجاح
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الموظف</th>
                                        <th>القسم</th>
                                        <th>الراتب الأساسي</th>
                                        <th>صافي الراتب</th>
                                        <th>تاريخ الدفع</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div class="employee-info">
                                                <div class="employee-avatar">ع</div>
                                                <div>
                                                    <h6 class="mb-0">علي محمود</h6>
                                                    <small class="text-muted">EMP1005</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>المبيعات</td>
                                        <td>7,500 ج.م</td>
                                        <td><strong>7,200 ج.م</strong></td>
                                        <td>2024-01-30</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-info">
                                                <i class="bi bi-download"></i> قسيمة الراتب
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="card mt-4">
            <div class="card-header">
                <h5>نتائج الاختبار</h5>
            </div>
            <div class="card-body">
                <div id="testResults">
                    <p class="text-muted">انقر على التبويبات لاختبار عملها...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script>
        $(document).ready(function() {
            console.log('✅ الصفحة تم تحميلها بنجاح');
            updateTestResults('✅ الصفحة تم تحميلها بنجاح');

            // Test tab functionality
            $('button[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
                const targetTab = $(e.target).attr('data-bs-target');
                const tabName = targetTab.replace('#', '');
                
                console.log('✅ تم تفعيل التبويب:', tabName);
                updateTestResults(`✅ تم تفعيل التبويب: ${tabName}`);
                
                // Test tab content visibility
                if ($(targetTab).hasClass('show') && $(targetTab).hasClass('active')) {
                    console.log('✅ محتوى التبويب ظاهر بشكل صحيح');
                    updateTestResults(`✅ محتوى التبويب "${tabName}" ظاهر بشكل صحيح`);
                } else {
                    console.log('❌ مشكلة في عرض محتوى التبويب');
                    updateTestResults(`❌ مشكلة في عرض محتوى التبويب "${tabName}"`);
                }
            });

            // Test tab click events
            $('button[data-bs-toggle="tab"]').on('click', function (e) {
                const tabName = $(this).text().trim();
                console.log('🖱️ تم النقر على التبويب:', tabName);
                updateTestResults(`🖱️ تم النقر على التبويب: ${tabName}`);
            });

            // Test Bootstrap tabs initialization
            if (typeof bootstrap !== 'undefined' && bootstrap.Tab) {
                console.log('✅ Bootstrap Tabs متاح ويعمل');
                updateTestResults('✅ Bootstrap Tabs متاح ويعمل');
            } else {
                console.log('❌ Bootstrap Tabs غير متاح');
                updateTestResults('❌ Bootstrap Tabs غير متاح');
            }

            // Test initial active tab
            const activeTab = $('.nav-tabs .nav-link.active');
            if (activeTab.length > 0) {
                console.log('✅ التبويب النشط الأولي موجود:', activeTab.text().trim());
                updateTestResults(`✅ التبويب النشط الأولي: ${activeTab.text().trim()}`);
            } else {
                console.log('❌ لا يوجد تبويب نشط أولي');
                updateTestResults('❌ لا يوجد تبويب نشط أولي');
            }

            // Auto-test all tabs after 2 seconds
            setTimeout(function() {
                console.log('🔄 بدء الاختبار التلقائي للتبويبات...');
                updateTestResults('🔄 بدء الاختبار التلقائي للتبويبات...');
                
                const tabs = ['pending-tab', 'processed-tab', 'paid-tab', 'active-tab'];
                let currentIndex = 0;
                
                function testNextTab() {
                    if (currentIndex < tabs.length) {
                        const tabId = tabs[currentIndex];
                        console.log(`🔄 اختبار التبويب: ${tabId}`);
                        
                        $(`#${tabId}`).click();
                        
                        setTimeout(testNextTab, 1000);
                        currentIndex++;
                    } else {
                        console.log('✅ تم الانتهاء من اختبار جميع التبويبات');
                        updateTestResults('✅ تم الانتهاء من اختبار جميع التبويبات بنجاح!');
                    }
                }
                
                testNextTab();
            }, 2000);
        });

        function updateTestResults(message) {
            const resultsDiv = $('#testResults');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            resultsDiv.append(`<div class="mb-1"><small class="text-muted">[${timestamp}]</small> ${message}</div>`);
            resultsDiv.scrollTop(resultsDiv[0].scrollHeight);
        }
    </script>
</body>
</html>
