@model FitHRPlus.Web.Models.Auth.LoginViewModel
@{
    ViewData["Title"] = "تسجيل الدخول - Login";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var currentLanguage = Context.Request.Cookies["PreferredLanguage"] ?? "ar";
    var isRtl = currentLanguage == "ar";
}

<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header text-center bg-primary text-white">
                <h4>
                    <i class="fas fa-sign-in-alt"></i>
                    @(currentLanguage == "ar" ? "تسجيل الدخول" : "Login")
                </h4>
            </div>
            <div class="card-body">
                @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                {
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle"></i>
                        @Model.ErrorMessage
                    </div>
                }

                @if (!string.IsNullOrEmpty(Model.SuccessMessage))
                {
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle"></i>
                        @Model.SuccessMessage
                    </div>
                }

                <form asp-action="Login" method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    
                    @if (!Model.RequiresTwoFactor)
                    {
                        <!-- Username/Email Field -->
                        <div class="mb-3">
                            <label asp-for="UsernameOrEmail" class="form-label">
                                <i class="fas fa-user"></i>
                                @(currentLanguage == "ar" ? "اسم المستخدم أو البريد الإلكتروني" : "Username or Email")
                            </label>
                            <input asp-for="UsernameOrEmail" class="form-control" placeholder="@(currentLanguage == "ar" ? "أدخل اسم المستخدم أو البريد الإلكتروني" : "Enter username or email")" />
                            <span asp-validation-for="UsernameOrEmail" class="text-danger"></span>
                        </div>

                        <!-- Password Field -->
                        <div class="mb-3">
                            <label asp-for="Password" class="form-label">
                                <i class="fas fa-lock"></i>
                                @(currentLanguage == "ar" ? "كلمة المرور" : "Password")
                            </label>
                            <input asp-for="Password" class="form-control" placeholder="@(currentLanguage == "ar" ? "أدخل كلمة المرور" : "Enter password")" />
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>

                        <!-- Remember Me -->
                        <div class="mb-3 form-check">
                            <input asp-for="RememberMe" class="form-check-input" />
                            <label asp-for="RememberMe" class="form-check-label">
                                @(currentLanguage == "ar" ? "تذكرني" : "Remember me")
                            </label>
                        </div>
                    }
                    else
                    {
                        <!-- Two-Factor Authentication -->
                        <div class="alert alert-info">
                            <i class="fas fa-shield-alt"></i>
                            @(currentLanguage == "ar" ? "يرجى إدخال رمز المصادقة الثنائية" : "Please enter your two-factor authentication code")
                        </div>

                        <input asp-for="TwoFactorToken" type="hidden" />
                        <input asp-for="UsernameOrEmail" type="hidden" />

                        <div class="mb-3">
                            <label asp-for="TwoFactorCode" class="form-label">
                                <i class="fas fa-key"></i>
                                @(currentLanguage == "ar" ? "رمز المصادقة الثنائية" : "Two-Factor Code")
                            </label>
                            <input asp-for="TwoFactorCode" class="form-control text-center" placeholder="000000" maxlength="6" />
                            <span asp-validation-for="TwoFactorCode" class="text-danger"></span>
                        </div>
                    }

                    <input asp-for="ReturnUrl" type="hidden" />

                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt"></i>
                            @(currentLanguage == "ar" ? "تسجيل الدخول" : "Login")
                        </button>
                    </div>
                </form>

                <!-- Additional Links -->
                <div class="text-center mt-3">
                    <p class="mb-2">
                        <a asp-action="Register" class="text-decoration-none">
                            <i class="fas fa-user-plus"></i>
                            @(currentLanguage == "ar" ? "إنشاء حساب جديد" : "Create new account")
                        </a>
                    </p>
                    <p class="mb-0">
                        <a href="#" class="text-decoration-none">
                            <i class="fas fa-question-circle"></i>
                            @(currentLanguage == "ar" ? "نسيت كلمة المرور؟" : "Forgot password?")
                        </a>
                    </p>
                </div>

                <!-- Language Switcher -->
                <div class="text-center mt-4 pt-3 border-top">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-secondary btn-sm @(currentLanguage == "ar" ? "active" : "")" onclick="changeLanguage('ar')">
                            العربية
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm @(currentLanguage == "en" ? "active" : "")" onclick="changeLanguage('en')">
                            English
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        function changeLanguage(lang) {
            document.cookie = "PreferredLanguage=" + lang + "; path=/; expires=" + new Date(Date.now() + 365*24*60*60*1000).toUTCString();
            location.reload();
        }

        // Auto-focus on first input
        $(document).ready(function() {
            $('input:visible:first').focus();
        });
    </script>
}
