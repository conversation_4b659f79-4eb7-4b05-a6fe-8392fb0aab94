using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Web.Models.Admin
{
    /// <summary>
    /// Department view model for web forms
    /// نموذج عرض القسم لنماذج الويب
    /// </summary>
    public class DepartmentViewModel
    {
        /// <summary>
        /// Department ID
        /// معرف القسم
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Department name
        /// اسم القسم
        /// </summary>
        [Required(ErrorMessage = "Department name is required")]
        [StringLength(200, ErrorMessage = "Department name cannot exceed 200 characters")]
        [Display(Name = "Department Name", Prompt = "Enter department name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Department name in Arabic
        /// اسم القسم بالعربية
        /// </summary>
        [StringLength(200, ErrorMessage = "Arabic department name cannot exceed 200 characters")]
        [Display(Name = "Department Name (Arabic)", Prompt = "Enter department name in Arabic")]
        public string? NameAr { get; set; }

        /// <summary>
        /// Department description
        /// وصف القسم
        /// </summary>
        [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
        [Display(Name = "Description", Prompt = "Enter department description")]
        [DataType(DataType.MultilineText)]
        public string? Description { get; set; }

        /// <summary>
        /// Department description in Arabic
        /// وصف القسم بالعربية
        /// </summary>
        [StringLength(1000, ErrorMessage = "Arabic description cannot exceed 1000 characters")]
        [Display(Name = "Description (Arabic)", Prompt = "Enter description in Arabic")]
        [DataType(DataType.MultilineText)]
        public string? DescriptionAr { get; set; }

        /// <summary>
        /// Department code (unique within company)
        /// رمز القسم (فريد داخل الشركة)
        /// </summary>
        [StringLength(20, ErrorMessage = "Department code cannot exceed 20 characters")]
        [Display(Name = "Department Code", Prompt = "Enter department code")]
        public string? Code { get; set; }

        /// <summary>
        /// Company ID
        /// معرف الشركة
        /// </summary>
        [Required(ErrorMessage = "Company is required")]
        public Guid CompanyId { get; set; }

        /// <summary>
        /// Company name
        /// اسم الشركة
        /// </summary>
        public string CompanyName { get; set; } = string.Empty;

        /// <summary>
        /// Parent department ID (for hierarchical structure)
        /// معرف القسم الأب (للهيكل الهرمي)
        /// </summary>
        [Display(Name = "Parent Department")]
        public Guid? ParentDepartmentId { get; set; }

        /// <summary>
        /// Parent department name
        /// اسم القسم الأب
        /// </summary>
        public string? ParentDepartmentName { get; set; }

        /// <summary>
        /// Department manager ID
        /// معرف مدير القسم
        /// </summary>
        [Display(Name = "Department Manager")]
        public Guid? ManagerId { get; set; }

        /// <summary>
        /// Department manager name
        /// اسم مدير القسم
        /// </summary>
        public string? ManagerName { get; set; }

        /// <summary>
        /// Budget allocated to the department
        /// الميزانية المخصصة للقسم
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "Budget must be a positive number")]
        [Display(Name = "Budget")]
        [DataType(DataType.Currency)]
        public decimal? Budget { get; set; }

        /// <summary>
        /// Location or floor
        /// الموقع أو الطابق
        /// </summary>
        [StringLength(200, ErrorMessage = "Location cannot exceed 200 characters")]
        [Display(Name = "Location", Prompt = "Enter department location")]
        public string? Location { get; set; }

        /// <summary>
        /// Department phone extension
        /// رقم هاتف القسم الداخلي
        /// </summary>
        [StringLength(20, ErrorMessage = "Phone extension cannot exceed 20 characters")]
        [Display(Name = "Phone Extension", Prompt = "Enter phone extension")]
        public string? PhoneExtension { get; set; }

        /// <summary>
        /// Department email
        /// البريد الإلكتروني للقسم
        /// </summary>
        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
        [Display(Name = "Department Email", Prompt = "Enter department email")]
        public string? Email { get; set; }

        /// <summary>
        /// Whether the department is active
        /// ما إذا كان القسم نشط
        /// </summary>
        [Display(Name = "Active")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Creation date
        /// تاريخ الإنشاء
        /// </summary>
        [Display(Name = "Created Date")]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Last update date
        /// تاريخ آخر تحديث
        /// </summary>
        [Display(Name = "Last Updated")]
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Number of employees in the department
        /// عدد الموظفين في القسم
        /// </summary>
        [Display(Name = "Employee Count")]
        public int EmployeeCount { get; set; }

        /// <summary>
        /// Number of positions in the department
        /// عدد المناصب في القسم
        /// </summary>
        [Display(Name = "Position Count")]
        public int PositionCount { get; set; }

        /// <summary>
        /// Sub-departments
        /// الأقسام الفرعية
        /// </summary>
        public List<DepartmentViewModel> SubDepartments { get; set; } = new();

        /// <summary>
        /// Positions in the department
        /// المناصب في القسم
        /// </summary>
        public List<PositionViewModel> Positions { get; set; } = new();

        /// <summary>
        /// Employees in the department
        /// الموظفين في القسم
        /// </summary>
        public List<EmployeeViewModel> Employees { get; set; } = new();

        // Navigation properties for dropdowns
        /// <summary>
        /// Available parent departments for selection
        /// الأقسام الأب المتاحة للاختيار
        /// </summary>
        public List<DepartmentViewModel> AvailableParentDepartments { get; set; } = new();

        /// <summary>
        /// Available managers for selection
        /// المديرين المتاحين للاختيار
        /// </summary>
        public List<EmployeeViewModel> AvailableManagers { get; set; } = new();
    }

    /// <summary>
    /// Position view model for web forms
    /// نموذج عرض المنصب لنماذج الويب
    /// </summary>
    public class PositionViewModel
    {
        /// <summary>
        /// Position ID
        /// معرف المنصب
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Position title
        /// مسمى المنصب
        /// </summary>
        [Required(ErrorMessage = "Position title is required")]
        [StringLength(200, ErrorMessage = "Position title cannot exceed 200 characters")]
        [Display(Name = "Position Title", Prompt = "Enter position title")]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Position title in Arabic
        /// مسمى المنصب بالعربية
        /// </summary>
        [StringLength(200, ErrorMessage = "Arabic position title cannot exceed 200 characters")]
        [Display(Name = "Position Title (Arabic)", Prompt = "Enter position title in Arabic")]
        public string? TitleAr { get; set; }

        /// <summary>
        /// Position description
        /// وصف المنصب
        /// </summary>
        [StringLength(2000, ErrorMessage = "Description cannot exceed 2000 characters")]
        [Display(Name = "Description", Prompt = "Enter position description")]
        [DataType(DataType.MultilineText)]
        public string? Description { get; set; }

        /// <summary>
        /// Position description in Arabic
        /// وصف المنصب بالعربية
        /// </summary>
        [StringLength(2000, ErrorMessage = "Arabic description cannot exceed 2000 characters")]
        [Display(Name = "Description (Arabic)", Prompt = "Enter description in Arabic")]
        [DataType(DataType.MultilineText)]
        public string? DescriptionAr { get; set; }

        /// <summary>
        /// Position code (unique within company)
        /// رمز المنصب (فريد داخل الشركة)
        /// </summary>
        [StringLength(20, ErrorMessage = "Position code cannot exceed 20 characters")]
        [Display(Name = "Position Code", Prompt = "Enter position code")]
        public string? Code { get; set; }

        /// <summary>
        /// Company ID
        /// معرف الشركة
        /// </summary>
        [Required(ErrorMessage = "Company is required")]
        public Guid CompanyId { get; set; }

        /// <summary>
        /// Company name
        /// اسم الشركة
        /// </summary>
        public string CompanyName { get; set; } = string.Empty;

        /// <summary>
        /// Department ID
        /// معرف القسم
        /// </summary>
        [Required(ErrorMessage = "Department is required")]
        [Display(Name = "Department")]
        public Guid DepartmentId { get; set; }

        /// <summary>
        /// Department name
        /// اسم القسم
        /// </summary>
        public string DepartmentName { get; set; } = string.Empty;

        /// <summary>
        /// Position level (Junior, Senior, Manager, etc.)
        /// مستوى المنصب (مبتدئ، خبير، مدير، إلخ)
        /// </summary>
        [StringLength(50, ErrorMessage = "Position level cannot exceed 50 characters")]
        [Display(Name = "Position Level")]
        public string? Level { get; set; }

        /// <summary>
        /// Minimum salary for the position
        /// الحد الأدنى للراتب للمنصب
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "Minimum salary must be a positive number")]
        [Display(Name = "Minimum Salary")]
        [DataType(DataType.Currency)]
        public decimal? MinSalary { get; set; }

        /// <summary>
        /// Maximum salary for the position
        /// الحد الأقصى للراتب للمنصب
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "Maximum salary must be a positive number")]
        [Display(Name = "Maximum Salary")]
        [DataType(DataType.Currency)]
        public decimal? MaxSalary { get; set; }

        /// <summary>
        /// Required qualifications
        /// المؤهلات المطلوبة
        /// </summary>
        [StringLength(1000, ErrorMessage = "Qualifications cannot exceed 1000 characters")]
        [Display(Name = "Required Qualifications", Prompt = "Enter required qualifications")]
        [DataType(DataType.MultilineText)]
        public string? RequiredQualifications { get; set; }

        /// <summary>
        /// Required skills
        /// المهارات المطلوبة
        /// </summary>
        [StringLength(1000, ErrorMessage = "Required skills cannot exceed 1000 characters")]
        [Display(Name = "Required Skills", Prompt = "Enter required skills")]
        [DataType(DataType.MultilineText)]
        public string? RequiredSkills { get; set; }

        /// <summary>
        /// Years of experience required
        /// سنوات الخبرة المطلوبة
        /// </summary>
        [Range(0, 50, ErrorMessage = "Required experience must be between 0 and 50 years")]
        [Display(Name = "Required Experience (Years)")]
        public int? RequiredExperience { get; set; }

        /// <summary>
        /// Whether the position is active
        /// ما إذا كان المنصب نشط
        /// </summary>
        [Display(Name = "Active")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Creation date
        /// تاريخ الإنشاء
        /// </summary>
        [Display(Name = "Created Date")]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Last update date
        /// تاريخ آخر تحديث
        /// </summary>
        [Display(Name = "Last Updated")]
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Number of employees in this position
        /// عدد الموظفين في هذا المنصب
        /// </summary>
        [Display(Name = "Employee Count")]
        public int EmployeeCount { get; set; }

        /// <summary>
        /// Employees in this position
        /// الموظفين في هذا المنصب
        /// </summary>
        public List<EmployeeViewModel> Employees { get; set; } = new();

        // Navigation properties for dropdowns
        /// <summary>
        /// Available departments for selection
        /// الأقسام المتاحة للاختيار
        /// </summary>
        public List<DepartmentViewModel> AvailableDepartments { get; set; } = new();

        /// <summary>
        /// Available position levels
        /// مستويات المناصب المتاحة
        /// </summary>
        public List<string> AvailableLevels { get; set; } = new()
        {
            "Entry Level",
            "Junior",
            "Mid-Level",
            "Senior",
            "Lead",
            "Manager",
            "Senior Manager",
            "Director",
            "Senior Director",
            "VP",
            "SVP",
            "Executive"
        };
    }
}
