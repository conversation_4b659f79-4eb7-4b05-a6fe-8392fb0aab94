# FIT HR Plus - Default Login Credentials
# بيانات تسجيل الدخول الافتراضية - FIT HR Plus

## 🔐 Default Admin Account / حساب المدير الافتراضي

عند تشغيل النظام لأول مرة، سيتم إنشاء حساب مدير افتراضي تلقائياً:

**Username / اسم المستخدم:** `admin`
**Password / كلمة المرور:** `Admin123!@#`
**Email / البريد الإلكتروني:** `<EMAIL>`

## 🚀 How to Login / كيفية تسجيل الدخول

1. قم بتشغيل التطبيق:
   ```bash
   dotnet run --project src/FitHRPlus.Web
   ```

2. افتح المتصفح وانتقل إلى: `https://localhost:5001` أو `http://localhost:5000`

3. انقر على "تسجيل الدخول" أو "Login"

4. أدخل بيانات الاعتماد الافتراضية:
   - **اسم المستخدم:** admin
   - **كلمة المرور:** Admin123!@#

## 🔒 Security Notes / ملاحظات الأمان

⚠️ **مهم جداً / VERY IMPORTANT:**

1. **غيّر كلمة المرور فوراً** بعد أول تسجيل دخول
2. **لا تستخدم هذه البيانات في بيئة الإنتاج**
3. **أنشئ مستخدمين جدد وأحذف المستخدم الافتراضي** في بيئة الإنتاج

## 👥 Default Roles / الأدوار الافتراضية

سيتم إنشاء الأدوار التالية تلقائياً:

1. **SuperAdmin / مدير النظام الفائق**
   - صلاحية كاملة على النظام
   - إدارة جميع الشركات والمستخدمين

2. **Admin / مدير**
   - إدارة الشركة
   - إدارة المستخدمين والموظفين

3. **HR / موارد بشرية**
   - إدارة الموظفين
   - إدارة الحضور والانصراف
   - إدارة الإجازات

4. **Employee / موظف**
   - عرض بيانات الحضور الشخصية
   - طلب الإجازات

## 🏢 Default Company / الشركة الافتراضية

سيتم إنشاء شركة تجريبية افتراضية:

- **اسم الشركة:** FIT HR Plus Demo Company
- **الاسم بالعربية:** شركة FIT HR Plus التجريبية
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +20123456789

## 🔄 Reset to Defaults / إعادة تعيين للافتراضي

لإعادة تعيين النظام للحالة الافتراضية:

1. احذف قاعدة البيانات:
   ```bash
   dotnet ef database drop --project src/FitHRPlus.Persistence --startup-project src/FitHRPlus.Web
   ```

2. أعد إنشاء قاعدة البيانات:
   ```bash
   dotnet ef database update --project src/FitHRPlus.Persistence --startup-project src/FitHRPlus.Web
   ```

3. شغّل التطبيق مرة أخرى لإعادة إنشاء البيانات الافتراضية

## 📞 Support / الدعم

إذا واجهت أي مشاكل في تسجيل الدخول:

1. تأكد من أن قاعدة البيانات تعمل بشكل صحيح
2. تحقق من logs التطبيق للأخطاء
3. تأكد من أن البيانات الافتراضية تم إنشاؤها بنجاح

## 🔧 Troubleshooting / استكشاف الأخطاء

### مشكلة: لا يمكن تسجيل الدخول
**الحل:**
1. تأكد من أن قاعدة البيانات متصلة
2. تحقق من أن DataSeeder تم تشغيله بنجاح
3. راجع logs التطبيق

### مشكلة: "Invalid credentials"
**الحل:**
1. تأكد من كتابة اسم المستخدم وكلمة المرور بشكل صحيح
2. تأكد من أن Caps Lock غير مفعل
3. جرب نسخ ولصق البيانات من هذا الملف

---

**تاريخ الإنشاء:** 2025-01-24
**الإصدار:** 1.0.0
