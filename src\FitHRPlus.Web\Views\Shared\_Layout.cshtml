@using Microsoft.AspNetCore.Localization
@{
    var requestCulture = Context.Features.Get<IRequestCultureFeature>();
    var currentCulture = requestCulture?.RequestCulture?.Culture?.Name ?? "ar";
    var isRtl = currentCulture.StartsWith("ar");
    var direction = isRtl ? "rtl" : "ltr";
    var language = isRtl ? "ar" : "en";
}
<!DOCTYPE html>
<html lang="@language" dir="@direction">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - FitHR Plus</title>

    @if (isRtl)
    {
        <!-- Bootstrap RTL CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <!-- <PERSON><PERSON><PERSON> Font for Arabic -->
        <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
        <!-- RTL Custom CSS -->
        <link rel="stylesheet" href="~/css/rtl.css" asp-append-version="true" />
    }
    else
    {
        <!-- Bootstrap LTR CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- Roboto Font for English -->
        <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    }
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <!-- HRMS Theme CSS -->
    <link rel="stylesheet" href="~/css/hrms-theme.css" asp-append-version="true" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />


</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>FitHR Plus</h3>
        </div>
        <ul class="sidebar-menu">
            <li>
                <a href="@Url.Action("Index", "Dashboard")" class="@(ViewContext.RouteData.Values["Controller"]?.ToString() == "Dashboard" ? "active" : "")">
                    <i class="bi bi-speedometer2"></i>
                    <span>لوحة التحكم</span>
                </a>
            </li>
            <li>
                <a href="#" onclick="toggleMenu(this)">
                    <i class="bi bi-people"></i>
                    <span>إدارة الموظفين</span>
                </a>
                <ul class="submenu @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Employees" || ViewContext.RouteData.Values["Controller"]?.ToString() == "Department" || ViewContext.RouteData.Values["Controller"]?.ToString() == "Position" ? "show" : "")">
                    <li><a href="@Url.Action("Index", "Employees")"><i class="bi bi-person-badge"></i> بيانات الموظفين</a></li>
                    <li><a href="@Url.Action("Index", "Department")"><i class="bi bi-diagram-3"></i> الأقسام</a></li>
                    <li><a href="@Url.Action("Index", "Position")"><i class="bi bi-gear"></i> المناصب</a></li>
                </ul>
            </li>
            <li>
                <a href="#" onclick="toggleMenu(this)">
                    <i class="bi bi-currency-dollar"></i>
                    <span>المرتبات والأجور</span>
                </a>
                <ul class="submenu @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Payroll" ? "show" : "")">
                    <li><a href="@Url.Action("Index", "Payroll")"><i class="bi bi-calculator"></i> كشوف المرتبات</a></li>
                    <li><a href="@Url.Action("Management", "Payroll")"><i class="bi bi-gear"></i> معالجة الرواتب</a></li>
                    <li><a href="@Url.Action("Generate", "Payroll")"><i class="bi bi-file-earmark-bar-graph"></i> إنشاء كشف راتب</a></li>
                </ul>
            </li>
            <li>
                <a href="#" onclick="toggleMenu(this)">
                    <i class="bi bi-clock"></i>
                    <span>الحضور والانصراف</span>
                </a>
                <ul class="submenu @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Attendance" ? "show" : "")">
                    <li><a href="@Url.Action("Index", "Attendance")"><i class="bi bi-fingerprint"></i> تسجيل الحضور</a></li>
                    <li><a href="@Url.Action("DailyReport", "Attendance")"><i class="bi bi-calendar"></i> التقرير اليومي</a></li>
                    <li><a href="@Url.Action("MonthlyReport", "Attendance")"><i class="bi bi-graph-up"></i> التقرير الشهري</a></li>
                </ul>
            </li>
            <li>
                <a href="#" onclick="toggleMenu(this)">
                    <i class="bi bi-airplane"></i>
                    <span>إدارة الإجازات</span>
                </a>
                <ul class="submenu @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Leave" ? "show" : "")">
                    <li><a href="@Url.Action("Index", "Leave")"><i class="bi bi-list"></i> طلبات الإجازات</a></li>
                    <li><a href="@Url.Action("Balance", "Leave")"><i class="bi bi-wallet2"></i> أرصدة الإجازات</a></li>
                </ul>
            </li>
            <li>
                <a href="#" onclick="toggleMenu(this)">
                    <i class="bi bi-bar-chart-line"></i>
                    <span>التقارير والتحليلات</span>
                </a>
                <ul class="submenu @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Reports" ? "show" : "")">
                    <li><a href="@Url.Action("Index", "Reports")"><i class="bi bi-file-earmark-bar-graph"></i> جميع التقارير</a></li>
                    <li><a href="@Url.Action("Analytics", "Reports")"><i class="bi bi-lightning"></i> التحليلات المتقدمة</a></li>
                </ul>
            </li>
            <li>
                <a href="@Url.Action("Index", "Notifications")" class="@(ViewContext.RouteData.Values["Controller"]?.ToString() == "Notifications" ? "active" : "")">
                    <i class="bi bi-bell"></i>
                    <span>الإشعارات</span>
                </a>
            </li>
            <li>
                <a href="#" onclick="toggleMenu(this)">
                    <i class="bi bi-gear"></i>
                    <span>إعدادات النظام</span>
                </a>
                <ul class="submenu @(ViewContext.RouteData.Values["Controller"]?.ToString() == "CompanySettings" ? "show" : "")">
                    <li><a href="@Url.Action("Index", "CompanySettings")"><i class="bi bi-sliders"></i> إعدادات الشركة</a></li>
                    <li><a href="#"><i class="bi bi-people"></i> إدارة المستخدمين</a></li>
                </ul>
            </li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Topbar -->
        <div class="topbar">
            <h4 class="dashboard-title">@ViewData["Title"]</h4>
            <div class="topbar-actions">
                <!-- Language Switcher -->
                <div class="dropdown me-3">
                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        @if (isRtl)
                        {
                            <i class="bi bi-translate"></i> <text>العربية</text>
                        }
                        else
                        {
                            <i class="bi bi-translate"></i> <text>English</text>
                        }
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <form asp-controller="Language" asp-action="SetLanguage" method="post" class="d-inline">
                                <input type="hidden" name="culture" value="ar" />
                                <input type="hidden" name="returnUrl" value="@Context.Request.Path" />
                                <button type="submit" class="dropdown-item @(isRtl ? "active" : "")">
                                    🇸🇦 العربية
                                </button>
                            </form>
                        </li>
                        <li>
                            <form asp-controller="Language" asp-action="SetLanguage" method="post" class="d-inline">
                                <input type="hidden" name="culture" value="en" />
                                <input type="hidden" name="returnUrl" value="@Context.Request.Path" />
                                <button type="submit" class="dropdown-item @(!isRtl ? "active" : "")">
                                    🇺🇸 English
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
                <span>
                    @if (isRtl)
                    {
                        <text>مرحباً @(User.Identity?.Name ?? "المستخدم")</text>
                    }
                    else
                    {
                        <text>Welcome @(User.Identity?.Name ?? "User")</text>
                    }
                </span>
            </div>
        </div>

        <!-- Page Content -->
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["WarningMessage"] != null)
            {
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    @TempData["WarningMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["InfoMessage"] != null)
            {
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    @TempData["InfoMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

        @RenderBody()
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <!-- Custom JS -->
    <script src="~/js/site.js" asp-append-version="true"></script>

    <script>
        function toggleMenu(element) {
            // إغلاق جميع القوائم الفرعية
            var allSubmenus = document.querySelectorAll('.submenu');
            allSubmenus.forEach(function(submenu) {
                if (!submenu.contains(element)) {
                    submenu.classList.remove('show');
                }
            });

            // فتح أو إغلاق القائمة الفرعية للعنصر المضغوط
            var submenu = element.nextElementSibling;
            if (submenu) {
                submenu.classList.toggle('show');
            }
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // Language switcher functionality
        function setLanguage(culture) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '@Url.Action("SetLanguage", "Language")';

            const cultureInput = document.createElement('input');
            cultureInput.type = 'hidden';
            cultureInput.name = 'culture';
            cultureInput.value = culture;

            const returnUrlInput = document.createElement('input');
            returnUrlInput.type = 'hidden';
            returnUrlInput.name = 'returnUrl';
            returnUrlInput.value = window.location.pathname;

            form.appendChild(cultureInput);
            form.appendChild(returnUrlInput);
            document.body.appendChild(form);
            form.submit();
        }

        // Apply RTL/LTR dynamically based on current culture
        function applyDirectionality() {
            const isRtl = '@isRtl'.toLowerCase() === 'true';
            const html = document.documentElement;

            if (isRtl) {
                html.setAttribute('dir', 'rtl');
                html.setAttribute('lang', 'ar');
                document.body.classList.add('rtl');
                document.body.classList.remove('ltr');
            } else {
                html.setAttribute('dir', 'ltr');
                html.setAttribute('lang', 'en');
                document.body.classList.add('ltr');
                document.body.classList.remove('rtl');
            }
        }

        // Apply directionality on page load
        document.addEventListener('DOMContentLoaded', applyDirectionality);
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
