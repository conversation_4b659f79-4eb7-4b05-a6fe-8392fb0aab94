@model FitHRPlus.Web.Models.Admin.AttendanceViewModel
@{
    ViewData["Title"] = "Attendance Details / تفاصيل الحضور";
    ViewData["PageTitle"] = "Attendance Details";
    ViewData["PageTitleAr"] = "تفاصيل الحضور";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">@ViewData["PageTitle"] / @ViewData["PageTitleAr"]</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="/">Home</a></li>
                        <li class="breadcrumb-item"><a href="@Url.Action("Index")">Attendance</a></li>
                        <li class="breadcrumb-item active">Details</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex gap-2 flex-wrap">
                <a href="@Url.Action("Index")" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    Back to List / العودة للقائمة
                </a>
                @if (!Model.IsApproved)
                {
                    <button type="button" class="btn btn-success" onclick="approveAttendance('@Model.Id')">
                        <i class="fas fa-check me-1"></i>
                        Approve / اعتماد
                    </button>
                }
                <button type="button" class="btn btn-danger" onclick="deleteAttendance('@Model.Id')">
                    <i class="fas fa-trash me-1"></i>
                    Delete / حذف
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Employee Information -->
        <div class="col-xl-4 col-lg-5">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Employee Information / معلومات الموظف</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="avatar-lg mx-auto mb-3">
                            <span class="avatar-title rounded-circle bg-soft-primary text-primary font-size-24">
                                @Model.EmployeeName.Substring(0, 1).ToUpper()
                            </span>
                        </div>
                        <h5 class="mb-1">@Model.EmployeeName</h5>
                        <p class="text-muted mb-0">@Model.EmployeeNumber</p>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-borderless mb-0">
                            <tbody>
                                <tr>
                                    <th class="ps-0" scope="row">Department:</th>
                                    <td class="text-muted">@(Model.DepartmentName ?? "N/A")</td>
                                </tr>
                                <tr>
                                    <th class="ps-0" scope="row">Position:</th>
                                    <td class="text-muted">@(Model.PositionTitle ?? "N/A")</td>
                                </tr>
                                <tr>
                                    <th class="ps-0" scope="row">Date:</th>
                                    <td class="text-muted">@Model.Date.ToString("dddd, dd MMMM yyyy")</td>
                                </tr>
                                <tr>
                                    <th class="ps-0" scope="row">Status:</th>
                                    <td>
                                        <span class="@Model.StatusBadgeClass">@Model.StatusDisplayText</span>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="ps-0" scope="row">Approval:</th>
                                    <td>
                                        @if (Model.IsApproved)
                                        {
                                            <span class="badge bg-success">Approved / معتمد</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-warning">Pending / في الانتظار</span>
                                        }
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Attendance Details -->
        <div class="col-xl-8 col-lg-7">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Attendance Details / تفاصيل الحضور</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Time Information -->
                        <div class="col-md-6">
                            <h6 class="mb-3">Time Information / معلومات الوقت</h6>
                            <div class="table-responsive">
                                <table class="table table-borderless mb-0">
                                    <tbody>
                                        <tr>
                                            <th class="ps-0" scope="row">Check In:</th>
                                            <td class="text-muted">
                                                @if (Model.CheckInTime.HasValue)
                                                {
                                                    <span class="fw-medium">@Model.CheckInTimeFormatted</span>
                                                    @if (Model.IsLate)
                                                    {
                                                        <br><small class="text-danger">Late by @Model.LateMinutes minutes</small>
                                                    }
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Not recorded</span>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <th class="ps-0" scope="row">Check Out:</th>
                                            <td class="text-muted">
                                                @if (Model.CheckOutTime.HasValue)
                                                {
                                                    <span class="fw-medium">@Model.CheckOutTimeFormatted</span>
                                                    @if (Model.IsEarlyDeparture)
                                                    {
                                                        <br><small class="text-warning">Early by @Model.EarlyDepartureMinutes minutes</small>
                                                    }
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Not recorded</span>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <th class="ps-0" scope="row">Working Hours:</th>
                                            <td class="text-muted">
                                                <span class="fw-medium">@Model.WorkingHoursFormatted</span>
                                            </td>
                                        </tr>
                                        @if (Model.HasOvertime)
                                        {
                                            <tr>
                                                <th class="ps-0" scope="row">Overtime:</th>
                                                <td class="text-info">
                                                    <span class="fw-medium">@Model.OvertimeHoursFormatted</span>
                                                </td>
                                            </tr>
                                        }
                                        <tr>
                                            <th class="ps-0" scope="row">Break Duration:</th>
                                            <td class="text-muted">
                                                <span class="fw-medium">@Model.BreakDurationFormatted</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Location Information -->
                        <div class="col-md-6">
                            <h6 class="mb-3">Location Information / معلومات الموقع</h6>
                            <div class="table-responsive">
                                <table class="table table-borderless mb-0">
                                    <tbody>
                                        <tr>
                                            <th class="ps-0" scope="row">Check In Location:</th>
                                            <td class="text-muted">
                                                @if (!string.IsNullOrEmpty(Model.CheckInLocation))
                                                {
                                                    <span class="fw-medium">@Model.CheckInLocation</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Not recorded</span>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <th class="ps-0" scope="row">Check Out Location:</th>
                                            <td class="text-muted">
                                                @if (!string.IsNullOrEmpty(Model.CheckOutLocation))
                                                {
                                                    <span class="fw-medium">@Model.CheckOutLocation</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Not recorded</span>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <th class="ps-0" scope="row">Check In Device:</th>
                                            <td class="text-muted">
                                                @if (!string.IsNullOrEmpty(Model.CheckInDevice))
                                                {
                                                    <span class="fw-medium">@Model.CheckInDevice</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Not recorded</span>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <th class="ps-0" scope="row">Check Out Device:</th>
                                            <td class="text-muted">
                                                @if (!string.IsNullOrEmpty(Model.CheckOutDevice))
                                                {
                                                    <span class="fw-medium">@Model.CheckOutDevice</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Not recorded</span>
                                                }
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Notes))
                    {
                        <hr>
                        <div class="row">
                            <div class="col-12">
                                <h6 class="mb-3">Notes / ملاحظات</h6>
                                <div class="bg-light p-3 rounded">
                                    <p class="mb-0">@Model.Notes</p>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>

            <!-- Timeline Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Timeline / الجدول الزمني</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        @if (Model.CheckInTime.HasValue)
                        {
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">Check In / الحضور</h6>
                                    <p class="timeline-text">
                                        Employee checked in at @Model.CheckInTimeFormatted
                                        @if (Model.IsLate)
                                        {
                                            <span class="text-danger">(Late by @Model.LateMinutes minutes)</span>
                                        }
                                    </p>
                                    @if (!string.IsNullOrEmpty(Model.CheckInLocation))
                                    {
                                        <small class="text-muted">Location: @Model.CheckInLocation</small>
                                    }
                                </div>
                            </div>
                        }

                        @if (Model.CheckOutTime.HasValue)
                        {
                            <div class="timeline-item">
                                <div class="timeline-marker bg-warning"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">Check Out / الانصراف</h6>
                                    <p class="timeline-text">
                                        Employee checked out at @Model.CheckOutTimeFormatted
                                        @if (Model.IsEarlyDeparture)
                                        {
                                            <span class="text-warning">(Early by @Model.EarlyDepartureMinutes minutes)</span>
                                        }
                                    </p>
                                    @if (!string.IsNullOrEmpty(Model.CheckOutLocation))
                                    {
                                        <small class="text-muted">Location: @Model.CheckOutLocation</small>
                                    }
                                </div>
                            </div>
                        }
                        else if (Model.CheckInTime.HasValue)
                        {
                            <div class="timeline-item">
                                <div class="timeline-marker bg-secondary"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">Still Working / لا يزال يعمل</h6>
                                    <p class="timeline-text">Employee has not checked out yet.</p>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function approveAttendance(id) {
            if (confirm('Are you sure you want to approve this attendance record?')) {
                fetch('@Url.Action("Approve")', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({ id: id })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while approving the record.');
                });
            }
        }

        function deleteAttendance(id) {
            if (confirm('Are you sure you want to delete this attendance record? This action cannot be undone.')) {
                fetch('@Url.Action("Delete")', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({ id: id })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = '@Url.Action("Index")';
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the record.');
                });
            }
        }
    </script>

    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 30px;
        }

        .timeline-marker {
            position: absolute;
            left: -22px;
            top: 0;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px #e9ecef;
        }

        .timeline-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 3px solid #dee2e6;
        }

        .timeline-title {
            margin-bottom: 5px;
            font-size: 14px;
            font-weight: 600;
        }

        .timeline-text {
            margin-bottom: 5px;
            font-size: 13px;
            color: #6c757d;
        }
    </style>
}
