namespace FitHRPlus.Application.DTOs.Common
{
    /// <summary>
    /// Paginated result wrapper for list operations
    /// نتيجة مقسمة للعمليات التي ترجع قوائم
    /// </summary>
    /// <typeparam name="T">Item type</typeparam>
    public class PaginatedResult<T>
    {
        /// <summary>
        /// List of items for current page
        /// قائمة العناصر للصفحة الحالية
        /// </summary>
        public List<T> Items { get; set; } = new();

        /// <summary>
        /// Current page number (1-based)
        /// رقم الصفحة الحالية (يبدأ من 1)
        /// </summary>
        public int CurrentPage { get; set; }

        /// <summary>
        /// Total number of pages
        /// إجمالي عدد الصفحات
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// Total number of items across all pages
        /// إجمالي عدد العناصر في جميع الصفحات
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Number of items per page
        /// عدد العناصر في كل صفحة
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// Indicates if there is a previous page
        /// يشير إلى وجود صفحة سابقة
        /// </summary>
        public bool HasPreviousPage => CurrentPage > 1;

        /// <summary>
        /// Indicates if there is a next page
        /// يشير إلى وجود صفحة تالية
        /// </summary>
        public bool HasNextPage => CurrentPage < TotalPages;

        /// <summary>
        /// Starting item number for current page
        /// رقم العنصر الأول في الصفحة الحالية
        /// </summary>
        public int StartItem => TotalCount == 0 ? 0 : (CurrentPage - 1) * PageSize + 1;

        /// <summary>
        /// Ending item number for current page
        /// رقم العنصر الأخير في الصفحة الحالية
        /// </summary>
        public int EndItem => Math.Min(CurrentPage * PageSize, TotalCount);

        /// <summary>
        /// Create empty paginated result
        /// إنشاء نتيجة مقسمة فارغة
        /// </summary>
        /// <param name="pageSize">Page size</param>
        /// <returns>Empty paginated result</returns>
        public static PaginatedResult<T> Empty(int pageSize = 10)
        {
            return new PaginatedResult<T>
            {
                Items = new List<T>(),
                CurrentPage = 1,
                TotalPages = 0,
                TotalCount = 0,
                PageSize = pageSize
            };
        }

        /// <summary>
        /// Create paginated result from list
        /// إنشاء نتيجة مقسمة من قائمة
        /// </summary>
        /// <param name="items">Items for current page</param>
        /// <param name="totalCount">Total count of all items</param>
        /// <param name="currentPage">Current page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Paginated result</returns>
        public static PaginatedResult<T> Create(List<T> items, int totalCount, int currentPage, int pageSize)
        {
            var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
            
            return new PaginatedResult<T>
            {
                Items = items,
                CurrentPage = currentPage,
                TotalPages = totalPages,
                TotalCount = totalCount,
                PageSize = pageSize
            };
        }

        /// <summary>
        /// Map paginated result to different type
        /// تحويل النتيجة المقسمة إلى نوع مختلف
        /// </summary>
        /// <typeparam name="TResult">Target type</typeparam>
        /// <param name="mapper">Mapping function</param>
        /// <returns>Mapped paginated result</returns>
        public PaginatedResult<TResult> Map<TResult>(Func<T, TResult> mapper)
        {
            return new PaginatedResult<TResult>
            {
                Items = Items.Select(mapper).ToList(),
                CurrentPage = CurrentPage,
                TotalPages = TotalPages,
                TotalCount = TotalCount,
                PageSize = PageSize
            };
        }
    }
}
