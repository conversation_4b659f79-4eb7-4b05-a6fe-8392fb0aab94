@model FitHRPlus.Web.Models.Reports.PayrollReportsViewModel
@{
    ViewData["Title"] = "تقارير كشوف المرتبات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        تقارير كشوف المرتبات
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Filter Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-filter me-2"></i>
                                        المرشحات
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <form method="get" class="row g-3">
                                        <div class="col-md-3">
                                            <label for="fromMonth" class="form-label">من شهر</label>
                                            <select class="form-select" id="fromMonth" name="fromMonth">
                                                @for (int i = 1; i <= 12; i++)
                                                {
                                                    var monthName = new DateTime(2023, i, 1).ToString("MMMM", new System.Globalization.CultureInfo("ar-EG"));
                                                    <option value="@i" selected="@(i == Model.FromMonth)">@monthName</option>
                                                }
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="fromYear" class="form-label">من سنة</label>
                                            <select class="form-select" id="fromYear" name="fromYear">
                                                @for (int i = DateTime.Today.Year; i >= DateTime.Today.Year - 5; i--)
                                                {
                                                    <option value="@i" selected="@(i == Model.FromYear)">@i</option>
                                                }
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="department" class="form-label">القسم</label>
                                            <select class="form-select" id="department" name="department">
                                                <option value="">جميع الأقسام</option>
                                                <option value="hr">الموارد البشرية</option>
                                                <option value="it">تكنولوجيا المعلومات</option>
                                                <option value="finance">المالية</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="reportType" class="form-label">نوع التقرير</label>
                                            <select class="form-select" id="reportType" name="reportType">
                                                <option value="summary">ملخص</option>
                                                <option value="detailed">تفصيلي</option>
                                                <option value="comparison">مقارنة</option>
                                                <option value="analysis">تحليل</option>
                                            </select>
                                        </div>
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-info">
                                                <i class="fas fa-search me-1"></i>
                                                إنشاء التقرير
                                            </button>
                                            <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                                                <i class="fas fa-undo me-1"></i>
                                                إعادة تعيين
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">إجمالي الرواتب</h6>
                                            <h3 class="mb-0">2,450,000 ج.م</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-dollar-sign fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">إجمالي البدلات</h6>
                                            <h3 class="mb-0">245,000 ج.م</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-plus-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">إجمالي الخصومات</h6>
                                            <h3 class="mb-0">125,000 ج.م</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-minus-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">صافي الرواتب</h6>
                                            <h3 class="mb-0">2,570,000 ج.م</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-hand-holding-usd fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payroll Data Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الموظف</th>
                                    <th>القسم</th>
                                    <th>الراتب الأساسي</th>
                                    <th>البدلات</th>
                                    <th>الخصومات</th>
                                    <th>الراتب الإجمالي</th>
                                    <th>صافي الراتب</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="fas fa-info-circle me-2"></i>
                                        لا توجد بيانات كشوف مرتبات متاحة للفترة المحددة
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Charts Section -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">توزيع الرواتب حسب القسم</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="departmentSalaryChart" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">اتجاه الرواتب الشهري</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="salaryTrendChart" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Breakdown -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">تفصيل البدلات</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>نوع البدل</th>
                                                    <th>المبلغ</th>
                                                    <th>النسبة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>بدل مواصلات</td>
                                                    <td>85,000 ج.م</td>
                                                    <td>35%</td>
                                                </tr>
                                                <tr>
                                                    <td>بدل طعام</td>
                                                    <td>60,000 ج.م</td>
                                                    <td>24%</td>
                                                </tr>
                                                <tr>
                                                    <td>بدل سكن</td>
                                                    <td>100,000 ج.م</td>
                                                    <td>41%</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">تفصيل الخصومات</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>نوع الخصم</th>
                                                    <th>المبلغ</th>
                                                    <th>النسبة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>التأمينات الاجتماعية</td>
                                                    <td>75,000 ج.م</td>
                                                    <td>60%</td>
                                                </tr>
                                                <tr>
                                                    <td>ضريبة الدخل</td>
                                                    <td>35,000 ج.م</td>
                                                    <td>28%</td>
                                                </tr>
                                                <tr>
                                                    <td>خصومات أخرى</td>
                                                    <td>15,000 ج.م</td>
                                                    <td>12%</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">روابط سريعة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <a href="@Url.Action("PayrollReport", "Payroll")" class="btn btn-outline-primary w-100 mb-2">
                                                <i class="fas fa-file-invoice-dollar me-1"></i>
                                                تقرير كشف المرتبات
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-outline-success w-100 mb-2" onclick="generateTaxReport()">
                                                <i class="fas fa-percentage me-1"></i>
                                                تقرير الضرائب
                                            </button>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-outline-warning w-100 mb-2" onclick="generateInsuranceReport()">
                                                <i class="fas fa-shield-alt me-1"></i>
                                                تقرير التأمينات
                                            </button>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-outline-info w-100 mb-2" onclick="generateBonusReport()">
                                                <i class="fas fa-gift me-1"></i>
                                                تقرير المكافآت
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button class="btn btn-success" onclick="exportToExcel()">
                                    <i class="fas fa-file-excel me-1"></i>
                                    تصدير إلى Excel
                                </button>
                                <button class="btn btn-danger" onclick="exportToPDF()">
                                    <i class="fas fa-file-pdf me-1"></i>
                                    تصدير إلى PDF
                                </button>
                                <button class="btn btn-primary" onclick="printReport()">
                                    <i class="fas fa-print me-1"></i>
                                    طباعة
                                </button>
                                <button class="btn btn-info" onclick="generatePayslips()">
                                    <i class="fas fa-receipt me-1"></i>
                                    إنشاء قسائم الراتب
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        function exportToExcel() {
            alert('سيتم تنفيذ وظيفة تصدير Excel');
        }

        function exportToPDF() {
            alert('سيتم تنفيذ وظيفة تصدير PDF');
        }

        function printReport() {
            window.print();
        }

        function resetFilters() {
            document.querySelector('form').reset();
        }

        function generateTaxReport() {
            alert('سيتم إنشاء تقرير الضرائب');
        }

        function generateInsuranceReport() {
            alert('سيتم إنشاء تقرير التأمينات');
        }

        function generateBonusReport() {
            alert('سيتم إنشاء تقرير المكافآت');
        }

        function generatePayslips() {
            alert('سيتم إنشاء قسائم الراتب');
        }

        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Placeholder for chart initialization
            console.log('Payroll charts will be initialized here');
        });
    </script>
}
