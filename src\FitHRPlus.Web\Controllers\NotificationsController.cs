using FitHRPlus.Application.DTOs.Notification;
using FitHRPlus.Application.Interfaces;
using FitHRPlus.Web.Models.Notifications;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace FitHRPlus.Web.Controllers
{
    /// <summary>
    /// Notifications management controller
    /// وحدة تحكم إدارة الإشعارات
    /// </summary>
    [Authorize]
    public class NotificationsController : Controller
    {
        private readonly INotificationService _notificationService;
        private readonly ILogger<NotificationsController> _logger;

        public NotificationsController(
            INotificationService notificationService,
            ILogger<NotificationsController> logger)
        {
            _notificationService = notificationService;
            _logger = logger;
        }

        /// <summary>
        /// Notifications list
        /// قائمة الإشعارات
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Index([FromQuery] NotificationListDto request)
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    TempData["ErrorMessage"] = "User information not found";
                    return RedirectToAction("Index", "Home");
                }

                request.UserId = userId;

                var result = await _notificationService.GetNotificationsAsync(request);

                if (result.IsSuccess)
                {
                    var viewModel = new NotificationListViewModel
                    {
                        Notifications = result.Data.Items.Select(MapToNotificationViewModel).ToList(),
                        Type = request.Type,
                        Category = request.Category,
                        Priority = request.Priority,
                        IsRead = request.IsRead,
                        SearchTerm = request.SearchTerm,
                        CurrentPage = result.Data.CurrentPage,
                        TotalPages = result.Data.TotalPages,
                        TotalCount = result.Data.TotalCount,
                        PageSize = result.Data.PageSize
                    };

                    // Load statistics
                    var statsResult = await _notificationService.GetNotificationStatisticsAsync(userId);
                    if (statsResult.IsSuccess)
                    {
                        viewModel.Statistics = MapToNotificationStatisticsViewModel(statsResult.Data);
                    }

                    return View(viewModel);
                }

                TempData["ErrorMessage"] = result.ErrorMessage;
                return View(new NotificationListViewModel());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading notifications");
                TempData["ErrorMessage"] = "An error occurred while loading notifications";
                return View(new NotificationListViewModel());
            }
        }

        /// <summary>
        /// Notification details
        /// تفاصيل الإشعار
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Details(Guid id)
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    TempData["ErrorMessage"] = "User information not found";
                    return RedirectToAction(nameof(Index));
                }

                var result = await _notificationService.GetNotificationByIdAsync(id);

                if (result.IsSuccess && result.Data != null)
                {
                    // Mark as read if not already read
                    if (!result.Data.IsRead)
                    {
                        await _notificationService.MarkAsReadAsync(id, userId);
                    }

                    var viewModel = MapToNotificationDetailsViewModel(result.Data);
                    return View(viewModel);
                }

                TempData["ErrorMessage"] = result.ErrorMessage ?? "Notification not found";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading notification details for ID: {Id}", id);
                TempData["ErrorMessage"] = "An error occurred while loading notification details";
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// Mark notification as read
        /// تسجيل الإشعار كمقروء
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> MarkAsRead(Guid id)
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    return Json(new { success = false, message = "User information not found" });
                }

                var result = await _notificationService.MarkAsReadAsync(id, userId);

                if (result.IsSuccess)
                {
                    return Json(new { success = true, message = "Notification marked as read" });
                }

                return Json(new { success = false, message = result.ErrorMessage });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking notification as read: {Id}", id);
                return Json(new { success = false, message = "An error occurred" });
            }
        }

        /// <summary>
        /// Mark all notifications as read
        /// تسجيل جميع الإشعارات كمقروءة
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> MarkAllAsRead()
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    return Json(new { success = false, message = "User information not found" });
                }

                var result = await _notificationService.MarkAllAsReadAsync(userId);

                if (result.IsSuccess)
                {
                    return Json(new { success = true, message = $"Marked {result.Data} notifications as read", count = result.Data });
                }

                return Json(new { success = false, message = result.ErrorMessage });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking all notifications as read");
                return Json(new { success = false, message = "An error occurred" });
            }
        }

        /// <summary>
        /// Delete notification
        /// حذف الإشعار
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    return Json(new { success = false, message = "User information not found" });
                }

                var result = await _notificationService.DeleteNotificationAsync(id, userId);

                if (result.IsSuccess)
                {
                    return Json(new { success = true, message = "Notification deleted successfully" });
                }

                return Json(new { success = false, message = result.ErrorMessage });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting notification: {Id}", id);
                return Json(new { success = false, message = "An error occurred" });
            }
        }

        /// <summary>
        /// Bulk action on notifications
        /// إجراء مجمع على الإشعارات
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> BulkAction([FromBody] BulkNotificationActionDto request)
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    return Json(new { success = false, message = "User information not found" });
                }

                var result = await _notificationService.BulkActionAsync(request, userId);

                if (result.IsSuccess)
                {
                    return Json(new { success = true, message = $"Action performed on {result.Data} notifications", count = result.Data });
                }

                return Json(new { success = false, message = result.ErrorMessage });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing bulk action on notifications");
                return Json(new { success = false, message = "An error occurred" });
            }
        }

        /// <summary>
        /// Get unread notification count (API endpoint)
        /// الحصول على عدد الإشعارات غير المقروءة (نقطة API)
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetUnreadCount()
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    return Json(new { success = false, count = 0 });
                }

                var result = await _notificationService.GetUnreadCountAsync(userId);

                if (result.IsSuccess)
                {
                    return Json(new { success = true, count = result.Data });
                }

                return Json(new { success = false, count = 0 });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unread notification count");
                return Json(new { success = false, count = 0 });
            }
        }

        /// <summary>
        /// Get recent notifications (API endpoint)
        /// الحصول على الإشعارات الحديثة (نقطة API)
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetRecent(int limit = 5)
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    return Json(new { success = false, notifications = new List<object>() });
                }

                var result = await _notificationService.GetRecentNotificationsAsync(userId, limit);

                if (result.IsSuccess)
                {
                    var notifications = result.Data.Select(n => new
                    {
                        id = n.Id,
                        title = n.Title,
                        message = n.Message,
                        type = n.Type,
                        isRead = n.IsRead,
                        timeAgo = n.TimeAgo,
                        actionUrl = n.ActionUrl,
                        icon = n.Icon
                    });

                    return Json(new { success = true, notifications });
                }

                return Json(new { success = false, notifications = new List<object>() });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recent notifications");
                return Json(new { success = false, notifications = new List<object>() });
            }
        }

        /// <summary>
        /// Notification preferences
        /// تفضيلات الإشعارات
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Preferences()
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    TempData["ErrorMessage"] = "User information not found";
                    return RedirectToAction("Index", "Home");
                }

                var result = await _notificationService.GetNotificationPreferencesAsync(userId);

                if (result.IsSuccess)
                {
                    var viewModel = MapToNotificationPreferencesViewModel(result.Data);
                    return View(viewModel);
                }

                TempData["ErrorMessage"] = result.ErrorMessage;
                return View(new NotificationPreferencesViewModel());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading notification preferences");
                TempData["ErrorMessage"] = "An error occurred while loading notification preferences";
                return View(new NotificationPreferencesViewModel());
            }
        }

        /// <summary>
        /// Update notification preferences
        /// تحديث تفضيلات الإشعارات
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Preferences(NotificationPreferencesViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return View(model);
                }

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    ModelState.AddModelError("", "User information not found");
                    return View(model);
                }

                var request = MapToUpdateNotificationPreferencesDto(model);
                var result = await _notificationService.UpdateNotificationPreferencesAsync(userId, request);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = "Notification preferences updated successfully";
                    return RedirectToAction(nameof(Preferences));
                }

                ModelState.AddModelError("", result.ErrorMessage ?? "Failed to update notification preferences");
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating notification preferences");
                ModelState.AddModelError("", "An error occurred while updating notification preferences");
                return View(model);
            }
        }

        // Helper methods
        private static NotificationViewModel MapToNotificationViewModel(NotificationDto dto)
        {
            return new NotificationViewModel
            {
                Id = dto.Id,
                Title = dto.Title,
                TitleAr = dto.TitleAr,
                Message = dto.Message,
                MessageAr = dto.MessageAr,
                Type = dto.Type,
                Category = dto.Category,
                Priority = dto.Priority,
                IsRead = dto.IsRead,
                ReadAt = dto.ReadAt,
                ActionUrl = dto.ActionUrl,
                ActionText = dto.ActionText,
                ActionTextAr = dto.ActionTextAr,
                Icon = dto.Icon,
                CreatedAt = dto.CreatedAt,
                TimeAgo = dto.TimeAgo,
                TypeBadgeClass = dto.TypeBadgeClass,
                PriorityBadgeClass = dto.PriorityBadgeClass,
                IsExpired = dto.IsExpired
            };
        }

        private static NotificationDetailsViewModel MapToNotificationDetailsViewModel(NotificationDto dto)
        {
            return new NotificationDetailsViewModel
            {
                Id = dto.Id,
                Title = dto.Title,
                TitleAr = dto.TitleAr,
                Message = dto.Message,
                MessageAr = dto.MessageAr,
                Type = dto.Type,
                Category = dto.Category,
                Priority = dto.Priority,
                IsRead = dto.IsRead,
                ReadAt = dto.ReadAt,
                ActionUrl = dto.ActionUrl,
                ActionText = dto.ActionText,
                ActionTextAr = dto.ActionTextAr,
                Icon = dto.Icon,
                Data = dto.Data,
                CreatedAt = dto.CreatedAt,
                ExpiresAt = dto.ExpiresAt,
                TimeAgo = dto.TimeAgo,
                TypeBadgeClass = dto.TypeBadgeClass,
                PriorityBadgeClass = dto.PriorityBadgeClass,
                IsExpired = dto.IsExpired
            };
        }

        private static NotificationStatisticsViewModel MapToNotificationStatisticsViewModel(NotificationStatisticsDto dto)
        {
            return new NotificationStatisticsViewModel
            {
                TotalNotifications = dto.TotalNotifications,
                UnreadNotifications = dto.UnreadNotifications,
                ReadNotifications = dto.ReadNotifications,
                ExpiredNotifications = dto.ExpiredNotifications,
                NotificationsByType = dto.NotificationsByType,
                NotificationsByCategory = dto.NotificationsByCategory,
                NotificationsByPriority = dto.NotificationsByPriority,
                ReadPercentage = dto.ReadPercentage
            };
        }

        private static NotificationPreferencesViewModel MapToNotificationPreferencesViewModel(NotificationPreferencesDto dto)
        {
            return new NotificationPreferencesViewModel
            {
                EmailNotifications = dto.EmailNotifications,
                PushNotifications = dto.PushNotifications,
                SmsNotifications = dto.SmsNotifications,
                LeaveNotifications = dto.LeaveNotifications,
                PayrollNotifications = dto.PayrollNotifications,
                AttendanceNotifications = dto.AttendanceNotifications,
                SystemNotifications = dto.SystemNotifications,
                QuietHoursStart = dto.QuietHoursStart,
                QuietHoursEnd = dto.QuietHoursEnd,
                DisabledCategories = dto.DisabledCategories
            };
        }

        private static UpdateNotificationPreferencesDto MapToUpdateNotificationPreferencesDto(NotificationPreferencesViewModel model)
        {
            return new UpdateNotificationPreferencesDto
            {
                EmailNotifications = model.EmailNotifications,
                PushNotifications = model.PushNotifications,
                SmsNotifications = model.SmsNotifications,
                LeaveNotifications = model.LeaveNotifications,
                PayrollNotifications = model.PayrollNotifications,
                AttendanceNotifications = model.AttendanceNotifications,
                SystemNotifications = model.SystemNotifications,
                QuietHoursStart = model.QuietHoursStart,
                QuietHoursEnd = model.QuietHoursEnd,
                DisabledCategories = model.DisabledCategories
            };
        }


    }
}
