@{
    ViewData["Title"] = "Payroll Report / تقرير كشوف المرتبات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        Payroll Report / تقرير كشوف المرتبات
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Report Date Selection -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <form method="get" class="d-flex align-items-end gap-3">
                                <div class="flex-grow-1">
                                    <label for="year" class="form-label">Year / السنة</label>
                                    <select class="form-select" id="year" name="year">
                                        @for (int i = DateTime.Today.Year; i >= DateTime.Today.Year - 5; i--)
                                        {
                                            <option value="@i" selected="@(i == ViewBag.ReportYear)">@i</option>
                                        }
                                    </select>
                                </div>
                                <div class="flex-grow-1">
                                    <label for="month" class="form-label">Month / الشهر</label>
                                    <select class="form-select" id="month" name="month">
                                        @for (int i = 1; i <= 12; i++)
                                        {
                                            var monthName = new DateTime(2023, i, 1).ToString("MMMM");
                                            <option value="@i" selected="@(i == ViewBag.ReportMonth)">@i - @monthName</option>
                                        }
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>
                                    Generate / إنشاء
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Report Content -->
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Payroll Period:</strong> @(new DateTime((int)ViewBag.ReportYear, (int)ViewBag.ReportMonth, 1).ToString("MMMM yyyy"))
                                <br>
                                <strong>فترة كشف المرتبات:</strong> @(new DateTime((int)ViewBag.ReportYear, (int)ViewBag.ReportMonth, 1).ToString("MMMM yyyy", new System.Globalization.CultureInfo("ar-EG")))
                            </div>
                        </div>
                    </div>

                    <!-- Payroll Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Total Employees / إجمالي الموظفين</h6>
                                            <h3 class="mb-0">0</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-users fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Total Gross Salary / إجمالي الراتب الإجمالي</h6>
                                            <h3 class="mb-0">0.00 EGP</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-dollar-sign fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Total Deductions / إجمالي الخصومات</h6>
                                            <h3 class="mb-0">0.00 EGP</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-minus-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Total Net Salary / إجمالي الراتب الصافي</h6>
                                            <h3 class="mb-0">0.00 EGP</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-hand-holding-usd fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payroll Details Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Employee / الموظف</th>
                                    <th>Department / القسم</th>
                                    <th>Basic Salary / الراتب الأساسي</th>
                                    <th>Allowances / البدلات</th>
                                    <th>Deductions / الخصومات</th>
                                    <th>Gross Salary / الراتب الإجمالي</th>
                                    <th>Net Salary / الراتب الصافي</th>
                                    <th>Status / الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="fas fa-info-circle me-2"></i>
                                        No payroll data available for this period / لا توجد بيانات كشوف مرتبات لهذه الفترة
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Charts Section -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Salary Distribution / توزيع الرواتب</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="salaryChart" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Department Payroll / كشوف مرتبات الأقسام</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="departmentPayrollChart" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Breakdown -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Allowances Breakdown / تفصيل البدلات</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Type / النوع</th>
                                                    <th>Amount / المبلغ</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td colspan="2" class="text-center text-muted">No data available / لا توجد بيانات</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Deductions Breakdown / تفصيل الخصومات</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Type / النوع</th>
                                                    <th>Amount / المبلغ</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td colspan="2" class="text-center text-muted">No data available / لا توجد بيانات</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button class="btn btn-success" onclick="exportToExcel()">
                                    <i class="fas fa-file-excel me-1"></i>
                                    Export to Excel / تصدير إلى Excel
                                </button>
                                <button class="btn btn-danger" onclick="exportToPDF()">
                                    <i class="fas fa-file-pdf me-1"></i>
                                    Export to PDF / تصدير إلى PDF
                                </button>
                                <button class="btn btn-primary" onclick="printReport()">
                                    <i class="fas fa-print me-1"></i>
                                    Print / طباعة
                                </button>
                                <button class="btn btn-info" onclick="generatePayslips()">
                                    <i class="fas fa-receipt me-1"></i>
                                    Generate Payslips / إنشاء قسائم الراتب
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function exportToExcel() {
        alert('Excel export functionality will be implemented / سيتم تنفيذ وظيفة تصدير Excel');
    }

    function exportToPDF() {
        alert('PDF export functionality will be implemented / سيتم تنفيذ وظيفة تصدير PDF');
    }

    function printReport() {
        window.print();
    }

    function generatePayslips() {
        alert('Payslip generation functionality will be implemented / سيتم تنفيذ وظيفة إنشاء قسائم الراتب');
    }

    // Initialize charts when page loads
    document.addEventListener('DOMContentLoaded', function() {
        // Placeholder for chart initialization
        console.log('Payroll charts will be initialized here');
    });
</script>
