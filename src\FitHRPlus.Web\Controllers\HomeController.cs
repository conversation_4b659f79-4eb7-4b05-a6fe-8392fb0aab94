using System.Diagnostics;
using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using FitHRPlus.Web.Models;
using FitHRPlus.Web.Models.Dashboard;
using FitHRPlus.Application.Interfaces;

namespace FitHRPlus.Web.Controllers;

[Authorize]
public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly IEmployeeService _employeeService;
    private readonly IAttendanceService _attendanceService;
    private readonly ILeaveService _leaveService;
    private readonly IPayrollService _payrollService;

    public HomeController(
        ILogger<HomeController> logger,
        IEmployeeService employeeService,
        IAttendanceService attendanceService,
        ILeaveService leaveService,
        IPayrollService payrollService)
    {
        _logger = logger;
        _employeeService = employeeService;
        _attendanceService = attendanceService;
        _leaveService = leaveService;
        _payrollService = payrollService;
    }

    public async Task<IActionResult> Index()
    {
        try
        {
            var companyIdClaim = User.FindFirst("CompanyId")?.Value;
            if (!Guid.TryParse(companyIdClaim, out var companyId))
            {
                return RedirectToAction("Login", "Account");
            }

            var viewModel = new DashboardViewModel
            {
                CompanyId = companyId
            };

            // Load comprehensive dashboard data
            await LoadDashboardDataAsync(viewModel, companyId);

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading dashboard");
            return View(new DashboardViewModel());
        }
    }

    private async Task LoadDashboardDataAsync(DashboardViewModel viewModel, Guid companyId)
    {
        try
        {
            // Load basic statistics
            viewModel.TotalEmployees = 150; // Placeholder
            viewModel.ActiveEmployees = 145; // Placeholder
            viewModel.TotalDepartments = 8; // Placeholder
            viewModel.TodayAttendance = 142; // Placeholder
            viewModel.PendingLeaveRequests = 5; // Placeholder
            viewModel.PendingPayrolls = 3; // Placeholder

            // Load chart data
            viewModel.EmployeesByDepartment = new Dictionary<string, int>
            {
                { "IT", 25 },
                { "HR", 15 },
                { "Finance", 20 },
                { "Marketing", 18 },
                { "Operations", 30 },
                { "Sales", 27 }
            };

            viewModel.AttendanceByMonth = new Dictionary<string, decimal>
            {
                { "Jan", 95.2m },
                { "Feb", 94.8m },
                { "Mar", 96.1m },
                { "Apr", 95.7m },
                { "May", 94.9m },
                { "Jun", 95.5m }
            };

            viewModel.SalariesByDepartment = new Dictionary<string, decimal>
            {
                { "IT", 125000m },
                { "HR", 85000m },
                { "Finance", 110000m },
                { "Marketing", 95000m },
                { "Operations", 140000m },
                { "Sales", 115000m }
            };

            // Load recent activities
            viewModel.RecentActivities = new List<DashboardActivityViewModel>
            {
                new() { Type = "Leave", Description = "Ahmed Ali submitted a leave request", Time = DateTime.Now.AddMinutes(-15), Icon = "fas fa-calendar-alt", Color = "text-warning" },
                new() { Type = "Attendance", Description = "Sarah Mohamed checked in", Time = DateTime.Now.AddMinutes(-30), Icon = "fas fa-clock", Color = "text-success" },
                new() { Type = "Payroll", Description = "Monthly payroll processed", Time = DateTime.Now.AddHours(-2), Icon = "fas fa-money-bill-wave", Color = "text-info" },
                new() { Type = "Employee", Description = "New employee onboarded", Time = DateTime.Now.AddHours(-4), Icon = "fas fa-user-plus", Color = "text-primary" }
            };

            // Load quick stats
            viewModel.QuickStats = new List<DashboardQuickStatViewModel>
            {
                new() { Title = "Attendance Rate", Value = "95.5%", Icon = "fas fa-percentage", Color = "bg-success", Change = "+2.1%" },
                new() { Title = "Average Salary", Value = "$4,250", Icon = "fas fa-dollar-sign", Color = "bg-info", Change = "+5.2%" },
                new() { Title = "Leave Utilization", Value = "78%", Icon = "fas fa-calendar-check", Color = "bg-warning", Change = "-1.3%" },
                new() { Title = "Employee Satisfaction", Value = "4.2/5", Icon = "fas fa-smile", Color = "bg-primary", Change = "+0.3" }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading dashboard data");
        }
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
