using FitHRPlus.Application.DTOs.CompanySettings;
using FitHRPlus.Application.Common;
using FitHRPlus.Domain.Entities;

namespace FitHRPlus.Application.Interfaces
{
    /// <summary>
    /// Company settings service interface
    /// واجهة خدمة إعدادات الشركة
    /// </summary>
    public interface ICompanySettingsService
    {
        /// <summary>
        /// Get company settings by company ID
        /// الحصول على إعدادات الشركة بواسطة معرف الشركة
        /// </summary>
        Task<CompanySettingsDto?> GetCompanySettingsAsync(Guid companyId);

        /// <summary>
        /// Create or update company settings
        /// إنشاء أو تحديث إعدادات الشركة
        /// </summary>
        Task<CompanySettingsDto> CreateOrUpdateCompanySettingsAsync(Guid companyId, CreateUpdateCompanySettingsDto dto);

        /// <summary>
        /// Update company settings
        /// تحديث إعدادات الشركة
        /// </summary>
        Task<ServiceResult<CompanySettingsDto>> UpdateCompanySettingsAsync(Guid companyId, UpdateCompanySettingsDto dto);

        /// <summary>
        /// Get work schedules for company
        /// الحصول على جداول العمل للشركة
        /// </summary>
        Task<List<WorkScheduleDto>> GetWorkSchedulesAsync(Guid companyId);

        /// <summary>
        /// Create work schedule
        /// إنشاء جدول عمل
        /// </summary>
        Task<WorkScheduleDto> CreateWorkScheduleAsync(Guid companyId, CreateWorkScheduleDto dto);

        /// <summary>
        /// Update work schedule
        /// تحديث جدول العمل
        /// </summary>
        Task<WorkScheduleDto> UpdateWorkScheduleAsync(Guid scheduleId, UpdateWorkScheduleDto dto);

        /// <summary>
        /// Delete work schedule
        /// حذف جدول العمل
        /// </summary>
        Task<bool> DeleteWorkScheduleAsync(Guid scheduleId);

        /// <summary>
        /// Get work schedule by ID
        /// الحصول على جدول العمل بواسطة المعرف
        /// </summary>
        Task<WorkScheduleDto?> GetWorkScheduleByIdAsync(Guid scheduleId);

        /// <summary>
        /// Set default work schedule
        /// تعيين جدول العمل الافتراضي
        /// </summary>
        Task<bool> SetDefaultWorkScheduleAsync(Guid companyId, Guid scheduleId);

        /// <summary>
        /// Get holidays for company
        /// الحصول على العطل للشركة
        /// </summary>
        Task<List<HolidayDto>> GetHolidaysAsync(Guid companyId, int? year = null);

        /// <summary>
        /// Create holiday
        /// إنشاء عطلة
        /// </summary>
        Task<HolidayDto> CreateHolidayAsync(Guid companyId, CreateHolidayDto dto);

        /// <summary>
        /// Update holiday
        /// تحديث العطلة
        /// </summary>
        Task<HolidayDto> UpdateHolidayAsync(Guid holidayId, UpdateHolidayDto dto);

        /// <summary>
        /// Delete holiday
        /// حذف العطلة
        /// </summary>
        Task<bool> DeleteHolidayAsync(Guid holidayId);

        /// <summary>
        /// Check if date is holiday
        /// التحقق من كون التاريخ عطلة
        /// </summary>
        Task<bool> IsHolidayAsync(Guid companyId, DateTime date);

        /// <summary>
        /// Check if date is weekend
        /// التحقق من كون التاريخ عطلة نهاية أسبوع
        /// </summary>
        Task<bool> IsWeekendAsync(Guid companyId, DateTime date);

        /// <summary>
        /// Get working days between two dates
        /// الحصول على أيام العمل بين تاريخين
        /// </summary>
        Task<int> GetWorkingDaysBetweenAsync(Guid companyId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Calculate overtime for hours worked
        /// حساب الوقت الإضافي للساعات المعملة
        /// </summary>
        Task<decimal> CalculateOvertimeAsync(Guid companyId, decimal hoursWorked);

        /// <summary>
        /// Get company working hours
        /// الحصول على ساعات عمل الشركة
        /// </summary>
        Task<(TimeSpan start, TimeSpan end)> GetWorkingHoursAsync(Guid companyId);

        /// <summary>
        /// Check if employee is late
        /// التحقق من تأخر الموظف
        /// </summary>
        Task<bool> IsEmployeeLateAsync(Guid companyId, TimeSpan arrivalTime, TimeSpan workStartTime);

        /// <summary>
        /// Check if employee left early
        /// التحقق من مغادرة الموظف مبكراً
        /// </summary>
        Task<bool> IsEmployeeEarlyLeaveAsync(Guid companyId, TimeSpan departureTime, TimeSpan workEndTime);

        /// <summary>
        /// Get leave entitlements for employee
        /// الحصول على استحقاقات الإجازة للموظف
        /// </summary>
        Task<Dictionary<string, decimal>> GetLeaveEntitlementsAsync(Guid companyId);

        /// <summary>
        /// Get tax and insurance rates
        /// الحصول على معدلات الضرائب والتأمينات
        /// </summary>
        Task<TaxInsuranceRatesDto> GetTaxInsuranceRatesAsync(Guid companyId);

        /// <summary>
        /// Initialize default company settings
        /// تهيئة إعدادات الشركة الافتراضية
        /// </summary>
        Task<CompanySettingsDto> InitializeDefaultSettingsAsync(Guid companyId);
    }
}
