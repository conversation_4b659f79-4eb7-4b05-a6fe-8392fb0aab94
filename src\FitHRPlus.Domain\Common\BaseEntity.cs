using System;

namespace FitHRPlus.Domain.Common
{
    /// <summary>
    /// Base entity class that provides common properties for all entities
    /// الكلاس الأساسي للكيانات الذي يوفر الخصائص المشتركة لجميع الكيانات
    /// </summary>
    public abstract class BaseEntity
    {
        /// <summary>
        /// Unique identifier for the entity
        /// المعرف الفريد للكيان
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Date and time when the entity was created
        /// تاريخ ووقت إنشاء الكيان
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Date and time when the entity was last updated
        /// تاريخ ووقت آخر تحديث للكيان
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// ID of the user who created this entity
        /// معرف المستخدم الذي أنشأ هذا الكيان
        /// </summary>
        public Guid? CreatedBy { get; set; }

        /// <summary>
        /// ID of the user who last updated this entity
        /// معرف المستخدم الذي قام بآخر تحديث لهذا الكيان
        /// </summary>
        public Guid? UpdatedBy { get; set; }

        /// <summary>
        /// Indicates whether the entity is active or soft deleted
        /// يشير إلى ما إذا كان الكيان نشطاً أم محذوفاً منطقياً
        /// </summary>
        public bool IsActive { get; set; } = true;
    }
}