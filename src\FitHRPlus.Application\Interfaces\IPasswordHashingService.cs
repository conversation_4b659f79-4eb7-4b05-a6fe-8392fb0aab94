namespace FitHRPlus.Application.Interfaces
{
    /// <summary>
    /// Password hashing service interface
    /// واجهة خدمة تشفير كلمات المرور
    /// </summary>
    public interface IPasswordHashingService
    {
        /// <summary>
        /// Hash a password with salt
        /// تشفير كلمة مرور مع الملح
        /// </summary>
        /// <param name="password">Plain text password</param>
        /// <returns>Hashed password and salt</returns>
        (string hashedPassword, string salt) HashPassword(string password);

        /// <summary>
        /// Verify a password against its hash
        /// التحقق من كلمة مرور مقابل تشفيرها
        /// </summary>
        /// <param name="password">Plain text password</param>
        /// <param name="hashedPassword">Hashed password</param>
        /// <param name="salt">Salt used for hashing</param>
        /// <returns>True if password matches, false otherwise</returns>
        bool VerifyPassword(string password, string hashedPassword, string salt);

        /// <summary>
        /// Generate a random salt
        /// إنشاء ملح عشوائي
        /// </summary>
        /// <returns>Random salt string</returns>
        string GenerateSalt();

        /// <summary>
        /// Check if password meets security requirements
        /// التحقق من أن كلمة المرور تلبي متطلبات الأمان
        /// </summary>
        /// <param name="password">Password to check</param>
        /// <returns>Password validation result</returns>
        PasswordValidationResult ValidatePassword(string password);

        /// <summary>
        /// Generate a secure random password
        /// إنشاء كلمة مرور عشوائية آمنة
        /// </summary>
        /// <param name="length">Password length</param>
        /// <param name="includeSpecialChars">Include special characters</param>
        /// <returns>Generated password</returns>
        string GenerateSecurePassword(int length = 12, bool includeSpecialChars = true);
    }

    /// <summary>
    /// Password validation result
    /// نتيجة التحقق من كلمة المرور
    /// </summary>
    public class PasswordValidationResult
    {
        /// <summary>
        /// Whether the password is valid
        /// ما إذا كانت كلمة المرور صالحة
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Validation error messages
        /// رسائل أخطاء التحقق
        /// </summary>
        public List<string> Errors { get; set; } = new();

        /// <summary>
        /// Arabic validation error messages
        /// رسائل أخطاء التحقق بالعربية
        /// </summary>
        public List<string> ErrorsAr { get; set; } = new();

        /// <summary>
        /// Password strength score (0-100)
        /// نقاط قوة كلمة المرور (0-100)
        /// </summary>
        public int StrengthScore { get; set; }

        /// <summary>
        /// Password strength level
        /// مستوى قوة كلمة المرور
        /// </summary>
        public PasswordStrength Strength { get; set; }

        /// <summary>
        /// Suggestions for improving password
        /// اقتراحات لتحسين كلمة المرور
        /// </summary>
        public List<string> Suggestions { get; set; } = new();

        /// <summary>
        /// Arabic suggestions for improving password
        /// اقتراحات لتحسين كلمة المرور بالعربية
        /// </summary>
        public List<string> SuggestionsAr { get; set; } = new();
    }

    /// <summary>
    /// Password strength levels
    /// مستويات قوة كلمة المرور
    /// </summary>
    public enum PasswordStrength
    {
        /// <summary>
        /// Very weak password
        /// كلمة مرور ضعيفة جداً
        /// </summary>
        VeryWeak = 0,

        /// <summary>
        /// Weak password
        /// كلمة مرور ضعيفة
        /// </summary>
        Weak = 1,

        /// <summary>
        /// Fair password
        /// كلمة مرور مقبولة
        /// </summary>
        Fair = 2,

        /// <summary>
        /// Good password
        /// كلمة مرور جيدة
        /// </summary>
        Good = 3,

        /// <summary>
        /// Strong password
        /// كلمة مرور قوية
        /// </summary>
        Strong = 4,

        /// <summary>
        /// Very strong password
        /// كلمة مرور قوية جداً
        /// </summary>
        VeryStrong = 5
    }

    /// <summary>
    /// Password hashing options
    /// خيارات تشفير كلمة المرور
    /// </summary>
    public class PasswordHashingOptions
    {
        /// <summary>
        /// Number of iterations for PBKDF2
        /// عدد التكرارات لـ PBKDF2
        /// </summary>
        public int Iterations { get; set; } = 100000;

        /// <summary>
        /// Salt size in bytes
        /// حجم الملح بالبايت
        /// </summary>
        public int SaltSize { get; set; } = 32;

        /// <summary>
        /// Hash size in bytes
        /// حجم التشفير بالبايت
        /// </summary>
        public int HashSize { get; set; } = 32;

        /// <summary>
        /// Minimum password length
        /// الحد الأدنى لطول كلمة المرور
        /// </summary>
        public int MinimumLength { get; set; } = 8;

        /// <summary>
        /// Maximum password length
        /// الحد الأقصى لطول كلمة المرور
        /// </summary>
        public int MaximumLength { get; set; } = 100;

        /// <summary>
        /// Require uppercase letters
        /// يتطلب أحرف كبيرة
        /// </summary>
        public bool RequireUppercase { get; set; } = true;

        /// <summary>
        /// Require lowercase letters
        /// يتطلب أحرف صغيرة
        /// </summary>
        public bool RequireLowercase { get; set; } = true;

        /// <summary>
        /// Require digits
        /// يتطلب أرقام
        /// </summary>
        public bool RequireDigits { get; set; } = true;

        /// <summary>
        /// Require special characters
        /// يتطلب أحرف خاصة
        /// </summary>
        public bool RequireSpecialChars { get; set; } = true;

        /// <summary>
        /// Minimum number of unique characters
        /// الحد الأدنى لعدد الأحرف الفريدة
        /// </summary>
        public int MinimumUniqueChars { get; set; } = 4;
    }
}
