using FitHRPlus.Domain.Common;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Domain.Entities
{
    /// <summary>
    /// Role entity for user authorization
    /// كيان الدور لتخويل المستخدمين
    /// </summary>
    public class Role : BaseEntity
    {
        /// <summary>
        /// Role name in English
        /// اسم الدور بالإنجليزية
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Role name in Arabic
        /// اسم الدور بالعربية
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// Role description in English
        /// وصف الدور بالإنجليزية
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Role description in Arabic
        /// وصف الدور بالعربية
        /// </summary>
        [MaxLength(500)]
        public string? DescriptionAr { get; set; }

        /// <summary>
        /// Indicates if this is a system-defined role
        /// يشير إلى ما إذا كان هذا دوراً محدداً من النظام
        /// </summary>
        public bool IsSystemRole { get; set; } = false;

        /// <summary>
        /// JSON array of permissions assigned to this role
        /// مصفوفة JSON للصلاحيات المخصصة لهذا الدور
        /// </summary>
        public string? Permissions { get; set; }

        // Navigation Properties - خصائص التنقل

        /// <summary>
        /// Users assigned to this role
        /// المستخدمون المخصصون لهذا الدور
        /// </summary>
        public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
    }
}