using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace FitHRPlus.Web.Models.Reports
{
    /// <summary>
    /// Reports dashboard view model
    /// نموذج عرض لوحة تحكم التقارير
    /// </summary>
    public class ReportsDashboardViewModel
    {
        public Guid CompanyId { get; set; }

        // Quick statistics
        public int TotalEmployees { get; set; }
        public int ActiveEmployees { get; set; }
        public int TotalDepartments { get; set; }
        public decimal TotalSalaries { get; set; }

        // Recent activity
        public int TodayAttendance { get; set; }
        public int PendingLeaveRequests { get; set; }
        public int PendingPayrolls { get; set; }

        // Charts data
        public Dictionary<string, int> EmployeesByDepartment { get; set; } = new();
        public Dictionary<string, decimal> AttendanceByMonth { get; set; } = new();
        public Dictionary<string, decimal> SalariesByDepartment { get; set; } = new();
        public Dictionary<string, int> LeavesByType { get; set; } = new();

        // Report links
        public List<ReportLinkViewModel> AvailableReports { get; set; } = new();
    }

    /// <summary>
    /// Report link view model
    /// نموذج عرض رابط التقرير
    /// </summary>
    public class ReportLinkViewModel
    {
        public string Title { get; set; } = string.Empty;
        public string TitleAr { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string DescriptionAr { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
    }

    /// <summary>
    /// Employee reports view model
    /// نموذج عرض تقارير الموظفين
    /// </summary>
    public class EmployeeReportsViewModel
    {
        public Guid CompanyId { get; set; }

        [Display(Name = "From Date / من تاريخ")]
        public DateTime FromDate { get; set; } = DateTime.Today.AddMonths(-1);

        [Display(Name = "To Date / إلى تاريخ")]
        public DateTime ToDate { get; set; } = DateTime.Today;

        [Display(Name = "Department / القسم")]
        public Guid? DepartmentId { get; set; }

        [Display(Name = "Position / المنصب")]
        public Guid? PositionId { get; set; }

        [Display(Name = "Employment Status / حالة التوظيف")]
        public string? EmploymentStatus { get; set; }

        [Display(Name = "Report Type / نوع التقرير")]
        public string ReportType { get; set; } = "Summary";

        // Filter options
        public List<SelectListItem> Departments { get; set; } = new();
        public List<SelectListItem> Positions { get; set; } = new();
        public List<SelectListItem> EmploymentStatuses { get; set; } = new()
        {
            new SelectListItem { Value = "Active", Text = "Active / نشط" },
            new SelectListItem { Value = "Inactive", Text = "Inactive / غير نشط" },
            new SelectListItem { Value = "Terminated", Text = "Terminated / منتهي الخدمة" }
        };
        public List<SelectListItem> ReportTypes { get; set; } = new()
        {
            new SelectListItem { Value = "Summary", Text = "Summary Report / تقرير ملخص" },
            new SelectListItem { Value = "Detailed", Text = "Detailed Report / تقرير مفصل" },
            new SelectListItem { Value = "Demographics", Text = "Demographics / التركيبة السكانية" },
            new SelectListItem { Value = "Performance", Text = "Performance / الأداء" },
            new SelectListItem { Value = "Turnover", Text = "Turnover Analysis / تحليل دوران الموظفين" }
        };

        // Report data
        public EmployeeStatisticsViewModel? Statistics { get; set; }
        public List<EmployeeReportItemViewModel> Employees { get; set; } = new();
        public Dictionary<string, int> DepartmentDistribution { get; set; } = new();
        public Dictionary<string, int> AgeDistribution { get; set; } = new();
        public Dictionary<string, int> TenureDistribution { get; set; } = new();
    }

    /// <summary>
    /// Employee statistics view model
    /// نموذج عرض إحصائيات الموظفين
    /// </summary>
    public class EmployeeStatisticsViewModel
    {
        public int TotalEmployees { get; set; }
        public int ActiveEmployees { get; set; }
        public int InactiveEmployees { get; set; }
        public int NewHires { get; set; }
        public int Terminations { get; set; }

        public decimal AverageAge { get; set; }
        public decimal AverageTenure { get; set; }
        public decimal TurnoverRate { get; set; }

        public int MaleEmployees { get; set; }
        public int FemaleEmployees { get; set; }

        // Calculated properties
        public decimal ActivePercentage => TotalEmployees > 0 ? (decimal)ActiveEmployees / TotalEmployees * 100 : 0;
        public decimal MalePercentage => TotalEmployees > 0 ? (decimal)MaleEmployees / TotalEmployees * 100 : 0;
        public decimal FemalePercentage => TotalEmployees > 0 ? (decimal)FemaleEmployees / TotalEmployees * 100 : 0;
    }

    /// <summary>
    /// Employee report item view model
    /// نموذج عرض عنصر تقرير الموظف
    /// </summary>
    public class EmployeeReportItemViewModel
    {
        public Guid Id { get; set; }
        public string EmployeeCode { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string FullNameAr { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string PositionTitle { get; set; } = string.Empty;
        public DateTime HireDate { get; set; }
        public string EmploymentStatus { get; set; } = string.Empty;
        public decimal BaseSalary { get; set; }
        public string Email { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public int Age { get; set; }
        public int YearsOfService { get; set; }
    }

    /// <summary>
    /// Attendance reports view model
    /// نموذج عرض تقارير الحضور
    /// </summary>
    public class AttendanceReportsViewModel
    {
        public Guid CompanyId { get; set; }

        [Display(Name = "From Date / من تاريخ")]
        public DateTime FromDate { get; set; } = DateTime.Today.AddDays(-30);

        [Display(Name = "To Date / إلى تاريخ")]
        public DateTime ToDate { get; set; } = DateTime.Today;

        [Display(Name = "Employee / الموظف")]
        public Guid? EmployeeId { get; set; }

        [Display(Name = "Department / القسم")]
        public Guid? DepartmentId { get; set; }

        [Display(Name = "Report Type / نوع التقرير")]
        public string ReportType { get; set; } = "Summary";

        // Filter options
        public List<SelectListItem> Employees { get; set; } = new();
        public List<SelectListItem> Departments { get; set; } = new();
        public List<SelectListItem> ReportTypes { get; set; } = new()
        {
            new SelectListItem { Value = "Summary", Text = "Summary Report / تقرير ملخص" },
            new SelectListItem { Value = "Detailed", Text = "Detailed Report / تقرير مفصل" },
            new SelectListItem { Value = "Overtime", Text = "Overtime Report / تقرير الوقت الإضافي" },
            new SelectListItem { Value = "Punctuality", Text = "Punctuality Report / تقرير الالتزام بالمواعيد" },
            new SelectListItem { Value = "Absenteeism", Text = "Absenteeism Report / تقرير الغياب" }
        };

        // Report data
        public AttendanceStatisticsViewModel? Statistics { get; set; }
        public Dictionary<string, decimal> AttendanceByDepartment { get; set; } = new();
        public Dictionary<string, decimal> AttendanceByMonth { get; set; } = new();
        public Dictionary<string, int> AttendanceByDay { get; set; } = new();
    }

    /// <summary>
    /// Attendance statistics view model
    /// نموذج عرض إحصائيات الحضور
    /// </summary>
    public class AttendanceStatisticsViewModel
    {
        public int TotalWorkingDays { get; set; }
        public int TotalPresentDays { get; set; }
        public int TotalAbsentDays { get; set; }
        public int TotalLateDays { get; set; }
        public int TotalOvertimeDays { get; set; }

        public decimal TotalWorkingHours { get; set; }
        public decimal TotalOvertimeHours { get; set; }
        public decimal AverageWorkingHours { get; set; }

        public decimal AttendanceRate { get; set; }
        public decimal PunctualityRate { get; set; }
        public decimal OvertimeRate { get; set; }

        // Calculated properties
        public string AttendanceRateDisplay => $"{AttendanceRate:F1}%";
        public string PunctualityRateDisplay => $"{PunctualityRate:F1}%";
        public string OvertimeRateDisplay => $"{OvertimeRate:F1}%";
    }

    /// <summary>
    /// Leave reports view model
    /// نموذج عرض تقارير الإجازات
    /// </summary>
    public class LeaveReportsViewModel
    {
        public Guid CompanyId { get; set; }

        [Display(Name = "From Date / من تاريخ")]
        public DateTime FromDate { get; set; } = DateTime.Today.AddMonths(-3);

        [Display(Name = "To Date / إلى تاريخ")]
        public DateTime ToDate { get; set; } = DateTime.Today;

        [Display(Name = "Employee / الموظف")]
        public Guid? EmployeeId { get; set; }

        [Display(Name = "Department / القسم")]
        public Guid? DepartmentId { get; set; }

        [Display(Name = "Leave Type / نوع الإجازة")]
        public Guid? LeaveTypeId { get; set; }

        [Display(Name = "Report Type / نوع التقرير")]
        public string ReportType { get; set; } = "Summary";

        // Filter options
        public List<SelectListItem> Employees { get; set; } = new();
        public List<SelectListItem> Departments { get; set; } = new();
        public List<SelectListItem> LeaveTypes { get; set; } = new();
        public List<SelectListItem> ReportTypes { get; set; } = new()
        {
            new SelectListItem { Value = "Summary", Text = "Summary Report / تقرير ملخص" },
            new SelectListItem { Value = "Detailed", Text = "Detailed Report / تقرير مفصل" },
            new SelectListItem { Value = "Balances", Text = "Leave Balances / أرصدة الإجازات" },
            new SelectListItem { Value = "Usage", Text = "Leave Usage / استخدام الإجازات" },
            new SelectListItem { Value = "Trends", Text = "Leave Trends / اتجاهات الإجازات" }
        };

        // Report data
        public LeaveStatisticsViewModel? Statistics { get; set; }
        public Dictionary<string, int> LeavesByType { get; set; } = new();
        public Dictionary<string, int> LeavesByDepartment { get; set; } = new();
        public Dictionary<string, int> LeavesByMonth { get; set; } = new();
    }

    /// <summary>
    /// Leave statistics view model
    /// نموذج عرض إحصائيات الإجازات
    /// </summary>
    public class LeaveStatisticsViewModel
    {
        public int TotalLeaveRequests { get; set; }
        public int ApprovedLeaves { get; set; }
        public int PendingLeaves { get; set; }
        public int RejectedLeaves { get; set; }

        public decimal TotalLeaveDays { get; set; }
        public decimal AverageLeaveDays { get; set; }
        public decimal LeaveUtilizationRate { get; set; }

        // Calculated properties
        public decimal ApprovalRate => TotalLeaveRequests > 0 ? (decimal)ApprovedLeaves / TotalLeaveRequests * 100 : 0;
        public string ApprovalRateDisplay => $"{ApprovalRate:F1}%";
        public string LeaveUtilizationDisplay => $"{LeaveUtilizationRate:F1}%";
    }

    /// <summary>
    /// Payroll reports view model
    /// نموذج عرض تقارير كشوف المرتبات
    /// </summary>
    public class PayrollReportsViewModel
    {
        public Guid CompanyId { get; set; }

        [Display(Name = "From Month / من شهر")]
        public int FromMonth { get; set; } = DateTime.Today.Month;

        [Display(Name = "From Year / من سنة")]
        public int FromYear { get; set; } = DateTime.Today.Year;

        [Display(Name = "To Month / إلى شهر")]
        public int ToMonth { get; set; } = DateTime.Today.Month;

        [Display(Name = "To Year / إلى سنة")]
        public int ToYear { get; set; } = DateTime.Today.Year;

        [Display(Name = "Department / القسم")]
        public Guid? DepartmentId { get; set; }

        [Display(Name = "Report Type / نوع التقرير")]
        public string ReportType { get; set; } = "Summary";

        // Filter options
        public List<SelectListItem> Departments { get; set; } = new();
        public List<SelectListItem> ReportTypes { get; set; } = new()
        {
            new SelectListItem { Value = "Summary", Text = "Summary Report / تقرير ملخص" },
            new SelectListItem { Value = "Detailed", Text = "Detailed Report / تقرير مفصل" },
            new SelectListItem { Value = "Comparison", Text = "Period Comparison / مقارنة الفترات" },
            new SelectListItem { Value = "Deductions", Text = "Deductions Report / تقرير الخصومات" },
            new SelectListItem { Value = "Allowances", Text = "Allowances Report / تقرير البدلات" }
        };

        // Report data
        public PayrollStatisticsViewModel? Statistics { get; set; }
        public Dictionary<string, decimal> SalariesByDepartment { get; set; } = new();
        public Dictionary<string, decimal> SalariesByMonth { get; set; } = new();
        public Dictionary<string, decimal> DeductionsByType { get; set; } = new();
        public Dictionary<string, decimal> AllowancesByType { get; set; } = new();

        // Additional properties for compatibility
        public string? Department { get; set; }
        public decimal TotalSalaries { get; set; }
        public decimal TotalAllowances { get; set; }
        public decimal TotalDeductions { get; set; }
        public decimal NetSalaries { get; set; }
        public Dictionary<string, decimal> SalaryTrend { get; set; } = new();
        public Dictionary<string, decimal> AllowanceBreakdown { get; set; } = new();
        public Dictionary<string, decimal> DeductionBreakdown { get; set; } = new();
    }

    /// <summary>
    /// Payroll statistics view model
    /// نموذج عرض إحصائيات كشوف المرتبات
    /// </summary>
    public class PayrollStatisticsViewModel
    {
        public int TotalPayrolls { get; set; }
        public decimal TotalGrossSalary { get; set; }
        public decimal TotalNetSalary { get; set; }
        public decimal TotalDeductions { get; set; }
        public decimal TotalAllowances { get; set; }

        public decimal AverageGrossSalary { get; set; }
        public decimal AverageNetSalary { get; set; }
        public decimal DeductionPercentage { get; set; }

        // Calculated properties
        public string DeductionPercentageDisplay => $"{DeductionPercentage:F1}%";
    }

    /// <summary>
    /// Analytics view model
    /// نموذج عرض التحليلات
    /// </summary>
    public class AnalyticsViewModel
    {
        public Guid CompanyId { get; set; }

        // Key Performance Indicators
        public decimal EmployeeTurnoverRate { get; set; }
        public decimal AttendanceRate { get; set; }
        public decimal LeaveUtilizationRate { get; set; }
        public decimal AverageSalary { get; set; }
        public decimal ProductivityIndex { get; set; }

        // Trend data
        public Dictionary<string, decimal> EmployeeGrowthTrend { get; set; } = new();
        public Dictionary<string, decimal> AttendanceTrend { get; set; } = new();
        public Dictionary<string, decimal> SalaryTrend { get; set; } = new();
        public Dictionary<string, decimal> LeaveTrend { get; set; } = new();

        // Comparative data
        public Dictionary<string, decimal> DepartmentPerformance { get; set; } = new();
        public Dictionary<string, decimal> PositionAnalysis { get; set; } = new();

        // Predictions and insights
        public List<AnalyticsInsightViewModel> Insights { get; set; } = new();
        public List<AnalyticsPredictionViewModel> Predictions { get; set; } = new();
    }

    /// <summary>
    /// Analytics insight view model
    /// نموذج عرض رؤية تحليلية
    /// </summary>
    public class AnalyticsInsightViewModel
    {
        public string Title { get; set; } = string.Empty;
        public string TitleAr { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string DescriptionAr { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // Positive, Negative, Neutral
        public string Icon { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public string Unit { get; set; } = string.Empty;
    }

    /// <summary>
    /// Analytics prediction view model
    /// نموذج عرض توقع تحليلي
    /// </summary>
    public class AnalyticsPredictionViewModel
    {
        public string Title { get; set; } = string.Empty;
        public string TitleAr { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string DescriptionAr { get; set; } = string.Empty;
        public DateTime PredictionDate { get; set; }
        public decimal Confidence { get; set; }
        public Dictionary<string, decimal> PredictionData { get; set; } = new();
    }


}
