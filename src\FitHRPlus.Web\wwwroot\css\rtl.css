/* RTL (Right-to-Left) Support for Arabic */

/* Base RTL Styles */
.rtl {
  direction: rtl;
  text-align: right;
}

.rtl * {
  direction: rtl;
}

/* Text Alignment */
.rtl .text-left {
  text-align: right !important;
}

.rtl .text-right {
  text-align: left !important;
}

.rtl .text-start {
  text-align: right !important;
}

.rtl .text-end {
  text-align: left !important;
}

/* Margins and Padding */
.rtl .me-1 { margin-left: 0.25rem !important; margin-right: 0 !important; }
.rtl .me-2 { margin-left: 0.5rem !important; margin-right: 0 !important; }
.rtl .me-3 { margin-left: 1rem !important; margin-right: 0 !important; }
.rtl .me-4 { margin-left: 1.5rem !important; margin-right: 0 !important; }
.rtl .me-5 { margin-left: 3rem !important; margin-right: 0 !important; }

.rtl .ms-1 { margin-right: 0.25rem !important; margin-left: 0 !important; }
.rtl .ms-2 { margin-right: 0.5rem !important; margin-left: 0 !important; }
.rtl .ms-3 { margin-right: 1rem !important; margin-left: 0 !important; }
.rtl .ms-4 { margin-right: 1.5rem !important; margin-left: 0 !important; }
.rtl .ms-5 { margin-right: 3rem !important; margin-left: 0 !important; }

.rtl .ms-auto { margin-right: auto !important; margin-left: 0 !important; }
.rtl .me-auto { margin-left: auto !important; margin-right: 0 !important; }

/* Padding */
.rtl .pe-1 { padding-left: 0.25rem !important; padding-right: 0 !important; }
.rtl .pe-2 { padding-left: 0.5rem !important; padding-right: 0 !important; }
.rtl .pe-3 { padding-left: 1rem !important; padding-right: 0 !important; }
.rtl .pe-4 { padding-left: 1.5rem !important; padding-right: 0 !important; }
.rtl .pe-5 { padding-left: 3rem !important; padding-right: 0 !important; }

.rtl .ps-1 { padding-right: 0.25rem !important; padding-left: 0 !important; }
.rtl .ps-2 { padding-right: 0.5rem !important; padding-left: 0 !important; }
.rtl .ps-3 { padding-right: 1rem !important; padding-left: 0 !important; }
.rtl .ps-4 { padding-right: 1.5rem !important; padding-left: 0 !important; }
.rtl .ps-5 { padding-right: 3rem !important; padding-left: 0 !important; }

/* Flexbox */
.rtl .justify-content-start {
  justify-content: flex-end !important;
}

.rtl .justify-content-end {
  justify-content: flex-start !important;
}

/* Sidebar RTL */
.rtl .sidebar {
  right: 0;
  left: auto;
}

.rtl .sidebar.active {
  margin-right: -250px;
  margin-left: 0;
}

.rtl .sidebar ul li a i {
  margin-left: 10px;
  margin-right: 0;
}

/* Content RTL */
.rtl #content {
  margin-right: 0;
  margin-left: auto;
}

/* Navbar RTL */
.rtl .navbar-nav {
  margin-right: auto !important;
  margin-left: 0 !important;
}

.rtl .navbar-nav .nav-link {
  padding-right: 0.5rem;
  padding-left: 1rem;
}

/* Dropdown RTL */
.rtl .dropdown-menu {
  right: 0 !important;
  left: auto !important;
}

.rtl .dropdown-menu-right {
  right: auto !important;
  left: 0 !important;
}

/* Form Controls RTL */
.rtl .form-control {
  text-align: right;
}

.rtl .form-check {
  padding-right: 1.25em;
  padding-left: 0;
}

.rtl .form-check-input {
  margin-right: -1.25em;
  margin-left: 0;
}

.rtl .form-check-label {
  padding-right: 0.25rem;
  padding-left: 0;
}

/* Input Groups RTL */
.rtl .input-group > .form-control:not(:last-child) {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.rtl .input-group > .form-control:not(:first-child) {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

/* Buttons RTL */
.rtl .btn-group > .btn:not(:last-child) {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.rtl .btn-group > .btn:not(:first-child) {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

/* Tables RTL */
.rtl .table th,
.rtl .table td {
  text-align: right;
}

.rtl .table th:first-child,
.rtl .table td:first-child {
  text-align: right;
}

.rtl .table th:last-child,
.rtl .table td:last-child {
  text-align: left;
}

/* Pagination RTL */
.rtl .page-link {
  margin-right: -1px;
  margin-left: 0;
}

.rtl .page-item:first-child .page-link {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.rtl .page-item:last-child .page-link {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

/* Breadcrumb RTL */
.rtl .breadcrumb-item + .breadcrumb-item::before {
  content: "\\";
  transform: scaleX(-1);
}

/* Alert RTL */
.rtl .alert-dismissible .btn-close {
  left: 0;
  right: auto;
}

/* Modal RTL */
.rtl .modal-header .btn-close {
  margin: -0.5rem 0 -0.5rem auto;
}

/* Carousel RTL */
.rtl .carousel-control-prev {
  right: 0;
  left: auto;
}

.rtl .carousel-control-next {
  left: 0;
  right: auto;
}

.rtl .carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='m11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3e");
}

.rtl .carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='m4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

/* Custom RTL Utilities */
.rtl .float-start {
  float: right !important;
}

.rtl .float-end {
  float: left !important;
}

/* Border Radius RTL */
.rtl .rounded-start {
  border-top-right-radius: 0.375rem !important;
  border-bottom-right-radius: 0.375rem !important;
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.rtl .rounded-end {
  border-top-left-radius: 0.375rem !important;
  border-bottom-left-radius: 0.375rem !important;
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

/* Arabic Font Improvements */
.rtl {
  font-family: 'Segoe UI', 'Tahoma', 'Arial', 'Helvetica Neue', sans-serif;
}

.rtl h1, .rtl h2, .rtl h3, .rtl h4, .rtl h5, .rtl h6 {
  font-weight: 600;
}

.rtl .sidebar ul li a {
  font-weight: 500;
}

/* Responsive RTL */
@media (max-width: 768px) {
  .rtl .sidebar {
    margin-right: -250px;
    margin-left: 0;
  }
  
  .rtl .sidebar.active {
    margin-right: 0;
  }
}
