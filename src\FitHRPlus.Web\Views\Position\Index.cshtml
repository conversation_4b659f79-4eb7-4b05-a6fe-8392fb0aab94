@model FitHRPlus.Web.Models.Position.PositionListViewModel
@{
    ViewData["Title"] = "Positions / المناصب";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-briefcase text-primary me-2"></i>
                        Positions / المناصب
                    </h2>
                    <p class="text-muted mb-0">Manage company positions / إدارة مناصب الشركة</p>
                </div>
                <div>
                    <a href="@Url.Action("Create")" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        New Position / منصب جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.TotalCount</h4>
                            <p class="mb-0">Total Positions</p>
                            <small>إجمالي المناصب</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-briefcase fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.Positions.Count(p => p.IsActive)</h4>
                            <p class="mb-0">Active Positions</p>
                            <small>المناصب النشطة</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.Positions.Sum(p => p.EmployeeCount)</h4>
                            <p class="mb-0">Total Employees</p>
                            <small>إجمالي الموظفين</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.Positions.GroupBy(p => p.DepartmentName).Count()</h4>
                            <p class="mb-0">Departments</p>
                            <small>الأقسام</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-building fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="searchTerm" class="form-label">Search / البحث</label>
                            <input type="text" class="form-control" id="searchTerm" name="searchTerm" 
                                   value="@Model.SearchTerm" placeholder="Search positions...">
                        </div>
                        <div class="col-md-4">
                            <label for="departmentId" class="form-label">Department / القسم</label>
                            <select class="form-select" id="departmentId" name="departmentId">
                                <option value="">All Departments / جميع الأقسام</option>
                                @foreach (var dept in Model.Positions.GroupBy(p => new { p.DepartmentName }).Select(g => g.Key))
                                {
                                    <option value="@dept.DepartmentName" selected="@(Model.DepartmentId?.ToString() == dept.DepartmentName)">
                                        @dept.DepartmentName
                                    </option>
                                }
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>
                                    Search / بحث
                                </button>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <a href="@Url.Action("Index")" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>
                                    Clear / مسح
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Positions Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        Positions List / قائمة المناصب
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Positions.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Position / المنصب</th>
                                        <th>Department / القسم</th>
                                        <th>Salary Range / نطاق الراتب</th>
                                        <th>Employees / الموظفين</th>
                                        <th>Status / الحالة</th>
                                        <th>Created / تاريخ الإنشاء</th>
                                        <th>Actions / الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var position in Model.Positions)
                                    {
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong>@position.Title</strong>
                                                    <br>
                                                    <small class="text-muted">@position.TitleAr</small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">@position.DepartmentName</span>
                                            </td>
                                            <td>
                                                @if (position.MinSalary.HasValue || position.MaxSalary.HasValue)
                                                {
                                                    <div>
                                                        @if (position.MinSalary.HasValue && position.MaxSalary.HasValue)
                                                        {
                                                            <span class="text-success">@position.MinSalary.Value.ToString("C")</span>
                                                            <span> - </span>
                                                            <span class="text-success">@position.MaxSalary.Value.ToString("C")</span>
                                                        }
                                                        else if (position.MinSalary.HasValue)
                                                        {
                                                            <span class="text-success">From @position.MinSalary.Value.ToString("C")</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="text-success">Up to @position.MaxSalary.Value.ToString("C")</span>
                                                        }
                                                    </div>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Not specified</span>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">@position.EmployeeCount</span>
                                            </td>
                                            <td>
                                                @if (position.IsActive)
                                                {
                                                    <span class="badge bg-success">Active / نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">Inactive / غير نشط</span>
                                                }
                                            </td>
                                            <td>
                                                <small>@position.CreatedAt.ToString("dd/MM/yyyy")</small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="@Url.Action("Details", new { id = position.Id })" 
                                                       class="btn btn-sm btn-outline-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="@Url.Action("Edit", new { id = position.Id })" 
                                                       class="btn btn-sm btn-outline-warning" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="confirmDelete('@position.Id', '@position.Title')" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if (Model.TotalPages > 1)
                        {
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    @if (Model.HasPreviousPage)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("Index", new { pageNumber = Model.PageNumber - 1, searchTerm = Model.SearchTerm, departmentId = Model.DepartmentId })">
                                                Previous / السابق
                                            </a>
                                        </li>
                                    }

                                    @for (int i = Math.Max(1, Model.PageNumber - 2); i <= Math.Min(Model.TotalPages, Model.PageNumber + 2); i++)
                                    {
                                        <li class="page-item @(i == Model.PageNumber ? "active" : "")">
                                            <a class="page-link" href="@Url.Action("Index", new { pageNumber = i, searchTerm = Model.SearchTerm, departmentId = Model.DepartmentId })">@i</a>
                                        </li>
                                    }

                                    @if (Model.HasNextPage)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("Index", new { pageNumber = Model.PageNumber + 1, searchTerm = Model.SearchTerm, departmentId = Model.DepartmentId })">
                                                Next / التالي
                                            </a>
                                        </li>
                                    }
                                </ul>
                            </nav>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No positions found / لا توجد مناصب</h5>
                            <p class="text-muted">Start by creating your first position / ابدأ بإنشاء أول منصب</p>
                            <a href="@Url.Action("Create")" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                Create Position / إنشاء منصب
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete / تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this position?</p>
                <p>هل أنت متأكد من حذف هذا المنصب؟</p>
                <p><strong id="positionName"></strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel / إلغاء</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">Delete / حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(id, name) {
            document.getElementById('positionName').textContent = name;
            document.getElementById('deleteForm').action = '@Url.Action("Delete")/' + id;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
}
