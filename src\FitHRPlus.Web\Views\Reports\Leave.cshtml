@model FitHRPlus.Web.Models.Reports.LeaveReportsViewModel
@{
    ViewData["Title"] = "تقارير الإجازات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        تقارير الإجازات
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Filter Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-filter me-2"></i>
                                        المرشحات
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <form method="get" class="row g-3">
                                        <div class="col-md-3">
                                            <label for="fromDate" class="form-label">من تاريخ</label>
                                            <input type="date" class="form-control" id="fromDate" name="fromDate" value="@Model.FromDate.ToString("yyyy-MM-dd")" />
                                        </div>
                                        <div class="col-md-3">
                                            <label for="toDate" class="form-label">إلى تاريخ</label>
                                            <input type="date" class="form-control" id="toDate" name="toDate" value="@Model.ToDate.ToString("yyyy-MM-dd")" />
                                        </div>
                                        <div class="col-md-3">
                                            <label for="leaveType" class="form-label">نوع الإجازة</label>
                                            <select class="form-select" id="leaveType" name="leaveType">
                                                <option value="">جميع الأنواع</option>
                                                <option value="annual">إجازة سنوية</option>
                                                <option value="sick">إجازة مرضية</option>
                                                <option value="emergency">إجازة طارئة</option>
                                                <option value="maternity">إجازة أمومة</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="status" class="form-label">الحالة</label>
                                            <select class="form-select" id="status" name="status">
                                                <option value="">جميع الحالات</option>
                                                <option value="pending">معلقة</option>
                                                <option value="approved">موافق عليها</option>
                                                <option value="rejected">مرفوضة</option>
                                                <option value="cancelled">ملغاة</option>
                                            </select>
                                        </div>
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-warning">
                                                <i class="fas fa-search me-1"></i>
                                                إنشاء التقرير
                                            </button>
                                            <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                                                <i class="fas fa-undo me-1"></i>
                                                إعادة تعيين
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">إجمالي الطلبات</h6>
                                            <h3 class="mb-0">45</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-calendar-alt fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">الطلبات المعتمدة</h6>
                                            <h3 class="mb-0">38</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-check-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">الطلبات المعلقة</h6>
                                            <h3 class="mb-0">5</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-clock fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">الطلبات المرفوضة</h6>
                                            <h3 class="mb-0">2</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-times-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Leave Data Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الموظف</th>
                                    <th>نوع الإجازة</th>
                                    <th>تاريخ البداية</th>
                                    <th>تاريخ النهاية</th>
                                    <th>عدد الأيام</th>
                                    <th>السبب</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الطلب</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="fas fa-info-circle me-2"></i>
                                        لا توجد بيانات إجازات متاحة للفترة المحددة
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Charts Section -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">توزيع الإجازات حسب النوع</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="leaveTypeChart" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">اتجاه الإجازات الشهري</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="leaveTrendChart" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Leave Balance Summary -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">ملخص أرصدة الإجازات</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>نوع الإجازة</th>
                                                    <th>إجمالي المخصص</th>
                                                    <th>المستخدم</th>
                                                    <th>المتبقي</th>
                                                    <th>النسبة المئوية</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>إجازة سنوية</td>
                                                    <td>21 يوم</td>
                                                    <td>15 يوم</td>
                                                    <td>6 أيام</td>
                                                    <td>
                                                        <div class="progress">
                                                            <div class="progress-bar bg-success" style="width: 71%">71%</div>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>إجازة مرضية</td>
                                                    <td>7 أيام</td>
                                                    <td>2 يوم</td>
                                                    <td>5 أيام</td>
                                                    <td>
                                                        <div class="progress">
                                                            <div class="progress-bar bg-warning" style="width: 29%">29%</div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button class="btn btn-success" onclick="exportToExcel()">
                                    <i class="fas fa-file-excel me-1"></i>
                                    تصدير إلى Excel
                                </button>
                                <button class="btn btn-danger" onclick="exportToPDF()">
                                    <i class="fas fa-file-pdf me-1"></i>
                                    تصدير إلى PDF
                                </button>
                                <button class="btn btn-primary" onclick="printReport()">
                                    <i class="fas fa-print me-1"></i>
                                    طباعة
                                </button>
                                <button class="btn btn-info" onclick="generateBalanceReport()">
                                    <i class="fas fa-balance-scale me-1"></i>
                                    تقرير الأرصدة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        function exportToExcel() {
            alert('سيتم تنفيذ وظيفة تصدير Excel');
        }

        function exportToPDF() {
            alert('سيتم تنفيذ وظيفة تصدير PDF');
        }

        function printReport() {
            window.print();
        }

        function resetFilters() {
            document.querySelector('form').reset();
        }

        function generateBalanceReport() {
            alert('سيتم إنشاء تقرير الأرصدة');
        }

        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Placeholder for chart initialization
            console.log('Leave charts will be initialized here');
        });
    </script>
}
