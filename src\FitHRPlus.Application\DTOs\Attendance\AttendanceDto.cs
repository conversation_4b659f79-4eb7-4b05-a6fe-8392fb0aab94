using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Application.DTOs.Attendance
{
    /// <summary>
    /// Attendance record data transfer object
    /// كائنة نقل بيانات سجل الحضور
    /// </summary>
    public class AttendanceDto
    {
        /// <summary>
        /// Attendance record ID
        /// معرف سجل الحضور
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Employee ID
        /// معرف الموظف
        /// </summary>
        [Required(ErrorMessage = "Employee is required")]
        public Guid EmployeeId { get; set; }

        /// <summary>
        /// Employee name
        /// اسم الموظف
        /// </summary>
        public string EmployeeName { get; set; } = string.Empty;

        /// <summary>
        /// Employee number
        /// رقم الموظف
        /// </summary>
        public string EmployeeNumber { get; set; } = string.Empty;

        /// <summary>
        /// Department name
        /// اسم القسم
        /// </summary>
        public string? DepartmentName { get; set; }

        /// <summary>
        /// Position title
        /// مسمى المنصب
        /// </summary>
        public string? PositionTitle { get; set; }

        /// <summary>
        /// Attendance date
        /// تاريخ الحضور
        /// </summary>
        [Required(ErrorMessage = "Date is required")]
        public DateTime Date { get; set; }

        /// <summary>
        /// Check-in time
        /// وقت الحضور
        /// </summary>
        public DateTime? CheckInTime { get; set; }

        /// <summary>
        /// Check-out time
        /// وقت الانصراف
        /// </summary>
        public DateTime? CheckOutTime { get; set; }

        /// <summary>
        /// Break start time
        /// وقت بداية الاستراحة
        /// </summary>
        public DateTime? BreakStartTime { get; set; }

        /// <summary>
        /// Break end time
        /// وقت نهاية الاستراحة
        /// </summary>
        public DateTime? BreakEndTime { get; set; }

        /// <summary>
        /// Total working hours
        /// إجمالي ساعات العمل
        /// </summary>
        public TimeSpan? WorkingHours { get; set; }

        /// <summary>
        /// Total break duration
        /// إجمالي مدة الاستراحة
        /// </summary>
        public TimeSpan? BreakDuration { get; set; }

        /// <summary>
        /// Overtime hours
        /// ساعات العمل الإضافي
        /// </summary>
        public TimeSpan? OvertimeHours { get; set; }

        /// <summary>
        /// Late arrival minutes
        /// دقائق التأخير
        /// </summary>
        public int LateMinutes { get; set; }

        /// <summary>
        /// Early departure minutes
        /// دقائق المغادرة المبكرة
        /// </summary>
        public int EarlyDepartureMinutes { get; set; }

        /// <summary>
        /// Attendance status
        /// حالة الحضور
        /// </summary>
        public string AttendanceStatus { get; set; } = "Present";

        /// <summary>
        /// Check-in location (GPS coordinates or location name)
        /// موقع الحضور (إحداثيات GPS أو اسم الموقع)
        /// </summary>
        public string? CheckInLocation { get; set; }

        /// <summary>
        /// Check-out location (GPS coordinates or location name)
        /// موقع الانصراف (إحداثيات GPS أو اسم الموقع)
        /// </summary>
        public string? CheckOutLocation { get; set; }

        /// <summary>
        /// Check-in device/method
        /// جهاز/طريقة الحضور
        /// </summary>
        public string? CheckInDevice { get; set; }

        /// <summary>
        /// Check-out device/method
        /// جهاز/طريقة الانصراف
        /// </summary>
        public string? CheckOutDevice { get; set; }

        /// <summary>
        /// Notes or comments
        /// ملاحظات أو تعليقات
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// Whether the record is approved
        /// ما إذا كان السجل معتمد
        /// </summary>
        public bool IsApproved { get; set; }

        /// <summary>
        /// Approved by user ID
        /// معرف المستخدم المعتمد
        /// </summary>
        public Guid? ApprovedBy { get; set; }

        /// <summary>
        /// Approval date
        /// تاريخ الاعتماد
        /// </summary>
        public DateTime? ApprovalDate { get; set; }

        /// <summary>
        /// Creation date
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Last update date
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Created by user ID
        /// معرف المستخدم المنشئ
        /// </summary>
        public Guid CreatedBy { get; set; }

        /// <summary>
        /// Updated by user ID
        /// معرف المستخدم المحدث
        /// </summary>
        public Guid UpdatedBy { get; set; }

        // Computed properties
        /// <summary>
        /// Whether employee is present
        /// ما إذا كان الموظف حاضر
        /// </summary>
        public bool IsPresent => AttendanceStatus == "Present" && CheckInTime.HasValue;

        /// <summary>
        /// Whether employee is late
        /// ما إذا كان الموظف متأخر
        /// </summary>
        public bool IsLate => LateMinutes > 0;

        /// <summary>
        /// Whether employee left early
        /// ما إذا كان الموظف غادر مبكراً
        /// </summary>
        public bool IsEarlyDeparture => EarlyDepartureMinutes > 0;

        /// <summary>
        /// Whether employee has overtime
        /// ما إذا كان الموظف لديه عمل إضافي
        /// </summary>
        public bool HasOvertime => OvertimeHours.HasValue && OvertimeHours.Value.TotalMinutes > 0;

        /// <summary>
        /// Working hours as formatted string
        /// ساعات العمل كنص منسق
        /// </summary>
        public string WorkingHoursFormatted => WorkingHours?.ToString(@"hh\:mm") ?? "00:00";

        /// <summary>
        /// Overtime hours as formatted string
        /// ساعات العمل الإضافي كنص منسق
        /// </summary>
        public string OvertimeHoursFormatted => OvertimeHours?.ToString(@"hh\:mm") ?? "00:00";

        /// <summary>
        /// Break duration as formatted string
        /// مدة الاستراحة كنص منسق
        /// </summary>
        public string BreakDurationFormatted => BreakDuration?.ToString(@"hh\:mm") ?? "00:00";
    }

    /// <summary>
    /// Attendance summary DTO for dashboard and reports
    /// كائنة نقل بيانات ملخص الحضور للوحة التحكم والتقارير
    /// </summary>
    public class AttendanceSummaryDto
    {
        /// <summary>
        /// Employee ID
        /// معرف الموظف
        /// </summary>
        public Guid EmployeeId { get; set; }

        /// <summary>
        /// Employee name
        /// اسم الموظف
        /// </summary>
        public string EmployeeName { get; set; } = string.Empty;

        /// <summary>
        /// Employee number
        /// رقم الموظف
        /// </summary>
        public string EmployeeNumber { get; set; } = string.Empty;

        /// <summary>
        /// Department name
        /// اسم القسم
        /// </summary>
        public string? DepartmentName { get; set; }

        /// <summary>
        /// Total working days
        /// إجمالي أيام العمل
        /// </summary>
        public int TotalWorkingDays { get; set; }

        /// <summary>
        /// Present days
        /// أيام الحضور
        /// </summary>
        public int PresentDays { get; set; }

        /// <summary>
        /// Absent days
        /// أيام الغياب
        /// </summary>
        public int AbsentDays { get; set; }

        /// <summary>
        /// Late days
        /// أيام التأخير
        /// </summary>
        public int LateDays { get; set; }

        /// <summary>
        /// Early departure days
        /// أيام المغادرة المبكرة
        /// </summary>
        public int EarlyDepartureDays { get; set; }

        /// <summary>
        /// Total working hours
        /// إجمالي ساعات العمل
        /// </summary>
        public TimeSpan TotalWorkingHours { get; set; }

        /// <summary>
        /// Total overtime hours
        /// إجمالي ساعات العمل الإضافي
        /// </summary>
        public TimeSpan TotalOvertimeHours { get; set; }

        /// <summary>
        /// Total late minutes
        /// إجمالي دقائق التأخير
        /// </summary>
        public int TotalLateMinutes { get; set; }

        /// <summary>
        /// Total early departure minutes
        /// إجمالي دقائق المغادرة المبكرة
        /// </summary>
        public int TotalEarlyDepartureMinutes { get; set; }

        /// <summary>
        /// Attendance percentage
        /// نسبة الحضور
        /// </summary>
        public double AttendancePercentage => TotalWorkingDays > 0 ? (double)PresentDays / TotalWorkingDays * 100 : 0;

        /// <summary>
        /// Punctuality percentage (on-time arrivals)
        /// نسبة الالتزام بالمواعيد
        /// </summary>
        public double PunctualityPercentage => PresentDays > 0 ? (double)(PresentDays - LateDays) / PresentDays * 100 : 0;

        /// <summary>
        /// Average working hours per day
        /// متوسط ساعات العمل يومياً
        /// </summary>
        public TimeSpan AverageWorkingHours => PresentDays > 0 ? 
            TimeSpan.FromTicks(TotalWorkingHours.Ticks / PresentDays) : TimeSpan.Zero;
    }
}
