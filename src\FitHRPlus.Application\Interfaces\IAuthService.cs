using FitHRPlus.Application.DTOs.Auth;
using FitHRPlus.Application.Common;

namespace FitHRPlus.Application.Interfaces
{
    /// <summary>
    /// Authentication service interface
    /// واجهة خدمة المصادقة
    /// </summary>
    public interface IAuthService
    {
        /// <summary>
        /// Authenticate user with username/email and password
        /// مصادقة المستخدم باستخدام اسم المستخدم/البريد الإلكتروني وكلمة المرور
        /// </summary>
        /// <param name="request">Login request</param>
        /// <returns>Login response with token and user info</returns>
        Task<ServiceResult<LoginResponseDto>> LoginAsync(LoginRequestDto request);

        /// <summary>
        /// Register a new user
        /// تسجيل مستخدم جديد
        /// </summary>
        /// <param name="request">Registration request</param>
        /// <returns>Registration result</returns>
        Task<ServiceResult<LoginResponseDto>> RegisterAsync(RegisterRequestDto request);

        /// <summary>
        /// Refresh access token using refresh token
        /// تحديث رمز الوصول باستخدام رمز التحديث
        /// </summary>
        /// <param name="refreshToken">Refresh token</param>
        /// <returns>New access token</returns>
        Task<ServiceResult<LoginResponseDto>> RefreshTokenAsync(string refreshToken);

        /// <summary>
        /// Logout user and invalidate tokens
        /// تسجيل خروج المستخدم وإبطال الرموز
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="refreshToken">Refresh token to invalidate</param>
        /// <returns>Logout result</returns>
        Task<ServiceResult<bool>> LogoutAsync(Guid userId, string refreshToken);

        /// <summary>
        /// Change user password
        /// تغيير كلمة مرور المستخدم
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="currentPassword">Current password</param>
        /// <param name="newPassword">New password</param>
        /// <returns>Password change result</returns>
        Task<ServiceResult<bool>> ChangePasswordAsync(Guid userId, string currentPassword, string newPassword);

        /// <summary>
        /// Send password reset email
        /// إرسال بريد إلكتروني لإعادة تعيين كلمة المرور
        /// </summary>
        /// <param name="email">User email</param>
        /// <returns>Reset email send result</returns>
        Task<ServiceResult<bool>> ForgotPasswordAsync(string email);

        /// <summary>
        /// Reset password using reset token
        /// إعادة تعيين كلمة المرور باستخدام رمز الإعادة
        /// </summary>
        /// <param name="email">User email</param>
        /// <param name="resetToken">Reset token</param>
        /// <param name="newPassword">New password</param>
        /// <returns>Password reset result</returns>
        Task<ServiceResult<bool>> ResetPasswordAsync(string email, string resetToken, string newPassword);

        /// <summary>
        /// Verify email address
        /// التحقق من البريد الإلكتروني
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="verificationToken">Verification token</param>
        /// <returns>Email verification result</returns>
        Task<ServiceResult<bool>> VerifyEmailAsync(Guid userId, string verificationToken);

        /// <summary>
        /// Send email verification
        /// إرسال التحقق من البريد الإلكتروني
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Verification email send result</returns>
        Task<ServiceResult<bool>> SendEmailVerificationAsync(Guid userId);

        /// <summary>
        /// Enable two-factor authentication
        /// تفعيل المصادقة الثنائية
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Two-factor setup result with QR code</returns>
        Task<ServiceResult<TwoFactorSetupDto>> EnableTwoFactorAsync(Guid userId);

        /// <summary>
        /// Verify and confirm two-factor authentication setup
        /// التحقق من وتأكيد إعداد المصادقة الثنائية
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="verificationCode">Verification code from authenticator app</param>
        /// <returns>Two-factor confirmation result</returns>
        Task<ServiceResult<bool>> ConfirmTwoFactorAsync(Guid userId, string verificationCode);

        /// <summary>
        /// Disable two-factor authentication
        /// تعطيل المصادقة الثنائية
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="password">User password for confirmation</param>
        /// <returns>Two-factor disable result</returns>
        Task<ServiceResult<bool>> DisableTwoFactorAsync(Guid userId, string password);
    }

    /// <summary>
    /// Two-factor authentication setup DTO
    /// كائنة نقل بيانات إعداد المصادقة الثنائية
    /// </summary>
    public class TwoFactorSetupDto
    {
        /// <summary>
        /// Secret key for authenticator app
        /// المفتاح السري لتطبيق المصادقة
        /// </summary>
        public string SecretKey { get; set; } = string.Empty;

        /// <summary>
        /// QR code as base64 image for easy setup
        /// رمز QR كصورة base64 للإعداد السهل
        /// </summary>
        public string QrCodeImage { get; set; } = string.Empty;

        /// <summary>
        /// Manual entry key (formatted for user input)
        /// مفتاح الإدخال اليدوي (منسق لإدخال المستخدم)
        /// </summary>
        public string ManualEntryKey { get; set; } = string.Empty;

        /// <summary>
        /// Backup codes for account recovery
        /// رموز النسخ الاحتياطي لاستعادة الحساب
        /// </summary>
        public List<string> BackupCodes { get; set; } = new();
    }
}
