# مواصفات التكامل مع النظامين السعودي والمصري
## Saudi-Egypt Integration Specifications

## 1. نظرة عامة على التكامل المزدوج
### Dual System Integration Overview

### 1.1 الهدف الاستراتيجي
تطوير نظام HRMS موحد يدعم القوانين والأنظمة في كل من:
- **مصر:** السوق الأساسي والمنطلق
- **السعودية:** السوق الثاني للتوسع الإقليمي
- **إمكانية التوسع:** لدول الخليج الأخرى لاحقاً

### 1.2 المبادئ الأساسية للتكامل
```mermaid
graph TB
    subgraph "Core System - النظام الأساسي"
        A[Unified Database<br/>قاعدة بيانات موحدة]
        B[Multi-Country Engine<br/>محرك متعدد البلدان]
        C[Localization Framework<br/>إطار التوطين]
    end
    
    subgraph "Egypt Module - الوحدة المصرية"
        D[Egyptian Labor Law<br/>قانون العمل المصري]
        E[Egyptian Tax System<br/>النظام الضريبي المصري]
        F[Egyptian Banks<br/>البنوك المصرية]
        G[Egyptian Government APIs<br/>APIs الحكومة المصرية]
    end
    
    subgraph "Saudi Module - الوحدة السعودية"
        H[Saudi Labor Law<br/>قانون العمل السعودي]
        I[ZATCA Integration<br/>تكامل الزكاة والضريبة]
        J[GOSI Integration<br/>تكامل التأمينات الاجتماعية]
        K[Absher Integration<br/>تكامل أبشر]
        L[Saudi Banks<br/>البنوك السعودية]
    end
    
    B --> D
    B --> H
    C --> E
    C --> I
    A --> F
    A --> L
```

---

## 2. التكامل مع الأنظمة السعودية
### Saudi Systems Integration

### 2.1 قانون العمل السعودي
#### Saudi Labor Law Compliance

**ساعات العمل:**
- 8 ساعات يومياً، 48 ساعة أسبوعياً
- 6 ساعات في رمضان للمسلمين
- الجمعة عطلة رسمية

**الإجازات السنوية:**
- 21 يوم للموظف الذي أمضى سنة واحدة
- 30 يوم للموظف الذي أمضى 5 سنوات
- إجازة الحج: 10 أيام مدفوعة مرة واحدة

**إجازة الأمومة:**
- 10 أسابيع مدفوعة الأجر
- إمكانية تمديد 4 أسابيع إضافية بدون راتب

```sql
-- جدول قوانين العمل حسب البلد
CREATE TABLE CountryLaborLaws (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    CountryCode NVARCHAR(3) NOT NULL, -- EG, SA, AE, etc.
    LawType NVARCHAR(50) NOT NULL, -- WorkingHours, Leave, Overtime
    LawName NVARCHAR(200) NOT NULL,
    LawNameAr NVARCHAR(200) NOT NULL,
    LawValue NVARCHAR(500), -- JSON configuration
    EffectiveDate DATE NOT NULL,
    ExpiryDate DATE,
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE()
);

-- بيانات أولية للقوانين السعودية
INSERT INTO CountryLaborLaws VALUES
(NEWID(), 'SA', 'WorkingHours', 'Standard Working Hours', 'ساعات العمل المعتادة', '{"daily": 8, "weekly": 48, "ramadan": 6}', '2023-01-01', NULL, 1, GETUTCDATE()),
(NEWID(), 'SA', 'AnnualLeave', 'Annual Leave Entitlement', 'استحقاق الإجازة السنوية', '{"year1": 21, "year5plus": 30}', '2023-01-01', NULL, 1, GETUTCDATE()),
(NEWID(), 'SA', 'MaternityLeave', 'Maternity Leave', 'إجازة الأمومة', '{"paid_weeks": 10, "unpaid_weeks": 4}', '2023-01-01', NULL, 1, GETUTCDATE());
```

### 2.2 التكامل مع هيئة الزكاة والضريبة والجمارك (ZATCA)
#### ZATCA Integration

**الضريبة المضافة (VAT):**
- معدل 15% على معظم السلع والخدمات
- إعفاءات للخدمات الطبية والتعليمية

**ضريبة الدخل:**
- 20% على الشركات الأجنبية
- إعفاء للشركات السعودية ودول الخليج

```csharp
// خدمة التكامل مع ZATCA
public class ZATCAIntegrationService
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    
    public async Task<ZATCAResponse> SubmitVATReturn(VATReturnModel vatReturn)
    {
        var endpoint = _configuration["ZATCA:VATReturnEndpoint"];
        var apiKey = _configuration["ZATCA:ApiKey"];
        
        var request = new HttpRequestMessage(HttpMethod.Post, endpoint)
        {
            Headers = { Authorization = new AuthenticationHeaderValue("Bearer", apiKey) },
            Content = JsonContent.Create(vatReturn)
        };
        
        var response = await _httpClient.SendAsync(request);
        return await response.Content.ReadFromJsonAsync<ZATCAResponse>();
    }
    
    public async Task<TaxCalculationResult> CalculateTax(decimal amount, string taxType, string countryCode)
    {
        return countryCode switch
        {
            "SA" => CalculateSaudiTax(amount, taxType),
            "EG" => CalculateEgyptianTax(amount, taxType),
            _ => throw new NotSupportedException($"Country {countryCode} not supported")
        };
    }
}
```

### 2.3 التكامل مع المؤسسة العامة للتأمينات الاجتماعية (GOSI)
#### GOSI Integration

**اشتراكات التأمينات الاجتماعية:**
- الموظف السعودي: 10% من الراتب
- صاحب العمل: 12% من الراتب
- الموظف غير السعودي: 2% (إصابات العمل فقط)

```sql
-- جدول معدلات التأمينات الاجتماعية
CREATE TABLE SocialInsuranceRates (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    CountryCode NVARCHAR(3) NOT NULL,
    EmployeeType NVARCHAR(50) NOT NULL, -- Citizen, NonCitizen
    EmployeeRate DECIMAL(5,2) NOT NULL,
    EmployerRate DECIMAL(5,2) NOT NULL,
    MaxSalary DECIMAL(18,2),
    EffectiveDate DATE NOT NULL,
    IsActive BIT DEFAULT 1
);

-- بيانات السعودية
INSERT INTO SocialInsuranceRates VALUES
(NEWID(), 'SA', 'Citizen', 10.00, 12.00, 45000, '2023-01-01', 1),
(NEWID(), 'SA', 'NonCitizen', 2.00, 2.00, 45000, '2023-01-01', 1);

-- بيانات مصر
INSERT INTO SocialInsuranceRates VALUES
(NEWID(), 'EG', 'Citizen', 14.00, 26.00, 7800, '2023-01-01', 1);
```

### 2.4 التكامل مع منصة أبشر
#### Absher Platform Integration

**الخدمات المتاحة:**
- التحقق من هوية الموظف
- استخراج تصاريح العمل
- تجديد الإقامات
- الاستعلام عن المخالفات

```csharp
public class AbsherIntegrationService
{
    public async Task<EmployeeVerificationResult> VerifyEmployee(string nationalId, string iqamaNumber)
    {
        var endpoint = "https://api.absher.sa/verify-employee";
        var request = new EmployeeVerificationRequest
        {
            NationalId = nationalId,
            IqamaNumber = iqamaNumber,
            RequestId = Guid.NewGuid().ToString()
        };
        
        // تنفيذ الطلب مع Absher API
        var response = await _httpClient.PostAsJsonAsync(endpoint, request);
        return await response.Content.ReadFromJsonAsync<EmployeeVerificationResult>();
    }
}
```

### 2.5 التكامل مع البنوك السعودية
#### Saudi Banks Integration

**البنوك الرئيسية:**
- البنك الأهلي السعودي (SNB)
- بنك الراجحي
- بنك ساب (SAIB)
- البنك السعودي الفرنسي
- بنك الرياض

```sql
-- جدول البنوك حسب البلد
CREATE TABLE CountryBanks (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    CountryCode NVARCHAR(3) NOT NULL,
    BankCode NVARCHAR(10) NOT NULL,
    BankName NVARCHAR(200) NOT NULL,
    BankNameAr NVARCHAR(200) NOT NULL,
    SwiftCode NVARCHAR(11),
    PayrollFileFormat NVARCHAR(50), -- SDD, WPS, etc.
    IsActive BIT DEFAULT 1
);

-- البنوك السعودية
INSERT INTO CountryBanks VALUES
(NEWID(), 'SA', '10', 'Saudi National Bank', 'البنك الأهلي السعودي', 'NCBKSAJE', 'WPS', 1),
(NEWID(), 'SA', '80', 'Al Rajhi Bank', 'بنك الراجحي', 'RJHISARI', 'WPS', 1),
(NEWID(), 'SA', '15', 'SAIB', 'البنك السعودي البريطاني', 'SIBCSARI', 'WPS', 1);

-- البنوك المصرية
INSERT INTO CountryBanks VALUES
(NEWID(), 'EG', 'NBE', 'National Bank of Egypt', 'البنك الأهلي المصري', 'NBEGEGCX', 'SDD', 1),
(NEWID(), 'EG', 'CIB', 'Commercial International Bank', 'البنك التجاري الدولي', 'CIBEEGCX', 'SDD', 1);
```

---

## 3. نظام إدارة البلدان المتعددة
### Multi-Country Management System

### 3.1 هيكل قاعدة البيانات المحدث
```sql
-- جدول البلدان المدعومة
CREATE TABLE SupportedCountries (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    CountryCode NVARCHAR(3) UNIQUE NOT NULL,
    CountryName NVARCHAR(100) NOT NULL,
    CountryNameAr NVARCHAR(100) NOT NULL,
    Currency NVARCHAR(3) NOT NULL,
    TimeZone NVARCHAR(50) NOT NULL,
    DateFormat NVARCHAR(20) NOT NULL,
    NumberFormat NVARCHAR(20) NOT NULL,
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE()
);

-- البيانات الأولية
INSERT INTO SupportedCountries VALUES
(NEWID(), 'EG', 'Egypt', 'مصر', 'EGP', 'Africa/Cairo', 'dd/MM/yyyy', '#,##0.00', 1, GETUTCDATE()),
(NEWID(), 'SA', 'Saudi Arabia', 'السعودية', 'SAR', 'Asia/Riyadh', 'dd/MM/yyyy', '#,##0.00', 1, GETUTCDATE());

-- تحديث جدول الشركات لدعم البلدان المتعددة
ALTER TABLE Companies ADD CountryCode NVARCHAR(3);
ALTER TABLE Companies ADD CONSTRAINT FK_Companies_Country 
    FOREIGN KEY (CountryCode) REFERENCES SupportedCountries(CountryCode);

-- تحديث جدول الموظفين
ALTER TABLE Employees ADD CountryCode NVARCHAR(3);
ALTER TABLE Employees ADD NationalityCode NVARCHAR(3);
ALTER TABLE Employees ADD CONSTRAINT FK_Employees_Country 
    FOREIGN KEY (CountryCode) REFERENCES SupportedCountries(CountryCode);
```

### 3.2 خدمة إدارة البلدان
```csharp
public class CountryManagementService
{
    private readonly ICountryRepository _countryRepository;
    private readonly ILaborLawService _laborLawService;
    
    public async Task<CountryConfiguration> GetCountryConfiguration(string countryCode)
    {
        var country = await _countryRepository.GetByCodeAsync(countryCode);
        var laborLaws = await _laborLawService.GetLaborLawsAsync(countryCode);
        
        return new CountryConfiguration
        {
            Country = country,
            LaborLaws = laborLaws,
            TaxRules = await GetTaxRulesAsync(countryCode),
            SocialInsuranceRules = await GetSocialInsuranceRulesAsync(countryCode),
            BankingRules = await GetBankingRulesAsync(countryCode)
        };
    }
    
    public async Task<PayrollCalculationResult> CalculatePayroll(
        Employee employee, 
        PayrollPeriod period)
    {
        var countryConfig = await GetCountryConfiguration(employee.CountryCode);
        
        return countryConfig.Country.CountryCode switch
        {
            "EG" => await _egyptianPayrollService.CalculateAsync(employee, period, countryConfig),
            "SA" => await _saudiPayrollService.CalculateAsync(employee, period, countryConfig),
            _ => throw new NotSupportedException($"Country {employee.CountryCode} not supported")
        };
    }
}
```

---

## 4. واجهة المستخدم متعددة البلدان
### Multi-Country User Interface

### 4.1 نظام التوطين المتقدم
```javascript
// نظام إدارة اللغات والبلدان
class CountryLocalizationManager {
    constructor() {
        this.currentCountry = localStorage.getItem('selectedCountry') || 'EG';
        this.currentLanguage = localStorage.getItem('language') || 'ar';
        this.translations = {};
        this.countryConfigs = {};
    }
    
    async loadCountryConfiguration(countryCode) {
        try {
            const response = await fetch(`/api/countries/${countryCode}/configuration`);
            this.countryConfigs[countryCode] = await response.json();
            return this.countryConfigs[countryCode];
        } catch (error) {
            console.error('Failed to load country configuration:', error);
        }
    }
    
    formatCurrency(amount, countryCode = null) {
        const country = countryCode || this.currentCountry;
        const config = this.countryConfigs[country];
        
        if (!config) return amount.toString();
        
        return new Intl.NumberFormat(this.getLocale(country), {
            style: 'currency',
            currency: config.currency
        }).format(amount);
    }
    
    formatDate(date, countryCode = null) {
        const country = countryCode || this.currentCountry;
        const config = this.countryConfigs[country];
        
        return new Intl.DateTimeFormat(this.getLocale(country), {
            dateStyle: 'short'
        }).format(new Date(date));
    }
    
    getLocale(countryCode) {
        const locales = {
            'EG': this.currentLanguage === 'ar' ? 'ar-EG' : 'en-EG',
            'SA': this.currentLanguage === 'ar' ? 'ar-SA' : 'en-SA'
        };
        return locales[countryCode] || 'en-US';
    }
}
```

### 4.2 مكون اختيار البلد
```html
<!-- Country Selector Component -->
<div class="country-selector">
    <label class="form-label" data-en="Select Country" data-ar="اختر البلد"></label>
    <select class="form-select" id="countrySelector" onchange="switchCountry(this.value)">
        <option value="EG" data-en="Egypt" data-ar="مصر">
            <img src="/flags/egypt.svg" alt="Egypt"> مصر - Egypt
        </option>
        <option value="SA" data-en="Saudi Arabia" data-ar="السعودية">
            <img src="/flags/saudi.svg" alt="Saudi Arabia"> السعودية - Saudi Arabia
        </option>
    </select>
</div>

<script>
function switchCountry(countryCode) {
    localStorage.setItem('selectedCountry', countryCode);
    
    // تحديث العملة والتنسيقات
    updateCurrencyDisplay(countryCode);
    
    // تحديث القوانين المعروضة
    updateLaborLawsDisplay(countryCode);
    
    // إعادة تحميل البيانات
    location.reload();
}

function updateCurrencyDisplay(countryCode) {
    const currencies = {
        'EG': { code: 'EGP', symbol: 'ج.م' },
        'SA': { code: 'SAR', symbol: 'ر.س' }
    };
    
    document.querySelectorAll('.currency-symbol').forEach(element => {
        element.textContent = currencies[countryCode].symbol;
    });
}
</script>
```

---

## 5. التكامل مع الخدمات الحكومية
### Government Services Integration

### 5.1 مقارنة الخدمات الحكومية

| الخدمة | مصر | السعودية |
|--------|-----|----------|
| **التأمينات الاجتماعية** | الهيئة القومية للتأمين الاجتماعي | المؤسسة العامة للتأمينات الاجتماعية (GOSI) |
| **الضرائب** | مصلحة الضرائب المصرية | هيئة الزكاة والضريبة والجمارك (ZATCA) |
| **العمل** | وزارة القوى العاملة | وزارة الموارد البشرية والتنمية الاجتماعية |
| **الهوية** | الأحوال المدنية | أبشر |

### 5.2 خدمة التكامل الموحدة
```csharp
public class GovernmentIntegrationService
{
    private readonly IEgyptianGovernmentService _egyptianService;
    private readonly ISaudiGovernmentService _saudiService;
    
    public async Task<EmployeeVerificationResult> VerifyEmployee(
        string employeeId, 
        string countryCode)
    {
        return countryCode switch
        {
            "EG" => await _egyptianService.VerifyNationalId(employeeId),
            "SA" => await _saudiService.VerifyNationalIdOrIqama(employeeId),
            _ => throw new NotSupportedException($"Country {countryCode} not supported")
        };
    }
    
    public async Task<TaxSubmissionResult> SubmitTaxReturn(
        TaxReturnModel taxReturn, 
        string countryCode)
    {
        return countryCode switch
        {
            "EG" => await _egyptianService.SubmitTaxReturn(taxReturn),
            "SA" => await _saudiService.SubmitZATCAReturn(taxReturn),
            _ => throw new NotSupportedException($"Country {countryCode} not supported")
        };
    }
}
```

---

## 6. نظام الرواتب متعدد البلدان
### Multi-Country Payroll System

### 6.1 محرك حساب الرواتب المرن
```csharp
public class MultiCountryPayrollEngine
{
    public async Task<PayrollResult> CalculatePayroll(
        Employee employee, 
        PayrollPeriod period)
    {
        var countryRules = await GetCountryPayrollRules(employee.CountryCode);
        var calculator = CreateCalculator(employee.CountryCode);
        
        return await calculator.Calculate(employee, period, countryRules);
    }
    
    private IPayrollCalculator CreateCalculator(string countryCode)
    {
        return countryCode switch
        {
            "EG" => new EgyptianPayrollCalculator(),
            "SA" => new SaudiPayrollCalculator(),
            _ => throw new NotSupportedException($"Country {countryCode} not supported")
        };
    }
}

// حاسبة الرواتب المصرية
public class EgyptianPayrollCalculator : IPayrollCalculator
{
    public async Task<PayrollResult> Calculate(
        Employee employee, 
        PayrollPeriod period, 
        CountryPayrollRules rules)
    {
        var result = new PayrollResult();
        
        // حساب الراتب الأساسي
        result.BasicSalary = employee.BaseSalary;
        
        // حساب الضرائب المصرية (شرائح من 0% إلى 25%)
        result.IncomeTax = CalculateEgyptianIncomeTax(result.BasicSalary);
        
        // حساب التأمينات الاجتماعية (14% موظف، 26% صاحب عمل)
        result.SocialInsuranceEmployee = result.BasicSalary * 0.14m;
        result.SocialInsuranceEmployer = result.BasicSalary * 0.26m;
        
        // الراتب الصافي
        result.NetSalary = result.BasicSalary - result.IncomeTax - result.SocialInsuranceEmployee;
        
        return result;
    }
}

// حاسبة الرواتب السعودية
public class SaudiPayrollCalculator : IPayrollCalculator
{
    public async Task<PayrollResult> Calculate(
        Employee employee, 
        PayrollPeriod period, 
        CountryPayrollRules rules)
    {
        var result = new PayrollResult();
        
        // حساب الراتب الأساسي
        result.BasicSalary = employee.BaseSalary;
        
        // لا توجد ضريبة دخل على السعوديين
        result.IncomeTax = employee.NationalityCode == "SA" ? 0 : 
                          CalculateForeignerIncomeTax(result.BasicSalary);
        
        // حساب التأمينات الاجتماعية
        if (employee.NationalityCode == "SA")
        {
            result.SocialInsuranceEmployee = Math.Min(result.BasicSalary * 0.10m, 4500); // حد أقصى 45000 ريال
            result.SocialInsuranceEmployer = Math.Min(result.BasicSalary * 0.12m, 5400);
        }
        else
        {
            result.SocialInsuranceEmployee = Math.Min(result.BasicSalary * 0.02m, 900); // إصابات العمل فقط
            result.SocialInsuranceEmployer = Math.Min(result.BasicSalary * 0.02m, 900);
        }
        
        result.NetSalary = result.BasicSalary - result.IncomeTax - result.SocialInsuranceEmployee;
        
        return result;
    }
}
```

---

## 7. التقارير متعددة البلدان
### Multi-Country Reporting

### 7.1 تقارير مقارنة بين البلدان
```sql
-- تقرير مقارنة التكاليف بين البلدان
CREATE VIEW CountryPayrollComparison AS
SELECT 
    c.CountryCode,
    c.CountryName,
    c.CountryNameAr,
    COUNT(e.Id) as TotalEmployees,
    SUM(p.BasicSalary) as TotalBasicSalary,
    SUM(p.SocialInsuranceEmployee) as TotalEmployeeInsurance,
    SUM(p.SocialInsuranceEmployer) as TotalEmployerInsurance,
    SUM(p.IncomeTax) as TotalIncomeTax,
    SUM(p.NetSalary) as TotalNetSalary,
    AVG(p.NetSalary) as AverageNetSalary
FROM SupportedCountries c
LEFT JOIN Employees e ON c.CountryCode = e.CountryCode
LEFT JOIN Payrolls p ON e.Id = p.EmployeeId
WHERE p.PayrollYear = YEAR(GETDATE()) 
    AND p.PayrollMonth = MONTH(GETDATE())
GROUP BY c.CountryCode, c.CountryName, c.CountryNameAr;
```

### 7.2 لوحة تحكم متعددة البلدان
```html
<div class="multi-country-dashboard">
    <div class="country-tabs">
        <button class="tab-button active" onclick="showCountryData('EG')">
            <img src="/flags/egypt.svg" alt="Egypt"> مصر
        </button>
        <button class="tab-button" onclick="showCountryData('SA')">
            <img src="/flags/saudi.svg" alt="Saudi"> السعودية
        </button>
        <button class="tab-button" onclick="showCountryData('ALL')">
            <i class="fas fa-globe"></i> الكل
        </button>
    </div>
    
    <div class="country-stats" id="country-stats">
        <!-- سيتم تحميل البيانات ديناميكياً -->
    </div>
    
    <div class="comparison-chart">
        <canvas id="countryComparisonChart"></canvas>
    </div>
</div>

<script>
async function showCountryData(countryCode) {
    const response = await fetch(`/api/dashboard/country/${countryCode}`);
    const data = await response.json();
    
    document.getElementById('country-stats').innerHTML = `
        <div class="stat-cards">
            <div class="stat-card">
                <h3>${data.totalEmployees}</h3>
                <p data-en="Total Employees" data-ar="إجمالي الموظفين"></p>
            </div>
            <div class="stat-card">
                <h3>${formatCurrency(data.totalPayroll, countryCode)}</h3>
                <p data-en="Total Payroll" data-ar="إجمالي الرواتب"></p>
            </div>
            <div class="stat-card">
                <h3>${data.attendanceRate}%</h3>
                <p data-en="Attendance Rate" data-ar="معدل الحضور"></p>
            </div>
        </div>
    `;
    
    updateComparisonChart(data);
}
</script>
```

---

## 8. خطة التوسع الإقليمي
### Regional Expansion Plan

### 8.1 مراحل التوسع
```mermaid
gantt
    title خطة التوسع الإقليمي
    dateFormat  YYYY-MM-DD
    section المرحلة الأولى
    مصر (السوق الأساسي)     :2024-01-01, 12M
    section المرحلة الثانية
    السعودية               :2024-07-01, 12M
    section المرحلة الثالثة
    الإمارات               :2025-01-01, 9M
    الكويت                 :2025-04-01, 9M
    section المرحلة الرابعة
    قطر والبحرين           :2025-10-01, 6M
```

### 8.2 متطلبات التوسع لكل دولة

**الإمارات العربية المتحدة:**
- تكامل مع وزارة الموارد البشرية والتوطين
- دعم نظام الحماية الاجتماعية
- تكامل مع البنوك الإماراتية

**الكويت:**
- تكامل مع المؤسسة العامة للتأمينات الاجتماعية
- دعم قانون العمل الكويتي
- تكامل مع بيت التمويل الكويتي والبنك الوطني

**قطر:**
- تكامل مع وزارة التنمية الإدارية والعمل والشؤون الاجتماعية
- دعم نظام كفالة العمالة
- تكامل مع البنوك القطرية

---

## 9. التكاليف الإضافية للتوسع
### Additional Expansion Costs

### 9.1 تكاليف التطوير الإضافية

| البند | التكلفة | المدة |
|-------|---------|-------|
|