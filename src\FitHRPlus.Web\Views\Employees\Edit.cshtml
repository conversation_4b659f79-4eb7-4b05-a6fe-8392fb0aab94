@model FitHRPlus.Web.Models.Employees.EmployeeViewModel
@{
    ViewData["Title"] = "تعديل الموظف";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-edit me-2"></i>
                        تعديل الموظف: @Model.FullName
                    </h4>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post" enctype="multipart/form-data">
                        <input asp-for="Id" type="hidden" />
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <!-- Personal Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-user me-2"></i>
                                    المعلومات الشخصية
                                </h5>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label asp-for="FirstName" class="form-label">الاسم الأول *</label>
                                <input asp-for="FirstName" class="form-control" />
                                <span asp-validation-for="FirstName" class="text-danger"></span>
                            </div>
                            <div class="col-md-4">
                                <label asp-for="MiddleName" class="form-label">الاسم الأوسط</label>
                                <input asp-for="MiddleName" class="form-control" />
                                <span asp-validation-for="MiddleName" class="text-danger"></span>
                            </div>
                            <div class="col-md-4">
                                <label asp-for="LastName" class="form-label">الاسم الأخير *</label>
                                <input asp-for="LastName" class="form-control" />
                                <span asp-validation-for="LastName" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label asp-for="Email" class="form-label">البريد الإلكتروني *</label>
                                <input asp-for="Email" class="form-control" type="email" />
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label asp-for="PhoneNumber" class="form-label">رقم الهاتف *</label>
                                <input asp-for="PhoneNumber" class="form-control" />
                                <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label asp-for="DateOfBirth" class="form-label">تاريخ الميلاد</label>
                                <input asp-for="DateOfBirth" class="form-control" type="date" />
                                <span asp-validation-for="DateOfBirth" class="text-danger"></span>
                            </div>
                            <div class="col-md-4">
                                <label asp-for="Gender" class="form-label">الجنس</label>
                                <select asp-for="Gender" class="form-select">
                                    <option value="">اختر الجنس</option>
                                    <option value="Male">ذكر</option>
                                    <option value="Female">أنثى</option>
                                </select>
                                <span asp-validation-for="Gender" class="text-danger"></span>
                            </div>
                            <div class="col-md-4">
                                <label asp-for="NationalId" class="form-label">رقم الهوية الوطنية</label>
                                <input asp-for="NationalId" class="form-control" />
                                <span asp-validation-for="NationalId" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- Employment Information -->
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-briefcase me-2"></i>
                                    معلومات التوظيف
                                </h5>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label asp-for="EmployeeNumber" class="form-label">رقم الموظف *</label>
                                <input asp-for="EmployeeNumber" class="form-control" />
                                <span asp-validation-for="EmployeeNumber" class="text-danger"></span>
                            </div>
                            <div class="col-md-4">
                                <label asp-for="DepartmentId" class="form-label">القسم *</label>
                                <select asp-for="DepartmentId" class="form-select" asp-items="ViewBag.Departments">
                                    <option value="">اختر القسم</option>
                                </select>
                                <span asp-validation-for="DepartmentId" class="text-danger"></span>
                            </div>
                            <div class="col-md-4">
                                <label asp-for="PositionId" class="form-label">المنصب *</label>
                                <select asp-for="PositionId" class="form-select" asp-items="ViewBag.Positions">
                                    <option value="">اختر المنصب</option>
                                </select>
                                <span asp-validation-for="PositionId" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label asp-for="HireDate" class="form-label">تاريخ التوظيف *</label>
                                <input asp-for="HireDate" class="form-control" type="date" />
                                <span asp-validation-for="HireDate" class="text-danger"></span>
                            </div>
                            <div class="col-md-4">
                                <label asp-for="Salary" class="form-label">الراتب الأساسي *</label>
                                <input asp-for="Salary" class="form-control" type="number" step="0.01" />
                                <span asp-validation-for="Salary" class="text-danger"></span>
                            </div>
                            <div class="col-md-4">
                                <label asp-for="EmploymentType" class="form-label">نوع التوظيف</label>
                                <select asp-for="EmploymentType" class="form-select">
                                    <option value="">اختر نوع التوظيف</option>
                                    <option value="FullTime">دوام كامل</option>
                                    <option value="PartTime">دوام جزئي</option>
                                    <option value="Contract">عقد</option>
                                    <option value="Intern">متدرب</option>
                                </select>
                                <span asp-validation-for="EmploymentType" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- Address Information -->
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-map-marker-alt me-2"></i>
                                    معلومات العنوان
                                </h5>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label asp-for="Address" class="form-label">العنوان</label>
                                <textarea asp-for="Address" class="form-control" rows="3"></textarea>
                                <span asp-validation-for="Address" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- Profile Picture -->
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-camera me-2"></i>
                                    الصورة الشخصية
                                </h5>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                @if (!string.IsNullOrEmpty(Model.ProfilePicture))
                                {
                                    <div class="mb-3">
                                        <label class="form-label">الصورة الحالية:</label>
                                        <div>
                                            <img src="@Model.ProfilePicture" alt="@Model.FullName" class="rounded" style="max-width: 150px; max-height: 150px;">
                                        </div>
                                    </div>
                                }
                                <label for="profilePicture" class="form-label">تغيير الصورة</label>
                                <input type="file" class="form-control" id="profilePicture" name="ProfilePictureFile" accept="image/*" />
                                <div class="form-text">الحد الأقصى لحجم الملف: 2 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF</div>
                                <input asp-for="ProfilePicture" type="hidden" />
                            </div>
                        </div>

                        <!-- Status -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                    <label asp-for="IsActive" class="form-check-label">
                                        الموظف نشط
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-save me-1"></i>
                                    حفظ التعديلات
                                </button>
                                <a href="@Url.Action("Details", new { id = Model.Id })" class="btn btn-info">
                                    <i class="fas fa-eye me-1"></i>
                                    عرض التفاصيل
                                </a>
                                <a href="@Url.Action("Index")" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>
                                    إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        // Department change handler to load positions
        $('#DepartmentId').change(function() {
            var departmentId = $(this).val();
            if (departmentId) {
                $.get('@Url.Action("GetPositionsByDepartment", "Employees")', { departmentId: departmentId }, function(data) {
                    var positionSelect = $('#PositionId');
                    var currentPositionId = '@Model.PositionId';
                    positionSelect.empty();
                    positionSelect.append('<option value="">اختر المنصب</option>');
                    $.each(data, function(index, position) {
                        var selected = position.value == currentPositionId ? 'selected' : '';
                        positionSelect.append('<option value="' + position.value + '" ' + selected + '>' + position.text + '</option>');
                    });
                });
            } else {
                $('#PositionId').empty().append('<option value="">اختر المنصب</option>');
            }
        });

        // Load positions on page load if department is selected
        $(document).ready(function() {
            if ($('#DepartmentId').val()) {
                $('#DepartmentId').trigger('change');
            }
        });

        // Profile picture preview
        $('#profilePicture').change(function() {
            var file = this.files[0];
            if (file) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    // Add preview functionality here if needed
                };
                reader.readAsDataURL(file);
            }
        });
    </script>
}
