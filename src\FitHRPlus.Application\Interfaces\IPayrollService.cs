using FitHRPlus.Application.DTOs.Common;
using FitHRPlus.Application.DTOs.Payroll;
using FitHRPlus.Application.Common;

namespace FitHRPlus.Application.Interfaces
{
    /// <summary>
    /// Payroll service interface
    /// واجهة خدمة كشوف المرتبات
    /// </summary>
    public interface IPayrollService
    {
        /// <summary>
        /// Get paginated list of payrolls
        /// الحصول على قائمة مقسمة لكشوف المرتبات
        /// </summary>
        /// <param name="request">List request parameters</param>
        /// <returns>Paginated payrolls</returns>
        Task<ServiceResult<PaginatedResult<PayrollDto>>> GetPayrollsAsync(PayrollListDto request);

        /// <summary>
        /// Get payroll by ID
        /// الحصول على كشف المرتبات بالمعرف
        /// </summary>
        /// <param name="id">Payroll ID</param>
        /// <returns>Payroll details</returns>
        Task<ServiceResult<PayrollDto>> GetPayrollByIdAsync(Guid id);

        /// <summary>
        /// Create new payroll
        /// إنشاء كشف مرتبات جديد
        /// </summary>
        /// <param name="request">Create request data</param>
        /// <param name="userId">User creating the payroll</param>
        /// <returns>Created payroll</returns>
        Task<ServiceResult<PayrollDto>> CreatePayrollAsync(CreatePayrollDto request, Guid userId);

        /// <summary>
        /// Update existing payroll
        /// تحديث كشف المرتبات الموجود
        /// </summary>
        /// <param name="request">Update request data</param>
        /// <param name="userId">User updating the payroll</param>
        /// <returns>Updated payroll</returns>
        Task<ServiceResult<PayrollDto>> UpdatePayrollAsync(UpdatePayrollDto request, Guid userId);

        /// <summary>
        /// Delete payroll
        /// حذف كشف المرتبات
        /// </summary>
        /// <param name="payrollId">Payroll ID</param>
        /// <param name="userId">User deleting the payroll</param>
        /// <returns>Success result</returns>
        Task<ServiceResult<bool>> DeletePayrollAsync(Guid payrollId, Guid userId);

        /// <summary>
        /// Process payroll (calculate all components)
        /// معالجة كشف المرتبات (حساب جميع المكونات)
        /// </summary>
        /// <param name="request">Process request data</param>
        /// <param name="userId">User processing the payroll</param>
        /// <returns>Processed payroll</returns>
        Task<ServiceResult<PayrollDto>> ProcessPayrollAsync(ProcessPayrollDto request, Guid userId);

        /// <summary>
        /// Approve payroll
        /// الموافقة على كشف المرتبات
        /// </summary>
        /// <param name="request">Approval request data</param>
        /// <param name="userId">User approving the payroll</param>
        /// <returns>Approved payroll</returns>
        Task<ServiceResult<PayrollDto>> ApprovePayrollAsync(ApprovePayrollDto request, Guid userId);

        /// <summary>
        /// Mark payroll as paid
        /// تسجيل كشف المرتبات كمدفوع
        /// </summary>
        /// <param name="request">Payment request data</param>
        /// <param name="userId">User marking as paid</param>
        /// <returns>Paid payroll</returns>
        Task<ServiceResult<PayrollDto>> PayPayrollAsync(PayPayrollDto request, Guid userId);

        /// <summary>
        /// Reject payroll
        /// رفض كشف المرتبات
        /// </summary>
        /// <param name="payrollId">Payroll ID</param>
        /// <param name="reason">Rejection reason</param>
        /// <param name="userId">User rejecting the payroll</param>
        /// <returns>Rejected payroll</returns>
        Task<ServiceResult<PayrollDto>> RejectPayrollAsync(Guid payrollId, string reason, Guid userId);

        /// <summary>
        /// Generate payrolls for all employees in a company for a specific month
        /// إنشاء كشوف المرتبات لجميع الموظفين في الشركة لشهر محدد
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="month">Payroll month</param>
        /// <param name="year">Payroll year</param>
        /// <param name="userId">User generating payrolls</param>
        /// <returns>Number of payrolls generated</returns>
        Task<ServiceResult<int>> GeneratePayrollsAsync(Guid companyId, int month, int year, Guid userId);

        /// <summary>
        /// Calculate payroll for an employee
        /// حساب كشف المرتبات للموظف
        /// </summary>
        /// <param name="employeeId">Employee ID</param>
        /// <param name="month">Payroll month</param>
        /// <param name="year">Payroll year</param>
        /// <returns>Calculated payroll data</returns>
        Task<ServiceResult<PayrollDto>> CalculatePayrollAsync(Guid employeeId, int month, int year);

        /// <summary>
        /// Get payroll statistics
        /// الحصول على إحصائيات كشوف المرتبات
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="month">Month (optional)</param>
        /// <param name="year">Year (optional)</param>
        /// <returns>Payroll statistics</returns>
        Task<ServiceResult<PayrollStatisticsDto>> GetPayrollStatisticsAsync(Guid companyId, int? month = null, int? year = null);

        /// <summary>
        /// Get employee payroll history
        /// الحصول على تاريخ كشوف مرتبات الموظف
        /// </summary>
        /// <param name="employeeId">Employee ID</param>
        /// <param name="year">Year (optional)</param>
        /// <returns>Employee payroll history</returns>
        Task<ServiceResult<List<PayrollDto>>> GetEmployeePayrollHistoryAsync(Guid employeeId, int? year = null);

        /// <summary>
        /// Export payrolls to Excel
        /// تصدير كشوف المرتبات إلى Excel
        /// </summary>
        /// <param name="request">Export request parameters</param>
        /// <returns>Excel file data</returns>
        Task<ServiceResult<byte[]>> ExportPayrollsAsync(PayrollListDto request);

        /// <summary>
        /// Get salary components for payroll calculation
        /// الحصول على مكونات الراتب لحساب كشف المرتبات
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="employeeId">Employee ID (optional)</param>
        /// <returns>List of salary components</returns>
        Task<ServiceResult<List<SalaryComponentDto>>> GetSalaryComponentsAsync(Guid companyId, Guid? employeeId = null);

        /// <summary>
        /// Validate payroll data before processing
        /// التحقق من بيانات كشف المرتبات قبل المعالجة
        /// </summary>
        /// <param name="payrollId">Payroll ID</param>
        /// <returns>Validation result</returns>
        Task<ServiceResult<bool>> ValidatePayrollAsync(Guid payrollId);

        /// <summary>
        /// Get payroll summary for a period
        /// الحصول على ملخص كشوف المرتبات لفترة
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="fromMonth">From month</param>
        /// <param name="fromYear">From year</param>
        /// <param name="toMonth">To month</param>
        /// <param name="toYear">To year</param>
        /// <returns>Payroll summary</returns>
        Task<ServiceResult<PayrollStatisticsDto>> GetPayrollSummaryAsync(Guid companyId, int fromMonth, int fromYear, int toMonth, int toYear);

        /// <summary>
        /// Bulk approve payrolls
        /// الموافقة المجمعة على كشوف المرتبات
        /// </summary>
        /// <param name="payrollIds">List of payroll IDs</param>
        /// <param name="userId">User approving payrolls</param>
        /// <returns>Number of approved payrolls</returns>
        Task<ServiceResult<int>> BulkApprovePayrollsAsync(List<Guid> payrollIds, Guid userId);

        /// <summary>
        /// Bulk pay payrolls
        /// الدفع المجمع لكشوف المرتبات
        /// </summary>
        /// <param name="payrollIds">List of payroll IDs</param>
        /// <param name="paymentMethod">Payment method</param>
        /// <param name="userId">User processing payments</param>
        /// <returns>Number of paid payrolls</returns>
        Task<ServiceResult<int>> BulkPayPayrollsAsync(List<Guid> payrollIds, string paymentMethod, Guid userId);
    }

    /// <summary>
    /// Salary component DTO
    /// DTO مكون الراتب
    /// </summary>
    public class SalaryComponentDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string NameAr { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // Allowance, Deduction
        public string CalculationType { get; set; } = string.Empty; // Fixed, Percentage
        public decimal Amount { get; set; }
        public decimal? Percentage { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
        public bool IsTaxable { get; set; }
        public bool IsStatutory { get; set; }
        public bool IsActive { get; set; }
        public string? Description { get; set; }
        public string? DescriptionAr { get; set; }
    }
}
