using FitHRPlus.Application.Common;
using FitHRPlus.Application.DTOs.Attendance;
using FitHRPlus.Application.Interfaces;
using FitHRPlus.Domain.Entities;
using FitHRPlus.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FitHRPlus.Infrastructure.Services
{
    /// <summary>
    /// Attendance service implementation
    /// تنفيذ خدمة الحضور والانصراف
    /// </summary>
    public class AttendanceService : IAttendanceService
    {
        private readonly FitHRContext _context;
        private readonly ILogger<AttendanceService> _logger;

        public AttendanceService(FitHRContext context, ILogger<AttendanceService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<ServiceResult<AttendanceListResponseDto>> GetAttendanceRecordsAsync(AttendanceListRequestDto request)
        {
            try
            {
                var query = _context.AttendanceRecords
                    .Include(a => a.Employee)
                        .ThenInclude(e => e.Department)
                    .Include(a => a.Employee)
                        .ThenInclude(e => e.Position)
                    .AsQueryable();

                // Apply filters
                if (request.EmployeeId.HasValue)
                {
                    query = query.Where(a => a.EmployeeId == request.EmployeeId.Value);
                }

                if (request.CompanyId.HasValue)
                {
                    query = query.Where(a => a.Employee.CompanyId == request.CompanyId.Value);
                }

                if (request.DepartmentId.HasValue)
                {
                    query = query.Where(a => a.Employee.DepartmentId == request.DepartmentId.Value);
                }

                if (request.PositionId.HasValue)
                {
                    query = query.Where(a => a.Employee.PositionId == request.PositionId.Value);
                }

                if (request.DateFrom.HasValue)
                {
                    query = query.Where(a => a.AttendanceDate >= request.DateFrom.Value.Date);
                }

                if (request.DateTo.HasValue)
                {
                    query = query.Where(a => a.AttendanceDate <= request.DateTo.Value.Date);
                }

                if (!string.IsNullOrEmpty(request.AttendanceStatus))
                {
                    query = query.Where(a => a.Status == request.AttendanceStatus);
                }

                if (request.ShowLateOnly == true)
                {
                    query = query.Where(a => a.LateMinutes > 0);
                }

                if (request.ShowEarlyDepartureOnly == true)
                {
                    query = query.Where(a => a.EarlyLeaveMinutes > 0);
                }

                if (request.ShowOvertimeOnly == true)
                {
                    query = query.Where(a => a.OvertimeHours > 0);
                }

                if (request.ShowUnapprovedOnly == true)
                {
                    query = query.Where(a => !a.IsApproved);
                }

                // Search filter
                if (!string.IsNullOrEmpty(request.SearchTerm))
                {
                    var searchTerm = request.SearchTerm.ToLower();
                    query = query.Where(a =>
                        a.Employee.FirstName.ToLower().Contains(searchTerm) ||
                        a.Employee.LastName.ToLower().Contains(searchTerm) ||
                        a.Employee.EmployeeCode.ToLower().Contains(searchTerm) ||
                        (a.Employee.Email != null && a.Employee.Email.ToLower().Contains(searchTerm)));
                }

                // Get total count before pagination
                var totalCount = await query.CountAsync();

                // Apply sorting
                query = ApplySorting(query, request.SortBy, request.SortDirection);

                // Apply pagination
                var attendances = await query
                    .Skip((request.Page - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .Select(a => MapToAttendanceDto(a))
                    .ToListAsync();

                // Calculate pagination info
                var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);
                var hasNextPage = request.Page < totalPages;
                var hasPreviousPage = request.Page > 1;

                // Get statistics
                var statistics = await GetAttendanceStatisticsForQuery(query);

                var response = new AttendanceListResponseDto
                {
                    Attendances = attendances,
                    TotalCount = totalCount,
                    CurrentPage = request.Page,
                    PageSize = request.PageSize,
                    TotalPages = totalPages,
                    HasNextPage = hasNextPage,
                    HasPreviousPage = hasPreviousPage,
                    Statistics = statistics
                };

                return ServiceResult<AttendanceListResponseDto>.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting attendance records");
                return ServiceResult<AttendanceListResponseDto>.Failure(
                    "An error occurred while retrieving attendance records",
                    "حدث خطأ أثناء استرداد سجلات الحضور",
                    "ATTENDANCE_LIST_ERROR");
            }
        }

        public async Task<ServiceResult<AttendanceDto>> GetAttendanceByIdAsync(Guid id)
        {
            try
            {
                var attendance = await _context.AttendanceRecords
                    .Include(a => a.Employee)
                        .ThenInclude(e => e.Department)
                    .Include(a => a.Employee)
                        .ThenInclude(e => e.Position)
                    .FirstOrDefaultAsync(a => a.Id == id);

                if (attendance == null)
                {
                    return ServiceResult<AttendanceDto>.Failure(
                        "Attendance record not found",
                        "سجل الحضور غير موجود",
                        "ATTENDANCE_NOT_FOUND");
                }

                var attendanceDto = MapToAttendanceDto(attendance);
                return ServiceResult<AttendanceDto>.Success(attendanceDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting attendance by ID: {AttendanceId}", id);
                return ServiceResult<AttendanceDto>.Failure(
                    "An error occurred while retrieving attendance record",
                    "حدث خطأ أثناء استرداد سجل الحضور",
                    "ATTENDANCE_GET_ERROR");
            }
        }

        public async Task<ServiceResult<AttendanceDto?>> GetEmployeeAttendanceAsync(Guid employeeId, DateTime date)
        {
            try
            {
                var attendance = await _context.AttendanceRecords
                    .Include(a => a.Employee)
                        .ThenInclude(e => e.Department)
                    .Include(a => a.Employee)
                        .ThenInclude(e => e.Position)
                    .FirstOrDefaultAsync(a => a.EmployeeId == employeeId && a.AttendanceDate.Date == date.Date);

                if (attendance == null)
                {
                    return ServiceResult<AttendanceDto?>.Success(null);
                }

                var attendanceDto = MapToAttendanceDto(attendance);
                return ServiceResult<AttendanceDto?>.Success(attendanceDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting employee attendance for employee: {EmployeeId}, date: {Date}", employeeId, date);
                return ServiceResult<AttendanceDto?>.Failure(
                    "An error occurred while retrieving employee attendance",
                    "حدث خطأ أثناء استرداد حضور الموظف",
                    "EMPLOYEE_ATTENDANCE_GET_ERROR");
            }
        }

        public async Task<ServiceResult<AttendanceDto>> CheckInAsync(CheckInOutRequestDto request, Guid recordedBy)
        {
            try
            {
                var checkInTime = request.Time ?? DateTime.Now;
                var date = checkInTime.Date;

                // Check if attendance record already exists for this date
                var existingAttendance = await _context.AttendanceRecords
                    .FirstOrDefaultAsync(a => a.EmployeeId == request.EmployeeId && a.AttendanceDate.Date == date);

                if (existingAttendance != null && existingAttendance.CheckInTime.HasValue)
                {
                    return ServiceResult<AttendanceDto>.Failure(
                        "Employee has already checked in for this date",
                        "الموظف سجل حضوره بالفعل لهذا التاريخ",
                        "ALREADY_CHECKED_IN");
                }

                AttendanceRecord attendance;
                if (existingAttendance != null)
                {
                    // Update existing record
                    attendance = existingAttendance;
                    attendance.CheckInTime = checkInTime;
                    // Parse location for GPS coordinates if needed
                    attendance.UpdatedAt = DateTime.UtcNow;
                    attendance.UpdatedBy = recordedBy;
                }
                else
                {
                    // Create new record
                    attendance = new AttendanceRecord
                    {
                        Id = Guid.NewGuid(),
                        EmployeeId = request.EmployeeId,
                        AttendanceDate = date,
                        CheckInTime = checkInTime,
                        CheckInLatitude = null, // GPS coordinates can be parsed from location
                        CheckInLongitude = null,
                        Status = "Present",
                        Notes = request.Notes,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        CreatedBy = recordedBy,
                        UpdatedBy = recordedBy
                    };

                    _context.AttendanceRecords.Add(attendance);
                }

                // Calculate late minutes and other metrics
                await CalculateAttendanceMetrics(attendance);

                await _context.SaveChangesAsync();

                // Reload with includes
                attendance = await _context.AttendanceRecords
                    .Include(a => a.Employee)
                        .ThenInclude(e => e.Department)
                    .Include(a => a.Employee)
                        .ThenInclude(e => e.Position)
                    .FirstAsync(a => a.Id == attendance.Id);

                var attendanceDto = MapToAttendanceDto(attendance);
                return ServiceResult<AttendanceDto>.Success(attendanceDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during check-in for employee: {EmployeeId}", request.EmployeeId);
                return ServiceResult<AttendanceDto>.Failure(
                    "An error occurred during check-in",
                    "حدث خطأ أثناء تسجيل الحضور",
                    "CHECKIN_ERROR");
            }
        }

        // TODO: Implement remaining methods
        public Task<ServiceResult<AttendanceDto>> CheckOutAsync(CheckInOutRequestDto request, Guid recordedBy)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<AttendanceDto>> StartBreakAsync(Guid employeeId, DateTime? time, Guid recordedBy)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<AttendanceDto>> EndBreakAsync(Guid employeeId, DateTime? time, Guid recordedBy)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<AttendanceDto>> CreateOrUpdateAttendanceAsync(AttendanceDto attendanceDto, Guid createdBy)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<bool>> DeleteAttendanceAsync(Guid id, Guid deletedBy)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<bool>> ApproveAttendanceAsync(Guid id, Guid approvedBy)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<int>> BulkApproveAttendanceAsync(List<Guid> ids, Guid approvedBy)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<AttendanceSummaryDto>> GetEmployeeAttendanceSummaryAsync(Guid employeeId, DateTime dateFrom, DateTime dateTo)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<DailyAttendanceReportDto>> GetDailyAttendanceReportAsync(DateTime date, Guid? companyId = null, Guid? departmentId = null)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<AttendanceStatisticsDto>> GetAttendanceStatisticsAsync(DateTime dateFrom, DateTime dateTo, Guid? companyId = null, Guid? departmentId = null)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<int>> GenerateAttendanceRecordsAsync(DateTime date, Guid companyId, Guid? departmentId, Guid createdBy)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<AttendanceImportResultDto>> ImportAttendanceRecordsAsync(byte[] fileData, Guid companyId, Guid importedBy)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<AttendanceExportResultDto>> ExportAttendanceRecordsAsync(AttendanceListRequestDto request)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<WorkingHoursCalculationDto>> CalculateWorkingHoursAsync(DateTime checkInTime, DateTime checkOutTime, TimeSpan? breakDuration, Guid employeeId)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<List<AttendanceDto>>> GetCurrentlyCheckedInEmployeesAsync(Guid? companyId = null, Guid? departmentId = null)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<AttendanceTrendsDto>> GetAttendanceTrendsAsync(Guid companyId, int days = 30)
        {
            throw new NotImplementedException();
        }

        // Helper methods
        private static IQueryable<AttendanceRecord> ApplySorting(IQueryable<AttendanceRecord> query, string? sortBy, string? sortDirection)
        {
            var isDescending = sortDirection?.ToLower() == "desc";

            return sortBy?.ToLower() switch
            {
                "date" => isDescending ? query.OrderByDescending(a => a.AttendanceDate) : query.OrderBy(a => a.AttendanceDate),
                "employee" => isDescending ? query.OrderByDescending(a => a.Employee.FirstName) : query.OrderBy(a => a.Employee.FirstName),
                "checkin" => isDescending ? query.OrderByDescending(a => a.CheckInTime) : query.OrderBy(a => a.CheckInTime),
                "checkout" => isDescending ? query.OrderByDescending(a => a.CheckOutTime) : query.OrderBy(a => a.CheckOutTime),
                "workinghours" => isDescending ? query.OrderByDescending(a => a.WorkingHours) : query.OrderBy(a => a.WorkingHours),
                "status" => isDescending ? query.OrderByDescending(a => a.Status) : query.OrderBy(a => a.Status),
                _ => query.OrderByDescending(a => a.AttendanceDate)
            };
        }

        private static AttendanceDto MapToAttendanceDto(AttendanceRecord attendance)
        {
            return new AttendanceDto
            {
                Id = attendance.Id,
                EmployeeId = attendance.EmployeeId,
                EmployeeName = $"{attendance.Employee.FirstName} {attendance.Employee.LastName}",
                EmployeeNumber = attendance.Employee.EmployeeCode,
                DepartmentName = attendance.Employee.Department?.Name,
                PositionTitle = attendance.Employee.Position?.Title,
                Date = attendance.AttendanceDate,
                CheckInTime = attendance.CheckInTime,
                CheckOutTime = attendance.CheckOutTime,
                BreakStartTime = null, // Not available in current entity
                BreakEndTime = null, // Not available in current entity
                WorkingHours = TimeSpan.FromHours((double)attendance.WorkingHours),
                BreakDuration = TimeSpan.FromHours((double)attendance.BreakHours),
                OvertimeHours = TimeSpan.FromHours((double)attendance.OvertimeHours),
                LateMinutes = attendance.LateMinutes,
                EarlyDepartureMinutes = attendance.EarlyLeaveMinutes,
                AttendanceStatus = attendance.Status,
                CheckInLocation = $"{attendance.CheckInLatitude},{attendance.CheckInLongitude}",
                CheckOutLocation = $"{attendance.CheckOutLatitude},{attendance.CheckOutLongitude}",
                CheckInDevice = "Web Portal", // Default value
                CheckOutDevice = "Web Portal", // Default value
                Notes = attendance.Notes,
                IsApproved = attendance.IsApproved,
                ApprovedBy = attendance.ApprovedBy,
                ApprovalDate = attendance.ApprovedAt,
                CreatedAt = attendance.CreatedAt,
                UpdatedAt = attendance.UpdatedAt,
                CreatedBy = attendance.CreatedBy ?? Guid.Empty,
                UpdatedBy = attendance.UpdatedBy ?? Guid.Empty
            };
        }

        private async Task<AttendanceStatisticsDto> GetAttendanceStatisticsForQuery(IQueryable<AttendanceRecord> query)
        {
            var stats = new AttendanceStatisticsDto();

            try
            {
                var attendances = await query.ToListAsync();

                stats.TotalRecords = attendances.Count;
                stats.TotalPresentDays = attendances.Count(a => a.Status == "Present");
                stats.TotalAbsentDays = attendances.Count(a => a.Status == "Absent");
                stats.TotalLateArrivals = attendances.Count(a => a.LateMinutes > 0);
                stats.TotalEarlyDepartures = attendances.Count(a => a.EarlyLeaveMinutes > 0);
                stats.TotalOvertimeRecords = attendances.Count(a => a.OvertimeHours > 0);

                if (stats.TotalRecords > 0)
                {
                    stats.AverageAttendancePercentage = (double)stats.TotalPresentDays / stats.TotalRecords * 100;
                }

                var workingAttendances = attendances.Where(a => a.WorkingHours > 0).ToList();
                if (workingAttendances.Any())
                {
                    stats.TotalWorkingHours = TimeSpan.FromHours(workingAttendances.Sum(a => (double)a.WorkingHours));
                    stats.AverageWorkingHours = TimeSpan.FromHours(workingAttendances.Average(a => (double)a.WorkingHours));
                }

                var overtimeAttendances = attendances.Where(a => a.OvertimeHours > 0).ToList();
                if (overtimeAttendances.Any())
                {
                    stats.TotalOvertimeHours = TimeSpan.FromHours(overtimeAttendances.Sum(a => (double)a.OvertimeHours));
                }

                // Department breakdown
                stats.DepartmentBreakdown = attendances
                    .Where(a => a.Employee.Department != null)
                    .GroupBy(a => new { a.Employee.DepartmentId, a.Employee.Department!.Name })
                    .Select(g => new DepartmentAttendanceStatsDto
                    {
                        DepartmentId = g.Key.DepartmentId,
                        DepartmentName = g.Key.Name,
                        TotalEmployees = g.Select(a => a.EmployeeId).Distinct().Count(),
                        PresentEmployees = g.Count(a => a.Status == "Present"),
                        AbsentEmployees = g.Count(a => a.Status == "Absent"),
                        LateEmployees = g.Count(a => a.LateMinutes > 0)
                    })
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating attendance statistics");
            }

            return stats;
        }

        private async Task CalculateAttendanceMetrics(AttendanceRecord attendance)
        {
            try
            {
                // Get employee's work schedule (for now, use default 9 AM start time)
                var standardStartTime = TimeSpan.FromHours(9); // 9:00 AM
                var standardEndTime = TimeSpan.FromHours(17); // 5:00 PM
                var standardWorkingHours = TimeSpan.FromHours(8);

                // Calculate late minutes
                if (attendance.CheckInTime.HasValue)
                {
                    var checkInTimeOfDay = attendance.CheckInTime.Value.TimeOfDay;
                    if (checkInTimeOfDay > standardStartTime)
                    {
                        attendance.LateMinutes = (int)(checkInTimeOfDay - standardStartTime).TotalMinutes;
                    }
                }

                // Calculate working hours and overtime if both check-in and check-out are available
                if (attendance.CheckInTime.HasValue && attendance.CheckOutTime.HasValue)
                {
                    var totalTime = attendance.CheckOutTime.Value - attendance.CheckInTime.Value;

                    // Subtract break hours if available
                    var netWorkingTime = totalTime - TimeSpan.FromHours((double)attendance.BreakHours);

                    attendance.WorkingHours = (decimal)netWorkingTime.TotalHours;

                    // Calculate overtime
                    if (netWorkingTime > standardWorkingHours)
                    {
                        attendance.OvertimeHours = (decimal)(netWorkingTime - standardWorkingHours).TotalHours;
                    }

                    // Calculate early departure
                    var checkOutTimeOfDay = attendance.CheckOutTime.Value.TimeOfDay;
                    if (checkOutTimeOfDay < standardEndTime)
                    {
                        attendance.EarlyLeaveMinutes = (int)(standardEndTime - checkOutTimeOfDay).TotalMinutes;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating attendance metrics for attendance: {AttendanceId}", attendance.Id);
            }
        }
    }
}
