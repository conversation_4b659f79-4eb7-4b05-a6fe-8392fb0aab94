@model FitHRPlus.Web.Models.Employees.EmployeeViewModel
@{
    ViewData["Title"] = "تفاصيل الموظف";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        تفاصيل الموظف
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Employee Header -->
                    <div class="row mb-4">
                        <div class="col-md-3 text-center">
                            @if (!string.IsNullOrEmpty(Model.ProfilePicture))
                            {
                                <img src="@Model.ProfilePicture" alt="@Model.FullName" class="rounded-circle img-fluid" style="max-width: 150px; max-height: 150px;">
                            }
                            else
                            {
                                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 150px; height: 150px;">
                                    <i class="fas fa-user fa-4x text-white"></i>
                                </div>
                            }
                            <h5 class="mt-3">@Model.FullName</h5>
                            <p class="text-muted">@Model.PositionName</p>
                            @if (Model.IsActive)
                            {
                                <span class="badge bg-success">نشط</span>
                            }
                            else
                            {
                                <span class="badge bg-danger">غير نشط</span>
                            }
                        </div>
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary">معلومات الاتصال</h6>
                                    <p><strong>البريد الإلكتروني:</strong> @Model.Email</p>
                                    <p><strong>رقم الهاتف:</strong> @Model.PhoneNumber</p>
                                    <p><strong>العنوان:</strong> @Model.Address</p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-primary">معلومات التوظيف</h6>
                                    <p><strong>رقم الموظف:</strong> @Model.EmployeeNumber</p>
                                    <p><strong>القسم:</strong> @Model.DepartmentName</p>
                                    <p><strong>تاريخ التوظيف:</strong> @Model.HireDate.ToString("dd/MM/yyyy")</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Personal Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-user me-2"></i>
                                المعلومات الشخصية
                            </h5>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>الاسم الأول:</strong>
                            <p>@Model.FirstName</p>
                        </div>
                        <div class="col-md-4">
                            <strong>الاسم الأوسط:</strong>
                            <p>@Model.MiddleName</p>
                        </div>
                        <div class="col-md-4">
                            <strong>الاسم الأخير:</strong>
                            <p>@Model.LastName</p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>تاريخ الميلاد:</strong>
                            <p>@(Model.DateOfBirth?.ToString("dd/MM/yyyy") ?? "غير محدد")</p>
                        </div>
                        <div class="col-md-4">
                            <strong>الجنس:</strong>
                            <p>@(Model.Gender == "Male" ? "ذكر" : Model.Gender == "Female" ? "أنثى" : "غير محدد")</p>
                        </div>
                        <div class="col-md-4">
                            <strong>رقم الهوية الوطنية:</strong>
                            <p>@Model.NationalId</p>
                        </div>
                    </div>

                    <!-- Employment Information -->
                    <div class="row mb-4 mt-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-briefcase me-2"></i>
                                معلومات التوظيف
                            </h5>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>رقم الموظف:</strong>
                            <p>@Model.EmployeeNumber</p>
                        </div>
                        <div class="col-md-4">
                            <strong>القسم:</strong>
                            <p>@Model.DepartmentName</p>
                        </div>
                        <div class="col-md-4">
                            <strong>المنصب:</strong>
                            <p>@Model.PositionName</p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>تاريخ التوظيف:</strong>
                            <p>@Model.HireDate.ToString("dd/MM/yyyy")</p>
                        </div>
                        <div class="col-md-4">
                            <strong>الراتب الأساسي:</strong>
                            <p>@Model.Salary.ToString("N2") ج.م</p>
                        </div>
                        <div class="col-md-4">
                            <strong>نوع التوظيف:</strong>
                            <p>@(Model.EmploymentType switch 
                            {
                                "FullTime" => "دوام كامل",
                                "PartTime" => "دوام جزئي", 
                                "Contract" => "عقد",
                                "Intern" => "متدرب",
                                _ => Model.EmploymentType
                            })</p>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4 mt-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-chart-bar me-2"></i>
                                إحصائيات الموظف
                            </h5>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h5>أيام العمل</h5>
                                    <h3>@(DateTime.Now.Subtract(Model.HireDate).Days)</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h5>معدل الحضور</h5>
                                    <h3>95%</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h5>الإجازات المتبقية</h5>
                                    <h3>15</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h5>ساعات العمل الإضافية</h5>
                                    <h3>24</h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <a href="@Url.Action("Edit", new { id = Model.Id })" class="btn btn-warning">
                                <i class="fas fa-edit me-1"></i>
                                تعديل الموظف
                            </a>
                            <a href="@Url.Action("Index")" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>
                                العودة للقائمة
                            </a>
                            <button type="button" class="btn btn-info" onclick="printEmployee()">
                                <i class="fas fa-print me-1"></i>
                                طباعة
                            </button>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="fas fa-cog me-1"></i>
                                    إجراءات أخرى
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#"><i class="fas fa-clock me-2"></i>سجل الحضور</a></li>
                                    <li><a class="dropdown-item" href="#"><i class="fas fa-calendar me-2"></i>طلبات الإجازة</a></li>
                                    <li><a class="dropdown-item" href="#"><i class="fas fa-money-bill me-2"></i>كشوف المرتبات</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="#" onclick="deactivateEmployee('@Model.Id')"><i class="fas fa-ban me-2"></i>إلغاء تفعيل</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function printEmployee() {
            window.print();
        }

        function deactivateEmployee(employeeId) {
            if (confirm('هل أنت متأكد من إلغاء تفعيل هذا الموظف؟')) {
                $.ajax({
                    url: '@Url.Action("Deactivate")',
                    type: 'POST',
                    data: {
                        id: employeeId,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            location.reload();
                        } else {
                            alert('حدث خطأ: ' + result.message);
                        }
                    },
                    error: function() {
                        alert('حدث خطأ أثناء إلغاء تفعيل الموظف');
                    }
                });
            }
        }
    </script>
}
