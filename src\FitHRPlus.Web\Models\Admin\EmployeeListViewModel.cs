using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Web.Models.Admin
{
    /// <summary>
    /// Employee list view model for employee management page
    /// نموذج عرض قائمة الموظفين لصفحة إدارة الموظفين
    /// </summary>
    public class EmployeeListViewModel
    {
        /// <summary>
        /// List of employees
        /// قائمة الموظفين
        /// </summary>
        public List<EmployeeViewModel> Employees { get; set; } = new();

        /// <summary>
        /// Search term for filtering
        /// مصطلح البحث للتصفية
        /// </summary>
        [Display(Name = "Search", Prompt = "Search by name, employee number, email...")]
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Department filter
        /// تصفية القسم
        /// </summary>
        [Display(Name = "Department")]
        public Guid? DepartmentId { get; set; }

        /// <summary>
        /// Position filter
        /// تصفية المنصب
        /// </summary>
        [Display(Name = "Position")]
        public Guid? PositionId { get; set; }

        /// <summary>
        /// Manager filter
        /// تصفية المدير
        /// </summary>
        [Display(Name = "Manager")]
        public Guid? ManagerId { get; set; }

        /// <summary>
        /// Employment status filter
        /// تصفية حالة التوظيف
        /// </summary>
        [Display(Name = "Employment Status")]
        public string? EmploymentStatus { get; set; }

        /// <summary>
        /// Employment type filter
        /// تصفية نوع التوظيف
        /// </summary>
        [Display(Name = "Employment Type")]
        public string? EmploymentType { get; set; }

        /// <summary>
        /// Gender filter
        /// تصفية الجنس
        /// </summary>
        [Display(Name = "Gender")]
        public string? Gender { get; set; }

        /// <summary>
        /// Active status filter
        /// تصفية الحالة النشطة
        /// </summary>
        [Display(Name = "Status")]
        public bool? IsActive { get; set; }

        /// <summary>
        /// Hire date from filter
        /// تصفية تاريخ التوظيف من
        /// </summary>
        [DataType(DataType.Date)]
        [Display(Name = "Hire Date From")]
        public DateTime? HireDateFrom { get; set; }

        /// <summary>
        /// Hire date to filter
        /// تصفية تاريخ التوظيف إلى
        /// </summary>
        [DataType(DataType.Date)]
        [Display(Name = "Hire Date To")]
        public DateTime? HireDateTo { get; set; }

        /// <summary>
        /// Age from filter
        /// تصفية العمر من
        /// </summary>
        [Range(18, 100, ErrorMessage = "Age must be between 18 and 100")]
        [Display(Name = "Age From")]
        public int? AgeFrom { get; set; }

        /// <summary>
        /// Age to filter
        /// تصفية العمر إلى
        /// </summary>
        [Range(18, 100, ErrorMessage = "Age must be between 18 and 100")]
        [Display(Name = "Age To")]
        public int? AgeTo { get; set; }

        /// <summary>
        /// Salary from filter
        /// تصفية الراتب من
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "Salary must be a positive number")]
        [Display(Name = "Salary From")]
        [DataType(DataType.Currency)]
        public decimal? SalaryFrom { get; set; }

        /// <summary>
        /// Salary to filter
        /// تصفية الراتب إلى
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "Salary must be a positive number")]
        [Display(Name = "Salary To")]
        [DataType(DataType.Currency)]
        public decimal? SalaryTo { get; set; }

        /// <summary>
        /// Sort field
        /// حقل الترتيب
        /// </summary>
        public string? SortBy { get; set; } = "FirstName";

        /// <summary>
        /// Sort direction (asc/desc)
        /// اتجاه الترتيب (تصاعدي/تنازلي)
        /// </summary>
        public string? SortDirection { get; set; } = "asc";

        /// <summary>
        /// Current page number
        /// رقم الصفحة الحالية
        /// </summary>
        public int CurrentPage { get; set; } = 1;

        /// <summary>
        /// Page size
        /// حجم الصفحة
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// Total number of employees
        /// العدد الإجمالي للموظفين
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Total number of pages
        /// العدد الإجمالي للصفحات
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// Whether there is a previous page
        /// ما إذا كانت هناك صفحة سابقة
        /// </summary>
        public bool HasPreviousPage => CurrentPage > 1;

        /// <summary>
        /// Whether there is a next page
        /// ما إذا كانت هناك صفحة تالية
        /// </summary>
        public bool HasNextPage => CurrentPage < TotalPages;

        /// <summary>
        /// Include inactive employees
        /// تضمين الموظفين غير النشطين
        /// </summary>
        [Display(Name = "Include Inactive")]
        public bool IncludeInactive { get; set; } = false;

        /// <summary>
        /// Include terminated employees
        /// تضمين الموظفين المنتهية خدمتهم
        /// </summary>
        [Display(Name = "Include Terminated")]
        public bool IncludeTerminated { get; set; } = false;

        // Filter options
        /// <summary>
        /// Available departments for filtering
        /// الأقسام المتاحة للتصفية
        /// </summary>
        public List<DepartmentViewModel> Departments { get; set; } = new();

        /// <summary>
        /// Available positions for filtering
        /// المناصب المتاحة للتصفية
        /// </summary>
        public List<PositionViewModel> Positions { get; set; } = new();

        /// <summary>
        /// Available managers for filtering
        /// المديرين المتاحين للتصفية
        /// </summary>
        public List<EmployeeViewModel> Managers { get; set; } = new();

        /// <summary>
        /// Available employment statuses
        /// حالات التوظيف المتاحة
        /// </summary>
        public List<string> EmploymentStatuses { get; set; } = new()
        {
            "Active",
            "Inactive",
            "Terminated",
            "On Leave",
            "Suspended"
        };

        /// <summary>
        /// Available employment types
        /// أنواع التوظيف المتاحة
        /// </summary>
        public List<string> EmploymentTypes { get; set; } = new()
        {
            "Full-time",
            "Part-time",
            "Contract",
            "Temporary",
            "Intern",
            "Consultant"
        };

        /// <summary>
        /// Available genders
        /// الأجناس المتاحة
        /// </summary>
        public List<string> Genders { get; set; } = new()
        {
            "Male",
            "Female"
        };

        /// <summary>
        /// Available page sizes
        /// أحجام الصفحات المتاحة
        /// </summary>
        public List<int> PageSizes { get; set; } = new() { 10, 25, 50, 100 };

        /// <summary>
        /// Available sort options
        /// خيارات الترتيب المتاحة
        /// </summary>
        public Dictionary<string, string> SortOptions { get; set; } = new()
        {
            { "FirstName", "First Name" },
            { "LastName", "Last Name" },
            { "EmployeeNumber", "Employee Number" },
            { "Email", "Email" },
            { "HireDate", "Hire Date" },
            { "Department", "Department" },
            { "Position", "Position" },
            { "Salary", "Salary" }
        };

        /// <summary>
        /// Statistics summary
        /// ملخص الإحصائيات
        /// </summary>
        public EmployeeStatisticsSummary Statistics { get; set; } = new();
    }

    /// <summary>
    /// Employee statistics summary for the list view
    /// ملخص إحصائيات الموظفين لعرض القائمة
    /// </summary>
    public class EmployeeStatisticsSummary
    {
        /// <summary>
        /// Total active employees
        /// إجمالي الموظفين النشطين
        /// </summary>
        public int TotalActive { get; set; }

        /// <summary>
        /// Total inactive employees
        /// إجمالي الموظفين غير النشطين
        /// </summary>
        public int TotalInactive { get; set; }

        /// <summary>
        /// Total terminated employees
        /// إجمالي الموظفين المنتهية خدمتهم
        /// </summary>
        public int TotalTerminated { get; set; }

        /// <summary>
        /// Total male employees
        /// إجمالي الموظفين الذكور
        /// </summary>
        public int TotalMale { get; set; }

        /// <summary>
        /// Total female employees
        /// إجمالي الموظفات الإناث
        /// </summary>
        public int TotalFemale { get; set; }

        /// <summary>
        /// Average age
        /// متوسط العمر
        /// </summary>
        public double AverageAge { get; set; }

        /// <summary>
        /// Average years of service
        /// متوسط سنوات الخدمة
        /// </summary>
        public double AverageYearsOfService { get; set; }

        /// <summary>
        /// New hires this month
        /// التوظيفات الجديدة هذا الشهر
        /// </summary>
        public int NewHiresThisMonth { get; set; }

        /// <summary>
        /// Terminations this month
        /// إنهاء الخدمات هذا الشهر
        /// </summary>
        public int TerminationsThisMonth { get; set; }
    }
}
