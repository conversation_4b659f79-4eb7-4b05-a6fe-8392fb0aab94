using FitHRPlus.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace FitHRPlus.Persistence
{
    /// <summary>
    /// Main database context for FIT HR Plus system
    /// سياق قاعدة البيانات الرئيسي لنظام FIT HR Plus
    /// </summary>
    public class FitHRContext : DbContext
    {
        public FitHRContext(DbContextOptions<FitHRContext> options) : base(options)
        {
        }

        // Core Entities - الكيانات الأساسية
        public DbSet<Company> Companies { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<UserRole> UserRoles { get; set; }

        // Organizational Structure - الهيكل التنظيمي
        public DbSet<Department> Departments { get; set; }
        public DbSet<Position> Positions { get; set; }
        public DbSet<Employee> Employees { get; set; }

        // Attendance Management - إدارة الحضور
        public DbSet<AttendanceRecord> AttendanceRecords { get; set; }
        public DbSet<BiometricDevice> BiometricDevices { get; set; }
        public DbSet<WorkShift> WorkShifts { get; set; }
        public DbSet<EmployeeShift> EmployeeShifts { get; set; }

        // Leave Management - إدارة الإجازات
        public DbSet<LeaveType> LeaveTypes { get; set; }
        public DbSet<LeaveRequest> LeaveRequests { get; set; }
        public DbSet<LeaveBalance> LeaveBalances { get; set; }
        public DbSet<Holiday> Holidays { get; set; }

        // Payroll Management - إدارة الرواتب
        public DbSet<SalaryComponent> SalaryComponents { get; set; }
        public DbSet<EmployeeSalary> EmployeeSalaries { get; set; }
        public DbSet<Payroll> Payrolls { get; set; }

        // System Management - إدارة النظام
        public DbSet<AuditLog> AuditLogs { get; set; }
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<CompanySettings> CompanySettings { get; set; }
        public DbSet<WorkSchedule> WorkSchedules { get; set; }
        public DbSet<WorkScheduleDay> WorkScheduleDays { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure entity relationships and constraints
            ConfigureCompanyEntity(modelBuilder);
            ConfigureUserEntities(modelBuilder);
            ConfigureOrganizationalEntities(modelBuilder);
            ConfigureAttendanceEntities(modelBuilder);
            ConfigureLeaveEntities(modelBuilder);
            ConfigurePayrollEntities(modelBuilder);
            ConfigureSystemEntities(modelBuilder);

            // Configure company settings entities
            ConfigureCompanySettingsEntities(modelBuilder);

            // Configure indexes for performance
            ConfigureIndexes(modelBuilder);
        }

        private void ConfigureCompanyEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Company>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.NameAr).IsRequired().HasMaxLength(200);
                entity.Property(e => e.TaxNumber).HasMaxLength(50);
                entity.Property(e => e.CommercialRegister).HasMaxLength(50);
                entity.Property(e => e.Email).HasMaxLength(100);
                entity.Property(e => e.Currency).HasMaxLength(3).HasDefaultValue("EGP");
                entity.Property(e => e.TimeZone).HasMaxLength(50).HasDefaultValue("Africa/Cairo");
                entity.Property(e => e.SubscriptionPlan).HasMaxLength(50).HasDefaultValue("Basic");

                // Unique constraints
                entity.HasIndex(e => e.TaxNumber).IsUnique().HasFilter("[TaxNumber] IS NOT NULL");
                entity.HasIndex(e => e.CommercialRegister).IsUnique().HasFilter("[CommercialRegister] IS NOT NULL");
                entity.HasIndex(e => e.Email).IsUnique().HasFilter("[Email] IS NOT NULL");
            });
        }

        private void ConfigureUserEntities(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Username).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Email).IsRequired().HasMaxLength(100);
                entity.Property(e => e.PasswordHash).IsRequired().HasMaxLength(500);
                entity.Property(e => e.Salt).IsRequired().HasMaxLength(100);
                entity.Property(e => e.FirstName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.LastName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.PreferredLanguage).HasMaxLength(5).HasDefaultValue("ar");
                entity.Property(e => e.TimeZone).HasMaxLength(50).HasDefaultValue("Africa/Cairo");

                // Unique constraints
                entity.HasIndex(e => e.Username).IsUnique();
                entity.HasIndex(e => e.Email).IsUnique();
            });

            modelBuilder.Entity<Role>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.NameAr).IsRequired().HasMaxLength(100);
                entity.HasIndex(e => e.Name).IsUnique();
            });

            modelBuilder.Entity<UserRole>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasOne(e => e.User).WithMany(u => u.UserRoles).HasForeignKey(e => e.UserId);
                entity.HasOne(e => e.Role).WithMany(r => r.UserRoles).HasForeignKey(e => e.RoleId);
                entity.HasOne(e => e.Company).WithMany().HasForeignKey(e => e.CompanyId);
                entity.HasOne(e => e.AssignedByUser).WithMany().HasForeignKey(e => e.AssignedBy).OnDelete(DeleteBehavior.NoAction);

                // Unique constraint: one role per user per company
                entity.HasIndex(e => new { e.UserId, e.CompanyId, e.RoleId }).IsUnique();
            });
        }

        private void ConfigureOrganizationalEntities(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Department>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.NameAr).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Code).IsRequired().HasMaxLength(20);

                // Configure decimal precision for financial fields
                entity.Property(e => e.Budget).HasPrecision(18, 2); // 18 digits total, 2 decimal places

                entity.HasOne(e => e.Company).WithMany(c => c.Departments).HasForeignKey(e => e.CompanyId);
                entity.HasOne(e => e.Manager).WithMany().HasForeignKey(e => e.ManagerId).OnDelete(DeleteBehavior.NoAction);
                entity.HasOne(e => e.ParentDepartment).WithMany(d => d.ChildDepartments).HasForeignKey(e => e.ParentDepartmentId).OnDelete(DeleteBehavior.NoAction);

                // Unique constraint: code per company
                entity.HasIndex(e => new { e.CompanyId, e.Code }).IsUnique();
            });

            modelBuilder.Entity<Position>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
                entity.Property(e => e.TitleAr).IsRequired().HasMaxLength(200);

                // Configure decimal precision for salary fields
                entity.Property(e => e.MinSalary).HasPrecision(18, 2); // 18 digits total, 2 decimal places
                entity.Property(e => e.MaxSalary).HasPrecision(18, 2); // 18 digits total, 2 decimal places

                entity.HasOne(e => e.Company).WithMany().HasForeignKey(e => e.CompanyId).OnDelete(DeleteBehavior.NoAction);
                entity.HasOne(e => e.Department).WithMany(d => d.Positions).HasForeignKey(e => e.DepartmentId).OnDelete(DeleteBehavior.NoAction);
            });

            modelBuilder.Entity<Employee>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.EmployeeCode).IsRequired().HasMaxLength(50);
                entity.Property(e => e.FirstName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.FirstNameAr).IsRequired().HasMaxLength(100);
                entity.Property(e => e.LastName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.LastNameAr).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Currency).HasMaxLength(3).HasDefaultValue("EGP");
                entity.Property(e => e.PayrollFrequency).HasMaxLength(20).HasDefaultValue("Monthly");

                // Configure decimal precision for salary fields
                entity.Property(e => e.BaseSalary).HasPrecision(18, 2); // 18 digits total, 2 decimal places

                entity.HasOne(e => e.Company).WithMany(c => c.Employees).HasForeignKey(e => e.CompanyId).OnDelete(DeleteBehavior.NoAction);
                entity.HasOne(e => e.Department).WithMany(d => d.Employees).HasForeignKey(e => e.DepartmentId).OnDelete(DeleteBehavior.NoAction);
                entity.HasOne(e => e.Position).WithMany(p => p.Employees).HasForeignKey(e => e.PositionId).OnDelete(DeleteBehavior.SetNull);
                entity.HasOne(e => e.User).WithOne(u => u.Employee).HasForeignKey<Employee>(e => e.UserId).OnDelete(DeleteBehavior.SetNull);
                entity.HasOne(e => e.Manager).WithMany(m => m.DirectReports).HasForeignKey(e => e.ReportsTo).OnDelete(DeleteBehavior.NoAction);
                entity.HasOne(e => e.WorkSchedule).WithMany(ws => ws.Employees).HasForeignKey(e => e.WorkScheduleId).OnDelete(DeleteBehavior.SetNull);

                // Unique constraints
                entity.HasIndex(e => new { e.CompanyId, e.EmployeeCode }).IsUnique();
                entity.HasIndex(e => e.NationalId).IsUnique().HasFilter("[NationalId] IS NOT NULL");
                entity.HasIndex(e => e.Email).IsUnique().HasFilter("[Email] IS NOT NULL");
                entity.HasIndex(e => e.PassportNumber).IsUnique().HasFilter("[PassportNumber] IS NOT NULL");

                // Enum conversions
                entity.Property(e => e.Gender).HasConversion<string>();
                entity.Property(e => e.MaritalStatus).HasConversion<string>();
                entity.Property(e => e.EmploymentType).HasConversion<string>();
            });
        }

        private void ConfigureAttendanceEntities(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<AttendanceRecord>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Status).HasMaxLength(20).HasDefaultValue("Present");
                entity.Property(e => e.AttendanceType).HasMaxLength(20).HasDefaultValue("Regular");

                // Configure decimal precision for time and location fields
                entity.Property(e => e.WorkingHours).HasPrecision(8, 2); // Hours with 2 decimal places
                entity.Property(e => e.OvertimeHours).HasPrecision(8, 2); // Hours with 2 decimal places
                entity.Property(e => e.BreakHours).HasPrecision(8, 2); // Hours with 2 decimal places
                entity.Property(e => e.CheckInLatitude).HasPrecision(10, 7); // GPS coordinates precision
                entity.Property(e => e.CheckInLongitude).HasPrecision(10, 7); // GPS coordinates precision
                entity.Property(e => e.CheckOutLatitude).HasPrecision(10, 7); // GPS coordinates precision
                entity.Property(e => e.CheckOutLongitude).HasPrecision(10, 7); // GPS coordinates precision

                entity.HasOne(e => e.Employee).WithMany(emp => emp.AttendanceRecords).HasForeignKey(e => e.EmployeeId);
                entity.HasOne(e => e.Device).WithMany(d => d.AttendanceRecords).HasForeignKey(e => e.DeviceId).OnDelete(DeleteBehavior.SetNull);
                entity.HasOne(e => e.ApprovedByEmployee).WithMany().HasForeignKey(e => e.ApprovedBy).OnDelete(DeleteBehavior.NoAction);

                // Unique constraint: one record per employee per date
                entity.HasIndex(e => new { e.EmployeeId, e.AttendanceDate }).IsUnique();
            });

            modelBuilder.Entity<BiometricDevice>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.DeviceName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.DeviceType).IsRequired().HasMaxLength(50);

                // Configure decimal precision for location fields
                entity.Property(e => e.Latitude).HasPrecision(10, 7); // GPS coordinates precision
                entity.Property(e => e.Longitude).HasPrecision(10, 7); // GPS coordinates precision

                entity.HasOne(e => e.Company).WithMany(c => c.BiometricDevices).HasForeignKey(e => e.CompanyId);
                entity.HasIndex(e => e.SerialNumber).IsUnique().HasFilter("[SerialNumber] IS NOT NULL");
            });

            modelBuilder.Entity<WorkShift>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.NameAr).IsRequired().HasMaxLength(200);
                entity.Property(e => e.WorkingDays).HasMaxLength(20).HasDefaultValue("1,2,3,4,5");

                entity.HasOne(e => e.Company).WithMany(c => c.WorkShifts).HasForeignKey(e => e.CompanyId);
            });

            modelBuilder.Entity<EmployeeShift>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasOne(e => e.Employee).WithMany().HasForeignKey(e => e.EmployeeId);
                entity.HasOne(e => e.Shift).WithMany(s => s.EmployeeShifts).HasForeignKey(e => e.ShiftId);
            });
        }

        private void ConfigureLeaveEntities(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<LeaveType>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.NameAr).IsRequired().HasMaxLength(200);

                entity.HasOne(e => e.Company).WithMany(c => c.LeaveTypes).HasForeignKey(e => e.CompanyId);
            });

            modelBuilder.Entity<LeaveRequest>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Status).HasMaxLength(20).HasDefaultValue("Pending");

                // Configure decimal precision for leave days
                entity.Property(e => e.TotalDays).HasPrecision(5, 2); // Days with 2 decimal places

                entity.HasOne(e => e.Employee).WithMany(emp => emp.LeaveRequests).HasForeignKey(e => e.EmployeeId);
                entity.HasOne(e => e.LeaveType).WithMany(lt => lt.LeaveRequests).HasForeignKey(e => e.LeaveTypeId);
                entity.HasOne(e => e.ApprovedByEmployee).WithMany().HasForeignKey(e => e.ApprovedBy).OnDelete(DeleteBehavior.NoAction);
            });

            modelBuilder.Entity<LeaveBalance>(entity =>
            {
                entity.HasKey(e => e.Id);

                // Configure decimal precision for leave balance fields
                entity.Property(e => e.EntitledDays).HasPrecision(5, 2); // Days with 2 decimal places
                entity.Property(e => e.UsedDays).HasPrecision(5, 2); // Days with 2 decimal places
                entity.Property(e => e.RemainingDays).HasPrecision(5, 2); // Days with 2 decimal places
                entity.Property(e => e.CarriedForwardDays).HasPrecision(5, 2); // Days with 2 decimal places

                entity.HasOne(e => e.Employee).WithMany().HasForeignKey(e => e.EmployeeId);
                entity.HasOne(e => e.LeaveType).WithMany(lt => lt.LeaveBalances).HasForeignKey(e => e.LeaveTypeId);

                // Unique constraint: one balance per employee per leave type per year
                entity.HasIndex(e => new { e.EmployeeId, e.LeaveTypeId, e.Year }).IsUnique();
            });

            modelBuilder.Entity<Holiday>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.NameAr).IsRequired().HasMaxLength(200);
                entity.Property(e => e.AppliesTo).HasMaxLength(50).HasDefaultValue("All");

                entity.HasOne(e => e.Company).WithMany(c => c.Holidays).HasForeignKey(e => e.CompanyId);
            });
        }

        private void ConfigurePayrollEntities(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<SalaryComponent>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.NameAr).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Type).IsRequired().HasMaxLength(20);

                // Configure decimal precision for financial fields
                entity.Property(e => e.Amount).HasPrecision(18, 2);
                entity.Property(e => e.Percentage).HasPrecision(5, 4); // For percentages like 0.1234 (12.34%)
                entity.Property(e => e.MinAmount).HasPrecision(18, 2);
                entity.Property(e => e.MaxAmount).HasPrecision(18, 2);

                entity.HasOne(e => e.Company).WithMany(c => c.SalaryComponents).HasForeignKey(e => e.CompanyId);
            });

            modelBuilder.Entity<EmployeeSalary>(entity =>
            {
                entity.HasKey(e => e.Id);

                // Configure decimal precision for financial fields
                entity.Property(e => e.Amount).HasPrecision(18, 2);
                entity.Property(e => e.Percentage).HasPrecision(5, 4); // For percentages like 0.1234 (12.34%)

                entity.HasOne(e => e.Employee).WithMany(emp => emp.EmployeeSalaries).HasForeignKey(e => e.EmployeeId);
                entity.HasOne(e => e.SalaryComponent).WithMany(sc => sc.EmployeeSalaries).HasForeignKey(e => e.SalaryComponentId);
            });

            modelBuilder.Entity<Payroll>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Status).HasMaxLength(20).HasDefaultValue("Draft");

                // Configure decimal precision for financial fields
                entity.Property(e => e.BasicSalary).HasPrecision(18, 2);
                entity.Property(e => e.TotalAllowances).HasPrecision(18, 2);
                entity.Property(e => e.TotalDeductions).HasPrecision(18, 2);
                entity.Property(e => e.GrossSalary).HasPrecision(18, 2);
                entity.Property(e => e.NetSalary).HasPrecision(18, 2);
                entity.Property(e => e.OvertimeHours).HasPrecision(8, 2); // Hours with 2 decimal places
                entity.Property(e => e.OvertimeAmount).HasPrecision(18, 2);
                entity.Property(e => e.IncomeTax).HasPrecision(18, 2);
                entity.Property(e => e.SocialInsuranceEmployee).HasPrecision(18, 2);
                entity.Property(e => e.SocialInsuranceEmployer).HasPrecision(18, 2);
                entity.Property(e => e.MedicalInsurance).HasPrecision(18, 2);
                entity.Property(e => e.LateDeduction).HasPrecision(18, 2);
                entity.Property(e => e.ActualWorkingDays).HasPrecision(5, 2); // Days with 2 decimal places
                entity.Property(e => e.AbsentDays).HasPrecision(5, 2);
                entity.Property(e => e.LeaveDays).HasPrecision(5, 2);

                entity.HasOne(e => e.Company).WithMany().HasForeignKey(e => e.CompanyId);
                entity.HasOne(e => e.Employee).WithMany(emp => emp.Payrolls).HasForeignKey(e => e.EmployeeId);
                entity.HasOne(e => e.ProcessedByEmployee).WithMany().HasForeignKey(e => e.ProcessedBy).OnDelete(DeleteBehavior.NoAction);
                entity.HasOne(e => e.ApprovedByEmployee).WithMany().HasForeignKey(e => e.ApprovedBy).OnDelete(DeleteBehavior.NoAction);
                entity.HasOne(e => e.PaidByEmployee).WithMany().HasForeignKey(e => e.PaidBy).OnDelete(DeleteBehavior.NoAction);

                // Unique constraint: one payroll per employee per month/year
                entity.HasIndex(e => new { e.EmployeeId, e.PayrollMonth, e.PayrollYear }).IsUnique();
            });
        }

        private void ConfigureSystemEntities(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<AuditLog>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Action).IsRequired().HasMaxLength(100);
                entity.Property(e => e.EntityName).IsRequired().HasMaxLength(100);

                entity.HasOne(e => e.User).WithMany(u => u.AuditLogs).HasForeignKey(e => e.UserId);
            });

            modelBuilder.Entity<Notification>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
                entity.Property(e => e.TitleAr).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Message).IsRequired().HasMaxLength(1000);
                entity.Property(e => e.MessageAr).IsRequired().HasMaxLength(1000);
                entity.Property(e => e.Type).HasMaxLength(50).HasDefaultValue("Info");

                entity.HasOne(e => e.User).WithMany(u => u.Notifications).HasForeignKey(e => e.UserId);
            });
        }

        private void ConfigureCompanySettingsEntities(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<CompanySettings>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.WeekendDays).IsRequired().HasMaxLength(20).HasDefaultValue("Friday,Saturday");
                entity.Property(e => e.Currency).IsRequired().HasMaxLength(10).HasDefaultValue("EGP");
                entity.Property(e => e.CurrencySymbol).IsRequired().HasMaxLength(10).HasDefaultValue("ج.م");
                entity.Property(e => e.DefaultLanguage).IsRequired().HasMaxLength(10).HasDefaultValue("ar");
                entity.Property(e => e.TimeZone).IsRequired().HasMaxLength(50).HasDefaultValue("Africa/Cairo");
                entity.Property(e => e.DateFormat).IsRequired().HasMaxLength(20).HasDefaultValue("dd/MM/yyyy");
                entity.Property(e => e.TimeFormat).IsRequired().HasMaxLength(20).HasDefaultValue("HH:mm");

                // Configure decimal precision for working hours and rates
                entity.Property(e => e.WorkingHoursPerDay).HasPrecision(5, 2); // Hours with 2 decimal places
                entity.Property(e => e.WorkingDaysPerWeek).HasPrecision(5, 2); // Days with 2 decimal places
                entity.Property(e => e.BreakTimeMinutes).HasPrecision(5, 0); // Minutes as whole numbers
                entity.Property(e => e.OvertimeThresholdHours).HasPrecision(5, 2); // Hours with 2 decimal places
                entity.Property(e => e.OvertimeMultiplier).HasPrecision(5, 2); // Multiplier like 1.5
                entity.Property(e => e.AnnualLeaveEntitlement).HasPrecision(5, 2); // Days with 2 decimal places
                entity.Property(e => e.SickLeaveEntitlement).HasPrecision(5, 2); // Days with 2 decimal places
                entity.Property(e => e.MaternityLeaveEntitlement).HasPrecision(5, 2); // Days with 2 decimal places
                entity.Property(e => e.PaternityLeaveEntitlement).HasPrecision(5, 2); // Days with 2 decimal places
                entity.Property(e => e.IncomeTaxRate).HasPrecision(5, 4); // Tax rates like 0.1400 (14%)
                entity.Property(e => e.SocialInsuranceEmployeeRate).HasPrecision(5, 4); // Insurance rates
                entity.Property(e => e.SocialInsuranceEmployerRate).HasPrecision(5, 4); // Insurance rates
                entity.Property(e => e.MedicalInsuranceRate).HasPrecision(5, 4); // Insurance rates

                entity.HasOne(e => e.Company).WithOne().HasForeignKey<CompanySettings>(e => e.CompanyId);

                // Unique constraint: one settings per company
                entity.HasIndex(e => e.CompanyId).IsUnique();
            });

            modelBuilder.Entity<WorkSchedule>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.NameAr).IsRequired().HasMaxLength(100);

                entity.HasOne(e => e.Company).WithMany().HasForeignKey(e => e.CompanyId);

                // Unique constraint: one default schedule per company
                entity.HasIndex(e => new { e.CompanyId, e.IsDefault })
                    .IsUnique()
                    .HasFilter("[IsDefault] = 1");
            });

            modelBuilder.Entity<WorkScheduleDay>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.DayOfWeek).HasConversion<int>();

                // Configure decimal precision for working hours
                entity.Property(e => e.WorkingHours).HasPrecision(5, 2); // Hours with 2 decimal places

                entity.HasOne(e => e.WorkSchedule).WithMany(ws => ws.WorkScheduleDays).HasForeignKey(e => e.WorkScheduleId);

                // Unique constraint: one day per schedule
                entity.HasIndex(e => new { e.WorkScheduleId, e.DayOfWeek }).IsUnique();
            });
        }

        private void ConfigureIndexes(ModelBuilder modelBuilder)
        {
            // Employee indexes for performance
            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.CompanyId)
                .HasDatabaseName("IX_Employees_CompanyId");

            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.DepartmentId)
                .HasDatabaseName("IX_Employees_DepartmentId");

            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.PositionId)
                .HasDatabaseName("IX_Employees_PositionId");

            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.HireDate)
                .HasDatabaseName("IX_Employees_HireDate");

            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.EmployeeCode)
                .HasDatabaseName("IX_Employees_EmployeeCode");

            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.IsActive)
                .HasDatabaseName("IX_Employees_IsActive");

            // Attendance indexes for performance
            modelBuilder.Entity<AttendanceRecord>()
                .HasIndex(e => e.AttendanceDate)
                .HasDatabaseName("IX_AttendanceRecords_Date");

            modelBuilder.Entity<AttendanceRecord>()
                .HasIndex(e => e.Status)
                .HasDatabaseName("IX_AttendanceRecords_Status");

            modelBuilder.Entity<AttendanceRecord>()
                .HasIndex(e => new { e.EmployeeId, e.AttendanceDate })
                .HasDatabaseName("IX_AttendanceRecords_Employee_Date");

            // Payroll indexes for performance
            modelBuilder.Entity<Payroll>()
                .HasIndex(e => new { e.PayrollMonth, e.PayrollYear })
                .HasDatabaseName("IX_Payrolls_Month_Year");

            modelBuilder.Entity<Payroll>()
                .HasIndex(e => e.Status)
                .HasDatabaseName("IX_Payrolls_Status");

            modelBuilder.Entity<Payroll>()
                .HasIndex(e => e.CompanyId)
                .HasDatabaseName("IX_Payrolls_CompanyId");

            // Leave indexes for performance
            modelBuilder.Entity<LeaveRequest>()
                .HasIndex(e => e.Status)
                .HasDatabaseName("IX_LeaveRequests_Status");

            modelBuilder.Entity<LeaveRequest>()
                .HasIndex(e => new { e.StartDate, e.EndDate })
                .HasDatabaseName("IX_LeaveRequests_DateRange");

            modelBuilder.Entity<LeaveRequest>()
                .HasIndex(e => e.EmployeeId)
                .HasDatabaseName("IX_LeaveRequests_EmployeeId");

            // Company indexes for performance
            modelBuilder.Entity<Company>()
                .HasIndex(e => e.IsActive)
                .HasDatabaseName("IX_Companies_IsActive");

            modelBuilder.Entity<Company>()
                .HasIndex(e => e.SubscriptionExpiry)
                .HasDatabaseName("IX_Companies_SubscriptionExpiry");

            // Department indexes for performance
            modelBuilder.Entity<Department>()
                .HasIndex(e => e.CompanyId)
                .HasDatabaseName("IX_Departments_CompanyId");

            modelBuilder.Entity<Department>()
                .HasIndex(e => e.IsActive)
                .HasDatabaseName("IX_Departments_IsActive");

            // Position indexes for performance
            modelBuilder.Entity<Position>()
                .HasIndex(e => e.DepartmentId)
                .HasDatabaseName("IX_Positions_DepartmentId");

            modelBuilder.Entity<Position>()
                .HasIndex(e => e.CompanyId)
                .HasDatabaseName("IX_Positions_CompanyId");

            // User indexes for performance
            modelBuilder.Entity<User>()
                .HasIndex(e => e.IsActive)
                .HasDatabaseName("IX_Users_IsActive");

            modelBuilder.Entity<User>()
                .HasIndex(e => e.LastLoginAt)
                .HasDatabaseName("IX_Users_LastLoginAt");

            // Audit log indexes for performance
            modelBuilder.Entity<AuditLog>()
                .HasIndex(e => e.CreatedAt)
                .HasDatabaseName("IX_AuditLogs_CreatedAt");

            modelBuilder.Entity<AuditLog>()
                .HasIndex(e => e.UserId)
                .HasDatabaseName("IX_AuditLogs_UserId");

            modelBuilder.Entity<AuditLog>()
                .HasIndex(e => e.EntityName)
                .HasDatabaseName("IX_AuditLogs_EntityName");
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            // Update timestamps for BaseEntity
            var entries = ChangeTracker.Entries()
                .Where(e => e.Entity is Domain.Common.BaseEntity && (e.State == EntityState.Added || e.State == EntityState.Modified));

            foreach (var entityEntry in entries)
            {
                var entity = (Domain.Common.BaseEntity)entityEntry.Entity;

                if (entityEntry.State == EntityState.Added)
                {
                    entity.CreatedAt = DateTime.UtcNow;
                }

                entity.UpdatedAt = DateTime.UtcNow;
            }

            return await base.SaveChangesAsync(cancellationToken);
        }
    }
}