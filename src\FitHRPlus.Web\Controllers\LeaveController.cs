using FitHRPlus.Application.DTOs.Employee;
using FitHRPlus.Application.DTOs.Employees;
using FitHRPlus.Application.DTOs.Leave;
using FitHRPlus.Application.Interfaces;
using FitHRPlus.Web.Models.Leave;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace FitHRPlus.Web.Controllers
{
    /// <summary>
    /// Leave management controller
    /// وحدة تحكم إدارة الإجازات
    /// </summary>
    [Authorize]
    public class LeaveController : Controller
    {
        private readonly ILeaveService _leaveService;
        private readonly IEmployeeService _employeeService;
        private readonly ILogger<LeaveController> _logger;

        public LeaveController(
            ILeaveService leaveService,
            IEmployeeService employeeService,
            ILogger<LeaveController> logger)
        {
            _leaveService = leaveService;
            _employeeService = employeeService;
            _logger = logger;
        }

        /// <summary>
        /// Leave requests list
        /// قائمة طلبات الإجازات
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Index([FromQuery] LeaveRequestListDto request)
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "Company information not found";
                    return RedirectToAction("Index", "Home");
                }

                request.CompanyId = companyId;

                // Set default date range if not provided
                if (!request.DateFrom.HasValue && !request.DateTo.HasValue)
                {
                    request.DateFrom = DateTime.Today.AddMonths(-3);
                    request.DateTo = DateTime.Today.AddMonths(3);
                }

                var result = await _leaveService.GetLeaveRequestsAsync(request);

                if (result.IsSuccess)
                {
                    var viewModel = new LeaveRequestListViewModel
                    {
                        LeaveRequests = result.Data.Items.Select(MapToLeaveRequestViewModel).ToList(),
                        DateFrom = request.DateFrom,
                        DateTo = request.DateTo,
                        Status = request.Status,
                        EmployeeId = request.EmployeeId,
                        LeaveTypeId = request.LeaveTypeId,
                        SearchTerm = request.SearchTerm,
                        CurrentPage = result.Data.CurrentPage,
                        TotalPages = result.Data.TotalPages,
                        TotalCount = result.Data.TotalCount,
                        PageSize = result.Data.PageSize
                    };

                    // Load filter options
                    await LoadFilterOptionsAsync(viewModel, companyId);

                    return View(viewModel);
                }

                TempData["ErrorMessage"] = result.ErrorMessage;
                return View(new LeaveRequestListViewModel());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading leave requests");
                TempData["ErrorMessage"] = "An error occurred while loading leave requests";
                return View(new LeaveRequestListViewModel());
            }
        }

        /// <summary>
        /// Create new leave request
        /// إنشاء طلب إجازة جديد
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Create()
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "Company information not found";
                    return RedirectToAction(nameof(Index));
                }

                var viewModel = new CreateLeaveRequestViewModel
                {
                    StartDate = DateTime.Today.AddDays(1),
                    EndDate = DateTime.Today.AddDays(1)
                };

                await LoadCreateFormDataAsync(viewModel, companyId);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading create leave request page");
                TempData["ErrorMessage"] = "An error occurred while loading the page";
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// Create new leave request - POST
        /// إنشاء طلب إجازة جديد - POST
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CreateLeaveRequestViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                    if (Guid.TryParse(companyIdClaim, out var companyId))
                    {
                        await LoadCreateFormDataAsync(model, companyId);
                    }
                    return View(model);
                }

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    ModelState.AddModelError("", "User information not found");
                    return View(model);
                }

                var request = new CreateLeaveRequestDto
                {
                    EmployeeId = model.EmployeeId,
                    LeaveTypeId = model.LeaveTypeId,
                    StartDate = model.StartDate,
                    EndDate = model.EndDate,
                    Reason = model.Reason,
                    ReasonAr = model.ReasonAr,
                    EmergencyContact = model.EmergencyContact,
                    EmergencyPhone = model.EmergencyPhone
                };

                var result = await _leaveService.CreateLeaveRequestAsync(request, userId);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = "Leave request created successfully";
                    return RedirectToAction(nameof(Details), new { id = result.Data!.Id });
                }

                ModelState.AddModelError("", result.ErrorMessage ?? "Failed to create leave request");
                var companyIdClaim2 = User.FindFirst("CompanyId")?.Value;
                if (Guid.TryParse(companyIdClaim2, out var companyId2))
                {
                    await LoadCreateFormDataAsync(model, companyId2);
                }
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating leave request");
                ModelState.AddModelError("", "An error occurred while creating the leave request");
                return View(model);
            }
        }

        /// <summary>
        /// Leave request details
        /// تفاصيل طلب الإجازة
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Details(Guid id)
        {
            try
            {
                var result = await _leaveService.GetLeaveRequestByIdAsync(id);

                if (result.IsSuccess && result.Data != null)
                {
                    var viewModel = MapToLeaveRequestDetailsViewModel(result.Data);
                    return View(viewModel);
                }

                TempData["ErrorMessage"] = result.ErrorMessage ?? "Leave request not found";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading leave request details for ID: {Id}", id);
                TempData["ErrorMessage"] = "An error occurred while loading leave request details";
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// Approve leave request
        /// الموافقة على طلب الإجازة
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Approve(Guid id, string? notes)
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    TempData["ErrorMessage"] = "User information not found";
                    return RedirectToAction(nameof(Details), new { id });
                }

                var request = new ApproveLeaveRequestDto
                {
                    LeaveRequestId = id,
                    Notes = notes
                };

                var result = await _leaveService.ApproveLeaveRequestAsync(request, userId);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = "Leave request approved successfully";
                }
                else
                {
                    TempData["ErrorMessage"] = result.ErrorMessage ?? "Failed to approve leave request";
                }

                return RedirectToAction(nameof(Details), new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving leave request: {Id}", id);
                TempData["ErrorMessage"] = "An error occurred while approving the leave request";
                return RedirectToAction(nameof(Details), new { id });
            }
        }

        /// <summary>
        /// Reject leave request
        /// رفض طلب الإجازة
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Reject(Guid id, string reason)
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    TempData["ErrorMessage"] = "User information not found";
                    return RedirectToAction(nameof(Details), new { id });
                }

                var request = new RejectLeaveRequestDto
                {
                    LeaveRequestId = id,
                    RejectionReason = reason
                };

                var result = await _leaveService.RejectLeaveRequestAsync(request, userId);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = "Leave request rejected successfully";
                }
                else
                {
                    TempData["ErrorMessage"] = result.ErrorMessage ?? "Failed to reject leave request";
                }

                return RedirectToAction(nameof(Details), new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting leave request: {Id}", id);
                TempData["ErrorMessage"] = "An error occurred while rejecting the leave request";
                return RedirectToAction(nameof(Details), new { id });
            }
        }

        // Helper methods
        private async Task LoadFilterOptionsAsync(LeaveRequestListViewModel viewModel, Guid companyId)
        {
            try
            {
                // Load employees
                var employeeRequest = new FitHRPlus.Application.DTOs.Employees.EmployeeListRequestDto { CompanyId = companyId, IncludeInactive = false };
                var employees = await _employeeService.GetEmployeesAsync(employeeRequest);
                if (employees.IsSuccess)
                {
                    viewModel.Employees = employees.Data.Employees.Select(e => new Microsoft.AspNetCore.Mvc.Rendering.SelectListItem
                    {
                        Value = e.Id.ToString(),
                        Text = $"{e.FullName} ({e.EmployeeNumber})"
                    }).ToList();
                }

                // Load leave types
                var leaveTypes = await _leaveService.GetLeaveTypesAsync(companyId);
                if (leaveTypes.IsSuccess)
                {
                    viewModel.LeaveTypes = leaveTypes.Data.Select(lt => new Microsoft.AspNetCore.Mvc.Rendering.SelectListItem
                    {
                        Value = lt.Id.ToString(),
                        Text = lt.Name
                    }).ToList();
                }

                // Status options
                viewModel.StatusOptions = new List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>
                {
                    new() { Value = "Pending", Text = "Pending / في الانتظار" },
                    new() { Value = "Approved", Text = "Approved / موافق عليه" },
                    new() { Value = "Rejected", Text = "Rejected / مرفوض" },
                    new() { Value = "Cancelled", Text = "Cancelled / ملغي" }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading filter options");
            }
        }

        private async Task LoadCreateFormDataAsync(CreateLeaveRequestViewModel viewModel, Guid companyId)
        {
            try
            {
                // Load employees
                var employeeRequest = new FitHRPlus.Application.DTOs.Employees.EmployeeListRequestDto { CompanyId = companyId, IncludeInactive = false };
                var employees = await _employeeService.GetEmployeesAsync(employeeRequest);
                if (employees.IsSuccess)
                {
                    viewModel.Employees = employees.Data.Employees.Select(e => new Microsoft.AspNetCore.Mvc.Rendering.SelectListItem
                    {
                        Value = e.Id.ToString(),
                        Text = $"{e.FullName} ({e.EmployeeNumber})"
                    }).ToList();
                }

                // Load leave types
                var leaveTypes = await _leaveService.GetLeaveTypesAsync(companyId);
                if (leaveTypes.IsSuccess)
                {
                    viewModel.LeaveTypes = leaveTypes.Data.Select(lt => new Microsoft.AspNetCore.Mvc.Rendering.SelectListItem
                    {
                        Value = lt.Id.ToString(),
                        Text = lt.Name
                    }).ToList();

                    // Load leave type info
                    foreach (var leaveType in leaveTypes.Data)
                    {
                        viewModel.LeaveTypeInfo[leaveType.Id] = new LeaveTypeInfoViewModel
                        {
                            Id = leaveType.Id,
                            Name = leaveType.Name,
                            NameAr = leaveType.NameAr,
                            Description = leaveType.Description,
                            DescriptionAr = leaveType.DescriptionAr,
                            MaxDaysPerYear = leaveType.MaxDaysPerYear,
                            MaxConsecutiveDays = leaveType.MaxConsecutiveDays,
                            MinDaysNotice = leaveType.MinDaysNotice,
                            IsPaid = leaveType.IsPaid,
                            RequiresApproval = leaveType.RequiresApproval,
                            RequiresDocument = leaveType.RequiresDocument,
                            CarryForward = leaveType.CarryForward,
                            CarryForwardLimit = leaveType.CarryForwardLimit,
                            Gender = leaveType.Gender
                        };
                    }
                }

                // Load leave balances for all employees and leave types
                if (employees.IsSuccess && leaveTypes.IsSuccess)
                {
                    foreach (var employee in employees.Data.Employees)
                    {
                        var balances = await _leaveService.GetLeaveBalancesAsync(employee.Id);
                        if (balances.IsSuccess)
                        {
                            foreach (var balance in balances.Data)
                            {
                                var key = $"{employee.Id}_{balance.LeaveTypeId}";
                                viewModel.LeaveBalances[key] = balance.RemainingDays;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading create form data");
            }
        }

        private static LeaveRequestViewModel MapToLeaveRequestViewModel(LeaveRequestDto dto)
        {
            return new LeaveRequestViewModel
            {
                Id = dto.Id,
                EmployeeId = dto.EmployeeId,
                EmployeeName = dto.EmployeeName,
                EmployeeNameAr = dto.EmployeeNameAr,
                EmployeeCode = dto.EmployeeCode,
                DepartmentName = dto.DepartmentName,
                DepartmentNameAr = dto.DepartmentNameAr,
                LeaveTypeId = dto.LeaveTypeId,
                LeaveTypeName = dto.LeaveTypeName,
                LeaveTypeNameAr = dto.LeaveTypeNameAr,
                StartDate = dto.StartDate,
                EndDate = dto.EndDate,
                TotalDays = dto.TotalDays,
                Reason = dto.Reason,
                ReasonAr = dto.ReasonAr,
                Status = dto.Status,
                StatusAr = dto.StatusAr,
                StatusBadgeClass = GetStatusBadgeClass(dto.Status),
                ApprovedBy = dto.ApprovedBy,
                ApprovedByName = dto.ApprovedByName,
                ApprovedAt = dto.ApprovedAt,
                RejectionReason = dto.RejectionReason,
                RejectionReasonAr = dto.RejectionReasonAr,
                CreatedAt = dto.CreatedAt,
                UpdatedAt = dto.UpdatedAt,
                CanApprove = dto.CanApprove,
                CanReject = dto.CanReject,
                CanCancel = dto.CanCancel,
                CanEdit = dto.CanEdit
            };
        }

        private static LeaveRequestDetailsViewModel MapToLeaveRequestDetailsViewModel(LeaveRequestDto dto)
        {
            return new LeaveRequestDetailsViewModel
            {
                Id = dto.Id,
                EmployeeId = dto.EmployeeId,
                EmployeeName = dto.EmployeeName,
                EmployeeNameAr = dto.EmployeeNameAr,
                EmployeeCode = dto.EmployeeCode,
                DepartmentName = dto.DepartmentName,
                DepartmentNameAr = dto.DepartmentNameAr,
                LeaveTypeId = dto.LeaveTypeId,
                LeaveTypeName = dto.LeaveTypeName,
                LeaveTypeNameAr = dto.LeaveTypeNameAr,
                StartDate = dto.StartDate,
                EndDate = dto.EndDate,
                TotalDays = dto.TotalDays,
                Reason = dto.Reason,
                ReasonAr = dto.ReasonAr,
                Status = dto.Status,
                StatusAr = dto.StatusAr,
                StatusBadgeClass = GetStatusBadgeClass(dto.Status),
                ApprovedBy = dto.ApprovedBy,
                ApprovedByName = dto.ApprovedByName,
                ApprovedAt = dto.ApprovedAt,
                RejectionReason = dto.RejectionReason,
                RejectionReasonAr = dto.RejectionReasonAr,
                EmergencyContact = dto.EmergencyContact,
                EmergencyPhone = dto.EmergencyPhone,
                CreatedAt = dto.CreatedAt,
                UpdatedAt = dto.UpdatedAt,
                CanApprove = dto.CanApprove,
                CanReject = dto.CanReject,
                CanCancel = dto.CanCancel,
                CanEdit = dto.CanEdit
            };
        }

        private static string GetStatusBadgeClass(string status)
        {
            return status switch
            {
                "Pending" => "bg-warning",
                "Approved" => "bg-success",
                "Rejected" => "bg-danger",
                "Cancelled" => "bg-secondary",
                _ => "bg-primary"
            };
        }

        /// <summary>
        /// Leave balances page
        /// صفحة أرصدة الإجازات
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Balance()
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "معلومات الشركة غير موجودة";
                    return RedirectToAction("Index", "Dashboard");
                }

                // Get all employees for the company
                var employeesResult = await _employeeService.GetEmployeesAsync(new FitHRPlus.Application.DTOs.Employees.EmployeeListRequestDto
                {
                    CompanyId = companyId,
                    PageSize = 1000 // Get all employees
                });

                var viewModel = new LeaveBalancesOverviewViewModel
                {
                    CompanyId = companyId,
                    Employees = new List<EmployeeBalanceViewModel>()
                };

                if (employeesResult.IsSuccess && employeesResult.Data?.Employees != null)
                {
                    foreach (var employee in employeesResult.Data.Employees)
                    {
                        var balancesResult = await _leaveService.GetLeaveBalancesAsync(employee.Id);

                        var employeeBalance = new EmployeeBalanceViewModel
                        {
                            EmployeeId = employee.Id,
                            EmployeeName = employee.FullName,
                            EmployeeCode = employee.EmployeeNumber,
                            Department = employee.DepartmentName,
                            LeaveBalances = new List<LeaveTypeBalanceViewModel>()
                        };

                        if (balancesResult.IsSuccess && balancesResult.Data != null)
                        {
                            foreach (var balance in balancesResult.Data)
                            {
                                employeeBalance.LeaveBalances.Add(new LeaveTypeBalanceViewModel
                                {
                                    LeaveTypeId = balance.LeaveTypeId,
                                    LeaveTypeName = balance.LeaveTypeName,
                                    TotalDays = (int)balance.EntitledDays,
                                    UsedDays = (int)balance.UsedDays,
                                    RemainingDays = (int)balance.RemainingDays,
                                    Year = balance.Year
                                });
                            }
                        }

                        viewModel.Employees.Add(employeeBalance);
                    }
                }

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading leave balances");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل أرصدة الإجازات";
                return RedirectToAction("Index");
            }
        }
    }
}
