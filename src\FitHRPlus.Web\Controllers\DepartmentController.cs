using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using FitHRPlus.Application.Interfaces;
using FitHRPlus.Application.DTOs.Employees;
using FitHRPlus.Web.Models.Department;
using System.Security.Claims;

namespace FitHRPlus.Web.Controllers
{
    /// <summary>
    /// Department management controller
    /// كونترولر إدارة الأقسام
    /// </summary>
    [Authorize]
    [Route("[controller]")]
    public class DepartmentController : Controller
    {
        private readonly IDepartmentService _departmentService;
        private readonly ILogger<DepartmentController> _logger;

        public DepartmentController(
            IDepartmentService departmentService,
            ILogger<DepartmentController> logger)
        {
            _departmentService = departmentService;
            _logger = logger;
        }

        /// <summary>
        /// Department list page
        /// صفحة قائمة الأقسام
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Index([FromQuery] DepartmentListRequestDto request)
        {
            try
            {
                // Get current user's company
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (Guid.TryParse(companyIdClaim, out var companyId))
                {
                    request.CompanyId = companyId;
                }

                var result = await _departmentService.GetDepartmentsAsync(request);

                if (result.IsSuccess)
                {
                    var viewModel = new DepartmentListViewModel
                    {
                        Departments = result.Data?.Items?.Select(MapToDepartmentViewModel).ToList() ?? new List<DepartmentViewModel>(),
                        TotalCount = result.Data?.TotalCount ?? 0,
                        PageNumber = request.PageNumber,
                        PageSize = request.PageSize,
                        SearchTerm = request.SearchTerm
                    };

                    return View(viewModel);
                }

                TempData["ErrorMessage"] = result.ErrorMessage;
                return View(new DepartmentListViewModel());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading departments");
                TempData["ErrorMessage"] = "An error occurred while loading departments";
                return View(new DepartmentListViewModel());
            }
        }

        /// <summary>
        /// Department details page
        /// صفحة تفاصيل القسم
        /// </summary>
        [HttpGet("{id}")]
        public async Task<IActionResult> Details(Guid id)
        {
            try
            {
                var result = await _departmentService.GetDepartmentByIdAsync(id);

                if (result.IsSuccess)
                {
                    var viewModel = MapToDepartmentViewModel(result.Data!);
                    return View(viewModel);
                }

                TempData["ErrorMessage"] = result.ErrorMessage;
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading department details for ID: {DepartmentId}", id);
                TempData["ErrorMessage"] = "An error occurred while loading department details";
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// Create department page (GET)
        /// صفحة إنشاء قسم (GET)
        /// </summary>
        [HttpGet("Create")]
        public IActionResult Create()
        {
            var companyIdClaim = User.FindFirst("CompanyId")?.Value;
            if (!Guid.TryParse(companyIdClaim, out var companyId))
            {
                TempData["ErrorMessage"] = "Company information not found";
                return RedirectToAction(nameof(Index));
            }

            var viewModel = new DepartmentViewModel
            {
                CompanyId = companyId,
                IsActive = true
            };

            return View(viewModel);
        }

        /// <summary>
        /// Create department (POST)
        /// إنشاء قسم (POST)
        /// </summary>
        [HttpPost("Create")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(DepartmentViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return View(model);
                }

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    TempData["ErrorMessage"] = "User information not found";
                    return View(model);
                }

                var createDto = new CreateDepartmentDto
                {
                    CompanyId = model.CompanyId,
                    Name = model.Name,
                    NameAr = model.NameAr,
                    Description = model.Description,
                    DescriptionAr = model.DescriptionAr,
                    Budget = model.Budget,
                    CreatedBy = userId
                };

                var result = await _departmentService.CreateDepartmentAsync(createDto);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = "Department created successfully";
                    return RedirectToAction(nameof(Index));
                }

                TempData["ErrorMessage"] = result.ErrorMessage;
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating department");
                TempData["ErrorMessage"] = "An error occurred while creating the department";
                return View(model);
            }
        }

        /// <summary>
        /// Edit department page (GET)
        /// صفحة تعديل قسم (GET)
        /// </summary>
        [HttpGet("Edit/{id}")]
        public async Task<IActionResult> Edit(Guid id)
        {
            try
            {
                var result = await _departmentService.GetDepartmentByIdAsync(id);

                if (result.IsSuccess)
                {
                    var viewModel = MapToDepartmentViewModel(result.Data!);
                    return View(viewModel);
                }

                TempData["ErrorMessage"] = result.ErrorMessage;
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading department for edit. ID: {DepartmentId}", id);
                TempData["ErrorMessage"] = "An error occurred while loading department details";
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// Edit department (POST)
        /// تعديل قسم (POST)
        /// </summary>
        [HttpPost("Edit/{id}")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(Guid id, DepartmentViewModel model)
        {
            try
            {
                if (id != model.Id)
                {
                    TempData["ErrorMessage"] = "Invalid department ID";
                    return RedirectToAction(nameof(Index));
                }

                if (!ModelState.IsValid)
                {
                    return View(model);
                }

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    TempData["ErrorMessage"] = "User information not found";
                    return View(model);
                }

                var updateDto = new UpdateDepartmentDto
                {
                    Id = model.Id,
                    Name = model.Name,
                    NameAr = model.NameAr,
                    Description = model.Description,
                    DescriptionAr = model.DescriptionAr,
                    Budget = model.Budget,
                    IsActive = model.IsActive,
                    UpdatedBy = userId
                };

                var result = await _departmentService.UpdateDepartmentAsync(updateDto);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = "Department updated successfully";
                    return RedirectToAction(nameof(Index));
                }

                TempData["ErrorMessage"] = result.ErrorMessage;
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating department. ID: {DepartmentId}", id);
                TempData["ErrorMessage"] = "An error occurred while updating the department";
                return View(model);
            }
        }

        /// <summary>
        /// Delete department
        /// حذف قسم
        /// </summary>
        [HttpPost("Delete/{id}")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    TempData["ErrorMessage"] = "User information not found";
                    return RedirectToAction(nameof(Index));
                }

                var result = await _departmentService.DeleteDepartmentAsync(id, userId);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = "Department deleted successfully";
                }
                else
                {
                    TempData["ErrorMessage"] = result.ErrorMessage;
                }

                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting department. ID: {DepartmentId}", id);
                TempData["ErrorMessage"] = "An error occurred while deleting the department";
                return RedirectToAction(nameof(Index));
            }
        }

        #region Private Methods

        private static DepartmentViewModel MapToDepartmentViewModel(DepartmentDto dto)
        {
            return new DepartmentViewModel
            {
                Id = dto.Id,
                CompanyId = dto.CompanyId,
                Name = dto.Name,
                NameAr = dto.NameAr,
                Description = dto.Description,
                DescriptionAr = dto.DescriptionAr,
                Budget = dto.Budget,
                EmployeeCount = dto.EmployeeCount,
                IsActive = dto.IsActive,
                CreatedAt = dto.CreatedAt,
                UpdatedAt = dto.UpdatedAt
            };
        }

        #endregion
    }
}
