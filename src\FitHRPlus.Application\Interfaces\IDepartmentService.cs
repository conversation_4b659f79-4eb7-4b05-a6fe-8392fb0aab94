using FitHRPlus.Application.Common;
using FitHRPlus.Application.DTOs.Employees;
using FitHRPlus.Application.DTOs.Common;

namespace FitHRPlus.Application.Interfaces
{
    /// <summary>
    /// Department service interface
    /// واجهة خدمة الأقسام
    /// </summary>
    public interface IDepartmentService
    {
        /// <summary>
        /// Get all departments for a company
        /// الحصول على جميع الأقسام للشركة
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="includeInactive">Include inactive departments</param>
        /// <returns>List of departments</returns>
        Task<ServiceResult<List<DepartmentDto>>> GetDepartmentsAsync(Guid companyId, bool includeInactive = false);

        /// <summary>
        /// Get departments with pagination and filtering
        /// الحصول على الأقسام مع التصفح والفلترة
        /// </summary>
        /// <param name="request">Request parameters</param>
        /// <returns>Paginated list of departments</returns>
        Task<ServiceResult<PaginatedResult<DepartmentDto>>> GetDepartmentsAsync(DepartmentListRequestDto request);

        /// <summary>
        /// Get department by ID
        /// الحصول على القسم بالمعرف
        /// </summary>
        /// <param name="id">Department ID</param>
        /// <returns>Department details</returns>
        Task<ServiceResult<DepartmentDto>> GetDepartmentByIdAsync(Guid id);

        /// <summary>
        /// Get department hierarchy for a company
        /// الحصول على الهيكل الهرمي للأقسام للشركة
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="includeInactive">Include inactive departments</param>
        /// <returns>Hierarchical department structure</returns>
        Task<ServiceResult<List<DepartmentDto>>> GetDepartmentHierarchyAsync(Guid companyId, bool includeInactive = false);

        /// <summary>
        /// Create new department
        /// إنشاء قسم جديد
        /// </summary>
        /// <param name="departmentDto">Department data</param>
        /// <param name="createdBy">User ID who created the department</param>
        /// <returns>Created department</returns>
        Task<ServiceResult<DepartmentDto>> CreateDepartmentAsync(DepartmentDto departmentDto, Guid createdBy);

        /// <summary>
        /// Create a new department
        /// إنشاء قسم جديد
        /// </summary>
        /// <param name="createDto">Create department DTO</param>
        /// <returns>Created department</returns>
        Task<ServiceResult<DepartmentDto>> CreateDepartmentAsync(CreateDepartmentDto createDto);

        /// <summary>
        /// Update existing department
        /// تحديث قسم موجود
        /// </summary>
        /// <param name="id">Department ID</param>
        /// <param name="departmentDto">Updated department data</param>
        /// <param name="updatedBy">User ID who updated the department</param>
        /// <returns>Updated department</returns>
        Task<ServiceResult<DepartmentDto>> UpdateDepartmentAsync(Guid id, DepartmentDto departmentDto, Guid updatedBy);

        /// <summary>
        /// Update existing department
        /// تحديث قسم موجود
        /// </summary>
        /// <param name="updateDto">Update department DTO</param>
        /// <returns>Updated department</returns>
        Task<ServiceResult<DepartmentDto>> UpdateDepartmentAsync(UpdateDepartmentDto updateDto);

        /// <summary>
        /// Delete department (soft delete)
        /// حذف القسم (حذف ناعم)
        /// </summary>
        /// <param name="id">Department ID</param>
        /// <param name="deletedBy">User ID who deleted the department</param>
        /// <returns>Deletion result</returns>
        Task<ServiceResult<bool>> DeleteDepartmentAsync(Guid id, Guid deletedBy);

        /// <summary>
        /// Activate/Deactivate department
        /// تفعيل/إلغاء تفعيل القسم
        /// </summary>
        /// <param name="id">Department ID</param>
        /// <param name="isActive">Active status</param>
        /// <param name="updatedBy">User ID who updated the status</param>
        /// <returns>Update result</returns>
        Task<ServiceResult<bool>> SetDepartmentActiveStatusAsync(Guid id, bool isActive, Guid updatedBy);

        /// <summary>
        /// Get sub-departments
        /// الحصول على الأقسام الفرعية
        /// </summary>
        /// <param name="parentDepartmentId">Parent department ID</param>
        /// <param name="includeInactive">Include inactive departments</param>
        /// <returns>List of sub-departments</returns>
        Task<ServiceResult<List<DepartmentDto>>> GetSubDepartmentsAsync(Guid parentDepartmentId, bool includeInactive = false);

        /// <summary>
        /// Move department to different parent
        /// نقل القسم إلى أب مختلف
        /// </summary>
        /// <param name="departmentId">Department ID</param>
        /// <param name="newParentId">New parent department ID (null for root level)</param>
        /// <param name="updatedBy">User ID who moved the department</param>
        /// <returns>Move result</returns>
        Task<ServiceResult<bool>> MoveDepartmentAsync(Guid departmentId, Guid? newParentId, Guid updatedBy);

        /// <summary>
        /// Validate department code uniqueness
        /// التحقق من فرادة رمز القسم
        /// </summary>
        /// <param name="code">Department code</param>
        /// <param name="companyId">Company ID</param>
        /// <param name="excludeDepartmentId">Department ID to exclude from check</param>
        /// <returns>Validation result</returns>
        Task<ServiceResult<bool>> ValidateDepartmentCodeAsync(string code, Guid companyId, Guid? excludeDepartmentId = null);

        /// <summary>
        /// Search departments
        /// البحث عن الأقسام
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <param name="companyId">Company ID</param>
        /// <param name="includeInactive">Include inactive departments</param>
        /// <returns>List of matching departments</returns>
        Task<ServiceResult<List<DepartmentDto>>> SearchDepartmentsAsync(string searchTerm, Guid companyId, bool includeInactive = false);
    }

    /// <summary>
    /// Position service interface
    /// واجهة خدمة المناصب
    /// </summary>
    public interface IPositionService
    {
        /// <summary>
        /// Get all positions for a company
        /// الحصول على جميع المناصب للشركة
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="includeInactive">Include inactive positions</param>
        /// <returns>List of positions</returns>
        Task<ServiceResult<List<PositionDto>>> GetPositionsAsync(Guid companyId, bool includeInactive = false);

        /// <summary>
        /// Get positions with pagination and filtering
        /// الحصول على المناصب مع التصفح والفلترة
        /// </summary>
        /// <param name="request">Request parameters</param>
        /// <returns>Paginated list of positions</returns>
        Task<ServiceResult<PaginatedResult<PositionDto>>> GetPositionsAsync(PositionListRequestDto request);

        /// <summary>
        /// Get positions by department
        /// الحصول على المناصب حسب القسم
        /// </summary>
        /// <param name="departmentId">Department ID</param>
        /// <param name="includeInactive">Include inactive positions</param>
        /// <returns>List of positions</returns>
        Task<ServiceResult<List<PositionDto>>> GetPositionsByDepartmentAsync(Guid departmentId, bool includeInactive = false);

        /// <summary>
        /// Get position by ID
        /// الحصول على المنصب بالمعرف
        /// </summary>
        /// <param name="id">Position ID</param>
        /// <returns>Position details</returns>
        Task<ServiceResult<PositionDto>> GetPositionByIdAsync(Guid id);

        /// <summary>
        /// Create new position
        /// إنشاء منصب جديد
        /// </summary>
        /// <param name="positionDto">Position data</param>
        /// <param name="createdBy">User ID who created the position</param>
        /// <returns>Created position</returns>
        Task<ServiceResult<PositionDto>> CreatePositionAsync(PositionDto positionDto, Guid createdBy);

        /// <summary>
        /// Create a new position
        /// إنشاء منصب جديد
        /// </summary>
        /// <param name="createDto">Create position DTO</param>
        /// <returns>Created position</returns>
        Task<ServiceResult<PositionDto>> CreatePositionAsync(CreatePositionDto createDto);

        /// <summary>
        /// Update existing position
        /// تحديث منصب موجود
        /// </summary>
        /// <param name="id">Position ID</param>
        /// <param name="positionDto">Updated position data</param>
        /// <param name="updatedBy">User ID who updated the position</param>
        /// <returns>Updated position</returns>
        Task<ServiceResult<PositionDto>> UpdatePositionAsync(Guid id, PositionDto positionDto, Guid updatedBy);

        /// <summary>
        /// Update existing position
        /// تحديث منصب موجود
        /// </summary>
        /// <param name="updateDto">Update position DTO</param>
        /// <returns>Updated position</returns>
        Task<ServiceResult<PositionDto>> UpdatePositionAsync(UpdatePositionDto updateDto);

        /// <summary>
        /// Delete position (soft delete)
        /// حذف المنصب (حذف ناعم)
        /// </summary>
        /// <param name="id">Position ID</param>
        /// <param name="deletedBy">User ID who deleted the position</param>
        /// <returns>Deletion result</returns>
        Task<ServiceResult<bool>> DeletePositionAsync(Guid id, Guid deletedBy);

        /// <summary>
        /// Activate/Deactivate position
        /// تفعيل/إلغاء تفعيل المنصب
        /// </summary>
        /// <param name="id">Position ID</param>
        /// <param name="isActive">Active status</param>
        /// <param name="updatedBy">User ID who updated the status</param>
        /// <returns>Update result</returns>
        Task<ServiceResult<bool>> SetPositionActiveStatusAsync(Guid id, bool isActive, Guid updatedBy);

        /// <summary>
        /// Validate position code uniqueness
        /// التحقق من فرادة رمز المنصب
        /// </summary>
        /// <param name="code">Position code</param>
        /// <param name="companyId">Company ID</param>
        /// <param name="excludePositionId">Position ID to exclude from check</param>
        /// <returns>Validation result</returns>
        Task<ServiceResult<bool>> ValidatePositionCodeAsync(string code, Guid companyId, Guid? excludePositionId = null);

        /// <summary>
        /// Search positions
        /// البحث عن المناصب
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <param name="companyId">Company ID</param>
        /// <param name="departmentId">Department ID (optional)</param>
        /// <param name="includeInactive">Include inactive positions</param>
        /// <returns>List of matching positions</returns>
        Task<ServiceResult<List<PositionDto>>> SearchPositionsAsync(string searchTerm, Guid companyId, Guid? departmentId = null, bool includeInactive = false);

        /// <summary>
        /// Get position salary range statistics
        /// الحصول على إحصائيات نطاق راتب المنصب
        /// </summary>
        /// <param name="positionId">Position ID</param>
        /// <returns>Salary range statistics</returns>
        Task<ServiceResult<PositionSalaryStatsDto>> GetPositionSalaryStatsAsync(Guid positionId);
    }

    /// <summary>
    /// Position salary statistics DTO
    /// كائنة نقل بيانات إحصائيات راتب المنصب
    /// </summary>
    public class PositionSalaryStatsDto
    {
        /// <summary>
        /// Position ID
        /// معرف المنصب
        /// </summary>
        public Guid PositionId { get; set; }

        /// <summary>
        /// Position title
        /// مسمى المنصب
        /// </summary>
        public string PositionTitle { get; set; } = string.Empty;

        /// <summary>
        /// Minimum salary defined for position
        /// الحد الأدنى للراتب المحدد للمنصب
        /// </summary>
        public decimal? MinSalary { get; set; }

        /// <summary>
        /// Maximum salary defined for position
        /// الحد الأقصى للراتب المحدد للمنصب
        /// </summary>
        public decimal? MaxSalary { get; set; }

        /// <summary>
        /// Average actual salary of employees in this position
        /// متوسط الراتب الفعلي للموظفين في هذا المنصب
        /// </summary>
        public decimal? AverageActualSalary { get; set; }

        /// <summary>
        /// Minimum actual salary of employees in this position
        /// الحد الأدنى للراتب الفعلي للموظفين في هذا المنصب
        /// </summary>
        public decimal? MinActualSalary { get; set; }

        /// <summary>
        /// Maximum actual salary of employees in this position
        /// الحد الأقصى للراتب الفعلي للموظفين في هذا المنصب
        /// </summary>
        public decimal? MaxActualSalary { get; set; }

        /// <summary>
        /// Number of employees in this position
        /// عدد الموظفين في هذا المنصب
        /// </summary>
        public int EmployeeCount { get; set; }
    }
}
