using FitHRPlus.Application.DTOs.Attendance;
using FitHRPlus.Application.Interfaces;
using FitHRPlus.Web.Models.Admin;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace FitHRPlus.Web.Controllers
{
    /// <summary>
    /// Attendance controller for attendance management
    /// وحدة تحكم الحضور لإدارة الحضور والانصراف
    /// </summary>
    [Authorize]
    public class AttendanceController : Controller
    {
        private readonly IAttendanceService _attendanceService;
        private readonly IEmployeeService _employeeService;
        private readonly IDepartmentService _departmentService;
        private readonly ILogger<AttendanceController> _logger;

        public AttendanceController(
            IAttendanceService attendanceService,
            IEmployeeService employeeService,
            IDepartmentService departmentService,
            ILogger<AttendanceController> logger)
        {
            _attendanceService = attendanceService;
            _employeeService = employeeService;
            _departmentService = departmentService;
            _logger = logger;
        }

        /// <summary>
        /// Attendance list page
        /// صفحة قائمة الحضور
        /// </summary>
        /// <param name="request">List request parameters</param>
        /// <returns>Attendance list view</returns>
        [HttpGet]
        public async Task<IActionResult> Index([FromQuery] AttendanceListRequestDto request)
        {
            try
            {
                // Get current user's company
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (Guid.TryParse(companyIdClaim, out var companyId))
                {
                    request.CompanyId = companyId;
                }

                // Set default date range if not provided
                if (!request.DateFrom.HasValue && !request.DateTo.HasValue)
                {
                    request.DateFrom = DateTime.Today.AddDays(-30);
                    request.DateTo = DateTime.Today;
                }

                var result = await _attendanceService.GetAttendanceRecordsAsync(request);

                if (result.IsSuccess)
                {
                    var viewModel = new AttendanceListViewModel
                    {
                        Attendances = result.Data!.Attendances.Select(a => new AttendanceViewModel
                        {
                            Id = a.Id,
                            EmployeeId = a.EmployeeId,
                            EmployeeName = a.EmployeeName,
                            EmployeeNumber = a.EmployeeNumber,
                            DepartmentName = a.DepartmentName,
                            PositionTitle = a.PositionTitle,
                            Date = a.Date,
                            CheckInTime = a.CheckInTime,
                            CheckOutTime = a.CheckOutTime,
                            WorkingHours = a.WorkingHours,
                            OvertimeHours = a.OvertimeHours,
                            LateMinutes = a.LateMinutes,
                            EarlyDepartureMinutes = a.EarlyDepartureMinutes,
                            AttendanceStatus = a.AttendanceStatus,
                            IsApproved = a.IsApproved,
                            Notes = a.Notes
                        }).ToList(),
                        EmployeeId = request.EmployeeId,
                        DepartmentId = request.DepartmentId,
                        DateFrom = request.DateFrom,
                        DateTo = request.DateTo,
                        AttendanceStatus = request.AttendanceStatus,
                        ShowLateOnly = request.ShowLateOnly,
                        ShowEarlyDepartureOnly = request.ShowEarlyDepartureOnly,
                        ShowOvertimeOnly = request.ShowOvertimeOnly,
                        ShowUnapprovedOnly = request.ShowUnapprovedOnly,
                        SearchTerm = request.SearchTerm,
                        CurrentPage = result.Data.CurrentPage,
                        TotalPages = result.Data.TotalPages,
                        TotalCount = result.Data.TotalCount,
                        PageSize = result.Data.PageSize,
                        Statistics = MapToStatisticsViewModel(result.Data.Statistics)
                    };

                    // Load filter options
                    await LoadFilterOptionsAsync(viewModel, companyId);

                    return View(viewModel);
                }

                TempData["ErrorMessage"] = result.ErrorMessage;
                return View(new AttendanceListViewModel());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading attendance list");
                TempData["ErrorMessage"] = "An error occurred while loading attendance records";
                return View(new AttendanceListViewModel());
            }
        }

        /// <summary>
        /// Attendance details page
        /// صفحة تفاصيل الحضور
        /// </summary>
        /// <param name="id">Attendance record ID</param>
        /// <returns>Attendance details view</returns>
        [HttpGet]
        public async Task<IActionResult> Details(Guid id)
        {
            try
            {
                var result = await _attendanceService.GetAttendanceByIdAsync(id);

                if (result.IsSuccess)
                {
                    var viewModel = MapToAttendanceViewModel(result.Data!);
                    return View(viewModel);
                }

                TempData["ErrorMessage"] = result.ErrorMessage;
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading attendance details for ID: {AttendanceId}", id);
                TempData["ErrorMessage"] = "An error occurred while loading attendance details";
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// Check-in page
        /// صفحة تسجيل الحضور
        /// </summary>
        /// <returns>Check-in view</returns>
        [HttpGet]
        public async Task<IActionResult> CheckIn()
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "Company information not found";
                    return RedirectToAction(nameof(Index));
                }

                var viewModel = new CheckInOutViewModel
                {
                    Time = DateTime.Now,
                    IsCheckIn = true
                };

                await LoadEmployeesForCheckInOut(viewModel, companyId);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading check-in page");
                TempData["ErrorMessage"] = "An error occurred while loading the page";
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// Check-in (POST)
        /// تسجيل الحضور (POST)
        /// </summary>
        /// <param name="model">Check-in view model</param>
        /// <returns>Redirect or check-in view with errors</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CheckIn(CheckInOutViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                    if (Guid.TryParse(companyIdClaim, out var companyId))
                    {
                        await LoadEmployeesForCheckInOut(model, companyId);
                    }
                    return View(model);
                }

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    TempData["ErrorMessage"] = "User information not found";
                    return View(model);
                }

                var request = new CheckInOutRequestDto
                {
                    EmployeeId = model.EmployeeId,
                    Time = model.Time,
                    Location = model.Location,
                    Device = model.Device,
                    Notes = model.Notes
                };

                var result = await _attendanceService.CheckInAsync(request, userId);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = "Check-in recorded successfully";
                    return RedirectToAction(nameof(Details), new { id = result.Data!.Id });
                }

                ModelState.AddModelError("", result.ErrorMessage ?? "Failed to record check-in");
                var companyIdClaim2 = User.FindFirst("CompanyId")?.Value;
                if (Guid.TryParse(companyIdClaim2, out var companyId2))
                {
                    await LoadEmployeesForCheckInOut(model, companyId2);
                }
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during check-in");
                ModelState.AddModelError("", "An error occurred while recording check-in");
                return View(model);
            }
        }

        /// <summary>
        /// Check-out page
        /// صفحة تسجيل الانصراف
        /// </summary>
        /// <returns>Check-out view</returns>
        [HttpGet]
        public async Task<IActionResult> CheckOut()
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "Company information not found";
                    return RedirectToAction(nameof(Index));
                }

                var viewModel = new CheckInOutViewModel
                {
                    Time = DateTime.Now,
                    IsCheckIn = false
                };

                await LoadEmployeesForCheckInOut(viewModel, companyId);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading check-out page");
                TempData["ErrorMessage"] = "An error occurred while loading the page";
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// Check-out (POST)
        /// تسجيل الانصراف (POST)
        /// </summary>
        /// <param name="model">Check-out view model</param>
        /// <returns>Redirect or check-out view with errors</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CheckOut(CheckInOutViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                    if (Guid.TryParse(companyIdClaim, out var companyId))
                    {
                        await LoadEmployeesForCheckInOut(model, companyId);
                    }
                    return View(model);
                }

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    TempData["ErrorMessage"] = "User information not found";
                    return View(model);
                }

                var request = new CheckInOutRequestDto
                {
                    EmployeeId = model.EmployeeId,
                    Time = model.Time,
                    Location = model.Location,
                    Device = model.Device,
                    Notes = model.Notes
                };

                var result = await _attendanceService.CheckOutAsync(request, userId);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = "Check-out recorded successfully";
                    return RedirectToAction(nameof(Details), new { id = result.Data!.Id });
                }

                ModelState.AddModelError("", result.ErrorMessage ?? "Failed to record check-out");
                var companyIdClaim2 = User.FindFirst("CompanyId")?.Value;
                if (Guid.TryParse(companyIdClaim2, out var companyId2))
                {
                    await LoadEmployeesForCheckInOut(model, companyId2);
                }
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during check-out");
                ModelState.AddModelError("", "An error occurred while recording check-out");
                return View(model);
            }
        }

        // Helper methods - will be implemented in next chunk
        private Task LoadFilterOptionsAsync(AttendanceListViewModel viewModel, Guid companyId)
        {
            throw new NotImplementedException();
        }

        private Task LoadEmployeesForCheckInOut(CheckInOutViewModel viewModel, Guid companyId)
        {
            throw new NotImplementedException();
        }

        private static AttendanceViewModel MapToAttendanceViewModel(AttendanceDto dto)
        {
            throw new NotImplementedException();
        }

        private static AttendanceStatisticsViewModel MapToStatisticsViewModel(AttendanceStatisticsDto dto)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// Daily attendance report
        /// تقرير الحضور اليومي
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> DailyReport(DateTime? date = null)
        {
            try
            {
                var reportDate = date ?? DateTime.Today;

                // Get current user's company
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "Company information not found.";
                    return RedirectToAction("Index", "Dashboard");
                }

                // For now, return a simple view with the date
                ViewBag.ReportDate = reportDate;
                ViewBag.CompanyId = companyId;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating daily attendance report");
                TempData["ErrorMessage"] = "Error generating daily report.";
                return RedirectToAction("Index");
            }
        }

        /// <summary>
        /// Monthly attendance report
        /// تقرير الحضور الشهري
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> MonthlyReport(int? year = null, int? month = null)
        {
            try
            {
                var reportYear = year ?? DateTime.Today.Year;
                var reportMonth = month ?? DateTime.Today.Month;

                // Get current user's company
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "Company information not found.";
                    return RedirectToAction("Index", "Dashboard");
                }

                // For now, return a simple view with the date
                ViewBag.ReportYear = reportYear;
                ViewBag.ReportMonth = reportMonth;
                ViewBag.CompanyId = companyId;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating monthly attendance report");
                TempData["ErrorMessage"] = "Error generating monthly report.";
                return RedirectToAction("Index");
            }
        }
    }
}
