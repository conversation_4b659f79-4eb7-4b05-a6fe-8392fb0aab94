<!-- Global Search Component -->
<div class="global-search-container">
    <div class="global-search-wrapper">
        <div class="search-input-group">
            <div class="search-icon">
                <i class="bi bi-search"></i>
            </div>
            <input type="text" 
                   class="form-control global-search-input" 
                   id="globalSearchInput"
                   placeholder="البحث في النظام... (Ctrl+K)"
                   autocomplete="off">
            <div class="search-shortcut">
                <kbd>Ctrl</kbd> + <kbd>K</kbd>
            </div>
        </div>
        
        <!-- Search Results Dropdown -->
        <div class="search-results-dropdown" id="searchResultsDropdown">
            <div class="search-results-header">
                <span class="results-count">نتائج البحث</span>
                <button type="button" class="btn-close-search" onclick="closeGlobalSearch()">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            
            <div class="search-results-content">
                <!-- Quick Actions -->
                <div class="search-section">
                    <div class="search-section-header">
                        <i class="bi bi-lightning"></i>
                        <span>إجراءات سريعة</span>
                    </div>
                    <div class="search-items" id="quickActions">
                        <div class="search-item" data-action="add-employee">
                            <div class="search-item-icon">
                                <i class="bi bi-person-plus"></i>
                            </div>
                            <div class="search-item-content">
                                <div class="search-item-title">إضافة موظف جديد</div>
                                <div class="search-item-description">إضافة موظف جديد للنظام</div>
                            </div>
                            <div class="search-item-shortcut">
                                <kbd>Alt</kbd> + <kbd>E</kbd>
                            </div>
                        </div>
                        
                        <div class="search-item" data-action="new-leave">
                            <div class="search-item-icon">
                                <i class="bi bi-calendar-plus"></i>
                            </div>
                            <div class="search-item-content">
                                <div class="search-item-title">طلب إجازة جديد</div>
                                <div class="search-item-description">إنشاء طلب إجازة جديد</div>
                            </div>
                            <div class="search-item-shortcut">
                                <kbd>Alt</kbd> + <kbd>L</kbd>
                            </div>
                        </div>
                        
                        <div class="search-item" data-action="generate-payroll">
                            <div class="search-item-icon">
                                <i class="bi bi-currency-dollar"></i>
                            </div>
                            <div class="search-item-content">
                                <div class="search-item-title">إنشاء كشف راتب</div>
                                <div class="search-item-description">إنشاء كشف راتب جديد</div>
                            </div>
                            <div class="search-item-shortcut">
                                <kbd>Alt</kbd> + <kbd>P</kbd>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Items -->
                <div class="search-section">
                    <div class="search-section-header">
                        <i class="bi bi-clock-history"></i>
                        <span>العناصر الأخيرة</span>
                    </div>
                    <div class="search-items" id="recentItems">
                        <!-- Will be populated dynamically -->
                    </div>
                </div>
                
                <!-- Search Results -->
                <div class="search-section" id="searchResultsSection" style="display: none;">
                    <div class="search-section-header">
                        <i class="bi bi-search"></i>
                        <span>نتائج البحث</span>
                    </div>
                    <div class="search-items" id="searchResults">
                        <!-- Will be populated dynamically -->
                    </div>
                </div>
                
                <!-- No Results -->
                <div class="search-no-results" id="noResults" style="display: none;">
                    <div class="no-results-icon">
                        <i class="bi bi-search"></i>
                    </div>
                    <div class="no-results-text">
                        <h6>لا توجد نتائج</h6>
                        <p>جرب البحث بكلمات مختلفة</p>
                    </div>
                </div>
                
                <!-- Loading -->
                <div class="search-loading" id="searchLoading" style="display: none;">
                    <div class="loading-spinner">
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">جاري البحث...</span>
                        </div>
                    </div>
                    <span>جاري البحث...</span>
                </div>
            </div>
            
            <div class="search-results-footer">
                <div class="search-tips">
                    <span class="search-tip">
                        <kbd>↑</kbd> <kbd>↓</kbd> للتنقل
                    </span>
                    <span class="search-tip">
                        <kbd>Enter</kbd> للاختيار
                    </span>
                    <span class="search-tip">
                        <kbd>Esc</kbd> للإغلاق
                    </span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Search Overlay -->
    <div class="search-overlay" id="searchOverlay" onclick="closeGlobalSearch()"></div>
</div>

<style>
.global-search-container {
    position: relative;
}

.global-search-wrapper {
    position: relative;
    width: 100%;
    max-width: 500px;
}

.search-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 12px;
    z-index: 10;
    color: #6c757d;
}

.global-search-input {
    padding: 12px 80px 12px 40px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #fff;
}

.global-search-input:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    outline: none;
}

.search-shortcut {
    position: absolute;
    right: 12px;
    display: flex;
    gap: 2px;
    font-size: 11px;
    color: #6c757d;
}

.search-shortcut kbd {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 2px 6px;
    font-size: 10px;
}

.search-results-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    z-index: 1050;
    max-height: 500px;
    overflow: hidden;
    display: none;
    margin-top: 8px;
}

.search-results-dropdown.show {
    display: block;
}

.search-results-header {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.results-count {
    font-size: 12px;
    font-weight: 600;
    color: #495057;
}

.btn-close-search {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 16px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.btn-close-search:hover {
    background: #e9ecef;
    color: #495057;
}

.search-results-content {
    max-height: 400px;
    overflow-y: auto;
}

.search-section {
    padding: 8px 0;
}

.search-section:not(:last-child) {
    border-bottom: 1px solid #f1f3f4;
}

.search-section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.search-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.search-item:hover,
.search-item.active {
    background: #f8f9fa;
}

.search-item-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #e9ecef;
    border-radius: 8px;
    color: #495057;
    font-size: 14px;
}

.search-item-content {
    flex: 1;
}

.search-item-title {
    font-size: 14px;
    font-weight: 500;
    color: #212529;
    margin-bottom: 2px;
}

.search-item-description {
    font-size: 12px;
    color: #6c757d;
}

.search-item-shortcut {
    display: flex;
    gap: 2px;
    font-size: 10px;
}

.search-item-shortcut kbd {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    padding: 2px 4px;
    font-size: 9px;
}

.search-no-results {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-results-icon {
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.5;
}

.no-results-text h6 {
    margin-bottom: 4px;
    color: #495057;
}

.search-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 20px;
    color: #6c757d;
}

.search-results-footer {
    padding: 8px 16px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

.search-tips {
    display: flex;
    gap: 16px;
    font-size: 11px;
    color: #6c757d;
}

.search-tip kbd {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    padding: 2px 4px;
    font-size: 9px;
    margin: 0 2px;
}

.search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1040;
    display: none;
}

.search-overlay.show {
    display: block;
}

@@media (max-width: 768px) {
    .global-search-wrapper {
        max-width: 100%;
    }

    .search-results-dropdown {
        left: -16px;
        right: -16px;
        max-height: 70vh;
    }

    .search-tips {
        display: none;
    }
}
</style>

<script>
$(document).ready(function() {
    initializeGlobalSearch();
});

let searchTimeout;
let currentSearchIndex = -1;
let searchResults = [];

function initializeGlobalSearch() {
    const searchInput = $('#globalSearchInput');
    const searchDropdown = $('#searchResultsDropdown');
    const searchOverlay = $('#searchOverlay');

    // Keyboard shortcuts
    $(document).on('keydown', function(e) {
        // Ctrl+K to open search
        if (e.ctrlKey && e.key === 'k') {
            e.preventDefault();
            openGlobalSearch();
        }

        // Escape to close search
        if (e.key === 'Escape') {
            closeGlobalSearch();
        }

        // Arrow keys navigation
        if (searchDropdown.hasClass('show')) {
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                navigateSearch('down');
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                navigateSearch('up');
            } else if (e.key === 'Enter') {
                e.preventDefault();
                selectSearchItem();
            }
        }

        // Quick action shortcuts
        if (e.altKey) {
            switch(e.key) {
                case 'e':
                    e.preventDefault();
                    executeQuickAction('add-employee');
                    break;
                case 'l':
                    e.preventDefault();
                    executeQuickAction('new-leave');
                    break;
                case 'p':
                    e.preventDefault();
                    executeQuickAction('generate-payroll');
                    break;
            }
        }
    });

    // Search input events
    searchInput.on('focus', function() {
        openGlobalSearch();
        loadRecentItems();
    });

    searchInput.on('input', function() {
        const query = $(this).val().trim();

        clearTimeout(searchTimeout);

        if (query.length === 0) {
            showQuickActions();
            loadRecentItems();
            return;
        }

        if (query.length < 2) {
            return;
        }

        searchTimeout = setTimeout(() => {
            performSearch(query);
        }, 300);
    });

    // Click handlers for search items
    $(document).on('click', '.search-item', function() {
        const action = $(this).data('action');
        const url = $(this).data('url');

        if (action) {
            executeQuickAction(action);
        } else if (url) {
            window.location.href = url;
        }

        closeGlobalSearch();
    });
}

function openGlobalSearch() {
    const searchDropdown = $('#searchResultsDropdown');
    const searchOverlay = $('#searchOverlay');
    const searchInput = $('#globalSearchInput');

    searchDropdown.addClass('show');
    searchOverlay.addClass('show');
    searchInput.focus();

    showQuickActions();
    loadRecentItems();
}

function closeGlobalSearch() {
    const searchDropdown = $('#searchResultsDropdown');
    const searchOverlay = $('#searchOverlay');
    const searchInput = $('#globalSearchInput');

    searchDropdown.removeClass('show');
    searchOverlay.removeClass('show');
    searchInput.blur();

    currentSearchIndex = -1;
    searchResults = [];
}

function showQuickActions() {
    $('#searchResultsSection').hide();
    $('#noResults').hide();
    $('#searchLoading').hide();
    $('.search-section').first().show();
}

function loadRecentItems() {
    // Load recent items from localStorage or API
    const recentItems = JSON.parse(localStorage.getItem('recentItems') || '[]');
    const recentItemsContainer = $('#recentItems');

    recentItemsContainer.empty();

    if (recentItems.length === 0) {
        recentItemsContainer.parent().hide();
        return;
    }

    recentItemsContainer.parent().show();

    recentItems.slice(0, 5).forEach(item => {
        const itemHtml = `
            <div class="search-item" data-url="${item.url}">
                <div class="search-item-icon">
                    <i class="${item.icon}"></i>
                </div>
                <div class="search-item-content">
                    <div class="search-item-title">${item.title}</div>
                    <div class="search-item-description">${item.description}</div>
                </div>
            </div>
        `;
        recentItemsContainer.append(itemHtml);
    });
}

function performSearch(query) {
    $('#searchLoading').show();
    $('#searchResultsSection').hide();
    $('#noResults').hide();

    // Simulate API call
    $.ajax({
        url: '/api/search',
        method: 'GET',
        data: { q: query },
        success: function(results) {
            displaySearchResults(results);
        },
        error: function() {
            // Fallback to mock results
            const mockResults = generateMockResults(query);
            displaySearchResults(mockResults);
        }
    });
}

function generateMockResults(query) {
    const mockData = [
        { type: 'employee', title: 'أحمد محمد', description: 'موظف - قسم التطوير', url: '/employees/1', icon: 'bi bi-person' },
        { type: 'employee', title: 'فاطمة علي', description: 'موظفة - قسم المحاسبة', url: '/employees/2', icon: 'bi bi-person' },
        { type: 'leave', title: 'طلب إجازة #123', description: 'إجازة سنوية - أحمد محمد', url: '/leaves/123', icon: 'bi bi-calendar' },
        { type: 'payroll', title: 'كشف راتب يناير 2024', description: 'كشف راتب شهر يناير', url: '/payroll/jan2024', icon: 'bi bi-currency-dollar' }
    ];

    return mockData.filter(item =>
        item.title.toLowerCase().includes(query.toLowerCase()) ||
        item.description.toLowerCase().includes(query.toLowerCase())
    );
}

function displaySearchResults(results) {
    $('#searchLoading').hide();

    if (results.length === 0) {
        $('#noResults').show();
        $('#searchResultsSection').hide();
        return;
    }

    const searchResultsContainer = $('#searchResults');
    searchResultsContainer.empty();

    results.forEach(result => {
        const resultHtml = `
            <div class="search-item" data-url="${result.url}">
                <div class="search-item-icon">
                    <i class="${result.icon}"></i>
                </div>
                <div class="search-item-content">
                    <div class="search-item-title">${result.title}</div>
                    <div class="search-item-description">${result.description}</div>
                </div>
            </div>
        `;
        searchResultsContainer.append(resultHtml);
    });

    $('#searchResultsSection').show();
    searchResults = results;
}

function navigateSearch(direction) {
    const items = $('.search-item:visible');

    if (items.length === 0) return;

    items.removeClass('active');

    if (direction === 'down') {
        currentSearchIndex = (currentSearchIndex + 1) % items.length;
    } else {
        currentSearchIndex = currentSearchIndex <= 0 ? items.length - 1 : currentSearchIndex - 1;
    }

    $(items[currentSearchIndex]).addClass('active');
}

function selectSearchItem() {
    const activeItem = $('.search-item.active');
    if (activeItem.length > 0) {
        activeItem.click();
    }
}

function executeQuickAction(action) {
    switch(action) {
        case 'add-employee':
            window.location.href = '/employees/create';
            break;
        case 'new-leave':
            window.location.href = '/leaves/create';
            break;
        case 'generate-payroll':
            window.location.href = '/payroll/generate';
            break;
    }

    closeGlobalSearch();
}

function addToRecentItems(item) {
    let recentItems = JSON.parse(localStorage.getItem('recentItems') || '[]');

    // Remove if already exists
    recentItems = recentItems.filter(recent => recent.url !== item.url);

    // Add to beginning
    recentItems.unshift(item);

    // Keep only last 10 items
    recentItems = recentItems.slice(0, 10);

    localStorage.setItem('recentItems', JSON.stringify(recentItems));
}
</script>
