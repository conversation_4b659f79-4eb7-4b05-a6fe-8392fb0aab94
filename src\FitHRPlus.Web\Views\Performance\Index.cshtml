@model FitHRPlus.Web.Models.Performance.PerformanceIndexViewModel
@{
    ViewData["Title"] = "تقييم الأداء";
}

<!-- Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-title">
            <h1><i class="bi bi-star"></i> تقييم الأداء</h1>
            <p>إدارة ومتابعة تقييمات أداء الموظفين</p>
        </div>
        <div class="page-actions">
            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#newEvaluationModal">
                <i class="bi bi-plus-circle"></i>
                تقييم جديد
            </button>
            <button type="button" class="btn btn-primary" onclick="generatePerformanceReport()">
                <i class="bi bi-graph-up"></i>
                تقرير الأداء
            </button>
            <div class="btn-group">
                <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="bi bi-download"></i>
                    تصدير
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="exportPerformance('excel')">
                        <i class="bi bi-file-earmark-excel me-2"></i>Excel
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportPerformance('pdf')">
                        <i class="bi bi-file-earmark-pdf me-2"></i>PDF
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-primary">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@(Model.TotalEvaluations ?? 0)</h3>
                        <p>إجمالي التقييمات</p>
                        <small>جميع تقييمات الأداء المسجلة</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-clipboard-check"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: 100%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-success">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@(Model.ExcellentPerformers ?? 0)</h3>
                        <p>أداء ممتاز</p>
                        <small>الموظفين ذوي الأداء المتميز</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-trophy"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: @(Model.TotalEvaluations > 0 ? (Model.ExcellentPerformers * 100 / Model.TotalEvaluations) : 0)%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-warning">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@(Model.PendingEvaluations ?? 0)</h3>
                        <p>تقييمات معلقة</p>
                        <small>التقييمات في انتظار المراجعة</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-clock-history"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: @(Model.TotalEvaluations > 0 ? (Model.PendingEvaluations * 100 / Model.TotalEvaluations) : 0)%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-info">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3>@(Model.AverageScore?.ToString("F1") ?? "0.0")</h3>
                        <p>متوسط النقاط</p>
                        <small>متوسط درجات التقييم العامة</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-graph-up-arrow"></i>
                    </div>
                </div>
                <div class="stats-card-progress">
                    <div class="progress-bar" style="width: @((Model.AverageScore ?? 0) * 20)%"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <div class="col-lg-8">
        <!-- Performance Evaluations -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title">تقييمات الأداء</h5>
                    <div class="card-actions">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary active" onclick="filterEvaluations('all')">
                                الكل
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="filterEvaluations('completed')">
                                مكتملة
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="filterEvaluations('pending')">
                                معلقة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                @if (Model.Evaluations?.Any() == true)
                {
                    <div class="evaluations-list">
                        @foreach (var evaluation in Model.Evaluations)
                        {
                            <div class="evaluation-item @(evaluation.Status?.ToLower())" data-status="@evaluation.Status?.ToLower()">
                                <div class="evaluation-content">
                                    <div class="evaluation-header">
                                        <div class="employee-info">
                                            <div class="employee-avatar">
                                                @evaluation.EmployeeName?.Substring(0, 1).ToUpper()
                                            </div>
                                            <div class="employee-details">
                                                <h6>@evaluation.EmployeeName</h6>
                                                <small>@evaluation.DepartmentName</small>
                                            </div>
                                        </div>
                                        <div class="evaluation-meta">
                                            <span class="evaluation-period">@evaluation.EvaluationPeriod</span>
                                            <span class="evaluation-date">@evaluation.EvaluationDate?.ToString("dd/MM/yyyy")</span>
                                        </div>
                                    </div>
                                    <div class="evaluation-body">
                                        <div class="evaluation-scores">
                                            <div class="score-item">
                                                <span class="score-label">الجودة</span>
                                                <div class="score-bar">
                                                    <div class="score-fill" style="width: @((evaluation.QualityScore ?? 0) * 20)%"></div>
                                                </div>
                                                <span class="score-value">@(evaluation.QualityScore ?? 0)/5</span>
                                            </div>
                                            <div class="score-item">
                                                <span class="score-label">الإنتاجية</span>
                                                <div class="score-bar">
                                                    <div class="score-fill" style="width: @((evaluation.ProductivityScore ?? 0) * 20)%"></div>
                                                </div>
                                                <span class="score-value">@(evaluation.ProductivityScore ?? 0)/5</span>
                                            </div>
                                            <div class="score-item">
                                                <span class="score-label">التعاون</span>
                                                <div class="score-bar">
                                                    <div class="score-fill" style="width: @((evaluation.TeamworkScore ?? 0) * 20)%"></div>
                                                </div>
                                                <span class="score-value">@(evaluation.TeamworkScore ?? 0)/5</span>
                                            </div>
                                        </div>
                                        <div class="evaluation-overall">
                                            <span class="overall-label">التقييم العام:</span>
                                            <span class="overall-score <EMAIL>?.ToLower()">
                                                @evaluation.OverallRating
                                            </span>
                                        </div>
                                    </div>
                                    <div class="evaluation-actions">
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewEvaluation('@evaluation.Id')">
                                            <i class="bi bi-eye"></i>
                                            عرض
                                        </button>
                                        @if (evaluation.Status == "Pending")
                                        {
                                            <button class="btn btn-sm btn-outline-success" onclick="approveEvaluation('@evaluation.Id')">
                                                <i class="bi bi-check"></i>
                                                موافقة
                                            </button>
                                        }
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editEvaluation('@evaluation.Id')">
                                            <i class="bi bi-pencil"></i>
                                            تعديل
                                        </button>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="bi bi-clipboard-x"></i>
                        </div>
                        <h5>لا توجد تقييمات أداء</h5>
                        <p>لم يتم العثور على أي تقييمات أداء</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newEvaluationModal">
                            <i class="bi bi-plus-circle"></i>
                            إضافة تقييم جديد
                        </button>
                    </div>
                }
            </div>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Performance Chart -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">توزيع الأداء</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="performanceChart" style="height: 250px;"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Top Performers -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">أفضل الموظفين</h5>
            </div>
            <div class="card-body">
                <div class="top-performers-list">
                    @if (Model.TopPerformers?.Any() == true)
                    {
                        @foreach (var performer in Model.TopPerformers.Take(5))
                        {
                            <div class="performer-item">
                                <div class="performer-avatar">
                                    @performer.Name?.Substring(0, 1).ToUpper()
                                </div>
                                <div class="performer-info">
                                    <h6>@performer.Name</h6>
                                    <small>@performer.Department</small>
                                </div>
                                <div class="performer-score">
                                    <span class="score">@performer.Score?.ToString("F1")</span>
                                    <div class="stars">
                                        @for (int i = 1; i <= 5; i++)
                                        {
                                            <i class="bi bi-star@(i <= (performer.Score ?? 0) ? "-fill" : "") text-warning"></i>
                                        }
                                    </div>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center text-muted">
                            <i class="bi bi-trophy"></i>
                            <p>لا توجد بيانات متاحة</p>
                        </div>
                    }
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="quick-actions">
                    <button class="quick-action-btn" data-bs-toggle="modal" data-bs-target="#newEvaluationModal">
                        <i class="bi bi-plus-circle"></i>
                        <span>تقييم جديد</span>
                    </button>
                    <button class="quick-action-btn" onclick="generatePerformanceReport()">
                        <i class="bi bi-graph-up"></i>
                        <span>تقرير الأداء</span>
                    </button>
                    <button class="quick-action-btn" onclick="viewPerformanceGoals()">
                        <i class="bi bi-target"></i>
                        <span>الأهداف</span>
                    </button>
                    <button class="quick-action-btn" onclick="scheduleReviews()">
                        <i class="bi bi-calendar-event"></i>
                        <span>جدولة المراجعات</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        $(document).ready(function() {
            loadPerformanceChart();
        });

        function loadPerformanceChart() {
            const ctx = document.getElementById('performanceChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['ممتاز', 'جيد جداً', 'جيد', 'مقبول', 'ضعيف'],
                    datasets: [{
                        data: [
                            @(Model.ExcellentPerformers ?? 0),
                            @(Model.VeryGoodPerformers ?? 0),
                            @(Model.GoodPerformers ?? 0),
                            @(Model.SatisfactoryPerformers ?? 0),
                            @(Model.PoorPerformers ?? 0)
                        ],
                        backgroundColor: [
                            '#28a745',
                            '#20c997',
                            '#ffc107',
                            '#fd7e14',
                            '#dc3545'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: {
                                    family: 'Tajawal'
                                },
                                padding: 15
                            }
                        }
                    }
                }
            });
        }

        function filterEvaluations(status) {
            $('.btn-group .btn').removeClass('active');
            $(`button[onclick="filterEvaluations('${status}')"]`).addClass('active');

            $('.evaluation-item').each(function() {
                const itemStatus = $(this).data('status');
                const show = status === 'all' || itemStatus === status;
                $(this).toggle(show);
            });
        }

        function viewEvaluation(id) {
            window.location.href = `@Url.Action("Details", "Performance")/${id}`;
        }

        function editEvaluation(id) {
            window.location.href = `@Url.Action("Edit", "Performance")/${id}`;
        }

        function approveEvaluation(id) {
            if (confirm('هل تريد الموافقة على هذا التقييم؟')) {
                $.ajax({
                    url: '@Url.Action("Approve", "Performance")',
                    type: 'POST',
                    data: {
                        id: id,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            showToast('تم الموافقة على التقييم بنجاح', 'success');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showToast('حدث خطأ: ' + result.message, 'error');
                        }
                    },
                    error: function() {
                        showToast('حدث خطأ أثناء الموافقة على التقييم', 'error');
                    }
                });
            }
        }

        function generatePerformanceReport() {
            showToast('جاري إنشاء تقرير الأداء...', 'info');
            setTimeout(() => {
                window.open('@Url.Action("GenerateReport", "Performance")', '_blank');
                showToast('تم إنشاء التقرير بنجاح', 'success');
            }, 2000);
        }

        function exportPerformance(format) {
            showToast(`جاري تصدير البيانات بصيغة ${format.toUpperCase()}...`, 'info');
            setTimeout(() => {
                window.open(`@Url.Action("Export", "Performance")?format=${format}`, '_blank');
                showToast('تم تصدير البيانات بنجاح', 'success');
            }, 2000);
        }

        function viewPerformanceGoals() {
            window.location.href = '@Url.Action("Goals", "Performance")';
        }

        function scheduleReviews() {
            window.location.href = '@Url.Action("ScheduleReviews", "Performance")';
        }

        function showToast(message, type = 'info') {
            const bgClass = type === 'success' ? 'bg-success' :
                           type === 'warning' ? 'bg-warning' :
                           type === 'error' ? 'bg-danger' : 'bg-info';

            const toast = $(`
                <div class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `);

            let container = $('.toast-container');
            if (container.length === 0) {
                container = $('<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
                $('body').append(container);
            }

            container.append(toast);
            const bsToast = new bootstrap.Toast(toast[0]);
            bsToast.show();

            toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    </script>
}
