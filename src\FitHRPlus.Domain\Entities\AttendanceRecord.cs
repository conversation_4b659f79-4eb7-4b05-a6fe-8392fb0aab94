using FitHRPlus.Domain.Common;
using System;
using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Domain.Entities
{
    /// <summary>
    /// Attendance record entity for tracking employee attendance
    /// كيان سجل الحضور لتتبع حضور الموظفين
    /// </summary>
    public class AttendanceRecord : BaseEntity
    {
        /// <summary>
        /// Employee ID
        /// معرف الموظف
        /// </summary>
        [Required]
        public Guid EmployeeId { get; set; }

        /// <summary>
        /// Biometric device ID (optional)
        /// معرف جهاز البصمة (اختياري)
        /// </summary>
        public Guid? DeviceId { get; set; }

        /// <summary>
        /// Attendance date
        /// تاريخ الحضور
        /// </summary>
        [Required]
        public DateTime AttendanceDate { get; set; }

        /// <summary>
        /// Check-in time
        /// وقت تسجيل الدخول
        /// </summary>
        public DateTime? CheckInTime { get; set; }

        /// <summary>
        /// Check-out time
        /// وقت تسجيل الخروج
        /// </summary>
        public DateTime? CheckOutTime { get; set; }

        /// <summary>
        /// Actual check-in time (before adjustments)
        /// الوقت الفعلي لتسجيل الدخول (قبل التعديلات)
        /// </summary>
        public DateTime? ActualCheckInTime { get; set; }

        /// <summary>
        /// Actual check-out time (before adjustments)
        /// الوقت الفعلي لتسجيل الخروج (قبل التعديلات)
        /// </summary>
        public DateTime? ActualCheckOutTime { get; set; }

        /// <summary>
        /// Total working hours
        /// إجمالي ساعات العمل
        /// </summary>
        public decimal WorkingHours { get; set; } = 0;

        /// <summary>
        /// Overtime hours
        /// ساعات العمل الإضافي
        /// </summary>
        public decimal OvertimeHours { get; set; } = 0;

        /// <summary>
        /// Break hours
        /// ساعات الاستراحة
        /// </summary>
        public decimal BreakHours { get; set; } = 0;

        /// <summary>
        /// Late minutes
        /// دقائق التأخير
        /// </summary>
        public int LateMinutes { get; set; } = 0;

        /// <summary>
        /// Early leave minutes
        /// دقائق المغادرة المبكرة
        /// </summary>
        public int EarlyLeaveMinutes { get; set; } = 0;

        /// <summary>
        /// Attendance status (Present, Absent, Late, EarlyLeave, Holiday, Leave)
        /// حالة الحضور
        /// </summary>
        [MaxLength(20)]
        public string Status { get; set; } = "Present";

        /// <summary>
        /// Attendance type (Regular, Overtime, Holiday)
        /// نوع الحضور
        /// </summary>
        [MaxLength(20)]
        public string AttendanceType { get; set; } = "Regular";

        /// <summary>
        /// Notes in English
        /// ملاحظات بالإنجليزية
        /// </summary>
        [MaxLength(500)]
        public string? Notes { get; set; }

        /// <summary>
        /// Notes in Arabic
        /// ملاحظات بالعربية
        /// </summary>
        [MaxLength(500)]
        public string? NotesAr { get; set; }

        // Location Information - معلومات الموقع

        /// <summary>
        /// Check-in latitude (GPS)
        /// خط العرض لتسجيل الدخول
        /// </summary>
        public decimal? CheckInLatitude { get; set; }

        /// <summary>
        /// Check-in longitude (GPS)
        /// خط الطول لتسجيل الدخول
        /// </summary>
        public decimal? CheckInLongitude { get; set; }

        /// <summary>
        /// Check-out latitude (GPS)
        /// خط العرض لتسجيل الخروج
        /// </summary>
        public decimal? CheckOutLatitude { get; set; }

        /// <summary>
        /// Check-out longitude (GPS)
        /// خط الطول لتسجيل الخروج
        /// </summary>
        public decimal? CheckOutLongitude { get; set; }

        // Approval Information - معلومات الموافقة

        /// <summary>
        /// Is approved by manager
        /// موافق عليه من المدير
        /// </summary>
        public bool IsApproved { get; set; } = false;

        /// <summary>
        /// Approved by employee ID
        /// معرف الموظف الذي وافق
        /// </summary>
        public Guid? ApprovedBy { get; set; }

        /// <summary>
        /// Approval timestamp
        /// وقت الموافقة
        /// </summary>
        public DateTime? ApprovedAt { get; set; }

        /// <summary>
        /// Approval notes
        /// ملاحظات الموافقة
        /// </summary>
        [MaxLength(500)]
        public string? ApprovalNotes { get; set; }

        // Navigation Properties - خصائص التنقل

        /// <summary>
        /// Employee who owns this attendance record
        /// الموظف الذي يملك سجل الحضور هذا
        /// </summary>
        public virtual Employee Employee { get; set; } = null!;

        /// <summary>
        /// Biometric device used for attendance
        /// جهاز البصمة المستخدم للحضور
        /// </summary>
        public virtual BiometricDevice? Device { get; set; }

        /// <summary>
        /// Employee who approved this record
        /// الموظف الذي وافق على هذا السجل
        /// </summary>
        public virtual Employee? ApprovedByEmployee { get; set; }
    }
}