using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Web.Models.Admin
{
    /// <summary>
    /// Employee view model for web forms
    /// نموذج عرض الموظف لنماذج الويب
    /// </summary>
    public class EmployeeViewModel
    {
        /// <summary>
        /// Employee ID
        /// معرف الموظف
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Employee number (unique within company)
        /// رقم الموظف (فريد داخل الشركة)
        /// </summary>
        [Required(ErrorMessage = "Employee number is required")]
        [Display(Name = "Employee Number", Prompt = "Enter employee number")]
        public string EmployeeNumber { get; set; } = string.Empty;

        /// <summary>
        /// First name
        /// الاسم الأول
        /// </summary>
        [Required(ErrorMessage = "First name is required")]
        [StringLength(100, ErrorMessage = "First name cannot exceed 100 characters")]
        [Display(Name = "First Name", Prompt = "Enter first name")]
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// Last name
        /// الاسم الأخير
        /// </summary>
        [Required(ErrorMessage = "Last name is required")]
        [StringLength(100, ErrorMessage = "Last name cannot exceed 100 characters")]
        [Display(Name = "Last Name", Prompt = "Enter last name")]
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// First name in Arabic
        /// الاسم الأول بالعربية
        /// </summary>
        [StringLength(100, ErrorMessage = "Arabic first name cannot exceed 100 characters")]
        [Display(Name = "First Name (Arabic)", Prompt = "Enter first name in Arabic")]
        public string? FirstNameAr { get; set; }

        /// <summary>
        /// Last name in Arabic
        /// الاسم الأخير بالعربية
        /// </summary>
        [StringLength(100, ErrorMessage = "Arabic last name cannot exceed 100 characters")]
        [Display(Name = "Last Name (Arabic)", Prompt = "Enter last name in Arabic")]
        public string? LastNameAr { get; set; }

        /// <summary>
        /// Full name (computed)
        /// الاسم الكامل (محسوب)
        /// </summary>
        public string FullName => $"{FirstName} {LastName}";

        /// <summary>
        /// Full name in Arabic (computed)
        /// الاسم الكامل بالعربية (محسوب)
        /// </summary>
        public string FullNameAr => $"{FirstNameAr} {LastNameAr}";

        /// <summary>
        /// Email address
        /// البريد الإلكتروني
        /// </summary>
        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
        [Display(Name = "Email", Prompt = "Enter email address")]
        public string? Email { get; set; }

        /// <summary>
        /// Phone number
        /// رقم الهاتف
        /// </summary>
        [Phone(ErrorMessage = "Invalid phone number format")]
        [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
        [Display(Name = "Phone", Prompt = "Enter phone number")]
        public string? Phone { get; set; }

        /// <summary>
        /// Mobile number
        /// رقم الجوال
        /// </summary>
        [Phone(ErrorMessage = "Invalid mobile number format")]
        [StringLength(20, ErrorMessage = "Mobile number cannot exceed 20 characters")]
        [Display(Name = "Mobile", Prompt = "Enter mobile number")]
        public string? Mobile { get; set; }

        /// <summary>
        /// National ID number
        /// رقم الهوية الوطنية
        /// </summary>
        [StringLength(20, ErrorMessage = "National ID cannot exceed 20 characters")]
        [Display(Name = "National ID", Prompt = "Enter national ID")]
        public string? NationalId { get; set; }

        /// <summary>
        /// Passport number
        /// رقم جواز السفر
        /// </summary>
        [StringLength(20, ErrorMessage = "Passport number cannot exceed 20 characters")]
        [Display(Name = "Passport Number", Prompt = "Enter passport number")]
        public string? PassportNumber { get; set; }

        /// <summary>
        /// Date of birth
        /// تاريخ الميلاد
        /// </summary>
        [DataType(DataType.Date)]
        [Display(Name = "Date of Birth")]
        public DateTime? DateOfBirth { get; set; }

        /// <summary>
        /// Gender (Male/Female)
        /// الجنس (ذكر/أنثى)
        /// </summary>
        [Display(Name = "Gender")]
        public string? Gender { get; set; }

        /// <summary>
        /// Marital status
        /// الحالة الاجتماعية
        /// </summary>
        [Display(Name = "Marital Status")]
        public string? MaritalStatus { get; set; }

        /// <summary>
        /// Nationality
        /// الجنسية
        /// </summary>
        [StringLength(50, ErrorMessage = "Nationality cannot exceed 50 characters")]
        [Display(Name = "Nationality", Prompt = "Enter nationality")]
        public string? Nationality { get; set; }

        /// <summary>
        /// Address
        /// العنوان
        /// </summary>
        [StringLength(500, ErrorMessage = "Address cannot exceed 500 characters")]
        [Display(Name = "Address", Prompt = "Enter address")]
        [DataType(DataType.MultilineText)]
        public string? Address { get; set; }

        /// <summary>
        /// City
        /// المدينة
        /// </summary>
        [StringLength(100, ErrorMessage = "City cannot exceed 100 characters")]
        [Display(Name = "City", Prompt = "Enter city")]
        public string? City { get; set; }

        /// <summary>
        /// Country
        /// الدولة
        /// </summary>
        [StringLength(100, ErrorMessage = "Country cannot exceed 100 characters")]
        [Display(Name = "Country", Prompt = "Enter country")]
        public string? Country { get; set; } = "Egypt";

        /// <summary>
        /// Hire date
        /// تاريخ التوظيف
        /// </summary>
        [Required(ErrorMessage = "Hire date is required")]
        [DataType(DataType.Date)]
        [Display(Name = "Hire Date")]
        public DateTime HireDate { get; set; }

        /// <summary>
        /// Termination date (if applicable)
        /// تاريخ انتهاء الخدمة (إن وجد)
        /// </summary>
        [DataType(DataType.Date)]
        [Display(Name = "Termination Date")]
        public DateTime? TerminationDate { get; set; }

        /// <summary>
        /// Employment status
        /// حالة التوظيف
        /// </summary>
        [Required(ErrorMessage = "Employment status is required")]
        [Display(Name = "Employment Status")]
        public string EmploymentStatus { get; set; } = "Active";

        /// <summary>
        /// Employment type (Full-time, Part-time, Contract, etc.)
        /// نوع التوظيف (دوام كامل، دوام جزئي، عقد، إلخ)
        /// </summary>
        [StringLength(50, ErrorMessage = "Employment type cannot exceed 50 characters")]
        [Display(Name = "Employment Type")]
        public string? EmploymentType { get; set; }

        /// <summary>
        /// Company ID
        /// معرف الشركة
        /// </summary>
        [Required(ErrorMessage = "Company is required")]
        public Guid CompanyId { get; set; }

        /// <summary>
        /// Company name
        /// اسم الشركة
        /// </summary>
        public string CompanyName { get; set; } = string.Empty;

        /// <summary>
        /// Department ID
        /// معرف القسم
        /// </summary>
        [Display(Name = "Department")]
        public Guid? DepartmentId { get; set; }

        /// <summary>
        /// Department name
        /// اسم القسم
        /// </summary>
        public string? DepartmentName { get; set; }

        /// <summary>
        /// Position ID
        /// معرف المنصب
        /// </summary>
        [Display(Name = "Position")]
        public Guid? PositionId { get; set; }

        /// <summary>
        /// Position title
        /// مسمى المنصب
        /// </summary>
        public string? PositionTitle { get; set; }

        /// <summary>
        /// Manager ID
        /// معرف المدير
        /// </summary>
        [Display(Name = "Manager")]
        public Guid? ManagerId { get; set; }

        /// <summary>
        /// Manager name
        /// اسم المدير
        /// </summary>
        public string? ManagerName { get; set; }

        /// <summary>
        /// Basic salary
        /// الراتب الأساسي
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "Salary must be a positive number")]
        [Display(Name = "Basic Salary")]
        [DataType(DataType.Currency)]
        public decimal? BasicSalary { get; set; }

        /// <summary>
        /// Profile picture URL
        /// رابط صورة الملف الشخصي
        /// </summary>
        [Display(Name = "Profile Picture")]
        public string? ProfilePicture { get; set; }

        /// <summary>
        /// Emergency contact name
        /// اسم جهة الاتصال في حالات الطوارئ
        /// </summary>
        [StringLength(200, ErrorMessage = "Emergency contact name cannot exceed 200 characters")]
        [Display(Name = "Emergency Contact Name", Prompt = "Enter emergency contact name")]
        public string? EmergencyContactName { get; set; }

        /// <summary>
        /// Emergency contact phone
        /// هاتف جهة الاتصال في حالات الطوارئ
        /// </summary>
        [StringLength(20, ErrorMessage = "Emergency contact phone cannot exceed 20 characters")]
        [Display(Name = "Emergency Contact Phone", Prompt = "Enter emergency contact phone")]
        public string? EmergencyContactPhone { get; set; }

        /// <summary>
        /// Emergency contact relationship
        /// علاقة جهة الاتصال في حالات الطوارئ
        /// </summary>
        [StringLength(50, ErrorMessage = "Emergency contact relationship cannot exceed 50 characters")]
        [Display(Name = "Emergency Contact Relationship", Prompt = "Enter relationship")]
        public string? EmergencyContactRelationship { get; set; }

        /// <summary>
        /// Notes
        /// ملاحظات
        /// </summary>
        [StringLength(1000, ErrorMessage = "Notes cannot exceed 1000 characters")]
        [Display(Name = "Notes", Prompt = "Enter any additional notes")]
        [DataType(DataType.MultilineText)]
        public string? Notes { get; set; }

        /// <summary>
        /// Whether the employee is active
        /// ما إذا كان الموظف نشط
        /// </summary>
        [Display(Name = "Active")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Age (computed from date of birth)
        /// العمر (محسوب من تاريخ الميلاد)
        /// </summary>
        public int? Age { get; set; }

        /// <summary>
        /// Years of service (computed from hire date)
        /// سنوات الخدمة (محسوبة من تاريخ التوظيف)
        /// </summary>
        public int YearsOfService { get; set; }

        // Navigation properties for dropdowns
        /// <summary>
        /// Available departments for selection
        /// الأقسام المتاحة للاختيار
        /// </summary>
        public List<DepartmentViewModel> AvailableDepartments { get; set; } = new();

        /// <summary>
        /// Available positions for selection
        /// المناصب المتاحة للاختيار
        /// </summary>
        public List<PositionViewModel> AvailablePositions { get; set; } = new();

        /// <summary>
        /// Available managers for selection
        /// المديرين المتاحين للاختيار
        /// </summary>
        public List<EmployeeViewModel> AvailableManagers { get; set; } = new();
    }
}
