@model FitHRPlus.Web.Models.Department.DepartmentViewModel
@{
    ViewData["Title"] = "Create Department / إنشاء قسم";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-plus text-primary me-2"></i>
                        Create Department / إنشاء قسم
                    </h2>
                    <p class="text-muted mb-0">Add a new department to your company / أضف قسماً جديداً لشركتك</p>
                </div>
                <div>
                    <a href="@Url.Action("Index")" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to List / العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Form -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>
                        Department Information / معلومات القسم
                    </h5>
                </div>
                <div class="card-body">
                    <form asp-action="Create" method="post">
                        @Html.AntiForgeryToken()
                        <input asp-for="CompanyId" type="hidden" />

                        <div class="row">
                            <!-- Department Name -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="Name" class="form-label">
                                    <i class="fas fa-tag me-1"></i>
                                    Department Name / اسم القسم
                                    <span class="text-danger">*</span>
                                </label>
                                <input asp-for="Name" class="form-control" placeholder="Enter department name" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>

                            <!-- Department Name (Arabic) -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="NameAr" class="form-label">
                                    <i class="fas fa-tag me-1"></i>
                                    Department Name (Arabic) / اسم القسم بالعربية
                                    <span class="text-danger">*</span>
                                </label>
                                <input asp-for="NameAr" class="form-control" placeholder="أدخل اسم القسم بالعربية" dir="rtl" />
                                <span asp-validation-for="NameAr" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Description -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="Description" class="form-label">
                                    <i class="fas fa-align-left me-1"></i>
                                    Description / الوصف
                                </label>
                                <textarea asp-for="Description" class="form-control" rows="4" 
                                          placeholder="Enter department description"></textarea>
                                <span asp-validation-for="Description" class="text-danger"></span>
                            </div>

                            <!-- Description (Arabic) -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="DescriptionAr" class="form-label">
                                    <i class="fas fa-align-right me-1"></i>
                                    Description (Arabic) / الوصف بالعربية
                                </label>
                                <textarea asp-for="DescriptionAr" class="form-control" rows="4" 
                                          placeholder="أدخل وصف القسم بالعربية" dir="rtl"></textarea>
                                <span asp-validation-for="DescriptionAr" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Budget -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="Budget" class="form-label">
                                    <i class="fas fa-dollar-sign me-1"></i>
                                    Budget / الميزانية
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input asp-for="Budget" class="form-control" type="number" step="0.01" 
                                           placeholder="0.00" />
                                </div>
                                <span asp-validation-for="Budget" class="text-danger"></span>
                                <div class="form-text">Optional budget allocation for this department</div>
                            </div>

                            <!-- Status -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="IsActive" class="form-label">
                                    <i class="fas fa-toggle-on me-1"></i>
                                    Status / الحالة
                                </label>
                                <div class="form-check form-switch">
                                    <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                    <label asp-for="IsActive" class="form-check-label">
                                        Active Department / قسم نشط
                                    </label>
                                </div>
                                <div class="form-text">Active departments can have employees assigned</div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <a href="@Url.Action("Index")" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        Cancel / إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        Create Department / إنشاء القسم
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Auto-generate Arabic name if not provided
            $('#Name').on('blur', function() {
                var nameAr = $('#NameAr').val();
                if (!nameAr) {
                    // You can implement auto-translation logic here if needed
                }
            });

            // Format budget input
            $('#Budget').on('input', function() {
                var value = $(this).val();
                if (value && !isNaN(value)) {
                    $(this).val(parseFloat(value).toFixed(2));
                }
            });

            // Form validation enhancement
            $('form').on('submit', function(e) {
                var isValid = true;
                
                // Check required fields
                if (!$('#Name').val().trim()) {
                    isValid = false;
                    $('#Name').addClass('is-invalid');
                } else {
                    $('#Name').removeClass('is-invalid');
                }
                
                if (!$('#NameAr').val().trim()) {
                    isValid = false;
                    $('#NameAr').addClass('is-invalid');
                } else {
                    $('#NameAr').removeClass('is-invalid');
                }
                
                if (!isValid) {
                    e.preventDefault();
                    toastr.error('Please fill in all required fields / يرجى ملء جميع الحقول المطلوبة');
                }
            });
        });
    </script>
}
