using FitHRPlus.Application.DTOs.Common;
using FitHRPlus.Application.DTOs.Leave;
using FitHRPlus.Application.DTOs.Employee;
using FitHRPlus.Application.Common;

namespace FitHRPlus.Application.Interfaces
{
    /// <summary>
    /// Leave management service interface
    /// واجهة خدمة إدارة الإجازات
    /// </summary>
    public interface ILeaveService
    {
        /// <summary>
        /// Get paginated list of leave requests
        /// الحصول على قائمة مقسمة لطلبات الإجازات
        /// </summary>
        /// <param name="request">List request parameters</param>
        /// <returns>Paginated leave requests</returns>
        Task<ServiceResult<PaginatedResult<LeaveRequestDto>>> GetLeaveRequestsAsync(LeaveRequestListDto request);

        /// <summary>
        /// Get leave request by ID
        /// الحصول على طلب الإجازة بالمعرف
        /// </summary>
        /// <param name="id">Leave request ID</param>
        /// <returns>Leave request details</returns>
        Task<ServiceResult<LeaveRequestDto>> GetLeaveRequestByIdAsync(Guid id);

        /// <summary>
        /// Create new leave request
        /// إنشاء طلب إجازة جديد
        /// </summary>
        /// <param name="request">Create request data</param>
        /// <param name="userId">User creating the request</param>
        /// <returns>Created leave request</returns>
        Task<ServiceResult<LeaveRequestDto>> CreateLeaveRequestAsync(CreateLeaveRequestDto request, Guid userId);

        /// <summary>
        /// Update existing leave request
        /// تحديث طلب الإجازة الموجود
        /// </summary>
        /// <param name="request">Update request data</param>
        /// <param name="userId">User updating the request</param>
        /// <returns>Updated leave request</returns>
        Task<ServiceResult<LeaveRequestDto>> UpdateLeaveRequestAsync(UpdateLeaveRequestDto request, Guid userId);

        /// <summary>
        /// Approve leave request
        /// الموافقة على طلب الإجازة
        /// </summary>
        /// <param name="request">Approval request data</param>
        /// <param name="userId">User approving the request</param>
        /// <returns>Approved leave request</returns>
        Task<ServiceResult<LeaveRequestDto>> ApproveLeaveRequestAsync(ApproveLeaveRequestDto request, Guid userId);

        /// <summary>
        /// Reject leave request
        /// رفض طلب الإجازة
        /// </summary>
        /// <param name="request">Rejection request data</param>
        /// <param name="userId">User rejecting the request</param>
        /// <returns>Rejected leave request</returns>
        Task<ServiceResult<LeaveRequestDto>> RejectLeaveRequestAsync(RejectLeaveRequestDto request, Guid userId);

        /// <summary>
        /// Cancel leave request
        /// إلغاء طلب الإجازة
        /// </summary>
        /// <param name="leaveRequestId">Leave request ID</param>
        /// <param name="userId">User cancelling the request</param>
        /// <returns>Cancelled leave request</returns>
        Task<ServiceResult<LeaveRequestDto>> CancelLeaveRequestAsync(Guid leaveRequestId, Guid userId);

        /// <summary>
        /// Delete leave request
        /// حذف طلب الإجازة
        /// </summary>
        /// <param name="leaveRequestId">Leave request ID</param>
        /// <param name="userId">User deleting the request</param>
        /// <returns>Success result</returns>
        Task<ServiceResult<bool>> DeleteLeaveRequestAsync(Guid leaveRequestId, Guid userId);

        /// <summary>
        /// Get leave types for a company
        /// الحصول على أنواع الإجازات للشركة
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <returns>List of leave types</returns>
        Task<ServiceResult<List<LeaveTypeDto>>> GetLeaveTypesAsync(Guid companyId);

        /// <summary>
        /// Get leave type by ID
        /// الحصول على نوع الإجازة بالمعرف
        /// </summary>
        /// <param name="id">Leave type ID</param>
        /// <returns>Leave type details</returns>
        Task<ServiceResult<LeaveTypeDto>> GetLeaveTypeByIdAsync(Guid id);

        /// <summary>
        /// Get leave balances for an employee
        /// الحصول على أرصدة الإجازات للموظف
        /// </summary>
        /// <param name="employeeId">Employee ID</param>
        /// <param name="year">Year (optional, defaults to current year)</param>
        /// <returns>List of leave balances</returns>
        Task<ServiceResult<List<LeaveBalanceDto>>> GetLeaveBalancesAsync(Guid employeeId, int? year = null);

        /// <summary>
        /// Get leave balance for specific employee and leave type
        /// الحصول على رصيد الإجازة لموظف ونوع إجازة محددين
        /// </summary>
        /// <param name="employeeId">Employee ID</param>
        /// <param name="leaveTypeId">Leave type ID</param>
        /// <param name="year">Year (optional, defaults to current year)</param>
        /// <returns>Leave balance</returns>
        Task<ServiceResult<LeaveBalanceDto>> GetLeaveBalanceAsync(Guid employeeId, Guid leaveTypeId, int? year = null);

        /// <summary>
        /// Update leave balance
        /// تحديث رصيد الإجازة
        /// </summary>
        /// <param name="employeeId">Employee ID</param>
        /// <param name="leaveTypeId">Leave type ID</param>
        /// <param name="year">Year</param>
        /// <param name="entitledDays">Entitled days</param>
        /// <param name="userId">User updating the balance</param>
        /// <returns>Updated leave balance</returns>
        Task<ServiceResult<LeaveBalanceDto>> UpdateLeaveBalanceAsync(Guid employeeId, Guid leaveTypeId, int year, decimal entitledDays, Guid userId);

        /// <summary>
        /// Calculate leave days between dates
        /// حساب أيام الإجازة بين التواريخ
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="companyId">Company ID for holiday calculation</param>
        /// <returns>Number of leave days</returns>
        Task<ServiceResult<decimal>> CalculateLeaveDaysAsync(DateTime startDate, DateTime endDate, Guid companyId);

        /// <summary>
        /// Get leave statistics
        /// الحصول على إحصائيات الإجازات
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="employeeId">Employee ID (optional)</param>
        /// <param name="year">Year (optional, defaults to current year)</param>
        /// <returns>Leave statistics</returns>
        Task<ServiceResult<LeaveStatisticsDto>> GetLeaveStatisticsAsync(Guid companyId, Guid? employeeId = null, int? year = null);

        /// <summary>
        /// Check if employee can take leave
        /// التحقق من إمكانية أخذ الموظف للإجازة
        /// </summary>
        /// <param name="employeeId">Employee ID</param>
        /// <param name="leaveTypeId">Leave type ID</param>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Validation result with messages</returns>
        Task<ServiceResult<bool>> ValidateLeaveRequestAsync(Guid employeeId, Guid leaveTypeId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Get employees who can approve leave requests
        /// الحصول على الموظفين الذين يمكنهم الموافقة على طلبات الإجازات
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="employeeId">Employee requesting leave</param>
        /// <returns>List of approvers</returns>
        Task<ServiceResult<List<EmployeeDto>>> GetLeaveApproversAsync(Guid companyId, Guid employeeId);

        /// <summary>
        /// Get leave requests pending approval for a user
        /// الحصول على طلبات الإجازات المعلقة للموافقة للمستخدم
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>List of pending leave requests</returns>
        Task<ServiceResult<List<LeaveRequestDto>>> GetPendingLeaveRequestsForApprovalAsync(Guid userId);

        /// <summary>
        /// Initialize leave balances for new employee
        /// تهيئة أرصدة الإجازات للموظف الجديد
        /// </summary>
        /// <param name="employeeId">Employee ID</param>
        /// <param name="hireDate">Hire date</param>
        /// <param name="userId">User initializing the balances</param>
        /// <returns>Success result</returns>
        Task<ServiceResult<bool>> InitializeLeaveBalancesAsync(Guid employeeId, DateTime hireDate, Guid userId);

        /// <summary>
        /// Process year-end leave balance carry forward
        /// معالجة ترحيل أرصدة الإجازات في نهاية السنة
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="fromYear">From year</param>
        /// <param name="toYear">To year</param>
        /// <param name="userId">User processing the carry forward</param>
        /// <returns>Success result</returns>
        Task<ServiceResult<bool>> ProcessYearEndCarryForwardAsync(Guid companyId, int fromYear, int toYear, Guid userId);
    }
}
