@model FitHRPlus.Web.Models.Payroll.PayrollListViewModel
@{
    ViewData["Title"] = "إدارة كشوف المرتبات";
}

<!-- Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-title">
            <h1><i class="bi bi-currency-dollar"></i> إدارة كشوف المرتبات</h1>
            <p>إدارة ومتابعة كشوف مرتبات الموظفين والحسابات المالية</p>
        </div>
        <div class="page-actions">
            <a href="@Url.Action("Generate")" class="btn btn-success">
                <i class="bi bi-plus-circle"></i>
                إنشاء كشف راتب
            </a>
            <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="bi bi-download"></i>
                    تصدير
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="exportToExcel()">
                        <i class="bi bi-file-earmark-excel me-2"></i>Excel
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportToPDF()">
                        <i class="bi bi-file-earmark-pdf me-2"></i>PDF
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportToCSV()">
                        <i class="bi bi-file-earmark-text me-2"></i>CSV
                    </a></li>
                </ul>
            </div>
            <button type="button" class="btn btn-info" onclick="openPayrollCalculator()">
                <i class="bi bi-calculator"></i>
                حاسبة الراتب
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
@if (Model.Statistics != null)
{
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card stats-card-primary">
                <div class="stats-card-body">
                    <div class="stats-card-content">
                        <div class="stats-card-info">
                            <h3>@Model.Statistics.TotalPayrolls</h3>
                            <p>إجمالي كشوف المرتبات</p>
                            <small>جميع الكشوف المسجلة في النظام</small>
                        </div>
                        <div class="stats-card-icon">
                            <i class="bi bi-file-earmark-text"></i>
                        </div>
                    </div>
                    <div class="stats-card-progress">
                        <div class="progress-bar" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card stats-card-success">
                <div class="stats-card-body">
                    <div class="stats-card-content">
                        <div class="stats-card-info">
                            <h3>@Model.Statistics.PaidPayrolls</h3>
                            <p>الكشوف المدفوعة</p>
                            <small>الكشوف التي تم دفعها للموظفين</small>
                        </div>
                        <div class="stats-card-icon">
                            <i class="bi bi-check-circle"></i>
                        </div>
                    </div>
                    <div class="stats-card-progress">
                        <div class="progress-bar" style="width: @(Model.Statistics.TotalPayrolls > 0 ? (Model.Statistics.PaidPayrolls * 100 / Model.Statistics.TotalPayrolls) : 0)%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card stats-card-warning">
                <div class="stats-card-body">
                    <div class="stats-card-content">
                        <div class="stats-card-info">
                            <h3>@Model.Statistics.ProcessedPayrolls</h3>
                            <p>الكشوف قيد المعالجة</p>
                            <small>الكشوف التي يتم معالجتها حالياً</small>
                        </div>
                        <div class="stats-card-icon">
                            <i class="bi bi-gear"></i>
                        </div>
                    </div>
                    <div class="stats-card-progress">
                        <div class="progress-bar" style="width: @(Model.Statistics.TotalPayrolls > 0 ? (Model.Statistics.ProcessedPayrolls * 100 / Model.Statistics.TotalPayrolls) : 0)%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card stats-card-info">
                <div class="stats-card-body">
                    <div class="stats-card-content">
                        <div class="stats-card-info">
                            <h3>@Model.Statistics.TotalNetSalary.ToString("C")</h3>
                            <p>إجمالي الراتب الصافي</p>
                            <small>مجموع جميع الرواتب الصافية</small>
                        </div>
                        <div class="stats-card-icon">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                    </div>
                    <div class="stats-card-progress">
                        <div class="progress-bar" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

<!-- Main Content -->
<div class="row">
    <div class="col-lg-8">
        <!-- Filters Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">تصفية البيانات</h5>
            </div>
            <div class="card-body">
                <form method="get" asp-action="Index" class="filter-form">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">الشهر</label>
                            <select name="PayrollMonth" class="form-select" asp-items="Model.MonthOptions">
                                <option value="">جميع الشهور</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">السنة</label>
                            <select name="PayrollYear" class="form-select" asp-items="Model.YearOptions">
                                <option value="">جميع السنوات</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الموظف</label>
                            <select name="EmployeeId" class="form-select" asp-items="Model.Employees">
                                <option value="">جميع الموظفين</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">القسم</label>
                            <select name="DepartmentId" class="form-select" asp-items="Model.Departments">
                                <option value="">جميع الأقسام</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الحالة</label>
                            <select name="Status" class="form-select" asp-items="Model.StatusOptions">
                                <option value="">جميع الحالات</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">البحث</label>
                            <input type="text" name="SearchTerm" class="form-control" placeholder="اسم الموظف أو الرقم الوظيفي" value="@Model.SearchTerm" />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search"></i>
                                    بحث
                                </button>
                                <a href="@Url.Action("Index")" class="btn btn-secondary">
                                    <i class="bi bi-x-circle"></i>
                                    مسح
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Payroll Table -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title">
                        سجلات كشوف المرتبات
                        <span class="badge bg-primary ms-2">@Model.TotalCount</span>
                    </h5>
                    <div class="card-actions">
                        <button type="button" class="btn btn-sm btn-success" onclick="bulkApprove()" disabled id="bulkApproveBtn">
                            <i class="bi bi-check-all"></i>
                            موافقة مجمعة
                        </button>
                        <button type="button" class="btn btn-sm btn-primary" onclick="bulkPay()" disabled id="bulkPayBtn">
                            <i class="bi bi-credit-card"></i>
                            دفع مجمع
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                @if (Model.Payrolls.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll(this)" />
                                    </th>
                                    <th>الموظف</th>
                                    <th>الفترة</th>
                                    <th>القسم</th>
                                    <th>الراتب الأساسي</th>
                                    <th>الراتب الإجمالي</th>
                                    <th>الراتب الصافي</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var payroll in Model.Payrolls)
                                {
                                    <tr class="payroll-row">
                                        <td>
                                            <input type="checkbox" class="payroll-checkbox" value="@payroll.Id" onchange="updateBulkButtons()" />
                                        </td>
                                        <td>
                                            <div class="employee-info">
                                                <div class="employee-avatar">
                                                    @payroll.EmployeeName.Substring(0, 1).ToUpper()
                                                </div>
                                                <div class="employee-details">
                                                    <h6>@payroll.EmployeeName</h6>
                                                    <small>@payroll.EmployeeCode</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="period-info">
                                                <span class="period">@payroll.PayrollPeriodDisplay</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="department-info">
                                                <span>@payroll.DepartmentName</span>
                                                @if (!string.IsNullOrEmpty(payroll.DepartmentNameAr))
                                                {
                                                    <small class="text-muted d-block">@payroll.DepartmentNameAr</small>
                                                }
                                            </div>
                                        </td>
                                        <td>
                                            <div class="salary-amount basic">
                                                @payroll.BasicSalary.ToString("C")
                                            </div>
                                        </td>
                                        <td>
                                            <div class="salary-amount gross">
                                                @payroll.GrossSalary.ToString("C")
                                            </div>
                                        </td>
                                        <td>
                                            <div class="salary-amount net">
                                                @payroll.NetSalary.ToString("C")
                                            </div>
                                        </td>
                                        <td>
                                            <span class="status-badge @payroll.StatusBadgeClass">
                                                @payroll.StatusAr
                                            </span>
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-ghost dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="bi bi-three-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="@Url.Action("Details", new { id = payroll.Id })">
                                                        <i class="bi bi-eye me-2"></i>عرض التفاصيل
                                                    </a></li>
                                                    @if (payroll.CanProcess)
                                                    {
                                                        <li><a class="dropdown-item" href="javascript:void(0)" onclick="processPayroll('@payroll.Id')">
                                                            <i class="bi bi-gear me-2"></i>معالجة
                                                        </a></li>
                                                    }
                                                    @if (payroll.CanApprove)
                                                    {
                                                        <li><a class="dropdown-item" href="javascript:void(0)" onclick="approvePayroll('@payroll.Id')">
                                                            <i class="bi bi-check me-2"></i>موافقة
                                                        </a></li>
                                            }
                                                    @if (payroll.CanPay)
                                                    {
                                                        <li><a class="dropdown-item" href="javascript:void(0)" onclick="payPayroll('@payroll.Id')">
                                                            <i class="bi bi-credit-card me-2"></i>دفع
                                                        </a></li>
                                                    }
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger" href="javascript:void(0)" onclick="deletePayroll('@payroll.Id')">
                                                        <i class="bi bi-trash me-2"></i>حذف
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="bi bi-file-earmark-text"></i>
                        </div>
                        <h5>لا توجد كشوف مرتبات</h5>
                        <p>لم يتم العثور على أي كشوف مرتبات. جرب تعديل معايير البحث أو إنشاء كشوف جديدة.</p>
                        <a href="@Url.Action("Generate")" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i>
                            إنشاء كشف راتب جديد
                        </a>
                    </div>
                }
            </div>

            <!-- Pagination -->
            @if (Model.TotalPages > 1)
            {
                <div class="card-footer">
                    <nav aria-label="صفحات كشوف المرتبات">
                        <ul class="pagination justify-content-center mb-0">
                            @if (Model.CurrentPage > 1)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="@Url.Action("Index", new { page = Model.CurrentPage - 1, pageSize = Model.PageSize, searchTerm = Model.SearchTerm, status = Model.Status, employeeId = Model.EmployeeId, departmentId = Model.DepartmentId, payrollMonth = Model.PayrollMonth, payrollYear = Model.PayrollYear })">
                                        <i class="bi bi-chevron-right"></i>
                                        السابق
                                    </a>
                                </li>
                            }

                            @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                            {
                                <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                    <a class="page-link" href="@Url.Action("Index", new { page = i, pageSize = Model.PageSize, searchTerm = Model.SearchTerm, status = Model.Status, employeeId = Model.EmployeeId, departmentId = Model.DepartmentId, payrollMonth = Model.PayrollMonth, payrollYear = Model.PayrollYear })">@i</a>
                                </li>
                            }

                            @if (Model.CurrentPage < Model.TotalPages)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="@Url.Action("Index", new { page = Model.CurrentPage + 1, pageSize = Model.PageSize, searchTerm = Model.SearchTerm, status = Model.Status, employeeId = Model.EmployeeId, departmentId = Model.DepartmentId, payrollMonth = Model.PayrollMonth, payrollYear = Model.PayrollYear })">
                                        التالي
                                        <i class="bi bi-chevron-left"></i>
                                    </a>
                                </li>
                            }
                        </ul>
                    </nav>
                </div>
            }
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Payroll Calculator -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">حاسبة الراتب</h5>
            </div>
            <div class="card-body">
                <div class="salary-calculator">
                    <div class="mb-3">
                        <label class="form-label">الراتب الأساسي</label>
                        <input type="number" class="form-control" id="basicSalary" placeholder="0.00" onchange="calculateSalary()">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البدلات</label>
                        <input type="number" class="form-control" id="allowances" placeholder="0.00" onchange="calculateSalary()">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الخصومات</label>
                        <input type="number" class="form-control" id="deductions" placeholder="0.00" onchange="calculateSalary()">
                    </div>
                    <div class="calculation-result">
                        <div class="result-item">
                            <span>الراتب الإجمالي:</span>
                            <span id="grossSalary" class="result-value">0.00</span>
                        </div>
                        <div class="result-item">
                            <span>الضرائب (14%):</span>
                            <span id="taxes" class="result-value">0.00</span>
                        </div>
                        <div class="result-item">
                            <span>التأمينات (11%):</span>
                            <span id="insurance" class="result-value">0.00</span>
                        </div>
                        <div class="result-item total">
                            <span>الراتب الصافي:</span>
                            <span id="netSalary" class="result-value">0.00</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payroll Summary -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">ملخص الرواتب</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="payrollChart" style="height: 250px;"></canvas>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="quick-actions">
                    <a href="@Url.Action("Generate")" class="quick-action-btn">
                        <i class="bi bi-plus-circle"></i>
                        <span>إنشاء كشف راتب</span>
                    </a>
                    <button class="quick-action-btn" onclick="exportToExcel()">
                        <i class="bi bi-file-earmark-excel"></i>
                        <span>تصدير Excel</span>
                    </button>
                    <button class="quick-action-btn" onclick="exportToPDF()">
                        <i class="bi bi-file-earmark-pdf"></i>
                        <span>تصدير PDF</span>
                    </button>
                    <button class="quick-action-btn" onclick="sendPayslips()">
                        <i class="bi bi-envelope"></i>
                        <span>إرسال كشوف الراتب</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        $(document).ready(function() {
            loadPayrollChart();
            initializeSalaryCalculator();
        });

        function loadPayrollChart() {
            const ctx = document.getElementById('payrollChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['مدفوعة', 'قيد المعالجة', 'في الانتظار'],
                    datasets: [{
                        label: 'عدد الكشوف',
                        data: [
                            @(Model.Statistics?.PaidPayrolls ?? 0),
                            @(Model.Statistics?.ProcessedPayrolls ?? 0),
                            @((Model.Statistics?.TotalPayrolls ?? 0) - (Model.Statistics?.PaidPayrolls ?? 0) - (Model.Statistics?.ProcessedPayrolls ?? 0))
                        ],
                        backgroundColor: [
                            '#28a745',
                            '#ffc107',
                            '#6c757d'
                        ],
                        borderRadius: 8,
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        function initializeSalaryCalculator() {
            calculateSalary(); // Initial calculation
        }

        function calculateSalary() {
            const basicSalary = parseFloat($('#basicSalary').val()) || 0;
            const allowances = parseFloat($('#allowances').val()) || 0;
            const deductions = parseFloat($('#deductions').val()) || 0;

            const grossSalary = basicSalary + allowances;
            const taxes = grossSalary * 0.14; // 14% tax
            const insurance = grossSalary * 0.11; // 11% insurance
            const netSalary = grossSalary - taxes - insurance - deductions;

            $('#grossSalary').text(grossSalary.toFixed(2));
            $('#taxes').text(taxes.toFixed(2));
            $('#insurance').text(insurance.toFixed(2));
            $('#netSalary').text(Math.max(0, netSalary).toFixed(2));
        }

        function toggleSelectAll(checkbox) {
            const checkboxes = document.querySelectorAll('.payroll-checkbox');
            checkboxes.forEach(cb => cb.checked = checkbox.checked);
            updateBulkButtons();
        }

        function updateBulkButtons() {
            const checkedBoxes = document.querySelectorAll('.payroll-checkbox:checked');
            const bulkApproveBtn = document.getElementById('bulkApproveBtn');
            const bulkPayBtn = document.getElementById('bulkPayBtn');

            if (checkedBoxes.length > 0) {
                bulkApproveBtn.disabled = false;
                bulkPayBtn.disabled = false;
            } else {
                bulkApproveBtn.disabled = true;
                bulkPayBtn.disabled = true;
            }
        }

        function processPayroll(id) {
            if (confirm('هل أنت متأكد من معالجة كشف المرتبات هذا؟')) {
                $.ajax({
                    url: '@Url.Action("Process")',
                    type: 'POST',
                    data: {
                        id: id,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            showToast('تم معالجة كشف المرتبات بنجاح', 'success');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showToast('حدث خطأ: ' + result.message, 'error');
                        }
                    },
                    error: function() {
                        showToast('حدث خطأ أثناء معالجة كشف المرتبات', 'error');
                    }
                });
            }
        }

        function approvePayroll(id) {
            if (confirm('هل أنت متأكد من الموافقة على كشف المرتبات هذا؟')) {
                $.ajax({
                    url: '@Url.Action("Approve")',
                    type: 'POST',
                    data: {
                        id: id,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            showToast('تم الموافقة على كشف المرتبات بنجاح', 'success');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showToast('حدث خطأ: ' + result.message, 'error');
                        }
                    },
                    error: function() {
                        showToast('حدث خطأ أثناء الموافقة على كشف المرتبات', 'error');
                    }
                });
            }
        }

        function payPayroll(id) {
            if (confirm('هل أنت متأكد من تسجيل كشف المرتبات كمدفوع؟')) {
                $.ajax({
                    url: '@Url.Action("Pay")',
                    type: 'POST',
                    data: {
                        id: id,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            showToast('تم تسجيل كشف المرتبات كمدفوع بنجاح', 'success');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showToast('حدث خطأ: ' + result.message, 'error');
                        }
                    },
                    error: function() {
                        showToast('حدث خطأ أثناء تسجيل كشف المرتبات كمدفوع', 'error');
                    }
                });
            }
        }

        function deletePayroll(id) {
            if (confirm('هل أنت متأكد من حذف كشف المرتبات هذا؟ لا يمكن التراجع عن هذا الإجراء.')) {
                $.ajax({
                    url: '@Url.Action("Delete")',
                    type: 'POST',
                    data: {
                        id: id,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            showToast('تم حذف كشف المرتبات بنجاح', 'success');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showToast('حدث خطأ: ' + result.message, 'error');
                        }
                    },
                    error: function() {
                        showToast('حدث خطأ أثناء حذف كشف المرتبات', 'error');
                    }
                });
            }
        }

        function bulkApprove() {
            const checkedBoxes = document.querySelectorAll('.payroll-checkbox:checked');
            if (checkedBoxes.length === 0) return;

            if (confirm(`هل أنت متأكد من الموافقة على ${checkedBoxes.length} كشف مرتبات؟`)) {
                const ids = Array.from(checkedBoxes).map(cb => cb.value);

                $.ajax({
                    url: '@Url.Action("BulkApprove")',
                    type: 'POST',
                    data: {
                        ids: ids,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            showToast(`تم الموافقة على ${result.count} كشف مرتبات بنجاح`, 'success');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showToast('حدث خطأ: ' + result.message, 'error');
                        }
                    },
                    error: function() {
                        showToast('حدث خطأ أثناء الموافقة المجمعة', 'error');
                    }
                });
            }
        }

        function bulkPay() {
            const checkedBoxes = document.querySelectorAll('.payroll-checkbox:checked');
            if (checkedBoxes.length === 0) return;

            if (confirm(`هل أنت متأكد من تسجيل ${checkedBoxes.length} كشف مرتبات كمدفوع؟`)) {
                const ids = Array.from(checkedBoxes).map(cb => cb.value);

                $.ajax({
                    url: '@Url.Action("BulkPay")',
                    type: 'POST',
                    data: {
                        ids: ids,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            showToast(`تم تسجيل ${result.count} كشف مرتبات كمدفوع بنجاح`, 'success');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showToast('حدث خطأ: ' + result.message, 'error');
                        }
                    },
                    error: function() {
                        showToast('حدث خطأ أثناء الدفع المجمع', 'error');
                    }
                });
            }
        }

        function exportToExcel() {
            showToast('جاري تصدير البيانات إلى Excel...', 'info');
            window.open('@Url.Action("ExportExcel", "Payroll")', '_blank');
        }

        function exportToPDF() {
            showToast('جاري تصدير البيانات إلى PDF...', 'info');
            window.open('@Url.Action("ExportPDF", "Payroll")', '_blank');
        }

        function exportToCSV() {
            showToast('جاري تصدير البيانات إلى CSV...', 'info');
            window.open('@Url.Action("ExportCSV", "Payroll")', '_blank');
        }

        function sendPayslips() {
            if (confirm('هل تريد إرسال كشوف الراتب عبر البريد الإلكتروني لجميع الموظفين؟')) {
                showToast('جاري إرسال كشوف الراتب...', 'info');

                $.ajax({
                    url: '@Url.Action("SendPayslips")',
                    type: 'POST',
                    data: {
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result.success) {
                            showToast(`تم إرسال ${result.count} كشف راتب بنجاح`, 'success');
                        } else {
                            showToast('حدث خطأ: ' + result.message, 'error');
                        }
                    },
                    error: function() {
                        showToast('حدث خطأ أثناء إرسال كشوف الراتب', 'error');
                    }
                });
            }
        }

        function openPayrollCalculator() {
            // Focus on the calculator in sidebar
            $('html, body').animate({
                scrollTop: $('.salary-calculator').offset().top - 100
            }, 500);

            $('#basicSalary').focus();
            showToast('استخدم حاسبة الراتب في الشريط الجانبي', 'info');
        }

        function showToast(message, type = 'info') {
            const bgClass = type === 'success' ? 'bg-success' :
                           type === 'warning' ? 'bg-warning' :
                           type === 'error' ? 'bg-danger' : 'bg-info';

            const toast = $(`
                <div class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `);

            let container = $('.toast-container');
            if (container.length === 0) {
                container = $('<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
                $('body').append(container);
            }

            container.append(toast);
            const bsToast = new bootstrap.Toast(toast[0]);
            bsToast.show();

            toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    </script>
}
