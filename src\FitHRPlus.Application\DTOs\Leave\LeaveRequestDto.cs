using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Application.DTOs.Leave
{
    /// <summary>
    /// Leave request DTO
    /// DTO طلب الإجازة
    /// </summary>
    public class LeaveRequestDto
    {
        public Guid Id { get; set; }
        public Guid EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string EmployeeNameAr { get; set; } = string.Empty;
        public string EmployeeCode { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string DepartmentNameAr { get; set; } = string.Empty;
        
        public Guid LeaveTypeId { get; set; }
        public string LeaveTypeName { get; set; } = string.Empty;
        public string LeaveTypeNameAr { get; set; } = string.Empty;
        
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalDays { get; set; }
        
        public string? Reason { get; set; }
        public string? ReasonAr { get; set; }
        
        public string Status { get; set; } = "Pending";
        public string StatusAr { get; set; } = "في الانتظار";
        
        public Guid? ApprovedBy { get; set; }
        public string? ApprovedByName { get; set; }
        public DateTime? ApprovedAt { get; set; }
        
        public string? RejectionReason { get; set; }
        public string? RejectionReasonAr { get; set; }
        
        public string? EmergencyContact { get; set; }
        public string? EmergencyPhone { get; set; }
        
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        
        public bool CanApprove { get; set; }
        public bool CanReject { get; set; }
        public bool CanCancel { get; set; }
        public bool CanEdit { get; set; }
    }

    /// <summary>
    /// Create leave request DTO
    /// DTO إنشاء طلب الإجازة
    /// </summary>
    public class CreateLeaveRequestDto
    {
        [Required(ErrorMessage = "Employee is required")]
        public Guid EmployeeId { get; set; }
        
        [Required(ErrorMessage = "Leave type is required")]
        public Guid LeaveTypeId { get; set; }
        
        [Required(ErrorMessage = "Start date is required")]
        [DataType(DataType.Date)]
        public DateTime StartDate { get; set; }
        
        [Required(ErrorMessage = "End date is required")]
        [DataType(DataType.Date)]
        public DateTime EndDate { get; set; }
        
        [MaxLength(1000, ErrorMessage = "Reason cannot exceed 1000 characters")]
        public string? Reason { get; set; }
        
        [MaxLength(1000, ErrorMessage = "Arabic reason cannot exceed 1000 characters")]
        public string? ReasonAr { get; set; }
        
        [MaxLength(200, ErrorMessage = "Emergency contact cannot exceed 200 characters")]
        public string? EmergencyContact { get; set; }
        
        [MaxLength(20, ErrorMessage = "Emergency phone cannot exceed 20 characters")]
        public string? EmergencyPhone { get; set; }
    }

    /// <summary>
    /// Update leave request DTO
    /// DTO تحديث طلب الإجازة
    /// </summary>
    public class UpdateLeaveRequestDto
    {
        [Required]
        public Guid Id { get; set; }
        
        [Required(ErrorMessage = "Leave type is required")]
        public Guid LeaveTypeId { get; set; }
        
        [Required(ErrorMessage = "Start date is required")]
        [DataType(DataType.Date)]
        public DateTime StartDate { get; set; }
        
        [Required(ErrorMessage = "End date is required")]
        [DataType(DataType.Date)]
        public DateTime EndDate { get; set; }
        
        [MaxLength(1000, ErrorMessage = "Reason cannot exceed 1000 characters")]
        public string? Reason { get; set; }
        
        [MaxLength(1000, ErrorMessage = "Arabic reason cannot exceed 1000 characters")]
        public string? ReasonAr { get; set; }
        
        [MaxLength(200, ErrorMessage = "Emergency contact cannot exceed 200 characters")]
        public string? EmergencyContact { get; set; }
        
        [MaxLength(20, ErrorMessage = "Emergency phone cannot exceed 20 characters")]
        public string? EmergencyPhone { get; set; }
    }

    /// <summary>
    /// Approve leave request DTO
    /// DTO الموافقة على طلب الإجازة
    /// </summary>
    public class ApproveLeaveRequestDto
    {
        [Required]
        public Guid LeaveRequestId { get; set; }
        
        [MaxLength(500, ErrorMessage = "Notes cannot exceed 500 characters")]
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Reject leave request DTO
    /// DTO رفض طلب الإجازة
    /// </summary>
    public class RejectLeaveRequestDto
    {
        [Required]
        public Guid LeaveRequestId { get; set; }
        
        [Required(ErrorMessage = "Rejection reason is required")]
        [MaxLength(500, ErrorMessage = "Rejection reason cannot exceed 500 characters")]
        public string RejectionReason { get; set; } = string.Empty;
        
        [MaxLength(500, ErrorMessage = "Arabic rejection reason cannot exceed 500 characters")]
        public string? RejectionReasonAr { get; set; }
    }

    /// <summary>
    /// Leave request list request DTO
    /// DTO طلب قائمة طلبات الإجازات
    /// </summary>
    public class LeaveRequestListDto
    {
        public Guid? CompanyId { get; set; }
        public Guid? EmployeeId { get; set; }
        public Guid? LeaveTypeId { get; set; }
        public string? Status { get; set; }
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
        public string? SearchTerm { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? SortBy { get; set; } = "CreatedAt";
        public string? SortDirection { get; set; } = "desc";
    }

    /// <summary>
    /// Leave type DTO
    /// DTO نوع الإجازة
    /// </summary>
    public class LeaveTypeDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string NameAr { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? DescriptionAr { get; set; }
        public int? MaxDaysPerYear { get; set; }
        public int? MaxConsecutiveDays { get; set; }
        public int MinDaysNotice { get; set; }
        public bool IsPaid { get; set; }
        public bool RequiresApproval { get; set; }
        public bool RequiresDocument { get; set; }
        public bool CarryForward { get; set; }
        public int CarryForwardLimit { get; set; }
        public string? Gender { get; set; }
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// Leave balance DTO
    /// DTO رصيد الإجازة
    /// </summary>
    public class LeaveBalanceDto
    {
        public Guid Id { get; set; }
        public Guid EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string EmployeeNameAr { get; set; } = string.Empty;
        
        public Guid LeaveTypeId { get; set; }
        public string LeaveTypeName { get; set; } = string.Empty;
        public string LeaveTypeNameAr { get; set; } = string.Empty;
        
        public int Year { get; set; }
        public decimal EntitledDays { get; set; }
        public decimal UsedDays { get; set; }
        public decimal RemainingDays { get; set; }
        public decimal CarriedForwardDays { get; set; }
        
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// Leave statistics DTO
    /// DTO إحصائيات الإجازات
    /// </summary>
    public class LeaveStatisticsDto
    {
        public int TotalRequests { get; set; }
        public int PendingRequests { get; set; }
        public int ApprovedRequests { get; set; }
        public int RejectedRequests { get; set; }
        public int CancelledRequests { get; set; }
        
        public decimal TotalDaysRequested { get; set; }
        public decimal TotalDaysApproved { get; set; }
        public decimal TotalDaysUsed { get; set; }
        
        public Dictionary<string, int> RequestsByType { get; set; } = new();
        public Dictionary<string, int> RequestsByMonth { get; set; } = new();
        public Dictionary<string, decimal> DaysByType { get; set; } = new();
    }
}
