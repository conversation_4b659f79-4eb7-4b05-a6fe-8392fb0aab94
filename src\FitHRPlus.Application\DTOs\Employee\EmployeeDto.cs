using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Application.DTOs.Employee
{
    /// <summary>
    /// Employee DTO for data transfer
    /// DTO الموظف لنقل البيانات
    /// </summary>
    public class EmployeeDto
    {
        public Guid Id { get; set; }
        public Guid CompanyId { get; set; }
        public Guid DepartmentId { get; set; }
        public Guid? PositionId { get; set; }
        public Guid? UserId { get; set; }

        public string EmployeeCode { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string FirstNameAr { get; set; } = string.Empty;
        public string? MiddleName { get; set; }
        public string? MiddleNameAr { get; set; }
        public string LastName { get; set; } = string.Empty;
        public string LastNameAr { get; set; } = string.Empty;

        public string FullName => $"{FirstName} {LastName}".Trim();
        public string FullNameAr => $"{FirstNameAr} {LastNameAr}".Trim();

        public string? NationalId { get; set; }
        public string? PassportNumber { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? PlaceOfBirth { get; set; }
        public string? PlaceOfBirthAr { get; set; }
        public string? Gender { get; set; }
        public string? MaritalStatus { get; set; }
        public string? Nationality { get; set; }
        public string? Religion { get; set; }
        public string? BloodType { get; set; }

        public string? Email { get; set; }
        public string? PersonalEmail { get; set; }
        public string? Phone { get; set; }
        public string? PersonalPhone { get; set; }
        public string? EmergencyContact { get; set; }
        public string? EmergencyPhone { get; set; }

        public string? Address { get; set; }
        public string? AddressAr { get; set; }
        public string? City { get; set; }
        public string? CityAr { get; set; }
        public string? PostalCode { get; set; }

        public DateTime HireDate { get; set; }
        public DateTime? ProbationEndDate { get; set; }
        public DateTime? ConfirmationDate { get; set; }
        public DateTime? TerminationDate { get; set; }
        public string? TerminationReason { get; set; }

        public string EmploymentType { get; set; } = string.Empty;
        public string? WorkLocation { get; set; }
        public Guid? ReportsTo { get; set; }

        public decimal? BaseSalary { get; set; }
        public string Currency { get; set; } = "EGP";
        public string PayrollFrequency { get; set; } = "Monthly";

        public string? BankName { get; set; }
        public string? BankAccountNumber { get; set; }
        public string? BankIBAN { get; set; }

        public string? ProfilePicture { get; set; }

        // Related entities
        public string DepartmentName { get; set; } = string.Empty;
        public string DepartmentNameAr { get; set; } = string.Empty;
        public string? PositionTitle { get; set; }
        public string? PositionTitleAr { get; set; }
        public string? ManagerName { get; set; }
        public string? ManagerNameAr { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public bool IsActive { get; set; }

        // Calculated properties
        public int Age => DateOfBirth.HasValue ? DateTime.Today.Year - DateOfBirth.Value.Year : 0;
        public int YearsOfService => DateTime.Today.Year - HireDate.Year;
        public bool IsOnProbation => ProbationEndDate.HasValue && DateTime.Today <= ProbationEndDate.Value;
        public bool IsTerminated => TerminationDate.HasValue;
    }

    /// <summary>
    /// Create employee DTO
    /// DTO إنشاء موظف
    /// </summary>
    public class CreateEmployeeDto
    {
        [Required(ErrorMessage = "Company is required")]
        public Guid CompanyId { get; set; }

        [Required(ErrorMessage = "Department is required")]
        public Guid DepartmentId { get; set; }

        public Guid? PositionId { get; set; }

        [Required(ErrorMessage = "Employee code is required")]
        [MaxLength(50, ErrorMessage = "Employee code cannot exceed 50 characters")]
        public string EmployeeCode { get; set; } = string.Empty;

        [Required(ErrorMessage = "First name is required")]
        [MaxLength(100, ErrorMessage = "First name cannot exceed 100 characters")]
        public string FirstName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Arabic first name is required")]
        [MaxLength(100, ErrorMessage = "Arabic first name cannot exceed 100 characters")]
        public string FirstNameAr { get; set; } = string.Empty;

        [MaxLength(100, ErrorMessage = "Middle name cannot exceed 100 characters")]
        public string? MiddleName { get; set; }

        [MaxLength(100, ErrorMessage = "Arabic middle name cannot exceed 100 characters")]
        public string? MiddleNameAr { get; set; }

        [Required(ErrorMessage = "Last name is required")]
        [MaxLength(100, ErrorMessage = "Last name cannot exceed 100 characters")]
        public string LastName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Arabic last name is required")]
        [MaxLength(100, ErrorMessage = "Arabic last name cannot exceed 100 characters")]
        public string LastNameAr { get; set; } = string.Empty;

        [MaxLength(20, ErrorMessage = "National ID cannot exceed 20 characters")]
        public string? NationalId { get; set; }

        [MaxLength(20, ErrorMessage = "Passport number cannot exceed 20 characters")]
        public string? PassportNumber { get; set; }

        public DateTime? DateOfBirth { get; set; }

        [MaxLength(200, ErrorMessage = "Place of birth cannot exceed 200 characters")]
        public string? PlaceOfBirth { get; set; }

        [MaxLength(200, ErrorMessage = "Arabic place of birth cannot exceed 200 characters")]
        public string? PlaceOfBirthAr { get; set; }

        public string? Gender { get; set; }
        public string? MaritalStatus { get; set; }

        [MaxLength(100, ErrorMessage = "Nationality cannot exceed 100 characters")]
        public string? Nationality { get; set; }

        [MaxLength(50, ErrorMessage = "Religion cannot exceed 50 characters")]
        public string? Religion { get; set; }

        [MaxLength(5, ErrorMessage = "Blood type cannot exceed 5 characters")]
        public string? BloodType { get; set; }

        [EmailAddress(ErrorMessage = "Invalid email format")]
        [MaxLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
        public string? Email { get; set; }

        [EmailAddress(ErrorMessage = "Invalid personal email format")]
        [MaxLength(100, ErrorMessage = "Personal email cannot exceed 100 characters")]
        public string? PersonalEmail { get; set; }

        [MaxLength(20, ErrorMessage = "Phone cannot exceed 20 characters")]
        public string? Phone { get; set; }

        [MaxLength(20, ErrorMessage = "Personal phone cannot exceed 20 characters")]
        public string? PersonalPhone { get; set; }

        [MaxLength(200, ErrorMessage = "Emergency contact cannot exceed 200 characters")]
        public string? EmergencyContact { get; set; }

        [MaxLength(20, ErrorMessage = "Emergency phone cannot exceed 20 characters")]
        public string? EmergencyPhone { get; set; }

        [MaxLength(500, ErrorMessage = "Address cannot exceed 500 characters")]
        public string? Address { get; set; }

        [MaxLength(500, ErrorMessage = "Arabic address cannot exceed 500 characters")]
        public string? AddressAr { get; set; }

        [MaxLength(100, ErrorMessage = "City cannot exceed 100 characters")]
        public string? City { get; set; }

        [MaxLength(100, ErrorMessage = "Arabic city cannot exceed 100 characters")]
        public string? CityAr { get; set; }

        [MaxLength(20, ErrorMessage = "Postal code cannot exceed 20 characters")]
        public string? PostalCode { get; set; }

        [Required(ErrorMessage = "Hire date is required")]
        public DateTime HireDate { get; set; }

        public DateTime? ProbationEndDate { get; set; }
        public DateTime? ConfirmationDate { get; set; }

        [Required(ErrorMessage = "Employment type is required")]
        public string EmploymentType { get; set; } = string.Empty;

        [MaxLength(200, ErrorMessage = "Work location cannot exceed 200 characters")]
        public string? WorkLocation { get; set; }

        public Guid? ReportsTo { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "Base salary must be positive")]
        public decimal? BaseSalary { get; set; }

        [MaxLength(3, ErrorMessage = "Currency cannot exceed 3 characters")]
        public string Currency { get; set; } = "EGP";

        [MaxLength(20, ErrorMessage = "Payroll frequency cannot exceed 20 characters")]
        public string PayrollFrequency { get; set; } = "Monthly";

        [MaxLength(200, ErrorMessage = "Bank name cannot exceed 200 characters")]
        public string? BankName { get; set; }

        [MaxLength(50, ErrorMessage = "Bank account number cannot exceed 50 characters")]
        public string? BankAccountNumber { get; set; }

        [MaxLength(50, ErrorMessage = "Bank IBAN cannot exceed 50 characters")]
        public string? BankIBAN { get; set; }
    }

    /// <summary>
    /// Update employee DTO
    /// DTO تحديث موظف
    /// </summary>
    public class UpdateEmployeeDto : CreateEmployeeDto
    {
        [Required]
        public Guid Id { get; set; }

        public DateTime? TerminationDate { get; set; }

        [MaxLength(500, ErrorMessage = "Termination reason cannot exceed 500 characters")]
        public string? TerminationReason { get; set; }
    }

    /// <summary>
    /// Employee list request DTO
    /// DTO طلب قائمة الموظفين
    /// </summary>
    public class EmployeeListRequestDto
    {
        public Guid? CompanyId { get; set; }
        public Guid? DepartmentId { get; set; }
        public Guid? PositionId { get; set; }
        public string? EmploymentType { get; set; }
        public bool? IsActive { get; set; }
        public string? SearchTerm { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? SortBy { get; set; } = "FirstName";
        public string? SortDirection { get; set; } = "asc";
    }
}
