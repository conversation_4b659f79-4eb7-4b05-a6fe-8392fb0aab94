@model FitHRPlus.Web.Models.Department.DepartmentListViewModel
@{
    ViewData["Title"] = "Departments / الأقسام";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-building text-primary me-2"></i>
                        Departments / الأقسام
                    </h2>
                    <p class="text-muted mb-0">Manage company departments / إدارة أقسام الشركة</p>
                </div>
                <div>
                    <a href="@Url.Action("Create")" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        New Department / قسم جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.TotalCount</h4>
                            <p class="mb-0">Total Departments</p>
                            <small>إجمالي الأقسام</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-building fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.Departments.Count(d => d.IsActive)</h4>
                            <p class="mb-0">Active Departments</p>
                            <small>الأقسام النشطة</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.Departments.Sum(d => d.EmployeeCount)</h4>
                            <p class="mb-0">Total Employees</p>
                            <small>إجمالي الموظفين</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.Departments.Where(d => d.Budget.HasValue).Sum(d => d.Budget.Value).ToString("C")</h4>
                            <p class="mb-0">Total Budget</p>
                            <small>إجمالي الميزانية</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-6">
                            <label for="searchTerm" class="form-label">Search / البحث</label>
                            <input type="text" class="form-control" id="searchTerm" name="searchTerm" 
                                   value="@Model.SearchTerm" placeholder="Search departments...">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>
                                    Search / بحث
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <a href="@Url.Action("Index")" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>
                                    Clear / مسح
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Departments Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        Departments List / قائمة الأقسام
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Departments.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Name / الاسم</th>
                                        <th>Description / الوصف</th>
                                        <th>Budget / الميزانية</th>
                                        <th>Employees / الموظفين</th>
                                        <th>Status / الحالة</th>
                                        <th>Created / تاريخ الإنشاء</th>
                                        <th>Actions / الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var department in Model.Departments)
                                    {
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong>@department.Name</strong>
                                                    <br>
                                                    <small class="text-muted">@department.NameAr</small>
                                                </div>
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(department.Description))
                                                {
                                                    <div>
                                                        @department.Description
                                                        @if (!string.IsNullOrEmpty(department.DescriptionAr))
                                                        {
                                                            <br>
                                                            <small class="text-muted">@department.DescriptionAr</small>
                                                        }
                                                    </div>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (department.Budget.HasValue)
                                                {
                                                    <span class="badge bg-info">@department.Budget.Value.ToString("C")</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">@department.EmployeeCount</span>
                                            </td>
                                            <td>
                                                @if (department.IsActive)
                                                {
                                                    <span class="badge bg-success">Active / نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">Inactive / غير نشط</span>
                                                }
                                            </td>
                                            <td>
                                                <small>@department.CreatedAt.ToString("dd/MM/yyyy")</small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="@Url.Action("Details", new { id = department.Id })" 
                                                       class="btn btn-sm btn-outline-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="@Url.Action("Edit", new { id = department.Id })" 
                                                       class="btn btn-sm btn-outline-warning" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="confirmDelete('@department.Id', '@department.Name')" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if (Model.TotalPages > 1)
                        {
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    @if (Model.HasPreviousPage)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("Index", new { pageNumber = Model.PageNumber - 1, searchTerm = Model.SearchTerm })">
                                                Previous / السابق
                                            </a>
                                        </li>
                                    }

                                    @for (int i = Math.Max(1, Model.PageNumber - 2); i <= Math.Min(Model.TotalPages, Model.PageNumber + 2); i++)
                                    {
                                        <li class="page-item @(i == Model.PageNumber ? "active" : "")">
                                            <a class="page-link" href="@Url.Action("Index", new { pageNumber = i, searchTerm = Model.SearchTerm })">@i</a>
                                        </li>
                                    }

                                    @if (Model.HasNextPage)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("Index", new { pageNumber = Model.PageNumber + 1, searchTerm = Model.SearchTerm })">
                                                Next / التالي
                                            </a>
                                        </li>
                                    }
                                </ul>
                            </nav>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No departments found / لا توجد أقسام</h5>
                            <p class="text-muted">Start by creating your first department / ابدأ بإنشاء أول قسم</p>
                            <a href="@Url.Action("Create")" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                Create Department / إنشاء قسم
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete / تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this department?</p>
                <p>هل أنت متأكد من حذف هذا القسم؟</p>
                <p><strong id="departmentName"></strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel / إلغاء</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">Delete / حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(id, name) {
            document.getElementById('departmentName').textContent = name;
            document.getElementById('deleteForm').action = '@Url.Action("Delete")/' + id;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
}
