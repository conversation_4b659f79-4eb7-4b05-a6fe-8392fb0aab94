﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FitHRPlus.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddCompanySettingsAndWorkSchedules : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Payrolls_Users_ApprovedBy",
                table: "Payrolls");

            migrationBuilder.AddColumn<string>(
                name: "NotesAr",
                table: "Payrolls",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "PaidAt",
                table: "Payrolls",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "PaidBy",
                table: "Payrolls",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ProcessedAt",
                table: "Payrolls",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "ProcessedBy",
                table: "Payrolls",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "WorkScheduleId",
                table: "Employees",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "CompanySettings",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CompanyId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    WorkingHoursStart = table.Column<TimeSpan>(type: "time", nullable: false),
                    WorkingHoursEnd = table.Column<TimeSpan>(type: "time", nullable: false),
                    WorkingHoursPerDay = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    WorkingDaysPerWeek = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    BreakTimeStart = table.Column<TimeSpan>(type: "time", nullable: true),
                    BreakTimeEnd = table.Column<TimeSpan>(type: "time", nullable: true),
                    BreakTimeMinutes = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    WeekendDays = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, defaultValue: "Friday,Saturday"),
                    LateGracePeriodMinutes = table.Column<int>(type: "int", nullable: false),
                    EarlyLeaveGracePeriodMinutes = table.Column<int>(type: "int", nullable: false),
                    OvertimeThresholdHours = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    OvertimeMultiplier = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    AnnualLeaveEntitlement = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    SickLeaveEntitlement = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    MaternityLeaveEntitlement = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    PaternityLeaveEntitlement = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    PayrollCutoffDay = table.Column<int>(type: "int", nullable: false),
                    PayrollPaymentDay = table.Column<int>(type: "int", nullable: false),
                    Currency = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false, defaultValue: "EGP"),
                    CurrencySymbol = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false, defaultValue: "ج.م"),
                    IncomeTaxRate = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    SocialInsuranceEmployeeRate = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    SocialInsuranceEmployerRate = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    MedicalInsuranceRate = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    EnableEmailNotifications = table.Column<bool>(type: "bit", nullable: false),
                    EnableSmsNotifications = table.Column<bool>(type: "bit", nullable: false),
                    EnablePushNotifications = table.Column<bool>(type: "bit", nullable: false),
                    DefaultLanguage = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false, defaultValue: "ar"),
                    TimeZone = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValue: "Africa/Cairo"),
                    DateFormat = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, defaultValue: "dd/MM/yyyy"),
                    TimeFormat = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, defaultValue: "HH:mm"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    UpdatedBy = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompanySettings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CompanySettings_Companies_CompanyId",
                        column: x => x.CompanyId,
                        principalTable: "Companies",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "WorkSchedules",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CompanyId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    NameAr = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    DescriptionAr = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    IsDefault = table.Column<bool>(type: "bit", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    UpdatedBy = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkSchedules", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WorkSchedules_Companies_CompanyId",
                        column: x => x.CompanyId,
                        principalTable: "Companies",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "WorkScheduleDays",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    WorkScheduleId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DayOfWeek = table.Column<int>(type: "int", nullable: false),
                    IsWorkingDay = table.Column<bool>(type: "bit", nullable: false),
                    StartTime = table.Column<TimeSpan>(type: "time", nullable: true),
                    EndTime = table.Column<TimeSpan>(type: "time", nullable: true),
                    BreakStartTime = table.Column<TimeSpan>(type: "time", nullable: true),
                    BreakEndTime = table.Column<TimeSpan>(type: "time", nullable: true),
                    WorkingHours = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    UpdatedBy = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkScheduleDays", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WorkScheduleDays_WorkSchedules_WorkScheduleId",
                        column: x => x.WorkScheduleId,
                        principalTable: "WorkSchedules",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Payrolls_PaidBy",
                table: "Payrolls",
                column: "PaidBy");

            migrationBuilder.CreateIndex(
                name: "IX_Payrolls_ProcessedBy",
                table: "Payrolls",
                column: "ProcessedBy");

            migrationBuilder.CreateIndex(
                name: "IX_Employees_WorkScheduleId",
                table: "Employees",
                column: "WorkScheduleId");

            migrationBuilder.CreateIndex(
                name: "IX_CompanySettings_CompanyId",
                table: "CompanySettings",
                column: "CompanyId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_WorkScheduleDays_WorkScheduleId_DayOfWeek",
                table: "WorkScheduleDays",
                columns: new[] { "WorkScheduleId", "DayOfWeek" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_WorkSchedules_CompanyId_IsDefault",
                table: "WorkSchedules",
                columns: new[] { "CompanyId", "IsDefault" },
                unique: true,
                filter: "[IsDefault] = 1");

            migrationBuilder.AddForeignKey(
                name: "FK_Employees_WorkSchedules_WorkScheduleId",
                table: "Employees",
                column: "WorkScheduleId",
                principalTable: "WorkSchedules",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_Payrolls_Employees_ApprovedBy",
                table: "Payrolls",
                column: "ApprovedBy",
                principalTable: "Employees",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Payrolls_Employees_PaidBy",
                table: "Payrolls",
                column: "PaidBy",
                principalTable: "Employees",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Payrolls_Employees_ProcessedBy",
                table: "Payrolls",
                column: "ProcessedBy",
                principalTable: "Employees",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Employees_WorkSchedules_WorkScheduleId",
                table: "Employees");

            migrationBuilder.DropForeignKey(
                name: "FK_Payrolls_Employees_ApprovedBy",
                table: "Payrolls");

            migrationBuilder.DropForeignKey(
                name: "FK_Payrolls_Employees_PaidBy",
                table: "Payrolls");

            migrationBuilder.DropForeignKey(
                name: "FK_Payrolls_Employees_ProcessedBy",
                table: "Payrolls");

            migrationBuilder.DropTable(
                name: "CompanySettings");

            migrationBuilder.DropTable(
                name: "WorkScheduleDays");

            migrationBuilder.DropTable(
                name: "WorkSchedules");

            migrationBuilder.DropIndex(
                name: "IX_Payrolls_PaidBy",
                table: "Payrolls");

            migrationBuilder.DropIndex(
                name: "IX_Payrolls_ProcessedBy",
                table: "Payrolls");

            migrationBuilder.DropIndex(
                name: "IX_Employees_WorkScheduleId",
                table: "Employees");

            migrationBuilder.DropColumn(
                name: "NotesAr",
                table: "Payrolls");

            migrationBuilder.DropColumn(
                name: "PaidAt",
                table: "Payrolls");

            migrationBuilder.DropColumn(
                name: "PaidBy",
                table: "Payrolls");

            migrationBuilder.DropColumn(
                name: "ProcessedAt",
                table: "Payrolls");

            migrationBuilder.DropColumn(
                name: "ProcessedBy",
                table: "Payrolls");

            migrationBuilder.DropColumn(
                name: "WorkScheduleId",
                table: "Employees");

            migrationBuilder.AddForeignKey(
                name: "FK_Payrolls_Users_ApprovedBy",
                table: "Payrolls",
                column: "ApprovedBy",
                principalTable: "Users",
                principalColumn: "Id");
        }
    }
}
