using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Web.Models.Admin
{
    /// <summary>
    /// Attendance list view model for attendance management page
    /// نموذج عرض قائمة الحضور لصفحة إدارة الحضور
    /// </summary>
    public class AttendanceListViewModel
    {
        /// <summary>
        /// List of attendance records
        /// قائمة سجلات الحضور
        /// </summary>
        public List<AttendanceViewModel> Attendances { get; set; } = new();

        /// <summary>
        /// Employee ID filter
        /// تصفية معرف الموظف
        /// </summary>
        [Display(Name = "Employee")]
        public Guid? EmployeeId { get; set; }

        /// <summary>
        /// Department ID filter
        /// تصفية معرف القسم
        /// </summary>
        [Display(Name = "Department")]
        public Guid? DepartmentId { get; set; }

        /// <summary>
        /// Date from filter
        /// تصفية التاريخ من
        /// </summary>
        [DataType(DataType.Date)]
        [Display(Name = "Date From")]
        public DateTime? DateFrom { get; set; }

        /// <summary>
        /// Date to filter
        /// تصفية التاريخ إلى
        /// </summary>
        [DataType(DataType.Date)]
        [Display(Name = "Date To")]
        public DateTime? DateTo { get; set; }

        /// <summary>
        /// Attendance status filter
        /// تصفية حالة الحضور
        /// </summary>
        [Display(Name = "Status")]
        public string? AttendanceStatus { get; set; }

        /// <summary>
        /// Show only late arrivals
        /// عرض المتأخرين فقط
        /// </summary>
        [Display(Name = "Late Only")]
        public bool? ShowLateOnly { get; set; }

        /// <summary>
        /// Show only early departures
        /// عرض المغادرين مبكراً فقط
        /// </summary>
        [Display(Name = "Early Departure Only")]
        public bool? ShowEarlyDepartureOnly { get; set; }

        /// <summary>
        /// Show only overtime
        /// عرض العمل الإضافي فقط
        /// </summary>
        [Display(Name = "Overtime Only")]
        public bool? ShowOvertimeOnly { get; set; }

        /// <summary>
        /// Show only unapproved records
        /// عرض السجلات غير المعتمدة فقط
        /// </summary>
        [Display(Name = "Unapproved Only")]
        public bool? ShowUnapprovedOnly { get; set; }

        /// <summary>
        /// Search term
        /// مصطلح البحث
        /// </summary>
        [Display(Name = "Search", Prompt = "Search by employee name, number...")]
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Sort field
        /// حقل الترتيب
        /// </summary>
        public string? SortBy { get; set; } = "Date";

        /// <summary>
        /// Sort direction
        /// اتجاه الترتيب
        /// </summary>
        public string? SortDirection { get; set; } = "desc";

        /// <summary>
        /// Current page number
        /// رقم الصفحة الحالية
        /// </summary>
        public int CurrentPage { get; set; } = 1;

        /// <summary>
        /// Page size
        /// حجم الصفحة
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// Total number of records
        /// العدد الإجمالي للسجلات
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Total number of pages
        /// العدد الإجمالي للصفحات
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// Whether there is a previous page
        /// ما إذا كانت هناك صفحة سابقة
        /// </summary>
        public bool HasPreviousPage => CurrentPage > 1;

        /// <summary>
        /// Whether there is a next page
        /// ما إذا كانت هناك صفحة تالية
        /// </summary>
        public bool HasNextPage => CurrentPage < TotalPages;

        /// <summary>
        /// Statistics summary
        /// ملخص الإحصائيات
        /// </summary>
        public AttendanceStatisticsViewModel Statistics { get; set; } = new();

        // Filter options
        /// <summary>
        /// Available departments for filtering
        /// الأقسام المتاحة للتصفية
        /// </summary>
        public List<DepartmentViewModel> Departments { get; set; } = new();

        /// <summary>
        /// Available employees for filtering
        /// الموظفين المتاحين للتصفية
        /// </summary>
        public List<EmployeeViewModel> Employees { get; set; } = new();

        /// <summary>
        /// Available attendance statuses
        /// حالات الحضور المتاحة
        /// </summary>
        public List<string> AttendanceStatuses { get; set; } = new()
        {
            "Present",
            "Absent",
            "Late",
            "OnLeave",
            "Sick"
        };

        /// <summary>
        /// Available page sizes
        /// أحجام الصفحات المتاحة
        /// </summary>
        public List<int> PageSizes { get; set; } = new() { 10, 25, 50, 100 };

        /// <summary>
        /// Available sort options
        /// خيارات الترتيب المتاحة
        /// </summary>
        public Dictionary<string, string> SortOptions { get; set; } = new()
        {
            { "Date", "Date" },
            { "Employee", "Employee" },
            { "CheckIn", "Check-in Time" },
            { "CheckOut", "Check-out Time" },
            { "WorkingHours", "Working Hours" },
            { "Status", "Status" }
        };
    }

    /// <summary>
    /// Attendance statistics view model
    /// نموذج عرض إحصائيات الحضور
    /// </summary>
    public class AttendanceStatisticsViewModel
    {
        /// <summary>
        /// Total attendance records
        /// إجمالي سجلات الحضور
        /// </summary>
        public int TotalRecords { get; set; }

        /// <summary>
        /// Total present days
        /// إجمالي أيام الحضور
        /// </summary>
        public int TotalPresentDays { get; set; }

        /// <summary>
        /// Total absent days
        /// إجمالي أيام الغياب
        /// </summary>
        public int TotalAbsentDays { get; set; }

        /// <summary>
        /// Total late arrivals
        /// إجمالي حالات التأخير
        /// </summary>
        public int TotalLateArrivals { get; set; }

        /// <summary>
        /// Total early departures
        /// إجمالي حالات المغادرة المبكرة
        /// </summary>
        public int TotalEarlyDepartures { get; set; }

        /// <summary>
        /// Total overtime records
        /// إجمالي سجلات العمل الإضافي
        /// </summary>
        public int TotalOvertimeRecords { get; set; }

        /// <summary>
        /// Average attendance percentage
        /// متوسط نسبة الحضور
        /// </summary>
        public double AverageAttendancePercentage { get; set; }

        /// <summary>
        /// Total working hours
        /// إجمالي ساعات العمل
        /// </summary>
        public TimeSpan TotalWorkingHours { get; set; }

        /// <summary>
        /// Total overtime hours
        /// إجمالي ساعات العمل الإضافي
        /// </summary>
        public TimeSpan TotalOvertimeHours { get; set; }

        /// <summary>
        /// Average working hours per day
        /// متوسط ساعات العمل يومياً
        /// </summary>
        public TimeSpan AverageWorkingHours { get; set; }

        /// <summary>
        /// Formatted total working hours
        /// إجمالي ساعات العمل منسق
        /// </summary>
        public string TotalWorkingHoursFormatted => TotalWorkingHours.ToString(@"hh\:mm");

        /// <summary>
        /// Formatted total overtime hours
        /// إجمالي ساعات العمل الإضافي منسق
        /// </summary>
        public string TotalOvertimeHoursFormatted => TotalOvertimeHours.ToString(@"hh\:mm");

        /// <summary>
        /// Formatted average working hours
        /// متوسط ساعات العمل منسق
        /// </summary>
        public string AverageWorkingHoursFormatted => AverageWorkingHours.ToString(@"hh\:mm");
    }

    /// <summary>
    /// Daily attendance report view model
    /// نموذج عرض تقرير الحضور اليومي
    /// </summary>
    public class DailyAttendanceReportViewModel
    {
        /// <summary>
        /// Report date
        /// تاريخ التقرير
        /// </summary>
        [DataType(DataType.Date)]
        [Display(Name = "Report Date")]
        public DateTime Date { get; set; } = DateTime.Today;

        /// <summary>
        /// Total employees
        /// إجمالي الموظفين
        /// </summary>
        [Display(Name = "Total Employees")]
        public int TotalEmployees { get; set; }

        /// <summary>
        /// Present employees
        /// الموظفين الحاضرين
        /// </summary>
        [Display(Name = "Present")]
        public int PresentEmployees { get; set; }

        /// <summary>
        /// Absent employees
        /// الموظفين الغائبين
        /// </summary>
        [Display(Name = "Absent")]
        public int AbsentEmployees { get; set; }

        /// <summary>
        /// Late employees
        /// الموظفين المتأخرين
        /// </summary>
        [Display(Name = "Late")]
        public int LateEmployees { get; set; }

        /// <summary>
        /// Early departure employees
        /// الموظفين المغادرين مبكراً
        /// </summary>
        [Display(Name = "Early Departure")]
        public int EarlyDepartureEmployees { get; set; }

        /// <summary>
        /// Overtime employees
        /// الموظفين في العمل الإضافي
        /// </summary>
        [Display(Name = "Overtime")]
        public int OvertimeEmployees { get; set; }

        /// <summary>
        /// Attendance percentage
        /// نسبة الحضور
        /// </summary>
        [Display(Name = "Attendance Rate")]
        public double AttendancePercentage => TotalEmployees > 0 ? 
            (double)PresentEmployees / TotalEmployees * 100 : 0;

        /// <summary>
        /// Department breakdown
        /// تفصيل الأقسام
        /// </summary>
        public List<DepartmentAttendanceStatsViewModel> DepartmentBreakdown { get; set; } = new();

        /// <summary>
        /// Detailed attendance records
        /// سجلات الحضور التفصيلية
        /// </summary>
        public List<AttendanceViewModel> AttendanceRecords { get; set; } = new();
    }

    /// <summary>
    /// Department attendance statistics view model
    /// نموذج عرض إحصائيات حضور القسم
    /// </summary>
    public class DepartmentAttendanceStatsViewModel
    {
        /// <summary>
        /// Department ID
        /// معرف القسم
        /// </summary>
        public Guid DepartmentId { get; set; }

        /// <summary>
        /// Department name
        /// اسم القسم
        /// </summary>
        public string DepartmentName { get; set; } = string.Empty;

        /// <summary>
        /// Total employees in department
        /// إجمالي الموظفين في القسم
        /// </summary>
        public int TotalEmployees { get; set; }

        /// <summary>
        /// Present employees
        /// الموظفين الحاضرين
        /// </summary>
        public int PresentEmployees { get; set; }

        /// <summary>
        /// Absent employees
        /// الموظفين الغائبين
        /// </summary>
        public int AbsentEmployees { get; set; }

        /// <summary>
        /// Late employees
        /// الموظفين المتأخرين
        /// </summary>
        public int LateEmployees { get; set; }

        /// <summary>
        /// Attendance percentage for department
        /// نسبة الحضور للقسم
        /// </summary>
        public double AttendancePercentage => TotalEmployees > 0 ? 
            (double)PresentEmployees / TotalEmployees * 100 : 0;

        /// <summary>
        /// CSS class for attendance percentage badge
        /// فئة CSS لشارة نسبة الحضور
        /// </summary>
        public string AttendancePercentageBadgeClass => AttendancePercentage switch
        {
            >= 90 => "badge bg-success",
            >= 80 => "badge bg-warning",
            >= 70 => "badge bg-danger",
            _ => "badge bg-dark"
        };
    }
}
