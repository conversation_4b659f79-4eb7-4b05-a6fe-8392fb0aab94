@model FitHRPlus.Web.Models.Position.PositionViewModel
@{
    ViewData["Title"] = "Create Position / إنشاء منصب";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-plus text-primary me-2"></i>
                        Create Position / إنشاء منصب
                    </h2>
                    <p class="text-muted mb-0">Add a new position to your company / أضف منصباً جديداً لشركتك</p>
                </div>
                <div>
                    <a href="@Url.Action("Index")" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to List / العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Form -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-briefcase me-2"></i>
                        Position Information / معلومات المنصب
                    </h5>
                </div>
                <div class="card-body">
                    <form asp-action="Create" method="post">
                        @Html.AntiForgeryToken()
                        <input asp-for="CompanyId" type="hidden" />

                        <div class="row">
                            <!-- Department -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="DepartmentId" class="form-label">
                                    <i class="fas fa-building me-1"></i>
                                    Department / القسم
                                    <span class="text-danger">*</span>
                                </label>
                                <select asp-for="DepartmentId" class="form-select">
                                    <option value="">Select Department / اختر القسم</option>
                                    @foreach (var dept in Model.Departments)
                                    {
                                        <option value="@dept.Id">@dept.DisplayName</option>
                                    }
                                </select>
                                <span asp-validation-for="DepartmentId" class="text-danger"></span>
                            </div>

                            <!-- Status -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="IsActive" class="form-label">
                                    <i class="fas fa-toggle-on me-1"></i>
                                    Status / الحالة
                                </label>
                                <div class="form-check form-switch">
                                    <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                    <label asp-for="IsActive" class="form-check-label">
                                        Active Position / منصب نشط
                                    </label>
                                </div>
                                <div class="form-text">Active positions can have employees assigned</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Position Title -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="Title" class="form-label">
                                    <i class="fas fa-tag me-1"></i>
                                    Position Title / عنوان المنصب
                                    <span class="text-danger">*</span>
                                </label>
                                <input asp-for="Title" class="form-control" placeholder="Enter position title" />
                                <span asp-validation-for="Title" class="text-danger"></span>
                            </div>

                            <!-- Position Title (Arabic) -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="TitleAr" class="form-label">
                                    <i class="fas fa-tag me-1"></i>
                                    Position Title (Arabic) / عنوان المنصب بالعربية
                                    <span class="text-danger">*</span>
                                </label>
                                <input asp-for="TitleAr" class="form-control" placeholder="أدخل عنوان المنصب بالعربية" dir="rtl" />
                                <span asp-validation-for="TitleAr" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Description -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="Description" class="form-label">
                                    <i class="fas fa-align-left me-1"></i>
                                    Description / الوصف
                                </label>
                                <textarea asp-for="Description" class="form-control" rows="4" 
                                          placeholder="Enter position description and responsibilities"></textarea>
                                <span asp-validation-for="Description" class="text-danger"></span>
                            </div>

                            <!-- Description (Arabic) -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="DescriptionAr" class="form-label">
                                    <i class="fas fa-align-right me-1"></i>
                                    Description (Arabic) / الوصف بالعربية
                                </label>
                                <textarea asp-for="DescriptionAr" class="form-control" rows="4" 
                                          placeholder="أدخل وصف المنصب والمسؤوليات بالعربية" dir="rtl"></textarea>
                                <span asp-validation-for="DescriptionAr" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Minimum Salary -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="MinSalary" class="form-label">
                                    <i class="fas fa-dollar-sign me-1"></i>
                                    Minimum Salary / الحد الأدنى للراتب
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input asp-for="MinSalary" class="form-control" type="number" step="0.01" 
                                           placeholder="0.00" />
                                </div>
                                <span asp-validation-for="MinSalary" class="text-danger"></span>
                                <div class="form-text">Optional minimum salary for this position</div>
                            </div>

                            <!-- Maximum Salary -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="MaxSalary" class="form-label">
                                    <i class="fas fa-dollar-sign me-1"></i>
                                    Maximum Salary / الحد الأقصى للراتب
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input asp-for="MaxSalary" class="form-control" type="number" step="0.01" 
                                           placeholder="0.00" />
                                </div>
                                <span asp-validation-for="MaxSalary" class="text-danger"></span>
                                <div class="form-text">Optional maximum salary for this position</div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <a href="@Url.Action("Index")" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        Cancel / إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        Create Position / إنشاء المنصب
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Salary validation
            function validateSalaries() {
                var minSalary = parseFloat($('#MinSalary').val()) || 0;
                var maxSalary = parseFloat($('#MaxSalary').val()) || 0;
                
                if (minSalary > 0 && maxSalary > 0 && minSalary > maxSalary) {
                    $('#MaxSalary').addClass('is-invalid');
                    if (!$('#MaxSalary').next('.invalid-feedback').length) {
                        $('#MaxSalary').after('<div class="invalid-feedback">Maximum salary must be greater than minimum salary</div>');
                    }
                    return false;
                } else {
                    $('#MaxSalary').removeClass('is-invalid');
                    $('#MaxSalary').next('.invalid-feedback').remove();
                    return true;
                }
            }

            $('#MinSalary, #MaxSalary').on('input blur', function() {
                validateSalaries();
            });

            // Format salary inputs
            $('#MinSalary, #MaxSalary').on('input', function() {
                var value = $(this).val();
                if (value && !isNaN(value)) {
                    $(this).val(parseFloat(value).toFixed(2));
                }
            });

            // Form validation enhancement
            $('form').on('submit', function(e) {
                var isValid = true;
                
                // Check required fields
                if (!$('#DepartmentId').val()) {
                    isValid = false;
                    $('#DepartmentId').addClass('is-invalid');
                } else {
                    $('#DepartmentId').removeClass('is-invalid');
                }
                
                if (!$('#Title').val().trim()) {
                    isValid = false;
                    $('#Title').addClass('is-invalid');
                } else {
                    $('#Title').removeClass('is-invalid');
                }
                
                if (!$('#TitleAr').val().trim()) {
                    isValid = false;
                    $('#TitleAr').addClass('is-invalid');
                } else {
                    $('#TitleAr').removeClass('is-invalid');
                }
                
                // Validate salaries
                if (!validateSalaries()) {
                    isValid = false;
                }
                
                if (!isValid) {
                    e.preventDefault();
                    toastr.error('Please fill in all required fields and fix validation errors / يرجى ملء جميع الحقول المطلوبة وإصلاح أخطاء التحقق');
                }
            });
        });
    </script>
}
