/* HRMS Theme - Based on TestControlPanel.html */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

* {
    font-family: 'Tajawal', sans-serif;
}

body {
    background-color: #f0f2f5;
    margin: 0;
    padding: 0;
}

/* Sidebar Styles */
.sidebar {
    background: linear-gradient(180deg, #1e3a8a, #2563eb);
    color: white;
    height: 100vh;
    width: 280px;
    position: fixed;
    right: 0;
    top: 0;
    overflow-y: auto;
    box-shadow: -3px 0 15px rgba(0,0,0,0.15);
    z-index: 1050;
    padding-top: 20px;
}

.sidebar-header {
    padding: 0 20px 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-header h3 {
    margin: 0;
    font-weight: 700;
    font-size: 1.5rem;
}

.sidebar-menu {
    padding: 0;
    list-style: none;
    margin-top: 20px;
}

.sidebar-menu > li {
    margin-bottom: 5px;
}

.sidebar-menu a {
    color: rgba(255,255,255,0.85);
    text-decoration: none;
    display: flex;
    align-items: center;
    padding: 12px 20px;
    border-radius: 0 30px 30px 0;
    transition: all 0.3s;
}

.sidebar-menu a:hover, .sidebar-menu a.active {
    background: rgba(255,255,255,0.15);
    color: white;
}

.sidebar-menu a i {
    margin-left: 12px;
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
}

.submenu {
    background: rgba(0,0,0,0.1);
    border-radius: 0 20px 20px 0;
    margin-right: 20px;
    overflow: hidden;
    list-style: none;
    padding: 0;
    display: none;
}

.submenu.show {
    display: block;
}

.submenu a {
    padding: 10px 30px;
    font-size: 0.9rem;
}

/* Main Content */
.main-content {
    margin-right: 280px;
    padding: 20px;
}

.topbar {
    background: white;
    border-radius: 10px;
    padding: 15px 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.topbar-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.topbar-actions .dropdown-item.active {
    background-color: #0d6efd;
    color: white;
}

.dashboard-title {
    font-weight: 700;
    color: #1e3a8a;
    margin: 0;
}

/* Stats Cards */
.stats-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    padding: 25px 20px;
    margin-bottom: 25px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05);
    position: relative;
    overflow: hidden;
    min-height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.stats-card i {
    font-size: 2.5rem;
    margin-bottom: 15px;
    display: block;
}

.stats-card h4 {
    font-weight: 700;
    margin: 0;
    font-size: 2rem;
    line-height: 1.2;
}

.stats-card p {
    color: #64748b;
    margin: 8px 0 0;
    font-size: 0.95rem;
    font-weight: 500;
}

.card-blue { border-right: 5px solid #1e3a8a; }
.card-green { border-right: 5px solid #22c55e; }
.card-orange { border-right: 5px solid #f59e0b; }
.card-red { border-right: 5px solid #ef4444; }

/* Chart Container */
.chart-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    padding: 25px;
    margin-bottom: 25px;
    border: 1px solid rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.chart-container:hover {
    box-shadow: 0 6px 20px rgba(0,0,0,0.08);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f1f5f9;
}

.chart-header h5 {
    font-weight: 700;
    color: #1e3a8a;
    margin: 0;
    font-size: 1.25rem;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 20px;
    margin-top: 0;
}

.action-card {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    border-radius: 12px;
    padding: 25px 20px;
    text-align: center;
    box-shadow: 0 4px 10px rgba(0,0,0,0.05);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05);
    position: relative;
    overflow: hidden;
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.action-card:hover::before {
    transform: scaleX(1);
}

.action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.action-card i {
    font-size: 2.2rem;
    margin-bottom: 15px;
    display: block;
}

.action-card p {
    margin: 0;
    font-weight: 600;
    color: #334155;
    font-size: 0.95rem;
}

/* Tables */
.table-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    padding: 20px;
    margin-bottom: 25px;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.table-header h5 {
    font-weight: 700;
    color: #1e3a8a;
    margin: 0;
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, #1e3a8a, #2563eb);
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 500;
}

.btn-success {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 500;
}

.btn-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 500;
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 500;
}

/* Forms */
.form-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    padding: 30px;
    margin-bottom: 25px;
}

.form-section-title {
    font-weight: 700;
    color: #1e3a8a;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    padding: 10px 15px;
}

.form-control:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

/* Dashboard Layout Improvements */
.row {
    margin-bottom: 0;
}

.row > [class*="col-"] {
    padding-left: 12px;
    padding-right: 12px;
    margin-bottom: 20px;
}

/* List group improvements */
.list-group-item {
    border: none;
    border-bottom: 1px solid #f1f5f9;
    padding: 15px 0;
}

.list-group-item:last-child {
    border-bottom: none;
}

.list-group-flush .list-group-item:first-child {
    border-top: none;
}

/* Chart canvas improvements */
canvas {
    max-width: 100%;
    height: auto !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }

    .main-content {
        margin-right: 0;
    }

    .stats-card {
        margin-bottom: 20px;
        min-height: 120px;
        padding: 20px 15px;
    }

    .stats-card h4 {
        font-size: 1.6rem;
    }

    .chart-container {
        padding: 20px 15px;
        margin-bottom: 20px;
    }

    .quick-actions {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 15px;
    }

    .action-card {
        padding: 20px 15px;
    }

    .action-card i {
        font-size: 1.8rem;
    }
}

@media (max-width: 576px) {
    .stats-card {
        min-height: 100px;
        padding: 15px;
    }

    .stats-card h4 {
        font-size: 1.4rem;
    }

    .stats-card i {
        font-size: 2rem;
        margin-bottom: 10px;
    }

    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
}
