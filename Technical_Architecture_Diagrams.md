# المخططات التقنية المفصلة لنظام HRMS
## Detailed Technical Architecture Diagrams

## 1. Clean Architecture Pattern

```mermaid
graph TB
    subgraph "Presentation Layer"
        A[Web Controllers]
        B[API Controllers]
        C[Mobile API]
        D[Admin Dashboard]
    end
    
    subgraph "Application Layer"
        E[Command Handlers]
        F[Query Handlers]
        G[Application Services]
        H[DTOs & ViewModels]
        I[Validators]
    end
    
    subgraph "Domain Layer"
        J[Entities]
        K[Value Objects]
        L[Domain Services]
        M[Repository Interfaces]
        N[Domain Events]
    end
    
    subgraph "Infrastructure Layer"
        O[Entity Framework]
        P[SQL Server]
        Q[Redis Cache]
        R[File Storage]
        S[External APIs]
        T[Email Service]
        U[SMS Service]
    end
    
    A --> E
    B --> F
    C --> G
    D --> H
    E --> J
    F --> K
    G --> L
    H --> M
    I --> N
    M --> O
    O --> P
    L --> Q
    N --> R
    G --> S
    E --> T
    F --> U
```

## 2. CQRS Implementation Pattern

```mermaid
graph LR
    subgraph "Command Side"
        A[Commands]
        B[Command Handlers]
        C[Domain Models]
        D[Write Database]
        E[Event Store]
    end
    
    subgraph "Query Side"
        F[Queries]
        G[Query Handlers]
        H[Read Models]
        I[Read Database]
        J[Projections]
    end
    
    subgraph "Event Bus"
        K[Domain Events]
        L[Event Handlers]
    end
    
    A --> B
    B --> C
    C --> D
    C --> E
    E --> K
    K --> L
    L --> J
    J --> I
    F --> G
    G --> H
    H --> I
```

## 3. Employee Management Module Architecture

```mermaid
graph TB
    subgraph "Employee Management"
        A[Employee Controller]
        B[Employee Service]
        C[Employee Repository]
        D[Employee Entity]
        E[Department Entity]
        F[Position Entity]
    end
    
    subgraph "Commands"
        G[CreateEmployeeCommand]
        H[UpdateEmployeeCommand]
        I[DeactivateEmployeeCommand]
    end
    
    subgraph "Queries"
        J[GetEmployeeQuery]
        K[GetEmployeesListQuery]
        L[SearchEmployeesQuery]
    end
    
    subgraph "Events"
        M[EmployeeCreatedEvent]
        N[EmployeeUpdatedEvent]
        O[EmployeeDeactivatedEvent]
    end
    
    A --> G
    A --> J
    G --> B
    J --> B
    B --> C
    C --> D
    D --> E
    D --> F
    B --> M
    M --> N
    N --> O
```

## 4. Attendance System Architecture

```mermaid
graph TB
    subgraph "Biometric Integration"
        A[Biometric Device SDK]
        B[Device Manager]
        C[Fingerprint Service]
        D[Face Recognition Service]
    end
    
    subgraph "Attendance Processing"
        E[Attendance Controller]
        F[Attendance Service]
        G[Time Calculation Service]
        H[GPS Validation Service]
    end
    
    subgraph "Data Storage"
        I[Attendance Repository]
        J[Raw Attendance Data]
        K[Processed Attendance]
        L[Attendance Rules]
    end
    
    A --> B
    B --> C
    B --> D
    C --> E
    D --> E
    E --> F
    F --> G
    F --> H
    G --> I
    H --> I
    I --> J
    I --> K
    I --> L
```

## 5. Payroll System Architecture

```mermaid
graph TB
    subgraph "Payroll Calculation"
        A[Payroll Controller]
        B[Payroll Service]
        C[Salary Calculator]
        D[Tax Calculator]
        E[Insurance Calculator]
    end
    
    subgraph "Egyptian Compliance"
        F[Tax Rules Engine]
        G[Social Insurance Rules]
        H[Labor Law Compliance]
        I[Government API Integration]
    end
    
    subgraph "Payment Processing"
        J[Payment Gateway]
        K[Bank Integration]
        L[Payment Validation]
        M[Payment History]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    B --> J
    J --> K
    K --> L
    L --> M
```

## 6. Security Architecture

```mermaid
graph TB
    subgraph "Authentication Layer"
        A[Multi-Factor Auth]
        B[Biometric Auth]
        C[JWT Token Service]
        D[OAuth2 Provider]
    end
    
    subgraph "Authorization Layer"
        E[Role-Based Access Control]
        F[Permission Manager]
        G[Resource Authorization]
        H[API Rate Limiting]
    end
    
    subgraph "Security Services"
        I[Encryption Service]
        J[Audit Logger]
        K[Security Scanner]
        L[Threat Detection]
    end
    
    A --> E
    B --> F
    C --> G
    D --> H
    E --> I
    F --> J
    G --> K
    H --> L
```

## 7. Mobile Application Architecture

```mermaid
graph TB
    subgraph "React Native App"
        A[Navigation]
        B[State Management - Redux]
        C[API Client]
        D[Offline Storage - SQLite]
    end
    
    subgraph "Features"
        E[Attendance Tracking]
        F[Leave Requests]
        G[Payslip Viewer]
        H[Document Scanner]
        I[Push Notifications]
    end
    
    subgraph "Device Integration"
        J[Biometric Scanner]
        K[GPS Location]
        L[Camera]
        M[File System]
    end
    
    A --> E
    B --> F
    C --> G
    D --> H
    E --> J
    F --> K
    G --> L
    H --> M
    I --> A
```

## 8. Data Flow Architecture

```mermaid
sequenceDiagram
    participant U as User
    participant W as Web App
    participant A as API Gateway
    participant S as Service Layer
    participant D as Database
    participant C as Cache
    participant E as External APIs
    
    U->>W: Login Request
    W->>A: Authentication
    A->>S: Validate Credentials
    S->>D: Check User Data
    D-->>S: User Information
    S->>C: Cache Session
    S-->>A: JWT Token
    A-->>W: Authentication Response
    W-->>U: Dashboard
    
    U->>W: Check Attendance
    W->>A: Attendance Request
    A->>S: Process Request
    S->>E: Biometric Verification
    E-->>S: Verification Result
    S->>D: Save Attendance
    S->>C: Update Cache
    S-->>A: Success Response
    A-->>W: Attendance Recorded
    W-->>U: Confirmation
```

## 9. Microservices Architecture

```mermaid
graph TB
    subgraph "API Gateway"
        A[NGINX Load Balancer]
        B[Authentication Service]
        C[Rate Limiting]
    end
    
    subgraph "Core Services"
        D[Employee Service]
        E[Attendance Service]
        F[Payroll Service]
        G[Reporting Service]
    end
    
    subgraph "Support Services"
        H[Notification Service]
        I[Document Service]
        J[Integration Service]
        K[Audit Service]
    end
    
    subgraph "Data Layer"
        L[Employee DB]
        M[Attendance DB]
        N[Payroll DB]
        O[Shared Cache]
    end
    
    A --> B
    B --> D
    B --> E
    B --> F
    B --> G
    D --> L
    E --> M
    F --> N
    G --> O
    H --> O
    I --> O
    J --> O
    K --> O
```

## 10. Deployment Architecture

```mermaid
graph TB
    subgraph "Production Environment"
        A[Load Balancer]
        B[Web Server 1]
        C[Web Server 2]
        D[API Server 1]
        E[API Server 2]
    end
    
    subgraph "Database Cluster"
        F[Primary SQL Server]
        G[Secondary SQL Server]
        H[Redis Cluster]
    end
    
    subgraph "External Services"
        I[Azure Blob Storage]
        J[SendGrid Email]
        K[Twilio SMS]
        L[Payment Gateways]
    end
    
    subgraph "Monitoring"
        M[Application Insights]
        N[Log Analytics]
        O[Health Checks]
    end
    
    A --> B
    A --> C
    B --> D
    C --> E
    D --> F
    E --> G
    F --> H
    D --> I
    E --> J
    B --> K
    C --> L
    A --> M
    M --> N
    N --> O
```

## 11. CI/CD Pipeline

```mermaid
graph LR
    A[Source Code] --> B[Build]
    B --> C[Unit Tests]
    C --> D[Integration Tests]
    D --> E[Security Scan]
    E --> F[Code Quality]
    F --> G[Docker Build]
    G --> H[Push to Registry]
    H --> I[Deploy to Staging]
    I --> J[Automated Tests]
    J --> K[Deploy to Production]
    K --> L[Health Check]
    L --> M[Monitoring]
```

## 12. Database Schema Relationships

```mermaid
erDiagram
    Companies ||--o{ Departments : has
    Companies ||--o{ Employees : employs
    Departments ||--o{ Employees : contains
    Employees ||--o{ AttendanceRecords : records
    Employees ||--o{ LeaveRequests : submits
    Employees ||--o{ Payrolls : receives
    Companies ||--o{ LeaveTypes : defines
    Companies ||--o{ SalaryComponents : configures
    Companies ||--o{ BiometricDevices : owns
    LeaveTypes ||--o{ LeaveRequests : categorizes
    SalaryComponents ||--o{ EmployeeSalaries : applies
    BiometricDevices ||--o{ AttendanceRecords : captures
    
    Companies {
        uniqueidentifier Id PK
        nvarchar Name
        nvarchar NameAr
        nvarchar TaxNumber
        datetime2 CreatedAt
    }
    
    Employees {
        uniqueidentifier Id PK
        uniqueidentifier CompanyId FK
        uniqueidentifier DepartmentId FK
        nvarchar EmployeeCode
        nvarchar FirstName
        nvarchar Email
        datetime2 CreatedAt
    }
    
    AttendanceRecords {
        uniqueidentifier Id PK
        uniqueidentifier EmployeeId FK
        datetime2 CheckInTime
        datetime2 CheckOutTime
        date AttendanceDate
    }
```

هذه المخططات التقنية المفصلة توضح الهيكل المعماري الشامل لنظام HRMS، مما يساعد فريق التطوير على فهم التصميم والتنفيذ بشكل واضح ومنظم.