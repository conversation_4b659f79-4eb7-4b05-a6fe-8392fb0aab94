using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Application.DTOs.Notification
{
    /// <summary>
    /// Notification DTO
    /// DTO الإشعار
    /// </summary>
    public class NotificationDto
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string TitleAr { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string MessageAr { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // Info, Warning, Error, Success
        public string Category { get; set; } = string.Empty; // Leave, Payroll, Attendance, System
        public string Priority { get; set; } = string.Empty; // Low, Medium, High, Critical
        public bool IsRead { get; set; }
        public DateTime? ReadAt { get; set; }
        public string? ActionUrl { get; set; }
        public string? ActionText { get; set; }
        public string? ActionTextAr { get; set; }
        public string? Icon { get; set; }
        public string? Data { get; set; } // JSON data for additional information
        public DateTime CreatedAt { get; set; }
        public DateTime? ExpiresAt { get; set; }

        // Calculated properties
        public bool IsExpired => ExpiresAt.HasValue && ExpiresAt.Value < DateTime.UtcNow;
        public string TimeAgo { get; set; } = string.Empty;
        public string TypeBadgeClass => GetTypeBadgeClass(Type);
        public string PriorityBadgeClass => GetPriorityBadgeClass(Priority);

        private static string GetTimeAgo(DateTime dateTime)
        {
            var timeSpan = DateTime.UtcNow - dateTime;
            
            if (timeSpan.TotalMinutes < 1)
                return "Just now / الآن";
            if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes}m ago / منذ {(int)timeSpan.TotalMinutes} دقيقة";
            if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours}h ago / منذ {(int)timeSpan.TotalHours} ساعة";
            if (timeSpan.TotalDays < 7)
                return $"{(int)timeSpan.TotalDays}d ago / منذ {(int)timeSpan.TotalDays} يوم";
            
            return dateTime.ToString("MMM dd, yyyy");
        }

        private static string GetTypeBadgeClass(string type)
        {
            return type switch
            {
                "Success" => "bg-success",
                "Warning" => "bg-warning",
                "Error" => "bg-danger",
                "Info" => "bg-info",
                _ => "bg-secondary"
            };
        }

        private static string GetPriorityBadgeClass(string priority)
        {
            return priority switch
            {
                "Critical" => "bg-danger",
                "High" => "bg-warning",
                "Medium" => "bg-info",
                "Low" => "bg-secondary",
                _ => "bg-light"
            };
        }
    }

    /// <summary>
    /// Create notification DTO
    /// DTO إنشاء إشعار
    /// </summary>
    public class CreateNotificationDto
    {
        [Required]
        public Guid UserId { get; set; }

        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [MaxLength(200)]
        public string? TitleAr { get; set; }

        [Required]
        [MaxLength(1000)]
        public string Message { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string? MessageAr { get; set; }

        [Required]
        [MaxLength(20)]
        public string Type { get; set; } = "Info";

        [Required]
        [MaxLength(50)]
        public string Category { get; set; } = "System";

        [MaxLength(20)]
        public string Priority { get; set; } = "Medium";

        [MaxLength(500)]
        public string? ActionUrl { get; set; }

        [MaxLength(100)]
        public string? ActionText { get; set; }

        [MaxLength(100)]
        public string? ActionTextAr { get; set; }

        [MaxLength(50)]
        public string? Icon { get; set; }

        public string? Data { get; set; }

        public DateTime? ExpiresAt { get; set; }
    }

    /// <summary>
    /// Notification list request DTO
    /// DTO طلب قائمة الإشعارات
    /// </summary>
    public class NotificationListDto
    {
        public Guid? UserId { get; set; }
        public string? Type { get; set; }
        public string? Category { get; set; }
        public string? Priority { get; set; }
        public bool? IsRead { get; set; }
        public bool IncludeExpired { get; set; } = false;
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? SearchTerm { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? SortBy { get; set; } = "CreatedAt";
        public string? SortDirection { get; set; } = "desc";
    }

    /// <summary>
    /// Bulk notification action DTO
    /// DTO إجراء مجمع للإشعارات
    /// </summary>
    public class BulkNotificationActionDto
    {
        [Required]
        public List<Guid> NotificationIds { get; set; } = new();

        [Required]
        public string Action { get; set; } = string.Empty; // MarkAsRead, MarkAsUnread, Delete
    }

    /// <summary>
    /// Notification statistics DTO
    /// DTO إحصائيات الإشعارات
    /// </summary>
    public class NotificationStatisticsDto
    {
        public int TotalNotifications { get; set; }
        public int UnreadNotifications { get; set; }
        public int ReadNotifications { get; set; }
        public int ExpiredNotifications { get; set; }
        public int TodayNotifications { get; set; }
        public int ThisWeekNotifications { get; set; }
        public int ThisMonthNotifications { get; set; }

        public Dictionary<string, int> NotificationsByType { get; set; } = new();
        public Dictionary<string, int> NotificationsByCategory { get; set; } = new();
        public Dictionary<string, int> NotificationsByPriority { get; set; } = new();

        // Calculated properties
        public decimal ReadPercentage => TotalNotifications > 0 ? (decimal)ReadNotifications / TotalNotifications * 100 : 0;
        public string ReadPercentageDisplay => $"{ReadPercentage:F1}%";
    }

    /// <summary>
    /// Notification template DTO
    /// DTO قالب الإشعار
    /// </summary>
    public class NotificationTemplateDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string NameAr { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string DescriptionAr { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string TitleTemplate { get; set; } = string.Empty;
        public string TitleTemplateAr { get; set; } = string.Empty;
        public string MessageTemplate { get; set; } = string.Empty;
        public string MessageTemplateAr { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public string? Icon { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// Create notification template DTO
    /// DTO إنشاء قالب إشعار
    /// </summary>
    public class CreateNotificationTemplateDto
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(100)]
        public string? NameAr { get; set; }

        [MaxLength(500)]
        public string? Description { get; set; }

        [MaxLength(500)]
        public string? DescriptionAr { get; set; }

        [Required]
        [MaxLength(50)]
        public string Category { get; set; } = string.Empty;

        [Required]
        [MaxLength(200)]
        public string TitleTemplate { get; set; } = string.Empty;

        [MaxLength(200)]
        public string? TitleTemplateAr { get; set; }

        [Required]
        [MaxLength(1000)]
        public string MessageTemplate { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string? MessageTemplateAr { get; set; }

        [Required]
        [MaxLength(20)]
        public string Type { get; set; } = "Info";

        [MaxLength(20)]
        public string Priority { get; set; } = "Medium";

        [MaxLength(50)]
        public string? Icon { get; set; }

        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// Notification preferences DTO
    /// DTO تفضيلات الإشعارات
    /// </summary>
    public class NotificationPreferencesDto
    {
        public Guid UserId { get; set; }
        public bool EmailNotifications { get; set; } = true;
        public bool PushNotifications { get; set; } = true;
        public bool SmsNotifications { get; set; } = false;
        public bool LeaveNotifications { get; set; } = true;
        public bool PayrollNotifications { get; set; } = true;
        public bool AttendanceNotifications { get; set; } = true;
        public bool SystemNotifications { get; set; } = true;
        public string? QuietHoursStart { get; set; }
        public string? QuietHoursEnd { get; set; }
        public List<string> DisabledCategories { get; set; } = new();
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// Update notification preferences DTO
    /// DTO تحديث تفضيلات الإشعارات
    /// </summary>
    public class UpdateNotificationPreferencesDto
    {
        public bool EmailNotifications { get; set; } = true;
        public bool PushNotifications { get; set; } = true;
        public bool SmsNotifications { get; set; } = false;
        public bool LeaveNotifications { get; set; } = true;
        public bool PayrollNotifications { get; set; } = true;
        public bool AttendanceNotifications { get; set; } = true;
        public bool SystemNotifications { get; set; } = true;
        public string? QuietHoursStart { get; set; }
        public string? QuietHoursEnd { get; set; }
        public List<string> DisabledCategories { get; set; } = new();
    }
}
