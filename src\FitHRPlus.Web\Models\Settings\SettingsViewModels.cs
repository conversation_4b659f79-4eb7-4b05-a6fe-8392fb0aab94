using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Web.Models.Settings
{
    /// <summary>
    /// Main settings view model
    /// نموذج عرض الإعدادات الرئيسية
    /// </summary>
    public class SettingsViewModel
    {
        public Guid CompanyId { get; set; }
        public CompanySettingsViewModel? CompanySettings { get; set; }
    }

    /// <summary>
    /// Company settings view model
    /// نموذج عرض إعدادات الشركة
    /// </summary>
    public class CompanySettingsViewModel
    {
        public Guid Id { get; set; }
        public Guid CompanyId { get; set; }

        [Required(ErrorMessage = "اسم الشركة مطلوب")]
        [Display(Name = "اسم الشركة")]
        public string CompanyName { get; set; } = string.Empty;

        [Display(Name = "اسم الشركة بالعربية")]
        public string CompanyNameAr { get; set; } = string.Empty;

        [Display(Name = "العنوان")]
        public string? Address { get; set; }

        [Display(Name = "العنوان بالعربية")]
        public string? AddressAr { get; set; }

        [Display(Name = "رقم الهاتف")]
        public string? Phone { get; set; }

        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [Display(Name = "البريد الإلكتروني")]
        public string? Email { get; set; }

        [Url(ErrorMessage = "رابط الموقع غير صحيح")]
        [Display(Name = "الموقع الإلكتروني")]
        public string? Website { get; set; }

        [Display(Name = "الرقم الضريبي")]
        public string? TaxNumber { get; set; }

        [Display(Name = "السجل التجاري")]
        public string? CommercialRegister { get; set; }

        [Required(ErrorMessage = "وقت بداية العمل مطلوب")]
        [Display(Name = "وقت بداية العمل")]
        public TimeSpan WorkingHoursStart { get; set; } = new TimeSpan(8, 0, 0);

        [Required(ErrorMessage = "وقت نهاية العمل مطلوب")]
        [Display(Name = "وقت نهاية العمل")]
        public TimeSpan WorkingHoursEnd { get; set; } = new TimeSpan(17, 0, 0);

        [Display(Name = "أيام العطلة الأسبوعية")]
        public string WeekendDays { get; set; } = "Friday,Saturday";

        [Required(ErrorMessage = "العملة مطلوبة")]
        [Display(Name = "العملة")]
        public string Currency { get; set; } = "EGP";

        [Required(ErrorMessage = "رمز العملة مطلوب")]
        [Display(Name = "رمز العملة")]
        public string CurrencySymbol { get; set; } = "ج.م";

        [Range(0, 1, ErrorMessage = "معدل ضريبة الدخل يجب أن يكون بين 0 و 1")]
        [Display(Name = "معدل ضريبة الدخل")]
        public decimal IncomeTaxRate { get; set; } = 0.14m;

        [Range(0, 1, ErrorMessage = "معدل التأمين الاجتماعي للموظف يجب أن يكون بين 0 و 1")]
        [Display(Name = "معدل التأمين الاجتماعي للموظف")]
        public decimal SocialInsuranceEmployeeRate { get; set; } = 0.11m;

        [Range(0, 1, ErrorMessage = "معدل التأمين الاجتماعي لصاحب العمل يجب أن يكون بين 0 و 1")]
        [Display(Name = "معدل التأمين الاجتماعي لصاحب العمل")]
        public decimal SocialInsuranceEmployerRate { get; set; } = 0.185m;

        [Range(0, 1, ErrorMessage = "معدل التأمين الطبي يجب أن يكون بين 0 و 1")]
        [Display(Name = "معدل التأمين الطبي")]
        public decimal MedicalInsuranceRate { get; set; } = 0.03m;

        [Display(Name = "تفعيل إشعارات البريد الإلكتروني")]
        public bool EnableEmailNotifications { get; set; } = true;

        [Display(Name = "تفعيل الإشعارات النصية")]
        public bool EnableSmsNotifications { get; set; } = false;

        [Display(Name = "تفعيل الإشعارات الفورية")]
        public bool EnablePushNotifications { get; set; } = true;

        [Required(ErrorMessage = "اللغة الافتراضية مطلوبة")]
        [Display(Name = "اللغة الافتراضية")]
        public string DefaultLanguage { get; set; } = "ar";

        [Required(ErrorMessage = "المنطقة الزمنية مطلوبة")]
        [Display(Name = "المنطقة الزمنية")]
        public string TimeZone { get; set; } = "Africa/Cairo";

        [Required(ErrorMessage = "تنسيق التاريخ مطلوب")]
        [Display(Name = "تنسيق التاريخ")]
        public string DateFormat { get; set; } = "dd/MM/yyyy";

        [Required(ErrorMessage = "تنسيق الوقت مطلوب")]
        [Display(Name = "تنسيق الوقت")]
        public string TimeFormat { get; set; } = "HH:mm";
    }

    /// <summary>
    /// System settings view model
    /// نموذج عرض إعدادات النظام
    /// </summary>
    public class SystemSettingsViewModel
    {
        [Display(Name = "وضع الصيانة")]
        public bool MaintenanceMode { get; set; }

        [Display(Name = "السماح بالتسجيل")]
        public bool AllowRegistration { get; set; } = true;

        [Display(Name = "تتطلب تأكيد البريد الإلكتروني")]
        public bool RequireEmailVerification { get; set; } = true;

        [Range(5, 480, ErrorMessage = "مهلة الجلسة يجب أن تكون بين 5 و 480 دقيقة")]
        [Display(Name = "مهلة الجلسة (بالدقائق)")]
        public int SessionTimeout { get; set; } = 30;

        [Range(3, 10, ErrorMessage = "الحد الأقصى لمحاولات تسجيل الدخول يجب أن يكون بين 3 و 10")]
        [Display(Name = "الحد الأقصى لمحاولات تسجيل الدخول")]
        public int MaxLoginAttempts { get; set; } = 5;

        [Range(6, 20, ErrorMessage = "الحد الأدنى لطول كلمة المرور يجب أن يكون بين 6 و 20")]
        [Display(Name = "الحد الأدنى لطول كلمة المرور")]
        public int PasswordMinLength { get; set; } = 8;

        [Display(Name = "تتطلب أحرف خاصة")]
        public bool RequireSpecialCharacters { get; set; } = true;

        [Display(Name = "تتطلب أرقام")]
        public bool RequireNumbers { get; set; } = true;

        [Display(Name = "تتطلب أحرف كبيرة")]
        public bool RequireUppercase { get; set; } = true;

        [Display(Name = "تتطلب أحرف صغيرة")]
        public bool RequireLowercase { get; set; } = true;
    }

    /// <summary>
    /// Backup settings view model
    /// نموذج عرض إعدادات النسخ الاحتياطي
    /// </summary>
    public class BackupSettingsViewModel
    {
        [Display(Name = "تفعيل النسخ الاحتياطي التلقائي")]
        public bool AutoBackupEnabled { get; set; } = true;

        [Display(Name = "تكرار النسخ الاحتياطي")]
        public string BackupFrequency { get; set; } = "Daily";

        [Range(1, 365, ErrorMessage = "مدة الاحتفاظ بالنسخ الاحتياطية يجب أن تكون بين 1 و 365 يوم")]
        [Display(Name = "مدة الاحتفاظ بالنسخ الاحتياطية (بالأيام)")]
        public int BackupRetentionDays { get; set; } = 30;

        [Display(Name = "تاريخ آخر نسخة احتياطية")]
        public DateTime? LastBackupDate { get; set; }

        [Display(Name = "حجم آخر نسخة احتياطية")]
        public string? LastBackupSize { get; set; }

        [Display(Name = "حالة آخر نسخة احتياطية")]
        public string? LastBackupStatus { get; set; }

        public List<BackupFileViewModel> BackupFiles { get; set; } = new();
    }

    /// <summary>
    /// Backup file view model
    /// نموذج عرض ملف النسخة الاحتياطية
    /// </summary>
    public class BackupFileViewModel
    {
        public string FileName { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public string FileSize { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
    }

    /// <summary>
    /// Notification settings view model
    /// نموذج عرض إعدادات الإشعارات
    /// </summary>
    public class NotificationSettingsViewModel
    {
        [Display(Name = "تفعيل إشعارات البريد الإلكتروني")]
        public bool EmailNotifications { get; set; } = true;

        [Display(Name = "تفعيل الإشعارات الفورية")]
        public bool PushNotifications { get; set; } = true;

        [Display(Name = "تفعيل الإشعارات النصية")]
        public bool SmsNotifications { get; set; } = false;

        [Display(Name = "إشعارات الإجازات")]
        public bool LeaveNotifications { get; set; } = true;

        [Display(Name = "إشعارات الرواتب")]
        public bool PayrollNotifications { get; set; } = true;

        [Display(Name = "إشعارات الحضور")]
        public bool AttendanceNotifications { get; set; } = true;

        [Display(Name = "إشعارات النظام")]
        public bool SystemNotifications { get; set; } = true;

        [Display(Name = "بداية ساعات الهدوء")]
        public string? QuietHoursStart { get; set; }

        [Display(Name = "نهاية ساعات الهدوء")]
        public string? QuietHoursEnd { get; set; }

        public List<string> DisabledCategories { get; set; } = new();
    }
}
