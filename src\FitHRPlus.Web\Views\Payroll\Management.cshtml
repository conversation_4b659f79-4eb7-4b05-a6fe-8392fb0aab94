@model FitHRPlus.Web.Models.Payroll.PayrollManagementViewModel
@{
    ViewData["Title"] = "معالجة كشوف المرتبات";
}

<!-- Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-title">
            <h1><i class="bi bi-currency-dollar"></i> معالجة كشوف المرتبات</h1>
            <p>إدارة ومعالجة كشوف مرتبات الموظفين لشهر @Model.CurrentMonthName @Model.CurrentYear</p>
        </div>
        <div class="page-actions">
            <a href="@Url.Action("Generate")" class="btn btn-success">
                <i class="bi bi-plus-circle"></i>
                إنشاء كشف راتب جديد
            </a>
            <button type="button" class="btn btn-primary" onclick="refreshAllTabs()">
                <i class="bi bi-arrow-clockwise"></i>
                تحديث البيانات
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-primary">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3 id="activeCount">0</h3>
                        <p>كشوف نشطة</p>
                        <small>كشوف جاهزة للمعالجة</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-file-earmark-text"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-warning">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3 id="pendingCount">0</h3>
                        <p>قيد الانتظار</p>
                        <small>كشوف تحتاج مراجعة</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-clock"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-info">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3 id="processedCount">0</h3>
                        <p>معالجة</p>
                        <small>كشوف تم معالجتها</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-gear"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-success">
            <div class="stats-card-body">
                <div class="stats-card-content">
                    <div class="stats-card-info">
                        <h3 id="paidCount">0</h3>
                        <p>مدفوعة</p>
                        <small>كشوف تم دفعها</small>
                    </div>
                    <div class="stats-card-icon">
                        <i class="bi bi-check-circle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payroll Management Tabs -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="payrollTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="active-tab" data-bs-toggle="tab" data-bs-target="#active" type="button" role="tab" aria-controls="active" aria-selected="true">
                            <i class="bi bi-file-earmark-text"></i>
                            كشوف نشطة
                            <span class="badge bg-primary ms-2" id="activeBadge">0</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" type="button" role="tab" aria-controls="pending" aria-selected="false">
                            <i class="bi bi-clock"></i>
                            قيد الانتظار
                            <span class="badge bg-warning ms-2" id="pendingBadge">0</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="processed-tab" data-bs-toggle="tab" data-bs-target="#processed" type="button" role="tab" aria-controls="processed" aria-selected="false">
                            <i class="bi bi-gear"></i>
                            معالجة
                            <span class="badge bg-info ms-2" id="processedBadge">0</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="paid-tab" data-bs-toggle="tab" data-bs-target="#paid" type="button" role="tab" aria-controls="paid" aria-selected="false">
                            <i class="bi bi-check-circle"></i>
                            مدفوعة
                            <span class="badge bg-success ms-2" id="paidBadge">0</span>
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="payrollTabContent">
                    <!-- Active Payrolls Tab -->
                    <div class="tab-pane fade show active" id="active" role="tabpanel" aria-labelledby="active-tab">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">كشوف المرتبات النشطة</h6>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-primary" onclick="loadTabData('active')">
                                    <i class="bi bi-arrow-clockwise"></i>
                                    تحديث
                                </button>
                                <button class="btn btn-sm btn-outline-success" onclick="bulkProcess('active')">
                                    <i class="bi bi-gear"></i>
                                    معالجة الكل
                                </button>
                            </div>
                        </div>
                        <div id="activeContent">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">جاري التحميل...</span>
                                </div>
                                <p class="mt-2 text-muted">جاري تحميل البيانات...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Pending Payrolls Tab -->
                    <div class="tab-pane fade" id="pending" role="tabpanel" aria-labelledby="pending-tab">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">كشوف المرتبات قيد الانتظار</h6>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-primary" onclick="loadTabData('pending')">
                                    <i class="bi bi-arrow-clockwise"></i>
                                    تحديث
                                </button>
                                <button class="btn btn-sm btn-outline-warning" onclick="bulkApprove('pending')">
                                    <i class="bi bi-check-circle"></i>
                                    الموافقة على الكل
                                </button>
                            </div>
                        </div>
                        <div id="pendingContent">
                            <div class="text-center py-4">
                                <i class="bi bi-clock display-4 text-muted"></i>
                                <p class="mt-2 text-muted">انقر على التبويب لتحميل البيانات</p>
                            </div>
                        </div>
                    </div>

                    <!-- Processed Payrolls Tab -->
                    <div class="tab-pane fade" id="processed" role="tabpanel" aria-labelledby="processed-tab">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">كشوف المرتبات المعالجة</h6>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-primary" onclick="loadTabData('processed')">
                                    <i class="bi bi-arrow-clockwise"></i>
                                    تحديث
                                </button>
                                <button class="btn btn-sm btn-outline-success" onclick="bulkPay('processed')">
                                    <i class="bi bi-currency-dollar"></i>
                                    دفع الكل
                                </button>
                            </div>
                        </div>
                        <div id="processedContent">
                            <div class="text-center py-4">
                                <i class="bi bi-gear display-4 text-muted"></i>
                                <p class="mt-2 text-muted">انقر على التبويب لتحميل البيانات</p>
                            </div>
                        </div>
                    </div>

                    <!-- Paid Payrolls Tab -->
                    <div class="tab-pane fade" id="paid" role="tabpanel" aria-labelledby="paid-tab">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">كشوف المرتبات المدفوعة</h6>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-primary" onclick="loadTabData('paid')">
                                    <i class="bi bi-arrow-clockwise"></i>
                                    تحديث
                                </button>
                                <button class="btn btn-sm btn-outline-info" onclick="exportPaidPayrolls()">
                                    <i class="bi bi-download"></i>
                                    تصدير
                                </button>
                            </div>
                        </div>
                        <div id="paidContent">
                            <div class="text-center py-4">
                                <i class="bi bi-check-circle display-4 text-muted"></i>
                                <p class="mt-2 text-muted">انقر على التبويب لتحميل البيانات</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
<script>
    $(document).ready(function() {
        // Initialize tabs
        initializeTabs();
        
        // Load active tab data on page load
        loadTabData('active');
    });

    function initializeTabs() {
        // Handle tab shown event
        $('button[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
            const targetTab = $(e.target).attr('data-bs-target').replace('#', '');
            console.log('Tab shown:', targetTab);
            
            // Load data for the active tab if not already loaded
            const content = $(`#${targetTab}Content`);
            if (content.find('.spinner-border, .display-4').length > 0) {
                loadTabData(targetTab);
            }
        });

        // Handle tab click event
        $('button[data-bs-toggle="tab"]').on('click', function (e) {
            console.log('Tab clicked:', $(this).attr('data-bs-target'));
        });
    }

    function loadTabData(tabName) {
        console.log('Loading data for tab:', tabName);
        
        const contentDiv = $(`#${tabName}Content`);
        
        // Show loading spinner
        contentDiv.html(`
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2 text-muted">جاري تحميل البيانات...</p>
            </div>
        `);

        // Make AJAX request
        $.ajax({
            url: '@Url.Action("GetPayrollsByStatus")',
            type: 'GET',
            data: {
                status: tabName,
                month: @Model.CurrentMonth,
                year: @Model.CurrentYear
            },
            success: function(response) {
                console.log('Data loaded for', tabName, ':', response);
                
                if (response.success) {
                    renderPayrollTable(tabName, response.data);
                    updateStatistics(tabName, response.data.length);
                } else {
                    showError(contentDiv, response.message || 'حدث خطأ أثناء تحميل البيانات');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading data for', tabName, ':', error);
                showError(contentDiv, 'حدث خطأ أثناء تحميل البيانات');
            }
        });
    }

    function renderPayrollTable(tabName, data) {
        const contentDiv = $(`#${tabName}Content`);
        
        if (data.length === 0) {
            contentDiv.html(`
                <div class="text-center py-4">
                    <i class="bi bi-inbox display-4 text-muted"></i>
                    <p class="mt-2 text-muted">لا توجد كشوف مرتبات في هذه الحالة</p>
                </div>
            `);
            return;
        }

        let tableHtml = `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll${tabName}" onchange="toggleSelectAll('${tabName}', this)">
                            </th>
                            <th>الموظف</th>
                            <th>القسم</th>
                            <th>الراتب الأساسي</th>
                            <th>البدلات</th>
                            <th>الخصومات</th>
                            <th>صافي الراتب</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        data.forEach(payroll => {
            tableHtml += `
                <tr>
                    <td>
                        <input type="checkbox" class="payroll-checkbox-${tabName}" value="${payroll.id}">
                    </td>
                    <td>
                        <div class="employee-info">
                            <div class="employee-avatar">${payroll.employeeName.charAt(0)}</div>
                            <div class="employee-details">
                                <h6 class="mb-0">${payroll.employeeName}</h6>
                                <small class="text-muted">${payroll.employeeCode}</small>
                            </div>
                        </div>
                    </td>
                    <td>${payroll.department}</td>
                    <td>${formatCurrency(payroll.basicSalary)}</td>
                    <td>${formatCurrency(payroll.allowances)}</td>
                    <td>${formatCurrency(payroll.deductions)}</td>
                    <td><strong>${formatCurrency(payroll.netSalary)}</strong></td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="viewPayroll('${payroll.id}')" title="عرض">
                                <i class="bi bi-eye"></i>
                            </button>
                            ${getActionButtons(tabName, payroll.id)}
                        </div>
                    </td>
                </tr>
            `;
        });

        tableHtml += `
                    </tbody>
                </table>
            </div>
        `;

        contentDiv.html(tableHtml);
    }

    function getActionButtons(tabName, payrollId) {
        switch(tabName) {
            case 'active':
                return `
                    <button class="btn btn-outline-warning" onclick="processPayroll('${payrollId}')" title="معالجة">
                        <i class="bi bi-gear"></i>
                    </button>
                `;
            case 'pending':
                return `
                    <button class="btn btn-outline-success" onclick="approvePayroll('${payrollId}')" title="موافقة">
                        <i class="bi bi-check-circle"></i>
                    </button>
                `;
            case 'processed':
                return `
                    <button class="btn btn-outline-success" onclick="payPayroll('${payrollId}')" title="دفع">
                        <i class="bi bi-currency-dollar"></i>
                    </button>
                `;
            case 'paid':
                return `
                    <button class="btn btn-outline-info" onclick="downloadPayslip('${payrollId}')" title="تحميل قسيمة الراتب">
                        <i class="bi bi-download"></i>
                    </button>
                `;
            default:
                return '';
        }
    }

    function updateStatistics(tabName, count) {
        $(`#${tabName}Count`).text(count);
        $(`#${tabName}Badge`).text(count);
    }

    function showError(contentDiv, message) {
        contentDiv.html(`
            <div class="text-center py-4">
                <i class="bi bi-exclamation-triangle display-4 text-danger"></i>
                <p class="mt-2 text-danger">${message}</p>
                <button class="btn btn-outline-primary" onclick="location.reload()">
                    <i class="bi bi-arrow-clockwise"></i>
                    إعادة المحاولة
                </button>
            </div>
        `);
    }

    function formatCurrency(amount) {
        return new Intl.NumberFormat('ar-EG', {
            style: 'currency',
            currency: 'EGP',
            minimumFractionDigits: 0
        }).format(amount);
    }

    function refreshAllTabs() {
        const activeTabs = ['active', 'pending', 'processed', 'paid'];
        activeTabs.forEach(tab => {
            const content = $(`#${tab}Content`);
            if (content.find('table').length > 0) {
                loadTabData(tab);
            }
        });
        
        showToast('تم تحديث جميع البيانات', 'success');
    }

    // Action functions
    function viewPayroll(id) {
        window.location.href = `@Url.Action("Details")?id=${id}`;
    }

    function processPayroll(id) {
        if (confirm('هل تريد معالجة كشف المرتبات هذا؟')) {
            showToast('جاري معالجة كشف المرتبات...', 'info');
            // TODO: Implement actual processing
            setTimeout(() => {
                showToast('تم معالجة كشف المرتبات بنجاح', 'success');
                loadTabData('active');
                loadTabData('processed');
            }, 1000);
        }
    }

    function approvePayroll(id) {
        if (confirm('هل تريد الموافقة على كشف المرتبات هذا؟')) {
            showToast('جاري الموافقة على كشف المرتبات...', 'info');
            // TODO: Implement actual approval
            setTimeout(() => {
                showToast('تم الموافقة على كشف المرتبات بنجاح', 'success');
                loadTabData('pending');
                loadTabData('processed');
            }, 1000);
        }
    }

    function payPayroll(id) {
        if (confirm('هل تريد تسجيل كشف المرتبات كمدفوع؟')) {
            showToast('جاري تسجيل الدفع...', 'info');
            // TODO: Implement actual payment
            setTimeout(() => {
                showToast('تم تسجيل الدفع بنجاح', 'success');
                loadTabData('processed');
                loadTabData('paid');
            }, 1000);
        }
    }

    function downloadPayslip(id) {
        showToast('جاري تحميل قسيمة الراتب...', 'info');
        // TODO: Implement actual download
        setTimeout(() => {
            showToast('تم تحميل قسيمة الراتب', 'success');
        }, 1000);
    }

    function toggleSelectAll(tabName, checkbox) {
        $(`.payroll-checkbox-${tabName}`).prop('checked', checkbox.checked);
    }

    function bulkProcess(tabName) {
        const checkedBoxes = $(`.payroll-checkbox-${tabName}:checked`);
        if (checkedBoxes.length === 0) {
            showToast('يرجى اختيار كشوف مرتبات للمعالجة', 'warning');
            return;
        }

        if (confirm(`هل تريد معالجة ${checkedBoxes.length} كشف مرتبات؟`)) {
            showToast(`جاري معالجة ${checkedBoxes.length} كشف مرتبات...`, 'info');
            // TODO: Implement bulk processing
            setTimeout(() => {
                showToast(`تم معالجة ${checkedBoxes.length} كشف مرتبات بنجاح`, 'success');
                loadTabData(tabName);
            }, 2000);
        }
    }

    function bulkApprove(tabName) {
        const checkedBoxes = $(`.payroll-checkbox-${tabName}:checked`);
        if (checkedBoxes.length === 0) {
            showToast('يرجى اختيار كشوف مرتبات للموافقة', 'warning');
            return;
        }

        if (confirm(`هل تريد الموافقة على ${checkedBoxes.length} كشف مرتبات؟`)) {
            showToast(`جاري الموافقة على ${checkedBoxes.length} كشف مرتبات...`, 'info');
            // TODO: Implement bulk approval
            setTimeout(() => {
                showToast(`تم الموافقة على ${checkedBoxes.length} كشف مرتبات بنجاح`, 'success');
                loadTabData(tabName);
            }, 2000);
        }
    }

    function bulkPay(tabName) {
        const checkedBoxes = $(`.payroll-checkbox-${tabName}:checked`);
        if (checkedBoxes.length === 0) {
            showToast('يرجى اختيار كشوف مرتبات للدفع', 'warning');
            return;
        }

        if (confirm(`هل تريد دفع ${checkedBoxes.length} كشف مرتبات؟`)) {
            showToast(`جاري دفع ${checkedBoxes.length} كشف مرتبات...`, 'info');
            // TODO: Implement bulk payment
            setTimeout(() => {
                showToast(`تم دفع ${checkedBoxes.length} كشف مرتبات بنجاح`, 'success');
                loadTabData(tabName);
            }, 2000);
        }
    }

    function exportPaidPayrolls() {
        showToast('جاري تصدير كشوف المرتبات المدفوعة...', 'info');
        // TODO: Implement export functionality
        setTimeout(() => {
            showToast('تم تصدير البيانات بنجاح', 'success');
        }, 1500);
    }

    function showToast(message, type = 'info') {
        const bgClass = type === 'success' ? 'bg-success' :
                       type === 'warning' ? 'bg-warning' :
                       type === 'error' ? 'bg-danger' : 'bg-info';

        const toast = $(`
            <div class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `);

        let container = $('.toast-container');
        if (container.length === 0) {
            container = $('<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
            $('body').append(container);
        }

        container.append(toast);
        const bsToast = new bootstrap.Toast(toast[0]);
        bsToast.show();

        toast.on('hidden.bs.toast', function() {
            $(this).remove();
        });
    }
</script>

<style>
.employee-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.employee-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.employee-details h6 {
    font-size: 14px;
    font-weight: 600;
}

.employee-details small {
    font-size: 12px;
}

.nav-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: #6c757d;
    font-weight: 500;
    padding: 12px 20px;
}

.nav-tabs .nav-link.active {
    background: none;
    border-bottom-color: #0d6efd;
    color: #0d6efd;
}

.nav-tabs .nav-link:hover {
    border-bottom-color: #dee2e6;
    color: #495057;
}

.badge {
    font-size: 10px;
    padding: 4px 6px;
}

.stats-card {
    transition: transform 0.2s;
}

.stats-card:hover {
    transform: translateY(-2px);
}
</style>
}
