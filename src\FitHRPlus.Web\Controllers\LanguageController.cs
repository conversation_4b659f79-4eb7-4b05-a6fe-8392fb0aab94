using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Localization;

namespace FitHRPlus.Web.Controllers
{
    /// <summary>
    /// Language management controller
    /// وحدة تحكم إدارة اللغة
    /// </summary>
    public class LanguageController : Controller
    {
        /// <summary>
        /// Set language and culture
        /// تعيين اللغة والثقافة
        /// </summary>
        [HttpPost]
        public IActionResult SetLanguage(string culture, string returnUrl)
        {
            if (!string.IsNullOrEmpty(culture))
            {
                Response.Cookies.Append(
                    CookieRequestCultureProvider.DefaultCookieName,
                    CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(culture)),
                    new CookieOptions 
                    { 
                        Expires = DateTimeOffset.UtcNow.AddYears(1),
                        IsEssential = true,
                        SameSite = SameSiteMode.Lax
                    }
                );
            }

            return LocalRedirect(returnUrl ?? "/");
        }

        /// <summary>
        /// Get current language information
        /// الحصول على معلومات اللغة الحالية
        /// </summary>
        [HttpGet]
        public IActionResult GetCurrentLanguage()
        {
            var culture = Request.HttpContext.Features.Get<IRequestCultureFeature>()?.RequestCulture?.Culture?.Name ?? "ar";
            
            return Json(new 
            { 
                culture = culture,
                isRtl = culture.StartsWith("ar"),
                direction = culture.StartsWith("ar") ? "rtl" : "ltr"
            });
        }
    }
}
