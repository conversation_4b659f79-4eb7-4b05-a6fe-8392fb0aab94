using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Web.Models.Admin
{
    /// <summary>
    /// User view model for administration
    /// نموذج عرض المستخدم للإدارة
    /// </summary>
    public class UserViewModel
    {
        /// <summary>
        /// User ID
        /// معرف المستخدم
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Username (unique)
        /// اسم المستخدم (فريد)
        /// </summary>
        [Required(ErrorMessage = "Username is required")]
        [StringLength(100, MinimumLength = 3, ErrorMessage = "Username must be between 3 and 100 characters")]
        [Display(Name = "Username")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// Email address (unique)
        /// البريد الإلكتروني (فريد)
        /// </summary>
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        [Display(Name = "Email")]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// First name
        /// الاسم الأول
        /// </summary>
        [Required(ErrorMessage = "First name is required")]
        [StringLength(100, ErrorMessage = "First name cannot exceed 100 characters")]
        [Display(Name = "First Name")]
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// Last name
        /// الاسم الأخير
        /// </summary>
        [Required(ErrorMessage = "Last name is required")]
        [StringLength(100, ErrorMessage = "Last name cannot exceed 100 characters")]
        [Display(Name = "Last Name")]
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// Full name (computed)
        /// الاسم الكامل (محسوب)
        /// </summary>
        [Display(Name = "Full Name")]
        public string FullName => $"{FirstName} {LastName}";

        /// <summary>
        /// Phone number
        /// رقم الهاتف
        /// </summary>
        [Phone(ErrorMessage = "Invalid phone number format")]
        [Display(Name = "Phone")]
        public string? Phone { get; set; }

        /// <summary>
        /// Profile picture URL
        /// رابط صورة الملف الشخصي
        /// </summary>
        [Display(Name = "Profile Picture")]
        public string? ProfilePicture { get; set; }

        /// <summary>
        /// Preferred language
        /// اللغة المفضلة
        /// </summary>
        [Display(Name = "Preferred Language")]
        public string PreferredLanguage { get; set; } = "ar";

        /// <summary>
        /// Time zone
        /// المنطقة الزمنية
        /// </summary>
        [Display(Name = "Time Zone")]
        public string TimeZone { get; set; } = "Africa/Cairo";

        /// <summary>
        /// Whether email is verified
        /// ما إذا كان البريد الإلكتروني محقق
        /// </summary>
        [Display(Name = "Email Verified")]
        public bool IsEmailVerified { get; set; }

        /// <summary>
        /// Whether phone is verified
        /// ما إذا كان الهاتف محقق
        /// </summary>
        [Display(Name = "Phone Verified")]
        public bool IsPhoneVerified { get; set; }

        /// <summary>
        /// Whether two-factor authentication is enabled
        /// ما إذا كانت المصادقة الثنائية مفعلة
        /// </summary>
        [Display(Name = "Two-Factor Enabled")]
        public bool TwoFactorEnabled { get; set; }

        /// <summary>
        /// Whether the user is active
        /// ما إذا كان المستخدم نشط
        /// </summary>
        [Display(Name = "Active")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Last login date
        /// تاريخ آخر تسجيل دخول
        /// </summary>
        [Display(Name = "Last Login")]
        public DateTime? LastLoginAt { get; set; }

        /// <summary>
        /// Creation date
        /// تاريخ الإنشاء
        /// </summary>
        [Display(Name = "Created At")]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Last update date
        /// تاريخ آخر تحديث
        /// </summary>
        [Display(Name = "Updated At")]
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// User roles in companies
        /// أدوار المستخدم في الشركات
        /// </summary>
        public List<UserRoleViewModel> Roles { get; set; } = new();

        /// <summary>
        /// Password (for creation/update)
        /// كلمة المرور (للإنشاء/التحديث)
        /// </summary>
        [DataType(DataType.Password)]
        [Display(Name = "Password")]
        public string? Password { get; set; }

        /// <summary>
        /// Password confirmation
        /// تأكيد كلمة المرور
        /// </summary>
        [DataType(DataType.Password)]
        [Display(Name = "Confirm Password")]
        [Compare("Password", ErrorMessage = "Password and confirmation password do not match")]
        public string? ConfirmPassword { get; set; }
    }

    /// <summary>
    /// User role view model
    /// نموذج عرض دور المستخدم
    /// </summary>
    public class UserRoleViewModel
    {
        /// <summary>
        /// Company ID
        /// معرف الشركة
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// Company name
        /// اسم الشركة
        /// </summary>
        [Display(Name = "Company")]
        public string CompanyName { get; set; } = string.Empty;

        /// <summary>
        /// Role ID
        /// معرف الدور
        /// </summary>
        public Guid RoleId { get; set; }

        /// <summary>
        /// Role name
        /// اسم الدور
        /// </summary>
        [Display(Name = "Role")]
        public string RoleName { get; set; } = string.Empty;

        /// <summary>
        /// Whether the role is active
        /// ما إذا كان الدور نشط
        /// </summary>
        [Display(Name = "Active")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Assignment date
        /// تاريخ التعيين
        /// </summary>
        [Display(Name = "Assigned At")]
        public DateTime AssignedAt { get; set; }
    }

    /// <summary>
    /// User list view model
    /// نموذج عرض قائمة المستخدمين
    /// </summary>
    public class UserListViewModel
    {
        /// <summary>
        /// List of users
        /// قائمة المستخدمين
        /// </summary>
        public List<UserViewModel> Users { get; set; } = new();

        /// <summary>
        /// Search term
        /// مصطلح البحث
        /// </summary>
        [Display(Name = "Search")]
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Filter by company
        /// تصفية حسب الشركة
        /// </summary>
        [Display(Name = "Company")]
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// Filter by role
        /// تصفية حسب الدور
        /// </summary>
        [Display(Name = "Role")]
        public Guid? RoleId { get; set; }

        /// <summary>
        /// Filter by active status
        /// تصفية حسب الحالة النشطة
        /// </summary>
        [Display(Name = "Status")]
        public bool? IsActive { get; set; }

        /// <summary>
        /// Filter by email verification
        /// تصفية حسب تحقق البريد الإلكتروني
        /// </summary>
        [Display(Name = "Email Verified")]
        public bool? IsEmailVerified { get; set; }

        /// <summary>
        /// Current page number
        /// رقم الصفحة الحالية
        /// </summary>
        public int CurrentPage { get; set; } = 1;

        /// <summary>
        /// Total number of pages
        /// العدد الإجمالي للصفحات
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// Total number of users
        /// العدد الإجمالي للمستخدمين
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Page size
        /// حجم الصفحة
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// Available companies for filtering
        /// الشركات المتاحة للتصفية
        /// </summary>
        public List<CompanyViewModel> Companies { get; set; } = new();

        /// <summary>
        /// Available roles for filtering
        /// الأدوار المتاحة للتصفية
        /// </summary>
        public List<RoleViewModel> Roles { get; set; } = new();
    }

    /// <summary>
    /// Role view model for dropdowns
    /// نموذج عرض الدور للقوائم المنسدلة
    /// </summary>
    public class RoleViewModel
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string NameAr { get; set; } = string.Empty;
    }
}
