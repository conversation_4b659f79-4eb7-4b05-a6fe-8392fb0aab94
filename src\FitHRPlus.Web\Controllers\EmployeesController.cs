using FitHRPlus.Application.DTOs.Employees;
using EmployeeDto = FitHRPlus.Application.DTOs.Employees.EmployeeDto;
using FitHRPlus.Application.Interfaces;
using FitHRPlus.Web.Models.Admin;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Security.Claims;
using EmployeeViewModel = FitHRPlus.Web.Models.Employees.EmployeeViewModel;
using EmployeeListViewModel = FitHRPlus.Web.Models.Employees.EmployeeListViewModel;
using EmployeeListItemViewModel = FitHRPlus.Web.Models.Employees.EmployeeListItemViewModel;

namespace FitHRPlus.Web.Controllers
{
    /// <summary>
    /// Employees controller for employee management
    /// وحدة تحكم الموظفين لإدارة الموظفين
    /// </summary>
    [Authorize]
    public class EmployeesController : Controller
    {
        private readonly IEmployeeService _employeeService;
        private readonly IDepartmentService _departmentService;
        private readonly IPositionService _positionService;
        private readonly ILogger<EmployeesController> _logger;

        public EmployeesController(
            IEmployeeService employeeService,
            IDepartmentService departmentService,
            IPositionService positionService,
            ILogger<EmployeesController> logger)
        {
            _employeeService = employeeService;
            _departmentService = departmentService;
            _positionService = positionService;
            _logger = logger;
        }

        /// <summary>
        /// Employee list page
        /// صفحة قائمة الموظفين
        /// </summary>
        /// <param name="request">List request parameters</param>
        /// <returns>Employee list view</returns>
        [HttpGet]
        public async Task<IActionResult> Index([FromQuery] EmployeeListRequestDto request)
        {
            try
            {
                // Get current user's company
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (Guid.TryParse(companyIdClaim, out var companyId))
                {
                    request.CompanyId = companyId;
                }
                else
                {
                    // For demo purposes, use a default company ID
                    request.CompanyId = Guid.NewGuid();
                }

                var result = await _employeeService.GetEmployeesAsync(request);

                if (result.IsSuccess)
                {
                    var viewModel = new EmployeeListViewModel
                    {
                        Employees = result.Data!.Employees.Select(e => new EmployeeListItemViewModel
                        {
                            Id = e.Id,
                            EmployeeNumber = e.EmployeeNumber,
                            FullName = e.FullName,
                            DepartmentName = e.DepartmentName ?? "",
                            PositionTitle = e.PositionTitle ?? "",
                            Email = e.Email ?? "",
                            Phone = e.Phone ?? "",
                            HireDate = e.HireDate,
                            IsActive = e.IsActive
                        }).ToList(),
                        SearchTerm = request.SearchTerm,
                        DepartmentId = request.DepartmentId,
                        PositionId = request.PositionId,
                        EmploymentStatus = request.EmploymentStatus,
                        EmploymentType = request.EmploymentType,
                        Gender = request.Gender,
                        IsActive = request.IsActive,
                        HireDateFrom = request.HireDateFrom,
                        HireDateTo = request.HireDateTo,
                        CurrentPage = result.Data.CurrentPage,
                        TotalPages = result.Data.TotalPages,
                        TotalCount = result.Data.TotalCount,
                        PageSize = result.Data.PageSize
                    };

                    // Load filter options
                    await LoadFilterOptionsAsync(viewModel, companyId);

                    return View(viewModel);
                }

                TempData["ErrorMessage"] = result.ErrorMessage;
                return View(new EmployeeListViewModel());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading employee list");
                TempData["ErrorMessage"] = "An error occurred while loading employees";
                return View(new EmployeeListViewModel());
            }
        }

        /// <summary>
        /// Employee details page
        /// صفحة تفاصيل الموظف
        /// </summary>
        /// <param name="id">Employee ID</param>
        /// <returns>Employee details view</returns>
        [HttpGet]
        public async Task<IActionResult> Details(Guid id)
        {
            try
            {
                var result = await _employeeService.GetEmployeeByIdAsync(id);

                if (result.IsSuccess)
                {
                    var viewModel = MapToEmployeeViewModel(result.Data!);
                    return View(viewModel);
                }

                TempData["ErrorMessage"] = result.ErrorMessage;
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading employee details for ID: {EmployeeId}", id);
                TempData["ErrorMessage"] = "An error occurred while loading employee details";
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// Create employee page (GET)
        /// صفحة إنشاء موظف (GET)
        /// </summary>
        /// <returns>Create employee view</returns>
        [HttpGet]
        public async Task<IActionResult> Create()
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                Guid companyId;
                if (!Guid.TryParse(companyIdClaim, out companyId))
                {
                    // For demo purposes, use a default company ID
                    companyId = Guid.NewGuid();
                }

                var viewModel = new EmployeeViewModel
                {
                    CompanyId = companyId,
                    HireDate = DateTime.Today,
                    EmploymentStatus = "Active",
                    IsActive = true
                };

                await LoadCreateEditOptionsAsync(viewModel, companyId);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading create employee page");
                TempData["ErrorMessage"] = "An error occurred while loading the page";
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// Create employee (POST)
        /// إنشاء موظف (POST)
        /// </summary>
        /// <param name="model">Employee view model</param>
        /// <returns>Redirect or create view with errors</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(EmployeeViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    await LoadCreateEditOptionsAsync(model, model.CompanyId);
                    return View(model);
                }

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                Guid userId;
                if (!Guid.TryParse(userIdClaim, out userId))
                {
                    // For demo purposes, use a default user ID
                    userId = Guid.NewGuid();
                }

                var employeeDto = MapToEmployeeDto(model);
                var result = await _employeeService.CreateEmployeeAsync(employeeDto, userId);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = "Employee created successfully";
                    return RedirectToAction(nameof(Details), new { id = result.Data!.Id });
                }

                if (result.ValidationErrors != null)
                {
                    foreach (var error in result.ValidationErrors)
                    {
                        foreach (var errorMessage in error.Value)
                        {
                            ModelState.AddModelError(error.Key, errorMessage);
                        }
                    }
                }
                else
                {
                    ModelState.AddModelError("", result.ErrorMessage ?? "Failed to create employee");
                }

                await LoadCreateEditOptionsAsync(model, model.CompanyId);
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating employee");
                ModelState.AddModelError("", "An error occurred while creating the employee");
                await LoadCreateEditOptionsAsync(model, model.CompanyId);
                return View(model);
            }
        }

        /// <summary>
        /// Edit employee page (GET)
        /// صفحة تعديل الموظف (GET)
        /// </summary>
        /// <param name="id">Employee ID</param>
        /// <returns>Edit employee view</returns>
        [HttpGet]
        public async Task<IActionResult> Edit(Guid id)
        {
            try
            {
                var result = await _employeeService.GetEmployeeByIdAsync(id);

                if (result.IsSuccess)
                {
                    var viewModel = MapToEmployeeViewModel(result.Data!);
                    await LoadCreateEditOptionsAsync(viewModel, viewModel.CompanyId);
                    return View(viewModel);
                }

                TempData["ErrorMessage"] = result.ErrorMessage;
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit employee page for ID: {EmployeeId}", id);
                TempData["ErrorMessage"] = "An error occurred while loading employee data";
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// Edit employee (POST)
        /// تعديل الموظف (POST)
        /// </summary>
        /// <param name="id">Employee ID</param>
        /// <param name="model">Employee view model</param>
        /// <returns>Redirect or edit view with errors</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(Guid id, EmployeeViewModel model)
        {
            try
            {
                if (id != model.Id)
                {
                    TempData["ErrorMessage"] = "Invalid employee ID";
                    return RedirectToAction(nameof(Index));
                }

                if (!ModelState.IsValid)
                {
                    await LoadCreateEditOptionsAsync(model, model.CompanyId);
                    return View(model);
                }

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    TempData["ErrorMessage"] = "User information not found";
                    return View(model);
                }

                var employeeDto = MapToEmployeeDto(model);
                var result = await _employeeService.UpdateEmployeeAsync(id, employeeDto, userId);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = "Employee updated successfully";
                    return RedirectToAction(nameof(Details), new { id });
                }

                if (result.ValidationErrors != null)
                {
                    foreach (var error in result.ValidationErrors)
                    {
                        foreach (var errorMessage in error.Value)
                        {
                            ModelState.AddModelError(error.Key, errorMessage);
                        }
                    }
                }
                else
                {
                    ModelState.AddModelError("", result.ErrorMessage ?? "Failed to update employee");
                }

                await LoadCreateEditOptionsAsync(model, model.CompanyId);
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating employee with ID: {EmployeeId}", id);
                ModelState.AddModelError("", "An error occurred while updating the employee");
                await LoadCreateEditOptionsAsync(model, model.CompanyId);
                return View(model);
            }
        }

        // Helper methods - will be implemented in next chunk
        private Task LoadFilterOptionsAsync(EmployeeListViewModel viewModel, Guid companyId)
        {
            throw new NotImplementedException();
        }

        private Task LoadCreateEditOptionsAsync(EmployeeViewModel viewModel, Guid companyId)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// Get positions by department for AJAX calls
        /// الحصول على المناصب حسب القسم للاستدعاءات AJAX
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetPositionsByDepartment(Guid departmentId)
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    return Json(new List<SelectListItem>());
                }

                var result = await _positionService.GetPositionsByDepartmentAsync(departmentId);
                if (!result.IsSuccess || result.Data == null)
                {
                    return Json(new List<SelectListItem>());
                }

                var selectList = result.Data.Select(p => new SelectListItem
                {
                    Value = p.Id.ToString(),
                    Text = p.Title ?? p.TitleAr ?? "غير محدد"
                }).ToList();

                return Json(selectList);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting positions by department {DepartmentId}", departmentId);
                return Json(new List<SelectListItem>());
            }
        }

        /// <summary>
        /// Delete employee
        /// حذف موظف
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    return Json(new { success = false, message = "غير مصرح لك بهذا الإجراء" });
                }

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    return Json(new { success = false, message = "User information not found" });
                }

                var result = await _employeeService.DeleteEmployeeAsync(id, userId);
                if (result.IsSuccess)
                {
                    return Json(new { success = true, message = "تم حذف الموظف بنجاح" });
                }

                return Json(new { success = false, message = result.ErrorMessage });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting employee {EmployeeId}", id);
                return Json(new { success = false, message = "حدث خطأ أثناء حذف الموظف" });
            }
        }



        private static EmployeeViewModel MapToEmployeeViewModel(EmployeeDto dto)
        {
            return new EmployeeViewModel
            {
                Id = dto.Id,
                CompanyId = dto.CompanyId,
                FirstName = dto.FirstName ?? string.Empty,
                MiddleName = dto.FirstNameAr,
                LastName = dto.LastName ?? string.Empty,
                Email = dto.Email ?? string.Empty,
                PhoneNumber = dto.Phone ?? string.Empty,
                DateOfBirth = dto.DateOfBirth,
                Gender = dto.Gender,
                NationalId = dto.NationalId,
                EmployeeNumber = dto.EmployeeNumber ?? string.Empty,
                DepartmentId = dto.DepartmentId ?? Guid.Empty,
                PositionId = dto.PositionId,
                HireDate = dto.HireDate,
                Salary = dto.BasicSalary ?? 0,
                EmploymentType = dto.EmploymentType,
                Address = dto.Address,
                ProfilePicture = dto.ProfilePicture,
                IsActive = dto.IsActive,
                DepartmentName = dto.DepartmentName ?? string.Empty,
                PositionName = dto.PositionTitle ?? string.Empty,
                FirstNameAr = dto.FirstNameAr ?? string.Empty,
                LastNameAr = dto.LastNameAr ?? string.Empty,
                Phone = dto.Phone ?? string.Empty,
                PositionTitle = dto.PositionTitle ?? string.Empty,
                ManagerName = dto.ManagerName ?? string.Empty,
                EmploymentStatus = dto.IsActive ? "نشط" : "غير نشط"
            };
        }

        private static EmployeeDto MapToEmployeeDto(EmployeeViewModel viewModel)
        {
            return new EmployeeDto
            {
                Id = viewModel.Id,
                CompanyId = viewModel.CompanyId,
                FirstName = viewModel.FirstName,
                LastNameAr = viewModel.MiddleName,
                LastName = viewModel.LastName,
                Email = viewModel.Email,
                Phone = viewModel.PhoneNumber,
                DateOfBirth = viewModel.DateOfBirth,
                Gender = viewModel.Gender,
                NationalId = viewModel.NationalId,
                EmployeeNumber = viewModel.EmployeeNumber,
                DepartmentId = viewModel.DepartmentId == Guid.Empty ? null : viewModel.DepartmentId,
                PositionId = viewModel.PositionId,
                HireDate = viewModel.HireDate,
                BasicSalary = viewModel.Salary,
                EmploymentType = viewModel.EmploymentType,
                Address = viewModel.Address,
                ProfilePicture = viewModel.ProfilePicture,
                IsActive = viewModel.IsActive,
                FirstNameAr = viewModel.FirstNameAr
            };
        }
    }
}
