using FitHRPlus.Application.DTOs.CompanySettings;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Web.Models.CompanySettings
{
    /// <summary>
    /// Company settings index view model
    /// نموذج عرض صفحة إعدادات الشركة الرئيسية
    /// </summary>
    public class CompanySettingsIndexViewModel
    {
        public Guid CompanyId { get; set; }
        public CompanySettingsDto? Settings { get; set; }
        public List<WorkScheduleDto> WorkSchedules { get; set; } = new();
        public List<HolidayDto> Holidays { get; set; } = new();

        // Helper properties
        public bool HasSettings => Settings != null;
        public int TotalWorkSchedules => WorkSchedules.Count;
        public int TotalHolidays => Holidays.Count;
        public WorkScheduleDto? DefaultWorkSchedule => WorkSchedules.FirstOrDefault(ws => ws.IsDefault);
    }

    /// <summary>
    /// Work schedules view model
    /// نموذج عرض جداول العمل
    /// </summary>
    public class WorkSchedulesViewModel
    {
        public Guid CompanyId { get; set; }
        public List<WorkScheduleDto> WorkSchedules { get; set; } = new();

        // Helper properties
        public int TotalSchedules => WorkSchedules.Count;
        public WorkScheduleDto? DefaultSchedule => WorkSchedules.FirstOrDefault(ws => ws.IsDefault);
        public bool HasDefaultSchedule => DefaultSchedule != null;
    }

    /// <summary>
    /// Create work schedule view model
    /// نموذج عرض إنشاء جدول العمل
    /// </summary>
    public class CreateWorkScheduleViewModel
    {
        public CreateWorkScheduleDto CreateDto { get; set; } = new();

        // Helper properties for UI
        public List<SelectListItem> DayOfWeekOptions { get; set; } = new()
        {
            new SelectListItem { Value = "0", Text = "Sunday / الأحد" },
            new SelectListItem { Value = "1", Text = "Monday / الاثنين" },
            new SelectListItem { Value = "2", Text = "Tuesday / الثلاثاء" },
            new SelectListItem { Value = "3", Text = "Wednesday / الأربعاء" },
            new SelectListItem { Value = "4", Text = "Thursday / الخميس" },
            new SelectListItem { Value = "5", Text = "Friday / الجمعة" },
            new SelectListItem { Value = "6", Text = "Saturday / السبت" }
        };

        public List<SelectListItem> TimeOptions { get; set; } = GenerateTimeOptions();

        private static List<SelectListItem> GenerateTimeOptions()
        {
            var options = new List<SelectListItem>();
            for (int hour = 0; hour < 24; hour++)
            {
                for (int minute = 0; minute < 60; minute += 30)
                {
                    var time = new TimeSpan(hour, minute, 0);
                    options.Add(new SelectListItem
                    {
                        Value = time.ToString(@"hh\:mm"),
                        Text = time.ToString(@"hh\:mm")
                    });
                }
            }
            return options;
        }
    }

    /// <summary>
    /// Edit work schedule view model
    /// نموذج عرض تعديل جدول العمل
    /// </summary>
    public class EditWorkScheduleViewModel
    {
        public WorkScheduleDto? Schedule { get; set; }
        public UpdateWorkScheduleDto UpdateDto { get; set; } = new();

        // Helper properties for UI
        public List<SelectListItem> DayOfWeekOptions { get; set; } = new()
        {
            new SelectListItem { Value = "0", Text = "Sunday / الأحد" },
            new SelectListItem { Value = "1", Text = "Monday / الاثنين" },
            new SelectListItem { Value = "2", Text = "Tuesday / الثلاثاء" },
            new SelectListItem { Value = "3", Text = "Wednesday / الأربعاء" },
            new SelectListItem { Value = "4", Text = "Thursday / الخميس" },
            new SelectListItem { Value = "5", Text = "Friday / الجمعة" },
            new SelectListItem { Value = "6", Text = "Saturday / السبت" }
        };

        public List<SelectListItem> TimeOptions { get; set; } = GenerateTimeOptions();

        private static List<SelectListItem> GenerateTimeOptions()
        {
            var options = new List<SelectListItem>();
            for (int hour = 0; hour < 24; hour++)
            {
                for (int minute = 0; minute < 60; minute += 30)
                {
                    var time = new TimeSpan(hour, minute, 0);
                    options.Add(new SelectListItem
                    {
                        Value = time.ToString(@"hh\:mm"),
                        Text = time.ToString(@"hh\:mm")
                    });
                }
            }
            return options;
        }
    }

    /// <summary>
    /// Holidays management view model
    /// نموذج عرض إدارة العطل
    /// </summary>
    public class HolidaysViewModel
    {
        public Guid CompanyId { get; set; }
        public List<HolidayDto> Holidays { get; set; } = new();
        public int Year { get; set; } = DateTime.Now.Year;

        // Filter properties
        public string? SearchTerm { get; set; }
        public string? HolidayType { get; set; }

        // Helper properties
        public int TotalHolidays => Holidays.Count;
        public List<HolidayDto> UpcomingHolidays => Holidays
            .Where(h => h.Date >= DateTime.Today)
            .OrderBy(h => h.Date)
            .Take(5)
            .ToList();

        public List<SelectListItem> YearOptions { get; set; } = GenerateYearOptions();
        public List<SelectListItem> HolidayTypeOptions { get; set; } = new()
        {
            new SelectListItem { Value = "", Text = "All Types / جميع الأنواع" },
            new SelectListItem { Value = "National", Text = "National / وطنية" },
            new SelectListItem { Value = "Religious", Text = "Religious / دينية" },
            new SelectListItem { Value = "Company", Text = "Company / الشركة" }
        };

        private static List<SelectListItem> GenerateYearOptions()
        {
            var options = new List<SelectListItem>();
            var currentYear = DateTime.Now.Year;
            for (int year = currentYear - 2; year <= currentYear + 5; year++)
            {
                options.Add(new SelectListItem
                {
                    Value = year.ToString(),
                    Text = year.ToString(),
                    Selected = year == currentYear
                });
            }
            return options;
        }
    }

    /// <summary>
    /// Create holiday view model
    /// نموذج عرض إنشاء العطلة
    /// </summary>
    public class CreateHolidayViewModel
    {
        public CreateHolidayDto CreateDto { get; set; } = new();

        public List<SelectListItem> RecurrenceTypeOptions { get; set; } = new()
        {
            new SelectListItem { Value = "", Text = "None / لا يوجد" },
            new SelectListItem { Value = "Yearly", Text = "Yearly / سنوياً" },
            new SelectListItem { Value = "Monthly", Text = "Monthly / شهرياً" }
        };

        public List<SelectListItem> AppliesToOptions { get; set; } = new()
        {
            new SelectListItem { Value = "All", Text = "All Employees / جميع الموظفين" },
            new SelectListItem { Value = "FullTime", Text = "Full-time Only / دوام كامل فقط" },
            new SelectListItem { Value = "PartTime", Text = "Part-time Only / دوام جزئي فقط" }
        };
    }

    /// <summary>
    /// Edit holiday view model
    /// نموذج عرض تعديل العطلة
    /// </summary>
    public class EditHolidayViewModel
    {
        public HolidayDto? Holiday { get; set; }
        public UpdateHolidayDto UpdateDto { get; set; } = new();

        public List<SelectListItem> RecurrenceTypeOptions { get; set; } = new()
        {
            new SelectListItem { Value = "", Text = "None / لا يوجد" },
            new SelectListItem { Value = "Yearly", Text = "Yearly / سنوياً" },
            new SelectListItem { Value = "Monthly", Text = "Monthly / شهرياً" }
        };

        public List<SelectListItem> AppliesToOptions { get; set; } = new()
        {
            new SelectListItem { Value = "All", Text = "All Employees / جميع الموظفين" },
            new SelectListItem { Value = "FullTime", Text = "Full-time Only / دوام كامل فقط" },
            new SelectListItem { Value = "PartTime", Text = "Part-time Only / دوام جزئي فقط" }
        };
    }

    /// <summary>
    /// Company settings form view model
    /// نموذج عرض نموذج إعدادات الشركة
    /// </summary>
    public class CompanySettingsFormViewModel
    {
        public CreateUpdateCompanySettingsDto Settings { get; set; } = new();

        // Helper properties for UI
        public List<SelectListItem> WeekendDaysOptions { get; set; } = new()
        {
            new SelectListItem { Value = "Sunday", Text = "Sunday / الأحد" },
            new SelectListItem { Value = "Monday", Text = "Monday / الاثنين" },
            new SelectListItem { Value = "Tuesday", Text = "Tuesday / الثلاثاء" },
            new SelectListItem { Value = "Wednesday", Text = "Wednesday / الأربعاء" },
            new SelectListItem { Value = "Thursday", Text = "Thursday / الخميس" },
            new SelectListItem { Value = "Friday", Text = "Friday / الجمعة" },
            new SelectListItem { Value = "Saturday", Text = "Saturday / السبت" }
        };

        public List<SelectListItem> CurrencyOptions { get; set; } = new()
        {
            new SelectListItem { Value = "EGP", Text = "Egyptian Pound (EGP) / الجنيه المصري" },
            new SelectListItem { Value = "USD", Text = "US Dollar (USD) / الدولار الأمريكي" },
            new SelectListItem { Value = "EUR", Text = "Euro (EUR) / اليورو" },
            new SelectListItem { Value = "SAR", Text = "Saudi Riyal (SAR) / الريال السعودي" },
            new SelectListItem { Value = "AED", Text = "UAE Dirham (AED) / الدرهم الإماراتي" }
        };

        public List<SelectListItem> LanguageOptions { get; set; } = new()
        {
            new SelectListItem { Value = "ar", Text = "Arabic / العربية" },
            new SelectListItem { Value = "en", Text = "English / الإنجليزية" }
        };

        public List<SelectListItem> TimeZoneOptions { get; set; } = new()
        {
            new SelectListItem { Value = "Africa/Cairo", Text = "Cairo (UTC+2) / القاهرة" },
            new SelectListItem { Value = "Asia/Riyadh", Text = "Riyadh (UTC+3) / الرياض" },
            new SelectListItem { Value = "Asia/Dubai", Text = "Dubai (UTC+4) / دبي" },
            new SelectListItem { Value = "UTC", Text = "UTC (UTC+0) / التوقيت العالمي" }
        };

        public List<SelectListItem> DateFormatOptions { get; set; } = new()
        {
            new SelectListItem { Value = "dd/MM/yyyy", Text = "DD/MM/YYYY" },
            new SelectListItem { Value = "MM/dd/yyyy", Text = "MM/DD/YYYY" },
            new SelectListItem { Value = "yyyy-MM-dd", Text = "YYYY-MM-DD" },
            new SelectListItem { Value = "dd-MM-yyyy", Text = "DD-MM-YYYY" }
        };

        public List<SelectListItem> TimeFormatOptions { get; set; } = new()
        {
            new SelectListItem { Value = "HH:mm", Text = "24 Hour (HH:MM)" },
            new SelectListItem { Value = "hh:mm tt", Text = "12 Hour (hh:mm AM/PM)" }
        };

        public List<SelectListItem> TimeOptions { get; set; } = GenerateTimeOptions();

        private static List<SelectListItem> GenerateTimeOptions()
        {
            var options = new List<SelectListItem>();
            for (int hour = 0; hour < 24; hour++)
            {
                for (int minute = 0; minute < 60; minute += 30)
                {
                    var time = new TimeSpan(hour, minute, 0);
                    options.Add(new SelectListItem
                    {
                        Value = time.ToString(@"hh\:mm"),
                        Text = time.ToString(@"hh\:mm")
                    });
                }
            }
            return options;
        }
    }
}
