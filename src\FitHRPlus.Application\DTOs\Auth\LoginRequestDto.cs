using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Application.DTOs.Auth
{
    /// <summary>
    /// Login request data transfer object
    /// كائنة نقل البيانات لطلب تسجيل الدخول
    /// </summary>
    public class LoginRequestDto
    {
        /// <summary>
        /// Username or email address
        /// اسم المستخدم أو البريد الإلكتروني
        /// </summary>
        [Required(ErrorMessage = "Username or email is required")]
        [StringLength(100, ErrorMessage = "Username or email cannot exceed 100 characters")]
        public string UsernameOrEmail { get; set; } = string.Empty;

        /// <summary>
        /// User password
        /// كلمة مرور المستخدم
        /// </summary>
        [Required(ErrorMessage = "Password is required")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "Password must be between 6 and 100 characters")]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// Remember me option
        /// خيار تذكرني
        /// </summary>
        public bool RememberMe { get; set; } = false;

        /// <summary>
        /// Two-factor authentication code (if enabled)
        /// رمز المصادقة الثنائية (إذا كان مفعلاً)
        /// </summary>
        [StringLength(10, ErrorMessage = "Two-factor code cannot exceed 10 characters")]
        public string? TwoFactorCode { get; set; }
    }
}
