using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using FitHRPlus.Application.Interfaces;
using FitHRPlus.Application.DTOs.CompanySettings;
using FitHRPlus.Web.Models.CompanySettings;

namespace FitHRPlus.Web.Controllers
{
    /// <summary>
    /// Company settings controller
    /// تحكم إعدادات الشركة
    /// </summary>
    [Authorize]
    public class CompanySettingsController : Controller
    {
        private readonly ICompanySettingsService _companySettingsService;
        private readonly ILogger<CompanySettingsController> _logger;

        public CompanySettingsController(
            ICompanySettingsService companySettingsService,
            ILogger<CompanySettingsController> logger)
        {
            _companySettingsService = companySettingsService;
            _logger = logger;
        }

        /// <summary>
        /// Company settings index page
        /// صفحة إعدادات الشركة الرئيسية
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Index()
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "Company information not found";
                    return RedirectToAction("Index", "Home");
                }

                var settings = await _companySettingsService.GetCompanySettingsAsync(companyId);
                var workSchedules = await _companySettingsService.GetWorkSchedulesAsync(companyId);
                var holidays = await _companySettingsService.GetHolidaysAsync(companyId, DateTime.Now.Year);

                var viewModel = new CompanySettingsIndexViewModel
                {
                    CompanyId = companyId,
                    Settings = settings,
                    WorkSchedules = workSchedules,
                    Holidays = holidays
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading company settings");
                TempData["ErrorMessage"] = "An error occurred while loading company settings";
                return RedirectToAction("Index", "Home");
            }
        }

        /// <summary>
        /// Update company settings
        /// تحديث إعدادات الشركة
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateSettings(CreateUpdateCompanySettingsDto dto)
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "Company information not found";
                    return RedirectToAction("Index");
                }

                if (!ModelState.IsValid)
                {
                    TempData["ErrorMessage"] = "Please correct the validation errors";
                    return RedirectToAction("Index");
                }

                await _companySettingsService.CreateOrUpdateCompanySettingsAsync(companyId, dto);
                TempData["SuccessMessage"] = "Company settings updated successfully";

                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating company settings");
                TempData["ErrorMessage"] = "An error occurred while updating company settings";
                return RedirectToAction("Index");
            }
        }

        /// <summary>
        /// Work schedules management page
        /// صفحة إدارة جداول العمل
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> WorkSchedules()
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "Company information not found";
                    return RedirectToAction("Index", "Home");
                }

                var workSchedules = await _companySettingsService.GetWorkSchedulesAsync(companyId);

                var viewModel = new WorkSchedulesViewModel
                {
                    CompanyId = companyId,
                    WorkSchedules = workSchedules
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading work schedules");
                TempData["ErrorMessage"] = "An error occurred while loading work schedules";
                return RedirectToAction("Index");
            }
        }

        /// <summary>
        /// Create work schedule page
        /// صفحة إنشاء جدول عمل
        /// </summary>
        [HttpGet]
        public IActionResult CreateWorkSchedule()
        {
            var viewModel = new CreateWorkScheduleViewModel();
            return View(viewModel);
        }

        /// <summary>
        /// Create work schedule
        /// إنشاء جدول عمل
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateWorkSchedule(CreateWorkScheduleDto dto)
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "Company information not found";
                    return RedirectToAction("WorkSchedules");
                }

                if (!ModelState.IsValid)
                {
                    var viewModel = new CreateWorkScheduleViewModel { CreateDto = dto };
                    return View(viewModel);
                }

                await _companySettingsService.CreateWorkScheduleAsync(companyId, dto);
                TempData["SuccessMessage"] = "Work schedule created successfully";

                return RedirectToAction("WorkSchedules");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating work schedule");
                TempData["ErrorMessage"] = "An error occurred while creating work schedule";
                var viewModel = new CreateWorkScheduleViewModel { CreateDto = dto };
                return View(viewModel);
            }
        }

        /// <summary>
        /// Edit work schedule page
        /// صفحة تعديل جدول العمل
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> EditWorkSchedule(Guid id)
        {
            try
            {
                var schedule = await _companySettingsService.GetWorkScheduleByIdAsync(id);
                if (schedule == null)
                {
                    TempData["ErrorMessage"] = "Work schedule not found";
                    return RedirectToAction("WorkSchedules");
                }

                var viewModel = new EditWorkScheduleViewModel
                {
                    Schedule = schedule,
                    UpdateDto = new UpdateWorkScheduleDto
                    {
                        Name = schedule.Name,
                        NameAr = schedule.NameAr,
                        Description = schedule.Description,
                        DescriptionAr = schedule.DescriptionAr,
                        IsDefault = schedule.IsDefault,
                        WorkScheduleDays = schedule.WorkScheduleDays.Select(d => new UpdateWorkScheduleDayDto
                        {
                            Id = d.Id,
                            DayOfWeek = d.DayOfWeek,
                            IsWorkingDay = d.IsWorkingDay,
                            StartTime = d.StartTime,
                            EndTime = d.EndTime,
                            BreakStartTime = d.BreakStartTime,
                            BreakEndTime = d.BreakEndTime,
                            WorkingHours = d.WorkingHours
                        }).ToList()
                    }
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading work schedule for editing");
                TempData["ErrorMessage"] = "An error occurred while loading work schedule";
                return RedirectToAction("WorkSchedules");
            }
        }

        /// <summary>
        /// Update work schedule
        /// تحديث جدول العمل
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditWorkSchedule(Guid id, UpdateWorkScheduleDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var schedule = await _companySettingsService.GetWorkScheduleByIdAsync(id);
                    var viewModel = new EditWorkScheduleViewModel
                    {
                        Schedule = schedule,
                        UpdateDto = dto
                    };
                    return View(viewModel);
                }

                await _companySettingsService.UpdateWorkScheduleAsync(id, dto);
                TempData["SuccessMessage"] = "Work schedule updated successfully";

                return RedirectToAction("WorkSchedules");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating work schedule");
                TempData["ErrorMessage"] = "An error occurred while updating work schedule";
                
                var schedule = await _companySettingsService.GetWorkScheduleByIdAsync(id);
                var viewModel = new EditWorkScheduleViewModel
                {
                    Schedule = schedule,
                    UpdateDto = dto
                };
                return View(viewModel);
            }
        }

        /// <summary>
        /// Delete work schedule
        /// حذف جدول العمل
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteWorkSchedule(Guid id)
        {
            try
            {
                var result = await _companySettingsService.DeleteWorkScheduleAsync(id);
                if (result)
                {
                    TempData["SuccessMessage"] = "Work schedule deleted successfully";
                }
                else
                {
                    TempData["ErrorMessage"] = "Work schedule not found";
                }

                return RedirectToAction("WorkSchedules");
            }
            catch (InvalidOperationException ex)
            {
                TempData["ErrorMessage"] = ex.Message;
                return RedirectToAction("WorkSchedules");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting work schedule");
                TempData["ErrorMessage"] = "An error occurred while deleting work schedule";
                return RedirectToAction("WorkSchedules");
            }
        }

        /// <summary>
        /// Set default work schedule
        /// تعيين جدول العمل الافتراضي
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SetDefaultWorkSchedule(Guid id)
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "Company information not found";
                    return RedirectToAction("WorkSchedules");
                }

                var result = await _companySettingsService.SetDefaultWorkScheduleAsync(companyId, id);
                if (result)
                {
                    TempData["SuccessMessage"] = "Default work schedule set successfully";
                }
                else
                {
                    TempData["ErrorMessage"] = "Work schedule not found";
                }

                return RedirectToAction("WorkSchedules");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting default work schedule");
                TempData["ErrorMessage"] = "An error occurred while setting default work schedule";
                return RedirectToAction("WorkSchedules");
            }
        }
    }
}
