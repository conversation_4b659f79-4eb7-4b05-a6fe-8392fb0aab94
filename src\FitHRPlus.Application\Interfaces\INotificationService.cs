using FitHRPlus.Application.DTOs.Common;
using FitHRPlus.Application.DTOs.Notification;
using FitHRPlus.Application.Common;

namespace FitHRPlus.Application.Interfaces
{
    /// <summary>
    /// Notification service interface
    /// واجهة خدمة الإشعارات
    /// </summary>
    public interface INotificationService
    {
        /// <summary>
        /// Get paginated list of notifications
        /// الحصول على قائمة مقسمة للإشعارات
        /// </summary>
        /// <param name="request">List request parameters</param>
        /// <returns>Paginated notifications</returns>
        Task<ServiceResult<PaginatedResult<NotificationDto>>> GetNotificationsAsync(NotificationListDto request);

        /// <summary>
        /// Get notification by ID
        /// الحصول على الإشعار بالمعرف
        /// </summary>
        /// <param name="id">Notification ID</param>
        /// <returns>Notification details</returns>
        Task<ServiceResult<NotificationDto>> GetNotificationByIdAsync(Guid id);

        /// <summary>
        /// Create new notification
        /// إنشاء إشعار جديد
        /// </summary>
        /// <param name="request">Create request data</param>
        /// <returns>Created notification</returns>
        Task<ServiceResult<NotificationDto>> CreateNotificationAsync(CreateNotificationDto request);

        /// <summary>
        /// Create notification from template
        /// إنشاء إشعار من قالب
        /// </summary>
        /// <param name="templateId">Template ID</param>
        /// <param name="userId">User ID</param>
        /// <param name="parameters">Template parameters</param>
        /// <returns>Created notification</returns>
        Task<ServiceResult<NotificationDto>> CreateNotificationFromTemplateAsync(Guid templateId, Guid userId, Dictionary<string, string> parameters);

        /// <summary>
        /// Mark notification as read
        /// تسجيل الإشعار كمقروء
        /// </summary>
        /// <param name="notificationId">Notification ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>Success result</returns>
        Task<ServiceResult<bool>> MarkAsReadAsync(Guid notificationId, Guid userId);

        /// <summary>
        /// Mark notification as unread
        /// تسجيل الإشعار كغير مقروء
        /// </summary>
        /// <param name="notificationId">Notification ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>Success result</returns>
        Task<ServiceResult<bool>> MarkAsUnreadAsync(Guid notificationId, Guid userId);

        /// <summary>
        /// Mark all notifications as read for user
        /// تسجيل جميع الإشعارات كمقروءة للمستخدم
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Number of notifications marked as read</returns>
        Task<ServiceResult<int>> MarkAllAsReadAsync(Guid userId);

        /// <summary>
        /// Delete notification
        /// حذف الإشعار
        /// </summary>
        /// <param name="notificationId">Notification ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>Success result</returns>
        Task<ServiceResult<bool>> DeleteNotificationAsync(Guid notificationId, Guid userId);

        /// <summary>
        /// Bulk action on notifications
        /// إجراء مجمع على الإشعارات
        /// </summary>
        /// <param name="request">Bulk action request</param>
        /// <param name="userId">User ID</param>
        /// <returns>Number of affected notifications</returns>
        Task<ServiceResult<int>> BulkActionAsync(BulkNotificationActionDto request, Guid userId);

        /// <summary>
        /// Get notification statistics for user
        /// الحصول على إحصائيات الإشعارات للمستخدم
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Notification statistics</returns>
        Task<ServiceResult<NotificationStatisticsDto>> GetNotificationStatisticsAsync(Guid userId);

        /// <summary>
        /// Get unread notification count for user
        /// الحصول على عدد الإشعارات غير المقروءة للمستخدم
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Unread count</returns>
        Task<ServiceResult<int>> GetUnreadCountAsync(Guid userId);

        /// <summary>
        /// Get recent notifications for user
        /// الحصول على الإشعارات الحديثة للمستخدم
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="limit">Number of notifications to return</param>
        /// <returns>Recent notifications</returns>
        Task<ServiceResult<List<NotificationDto>>> GetRecentNotificationsAsync(Guid userId, int limit = 10);

        /// <summary>
        /// Send notification to user
        /// إرسال إشعار للمستخدم
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="title">Notification title</param>
        /// <param name="message">Notification message</param>
        /// <param name="type">Notification type</param>
        /// <param name="category">Notification category</param>
        /// <param name="actionUrl">Action URL (optional)</param>
        /// <returns>Created notification</returns>
        Task<ServiceResult<NotificationDto>> SendNotificationAsync(Guid userId, string title, string message, string type = "Info", string category = "System", string? actionUrl = null);

        /// <summary>
        /// Send notification to multiple users
        /// إرسال إشعار لعدة مستخدمين
        /// </summary>
        /// <param name="userIds">List of user IDs</param>
        /// <param name="title">Notification title</param>
        /// <param name="message">Notification message</param>
        /// <param name="type">Notification type</param>
        /// <param name="category">Notification category</param>
        /// <param name="actionUrl">Action URL (optional)</param>
        /// <returns>Number of notifications sent</returns>
        Task<ServiceResult<int>> SendBulkNotificationAsync(List<Guid> userIds, string title, string message, string type = "Info", string category = "System", string? actionUrl = null);

        /// <summary>
        /// Send notification to all users in company
        /// إرسال إشعار لجميع المستخدمين في الشركة
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="title">Notification title</param>
        /// <param name="message">Notification message</param>
        /// <param name="type">Notification type</param>
        /// <param name="category">Notification category</param>
        /// <param name="actionUrl">Action URL (optional)</param>
        /// <returns>Number of notifications sent</returns>
        Task<ServiceResult<int>> SendCompanyNotificationAsync(Guid companyId, string title, string message, string type = "Info", string category = "System", string? actionUrl = null);

        /// <summary>
        /// Clean up expired notifications
        /// تنظيف الإشعارات المنتهية الصلاحية
        /// </summary>
        /// <returns>Number of notifications cleaned up</returns>
        Task<ServiceResult<int>> CleanupExpiredNotificationsAsync();

        /// <summary>
        /// Get notification templates
        /// الحصول على قوالب الإشعارات
        /// </summary>
        /// <param name="category">Category filter (optional)</param>
        /// <returns>List of notification templates</returns>
        Task<ServiceResult<List<NotificationTemplateDto>>> GetNotificationTemplatesAsync(string? category = null);

        /// <summary>
        /// Create notification template
        /// إنشاء قالب إشعار
        /// </summary>
        /// <param name="request">Create template request</param>
        /// <returns>Created template</returns>
        Task<ServiceResult<NotificationTemplateDto>> CreateNotificationTemplateAsync(CreateNotificationTemplateDto request);

        /// <summary>
        /// Get user notification preferences
        /// الحصول على تفضيلات إشعارات المستخدم
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Notification preferences</returns>
        Task<ServiceResult<NotificationPreferencesDto>> GetNotificationPreferencesAsync(Guid userId);

        /// <summary>
        /// Update user notification preferences
        /// تحديث تفضيلات إشعارات المستخدم
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="request">Update preferences request</param>
        /// <returns>Updated preferences</returns>
        Task<ServiceResult<NotificationPreferencesDto>> UpdateNotificationPreferencesAsync(Guid userId, UpdateNotificationPreferencesDto request);

        /// <summary>
        /// Send email notification
        /// إرسال إشعار بريد إلكتروني
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="subject">Email subject</param>
        /// <param name="body">Email body</param>
        /// <returns>Success result</returns>
        Task<ServiceResult<bool>> SendEmailNotificationAsync(Guid userId, string subject, string body);

        /// <summary>
        /// Send push notification
        /// إرسال إشعار فوري
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="title">Notification title</param>
        /// <param name="body">Notification body</param>
        /// <param name="data">Additional data</param>
        /// <returns>Success result</returns>
        Task<ServiceResult<bool>> SendPushNotificationAsync(Guid userId, string title, string body, Dictionary<string, string>? data = null);

        /// <summary>
        /// Send SMS notification
        /// إرسال إشعار رسالة نصية
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="message">SMS message</param>
        /// <returns>Success result</returns>
        Task<ServiceResult<bool>> SendSmsNotificationAsync(Guid userId, string message);
    }
}
