@{
    ViewData["Title"] = "Monthly Attendance Report / التقرير الشهري للحضور";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        Monthly Attendance Report / التقرير الشهري للحضور
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Report Date Selection -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <form method="get" class="d-flex align-items-end gap-3">
                                <div class="flex-grow-1">
                                    <label for="year" class="form-label">Year / السنة</label>
                                    <select class="form-select" id="year" name="year">
                                        @for (int i = DateTime.Today.Year; i >= DateTime.Today.Year - 5; i--)
                                        {
                                            <option value="@i" selected="@(i == ViewBag.ReportYear)">@i</option>
                                        }
                                    </select>
                                </div>
                                <div class="flex-grow-1">
                                    <label for="month" class="form-label">Month / الشهر</label>
                                    <select class="form-select" id="month" name="month">
                                        @for (int i = 1; i <= 12; i++)
                                        {
                                            var monthName = new DateTime(2023, i, 1).ToString("MMMM");
                                            <option value="@i" selected="@(i == ViewBag.ReportMonth)">@i - @monthName</option>
                                        }
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>
                                    Generate / إنشاء
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Report Content -->
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Report Period:</strong> @(new DateTime((int)ViewBag.ReportYear, (int)ViewBag.ReportMonth, 1).ToString("MMMM yyyy"))
                                <br>
                                <strong>فترة التقرير:</strong> @(new DateTime((int)ViewBag.ReportYear, (int)ViewBag.ReportMonth, 1).ToString("MMMM yyyy", new System.Globalization.CultureInfo("ar-EG")))
                            </div>
                        </div>
                    </div>

                    <!-- Monthly Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Total Days / إجمالي الأيام</h6>
                                    <h3 class="mb-0">@DateTime.DaysInMonth((int)ViewBag.ReportYear, (int)ViewBag.ReportMonth)</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Working Days / أيام العمل</h6>
                                    <h3 class="mb-0">22</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Avg Attendance / متوسط الحضور</h6>
                                    <h3 class="mb-0">85%</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Late Days / أيام التأخير</h6>
                                    <h3 class="mb-0">0</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Absent Days / أيام الغياب</h6>
                                    <h3 class="mb-0">0</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Leave Days / أيام الإجازة</h6>
                                    <h3 class="mb-0">0</h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Employee Summary Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Employee / الموظف</th>
                                    <th>Department / القسم</th>
                                    <th>Present Days / أيام الحضور</th>
                                    <th>Absent Days / أيام الغياب</th>
                                    <th>Late Days / أيام التأخير</th>
                                    <th>Leave Days / أيام الإجازة</th>
                                    <th>Total Hours / إجمالي الساعات</th>
                                    <th>Attendance % / نسبة الحضور</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="fas fa-info-circle me-2"></i>
                                        No attendance data available for this month / لا توجد بيانات حضور لهذا الشهر
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Charts Section -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Daily Attendance Trend / اتجاه الحضور اليومي</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="attendanceChart" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Department Attendance / حضور الأقسام</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="departmentChart" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button class="btn btn-success" onclick="exportToExcel()">
                                    <i class="fas fa-file-excel me-1"></i>
                                    Export to Excel / تصدير إلى Excel
                                </button>
                                <button class="btn btn-danger" onclick="exportToPDF()">
                                    <i class="fas fa-file-pdf me-1"></i>
                                    Export to PDF / تصدير إلى PDF
                                </button>
                                <button class="btn btn-primary" onclick="printReport()">
                                    <i class="fas fa-print me-1"></i>
                                    Print / طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function exportToExcel() {
        alert('Excel export functionality will be implemented / سيتم تنفيذ وظيفة تصدير Excel');
    }

    function exportToPDF() {
        alert('PDF export functionality will be implemented / سيتم تنفيذ وظيفة تصدير PDF');
    }

    function printReport() {
        window.print();
    }

    // Initialize charts when page loads
    document.addEventListener('DOMContentLoaded', function() {
        // Placeholder for chart initialization
        console.log('Charts will be initialized here');
    });
</script>
