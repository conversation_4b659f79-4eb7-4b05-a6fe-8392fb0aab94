using FitHRPlus.Application.DTOs.Payroll;
using FitHRPlus.Application.Interfaces;
using FitHRPlus.Web.Models.Payroll;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace FitHRPlus.Web.Controllers
{
    /// <summary>
    /// Payroll management controller
    /// وحدة تحكم إدارة كشوف المرتبات
    /// </summary>
    [Authorize]
    public class PayrollController : Controller
    {
        private readonly IPayrollService _payrollService;
        private readonly ILogger<PayrollController> _logger;

        public PayrollController(
            IPayrollService payrollService,
            ILogger<PayrollController> logger)
        {
            _payrollService = payrollService;
            _logger = logger;
        }

        /// <summary>
        /// Payroll list
        /// قائمة كشوف المرتبات
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Index([FromQuery] PayrollListDto request)
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "Company information not found";
                    return RedirectToAction("Index", "Home");
                }

                request.CompanyId = companyId;

                // Set default date range if not provided
                if (!request.PayrollMonth.HasValue && !request.PayrollYear.HasValue)
                {
                    request.PayrollMonth = DateTime.Today.Month;
                    request.PayrollYear = DateTime.Today.Year;
                }

                var result = await _payrollService.GetPayrollsAsync(request);

                if (result.IsSuccess)
                {
                    var viewModel = new PayrollListViewModel
                    {
                        Payrolls = result.Data.Items.Select(MapToPayrollViewModel).ToList(),
                        PayrollMonth = request.PayrollMonth,
                        PayrollYear = request.PayrollYear,
                        Status = request.Status,
                        EmployeeId = request.EmployeeId,
                        DepartmentId = request.DepartmentId,
                        SearchTerm = request.SearchTerm,
                        CurrentPage = result.Data.CurrentPage,
                        TotalPages = result.Data.TotalPages,
                        TotalCount = result.Data.TotalCount,
                        PageSize = result.Data.PageSize
                    };

                    // Load filter options
                    await LoadFilterOptionsAsync(viewModel, companyId);

                    return View(viewModel);
                }

                TempData["ErrorMessage"] = result.ErrorMessage;
                return View(new PayrollListViewModel());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading payrolls");
                TempData["ErrorMessage"] = "An error occurred while loading payrolls";
                return View(new PayrollListViewModel());
            }
        }

        /// <summary>
        /// Payroll details
        /// تفاصيل كشف المرتبات
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Details(Guid id)
        {
            try
            {
                var result = await _payrollService.GetPayrollByIdAsync(id);

                if (result.IsSuccess && result.Data != null)
                {
                    var viewModel = MapToPayrollDetailsViewModel(result.Data);
                    return View(viewModel);
                }

                TempData["ErrorMessage"] = result.ErrorMessage ?? "Payroll not found";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading payroll details for ID: {Id}", id);
                TempData["ErrorMessage"] = "An error occurred while loading payroll details";
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// Generate payrolls for all employees
        /// إنشاء كشوف المرتبات لجميع الموظفين
        /// </summary>
        [HttpGet]
        public IActionResult Generate()
        {
            var viewModel = new GeneratePayrollViewModel
            {
                PayrollMonth = DateTime.Today.Month,
                PayrollYear = DateTime.Today.Year
            };

            return View(viewModel);
        }

        /// <summary>
        /// Generate payrolls for all employees - POST
        /// إنشاء كشوف المرتبات لجميع الموظفين - POST
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Generate(GeneratePayrollViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return View(model);
                }

                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    ModelState.AddModelError("", "Company information not found");
                    return View(model);
                }

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    ModelState.AddModelError("", "User information not found");
                    return View(model);
                }

                var result = await _payrollService.GeneratePayrollsAsync(companyId, model.PayrollMonth, model.PayrollYear, userId);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = $"Successfully generated {result.Data} payroll records";
                    return RedirectToAction(nameof(Index), new { payrollMonth = model.PayrollMonth, payrollYear = model.PayrollYear });
                }

                ModelState.AddModelError("", result.ErrorMessage ?? "Failed to generate payrolls");
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating payrolls");
                ModelState.AddModelError("", "An error occurred while generating payrolls");
                return View(model);
            }
        }

        /// <summary>
        /// Process payroll
        /// معالجة كشف المرتبات
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Process(Guid id, string? notes)
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    TempData["ErrorMessage"] = "User information not found";
                    return RedirectToAction(nameof(Details), new { id });
                }

                var request = new ProcessPayrollDto
                {
                    PayrollId = id,
                    Notes = notes
                };

                var result = await _payrollService.ProcessPayrollAsync(request, userId);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = "Payroll processed successfully";
                }
                else
                {
                    TempData["ErrorMessage"] = result.ErrorMessage ?? "Failed to process payroll";
                }

                return RedirectToAction(nameof(Details), new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing payroll: {Id}", id);
                TempData["ErrorMessage"] = "An error occurred while processing the payroll";
                return RedirectToAction(nameof(Details), new { id });
            }
        }

        /// <summary>
        /// Approve payroll
        /// الموافقة على كشف المرتبات
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Approve(Guid id, string? notes)
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    TempData["ErrorMessage"] = "User information not found";
                    return RedirectToAction(nameof(Details), new { id });
                }

                var request = new ApprovePayrollDto
                {
                    PayrollId = id,
                    Notes = notes
                };

                var result = await _payrollService.ApprovePayrollAsync(request, userId);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = "Payroll approved successfully";
                }
                else
                {
                    TempData["ErrorMessage"] = result.ErrorMessage ?? "Failed to approve payroll";
                }

                return RedirectToAction(nameof(Details), new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving payroll: {Id}", id);
                TempData["ErrorMessage"] = "An error occurred while approving the payroll";
                return RedirectToAction(nameof(Details), new { id });
            }
        }

        /// <summary>
        /// Mark payroll as paid
        /// تسجيل كشف المرتبات كمدفوع
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Pay(Guid id, string? notes, string? paymentMethod, string? paymentReference)
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    TempData["ErrorMessage"] = "User information not found";
                    return RedirectToAction(nameof(Details), new { id });
                }

                var request = new PayPayrollDto
                {
                    PayrollId = id,
                    Notes = notes,
                    PaymentMethod = paymentMethod,
                    PaymentReference = paymentReference
                };

                var result = await _payrollService.PayPayrollAsync(request, userId);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = "Payroll marked as paid successfully";
                }
                else
                {
                    TempData["ErrorMessage"] = result.ErrorMessage ?? "Failed to mark payroll as paid";
                }

                return RedirectToAction(nameof(Details), new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking payroll as paid: {Id}", id);
                TempData["ErrorMessage"] = "An error occurred while marking the payroll as paid";
                return RedirectToAction(nameof(Details), new { id });
            }
        }

        // Helper methods
        private async Task LoadFilterOptionsAsync(PayrollListViewModel viewModel, Guid companyId)
        {
            // Implementation will be added
        }

        private static PayrollViewModel MapToPayrollViewModel(PayrollDto dto)
        {
            return new PayrollViewModel
            {
                Id = dto.Id,
                EmployeeId = dto.EmployeeId,
                EmployeeName = dto.EmployeeName,
                EmployeeNameAr = dto.EmployeeNameAr,
                EmployeeCode = dto.EmployeeCode,
                DepartmentName = dto.DepartmentName,
                DepartmentNameAr = dto.DepartmentNameAr,
                PositionTitle = dto.PositionTitle,
                PositionTitleAr = dto.PositionTitleAr,
                PayrollMonth = dto.PayrollMonth,
                PayrollYear = dto.PayrollYear,
                BasicSalary = dto.BasicSalary,
                GrossSalary = dto.GrossSalary,
                NetSalary = dto.NetSalary,
                Status = dto.Status,
                StatusAr = dto.StatusAr,
                StatusBadgeClass = GetStatusBadgeClass(dto.Status),
                CreatedAt = dto.CreatedAt,
                CanEdit = dto.CanEdit,
                CanProcess = dto.CanProcess,
                CanApprove = dto.CanApprove,
                CanPay = dto.CanPay
            };
        }

        private static PayrollDetailsViewModel MapToPayrollDetailsViewModel(PayrollDto dto)
        {
            return new PayrollDetailsViewModel
            {
                Id = dto.Id,
                EmployeeId = dto.EmployeeId,
                EmployeeName = dto.EmployeeName,
                EmployeeNameAr = dto.EmployeeNameAr,
                EmployeeCode = dto.EmployeeCode,
                DepartmentName = dto.DepartmentName,
                DepartmentNameAr = dto.DepartmentNameAr,
                PositionTitle = dto.PositionTitle,
                PositionTitleAr = dto.PositionTitleAr,
                PayrollMonth = dto.PayrollMonth,
                PayrollYear = dto.PayrollYear,
                BasicSalary = dto.BasicSalary,
                TotalAllowances = dto.TotalAllowances,
                GrossSalary = dto.GrossSalary,
                TotalDeductions = dto.TotalDeductions,
                NetSalary = dto.NetSalary,
                Status = dto.Status,
                StatusAr = dto.StatusAr,
                StatusBadgeClass = GetStatusBadgeClass(dto.Status),
                CreatedAt = dto.CreatedAt,
                CanEdit = dto.CanEdit,
                CanProcess = dto.CanProcess,
                CanApprove = dto.CanApprove,
                CanPay = dto.CanPay
            };
        }

        private static string GetStatusBadgeClass(string status)
        {
            return status switch
            {
                "Draft" => "bg-secondary",
                "Processed" => "bg-warning",
                "Approved" => "bg-success",
                "Paid" => "bg-primary",
                "Rejected" => "bg-danger",
                _ => "bg-light"
            };
        }

        /// <summary>
        /// Payroll report
        /// تقرير كشوف المرتبات
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> PayrollReport(int? year = null, int? month = null)
        {
            try
            {
                var reportYear = year ?? DateTime.Today.Year;
                var reportMonth = month ?? DateTime.Today.Month;

                // Get current user's company
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "Company information not found.";
                    return RedirectToAction("Index", "Dashboard");
                }

                // For now, return a simple view with the date
                ViewBag.ReportYear = reportYear;
                ViewBag.ReportMonth = reportMonth;
                ViewBag.CompanyId = companyId;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating payroll report");
                TempData["ErrorMessage"] = "Error generating payroll report.";
                return RedirectToAction("Index");
            }
        }

        /// <summary>
        /// Process payroll (mark as processed)
        /// معالجة كشف المرتبات (تسجيل كمعالج)
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Process(Guid id)
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    return Json(new { success = false, message = "Company information not found" });
                }

                var request = new ProcessPayrollDto { PayrollId = id };
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    return Json(new { success = false, message = "User information not found" });
                }

                var result = await _payrollService.ProcessPayrollAsync(request, userId);
                if (result.IsSuccess)
                {
                    return Json(new { success = true, message = "تم معالجة كشف المرتبات بنجاح" });
                }

                return Json(new { success = false, message = result.ErrorMessage });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing payroll {PayrollId}", id);
                return Json(new { success = false, message = "حدث خطأ أثناء معالجة كشف المرتبات" });
            }
        }

        /// <summary>
        /// Approve payroll
        /// الموافقة على كشف المرتبات
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Approve(Guid id)
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    return Json(new { success = false, message = "Company information not found" });
                }

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    return Json(new { success = false, message = "User information not found" });
                }

                var request = new ApprovePayrollDto { PayrollId = id };
                var result = await _payrollService.ApprovePayrollAsync(request, userId);
                if (result.IsSuccess)
                {
                    return Json(new { success = true, message = "تم الموافقة على كشف المرتبات بنجاح" });
                }

                return Json(new { success = false, message = result.ErrorMessage });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving payroll {PayrollId}", id);
                return Json(new { success = false, message = "حدث خطأ أثناء الموافقة على كشف المرتبات" });
            }
        }

        /// <summary>
        /// Mark payroll as paid
        /// تسجيل كشف المرتبات كمدفوع
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> MarkAsPaid(Guid id, string paymentMethod, string paymentReference)
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    return Json(new { success = false, message = "Company information not found" });
                }

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!Guid.TryParse(userIdClaim, out var userId))
                {
                    return Json(new { success = false, message = "User information not found" });
                }

                var request = new PayPayrollDto
                {
                    PayrollId = id,
                    PaymentMethod = paymentMethod,
                    PaymentReference = paymentReference
                };
                var result = await _payrollService.PayPayrollAsync(request, userId);
                if (result.IsSuccess)
                {
                    return Json(new { success = true, message = "تم تسجيل كشف المرتبات كمدفوع بنجاح" });
                }

                return Json(new { success = false, message = result.ErrorMessage });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking payroll {PayrollId} as paid", id);
                return Json(new { success = false, message = "حدث خطأ أثناء تسجيل كشف المرتبات" });
            }
        }

        /// <summary>
        /// Delete payroll
        /// حذف كشف المرتبات
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    return Json(new { success = false, message = "Company information not found" });
                }

                var result = await _payrollService.DeletePayrollAsync(id, companyId);
                if (result.IsSuccess)
                {
                    return Json(new { success = true, message = "تم حذف كشف المرتبات بنجاح" });
                }

                return Json(new { success = false, message = result.ErrorMessage });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting payroll {PayrollId}", id);
                return Json(new { success = false, message = "حدث خطأ أثناء حذف كشف المرتبات" });
            }
        }

        /// <summary>
        /// Payroll management with tabs
        /// إدارة كشوف المرتبات مع التبويبات
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Management()
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    TempData["ErrorMessage"] = "معلومات الشركة غير موجودة";
                    return RedirectToAction("Index", "Dashboard");
                }

                // Get payroll statistics for the current month
                var currentMonth = DateTime.Today.Month;
                var currentYear = DateTime.Today.Year;

                var viewModel = new PayrollManagementViewModel
                {
                    CurrentMonth = currentMonth,
                    CurrentYear = currentYear,
                    // Initialize with empty data - will be loaded via AJAX
                    ActivePayrolls = new List<PayrollItemViewModel>(),
                    PendingPayrolls = new List<PayrollItemViewModel>(),
                    ProcessedPayrolls = new List<PayrollItemViewModel>(),
                    PaidPayrolls = new List<PayrollItemViewModel>()
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading payroll management page");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل صفحة إدارة الرواتب";
                return RedirectToAction("Index");
            }
        }

        /// <summary>
        /// Get payrolls by status (AJAX)
        /// الحصول على كشوف المرتبات حسب الحالة
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetPayrollsByStatus(string status, int month, int year)
        {
            try
            {
                var companyIdClaim = User.FindFirst("CompanyId")?.Value;
                if (!Guid.TryParse(companyIdClaim, out var companyId))
                {
                    return Json(new { success = false, message = "معلومات الشركة غير موجودة" });
                }

                // TODO: Implement actual data retrieval from service
                // For now, return mock data
                var mockPayrolls = GenerateMockPayrolls(status, month, year);

                return Json(new { success = true, data = mockPayrolls });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payrolls by status: {Status}", status);
                return Json(new { success = false, message = "حدث خطأ أثناء تحميل البيانات" });
            }
        }

        private List<object> GenerateMockPayrolls(string status, int month, int year)
        {
            var mockData = new List<object>();
            var random = new Random();
            var employees = new[] { "أحمد محمد", "فاطمة علي", "محمد حسن", "سارة أحمد", "علي محمود" };
            var departments = new[] { "تقنية المعلومات", "الموارد البشرية", "المحاسبة", "التسويق", "المبيعات" };

            for (int i = 0; i < 5; i++)
            {
                mockData.Add(new
                {
                    id = Guid.NewGuid(),
                    employeeName = employees[i],
                    employeeCode = $"EMP{1000 + i}",
                    department = departments[i],
                    basicSalary = random.Next(5000, 15000),
                    allowances = random.Next(500, 2000),
                    deductions = random.Next(200, 800),
                    netSalary = random.Next(6000, 16000),
                    status = status,
                    payrollPeriod = $"{year}-{month:D2}",
                    createdDate = DateTime.Now.AddDays(-random.Next(1, 30)).ToString("yyyy-MM-dd")
                });
            }

            return mockData;
        }
    }
}
