@model FitHRPlus.Web.Models.Leave.LeaveRequestListViewModel
@{
    ViewData["Title"] = "Leave Requests / طلبات الإجازات";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-calendar-alt text-primary me-2"></i>
                        Leave Requests / طلبات الإجازات
                    </h2>
                    <p class="text-muted mb-0">Manage employee leave requests / إدارة طلبات إجازات الموظفين</p>
                </div>
                <div>
                    <a href="@Url.Action("Create")" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        New Request / طلب جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    @if (Model.Statistics != null)
    {
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">@Model.Statistics.TotalRequests</h4>
                                <p class="mb-0">Total Requests / إجمالي الطلبات</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-calendar-check fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">@Model.Statistics.PendingRequests</h4>
                                <p class="mb-0">Pending / في الانتظار</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">@Model.Statistics.ApprovedRequests</h4>
                                <p class="mb-0">Approved / موافق عليها</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">@Model.Statistics.RejectedRequests</h4>
                                <p class="mb-0">Rejected / مرفوضة</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-times-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>
                Filters / التصفية
            </h5>
        </div>
        <div class="card-body">
            <form method="get" asp-action="Index">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">Employee / الموظف</label>
                        <select name="EmployeeId" class="form-select" asp-items="Model.Employees">
                            <option value="">All Employees / جميع الموظفين</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Leave Type / نوع الإجازة</label>
                        <select name="LeaveTypeId" class="form-select" asp-items="Model.LeaveTypes">
                            <option value="">All Types / جميع الأنواع</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Status / الحالة</label>
                        <select name="Status" class="form-select" asp-items="Model.StatusOptions">
                            <option value="">All Status / جميع الحالات</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">From Date / من تاريخ</label>
                        <input type="date" name="DateFrom" class="form-control" value="@Model.DateFrom?.ToString("yyyy-MM-dd")" />
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">To Date / إلى تاريخ</label>
                        <input type="date" name="DateTo" class="form-control" value="@Model.DateTo?.ToString("yyyy-MM-dd")" />
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <input type="text" name="SearchTerm" class="form-control" placeholder="Search by employee name or code / البحث بالاسم أو الكود" value="@Model.SearchTerm" />
                    </div>
                    <div class="col-md-6">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search me-1"></i>
                            Search / بحث
                        </button>
                        <a href="@Url.Action("Index")" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>
                            Clear / مسح
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Leave Requests Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                Leave Requests / طلبات الإجازات
                <span class="badge bg-primary ms-2">@Model.TotalCount</span>
            </h5>
        </div>
        <div class="card-body">
            @if (Model.LeaveRequests.Any())
            {
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Employee / الموظف</th>
                                <th>Leave Type / نوع الإجازة</th>
                                <th>Start Date / تاريخ البداية</th>
                                <th>End Date / تاريخ النهاية</th>
                                <th>Days / الأيام</th>
                                <th>Status / الحالة</th>
                                <th>Created / تاريخ الإنشاء</th>
                                <th>Actions / الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var request in Model.LeaveRequests)
                            {
                                <tr>
                                    <td>
                                        <div>
                                            <strong>@request.EmployeeName</strong>
                                            <br />
                                            <small class="text-muted">@request.EmployeeCode</small>
                                            <br />
                                            <small class="text-muted">@request.DepartmentName</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-bold">@request.LeaveTypeName</span>
                                        <br />
                                        <small class="text-muted">@request.LeaveTypeNameAr</small>
                                    </td>
                                    <td>@request.StartDate.ToString("dd/MM/yyyy")</td>
                                    <td>@request.EndDate.ToString("dd/MM/yyyy")</td>
                                    <td>
                                        <span class="badge bg-info">@request.TotalDays days</span>
                                    </td>
                                    <td>
                                        <span class="badge @request.StatusBadgeClass">
                                            @request.Status / @request.StatusAr
                                        </span>
                                    </td>
                                    <td>@request.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="@Url.Action("Details", new { id = request.Id })" class="btn btn-sm btn-outline-primary" title="View Details / عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if (request.CanEdit)
                                            {
                                                <a href="@Url.Action("Edit", new { id = request.Id })" class="btn btn-sm btn-outline-warning" title="Edit / تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            }
                                            @if (request.CanApprove)
                                            {
                                                <button type="button" class="btn btn-sm btn-outline-success" onclick="approveRequest('@request.Id')" title="Approve / موافقة">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            }
                                            @if (request.CanReject)
                                            {
                                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="rejectRequest('@request.Id')" title="Reject / رفض">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            }
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if (Model.TotalPages > 1)
                {
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            @if (Model.CurrentPage > 1)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="@Url.Action("Index", new { page = Model.CurrentPage - 1, pageSize = Model.PageSize, searchTerm = Model.SearchTerm, status = Model.Status, employeeId = Model.EmployeeId, leaveTypeId = Model.LeaveTypeId, dateFrom = Model.DateFrom?.ToString("yyyy-MM-dd"), dateTo = Model.DateTo?.ToString("yyyy-MM-dd") })">Previous</a>
                                </li>
                            }

                            @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                            {
                                <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                    <a class="page-link" href="@Url.Action("Index", new { page = i, pageSize = Model.PageSize, searchTerm = Model.SearchTerm, status = Model.Status, employeeId = Model.EmployeeId, leaveTypeId = Model.LeaveTypeId, dateFrom = Model.DateFrom?.ToString("yyyy-MM-dd"), dateTo = Model.DateTo?.ToString("yyyy-MM-dd") })">@i</a>
                                </li>
                            }

                            @if (Model.CurrentPage < Model.TotalPages)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="@Url.Action("Index", new { page = Model.CurrentPage + 1, pageSize = Model.PageSize, searchTerm = Model.SearchTerm, status = Model.Status, employeeId = Model.EmployeeId, leaveTypeId = Model.LeaveTypeId, dateFrom = Model.DateFrom?.ToString("yyyy-MM-dd"), dateTo = Model.DateTo?.ToString("yyyy-MM-dd") })">Next</a>
                                </li>
                            }
                        </ul>
                    </nav>
                }
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No leave requests found / لا توجد طلبات إجازات</h5>
                    <p class="text-muted">Try adjusting your search criteria or create a new leave request</p>
                    <a href="@Url.Action("Create")" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        Create New Request / إنشاء طلب جديد
                    </a>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function approveRequest(id) {
            if (confirm('Are you sure you want to approve this leave request? / هل أنت متأكد من الموافقة على طلب الإجازة هذا؟')) {
                $.post('@Url.Action("Approve")', { id: id }, function(result) {
                    location.reload();
                }).fail(function() {
                    alert('Error approving request / خطأ في الموافقة على الطلب');
                });
            }
        }

        function rejectRequest(id) {
            var reason = prompt('Please enter rejection reason / يرجى إدخال سبب الرفض:');
            if (reason && reason.trim() !== '') {
                $.post('@Url.Action("Reject")', { id: id, reason: reason }, function(result) {
                    location.reload();
                }).fail(function() {
                    alert('Error rejecting request / خطأ في رفض الطلب');
                });
            }
        }
    </script>
}
