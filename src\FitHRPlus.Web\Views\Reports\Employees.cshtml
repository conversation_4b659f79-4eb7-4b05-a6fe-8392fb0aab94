@model FitHRPlus.Web.Models.Reports.EmployeeReportsViewModel
@{
    ViewData["Title"] = "تقارير الموظفين";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        تقارير الموظفين
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Filter Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-filter me-2"></i>
                                        المرشحات
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <form method="get" class="row g-3">
                                        <div class="col-md-3">
                                            <label for="department" class="form-label">القسم</label>
                                            <select class="form-select" id="department" name="department">
                                                <option value="">جميع الأقسام</option>
                                                <option value="hr">الموارد البشرية</option>
                                                <option value="it">تكنولوجيا المعلومات</option>
                                                <option value="finance">المالية</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="position" class="form-label">المنصب</label>
                                            <select class="form-select" id="position" name="position">
                                                <option value="">جميع المناصب</option>
                                                <option value="manager">مدير</option>
                                                <option value="employee">موظف</option>
                                                <option value="intern">متدرب</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="status" class="form-label">الحالة</label>
                                            <select class="form-select" id="status" name="status">
                                                <option value="">جميع الحالات</option>
                                                <option value="active">نشط</option>
                                                <option value="inactive">غير نشط</option>
                                                <option value="terminated">منتهي الخدمة</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="reportType" class="form-label">نوع التقرير</label>
                                            <select class="form-select" id="reportType" name="reportType">
                                                <option value="summary">ملخص</option>
                                                <option value="detailed">تفصيلي</option>
                                                <option value="demographics">ديموغرافي</option>
                                                <option value="performance">الأداء</option>
                                            </select>
                                        </div>
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search me-1"></i>
                                                إنشاء التقرير
                                            </button>
                                            <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                                                <i class="fas fa-undo me-1"></i>
                                                إعادة تعيين
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">إجمالي الموظفين</h6>
                                            <h3 class="mb-0">150</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-users fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">الموظفين النشطين</h6>
                                            <h3 class="mb-0">142</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-user-check fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">الموظفين الجدد</h6>
                                            <h3 class="mb-0">8</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-user-plus fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">الأقسام النشطة</h6>
                                            <h3 class="mb-0">12</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-building fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Employee Data Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الاسم</th>
                                    <th>القسم</th>
                                    <th>المنصب</th>
                                    <th>تاريخ التوظيف</th>
                                    <th>الراتب</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="7" class="text-center text-muted py-4">
                                        <i class="fas fa-info-circle me-2"></i>
                                        لا توجد بيانات موظفين متاحة
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Charts Section -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">توزيع الموظفين حسب القسم</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="departmentChart" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">توزيع الموظفين حسب العمر</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="ageChart" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button class="btn btn-success" onclick="exportToExcel()">
                                    <i class="fas fa-file-excel me-1"></i>
                                    تصدير إلى Excel
                                </button>
                                <button class="btn btn-danger" onclick="exportToPDF()">
                                    <i class="fas fa-file-pdf me-1"></i>
                                    تصدير إلى PDF
                                </button>
                                <button class="btn btn-primary" onclick="printReport()">
                                    <i class="fas fa-print me-1"></i>
                                    طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        function exportToExcel() {
            alert('سيتم تنفيذ وظيفة تصدير Excel');
        }

        function exportToPDF() {
            alert('سيتم تنفيذ وظيفة تصدير PDF');
        }

        function printReport() {
            window.print();
        }

        function resetFilters() {
            document.querySelector('form').reset();
        }

        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Placeholder for chart initialization
            console.log('Employee charts will be initialized here');
        });
    </script>
}
