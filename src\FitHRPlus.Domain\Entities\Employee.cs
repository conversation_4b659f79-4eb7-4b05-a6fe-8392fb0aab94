using FitHRPlus.Domain.Common;
using FitHRPlus.Domain.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace FitHRPlus.Domain.Entities
{
    /// <summary>
    /// Employee entity representing company employees
    /// كيان الموظف الذي يمثل موظفي الشركة
    /// </summary>
    public class Employee : BaseEntity
    {
        /// <summary>
        /// Company ID that employs this employee
        /// معرف الشركة التي توظف هذا الموظف
        /// </summary>
        [Required]
        public Guid CompanyId { get; set; }

        /// <summary>
        /// Department ID where employee works
        /// معرف القسم الذي يعمل به الموظف
        /// </summary>
        [Required]
        public Guid DepartmentId { get; set; }

        /// <summary>
        /// Position ID of the employee
        /// معرف منصب الموظف
        /// </summary>
        public Guid? PositionId { get; set; }

        /// <summary>
        /// User ID for system access (optional)
        /// معرف المستخدم للوصول للنظام (اختياري)
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// Unique employee code within company
        /// رمز الموظف الفريد داخل الشركة
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string EmployeeCode { get; set; } = string.Empty;

        // Personal Information - المعلومات الشخصية

        /// <summary>
        /// First name in English
        /// الاسم الأول بالإنجليزية
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// First name in Arabic
        /// الاسم الأول بالعربية
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string FirstNameAr { get; set; } = string.Empty;

        /// <summary>
        /// Middle name in English
        /// الاسم الأوسط بالإنجليزية
        /// </summary>
        [MaxLength(100)]
        public string? MiddleName { get; set; }

        /// <summary>
        /// Middle name in Arabic
        /// الاسم الأوسط بالعربية
        /// </summary>
        [MaxLength(100)]
        public string? MiddleNameAr { get; set; }

        /// <summary>
        /// Last name in English
        /// اسم العائلة بالإنجليزية
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// Last name in Arabic
        /// اسم العائلة بالعربية
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string LastNameAr { get; set; } = string.Empty;

        /// <summary>
        /// National ID number
        /// رقم الهوية الوطنية
        /// </summary>
        [MaxLength(20)]
        public string? NationalId { get; set; }

        /// <summary>
        /// Passport number
        /// رقم جواز السفر
        /// </summary>
        [MaxLength(20)]
        public string? PassportNumber { get; set; }

        /// <summary>
        /// Date of birth
        /// تاريخ الميلاد
        /// </summary>
        public DateTime? DateOfBirth { get; set; }

        /// <summary>
        /// Place of birth in English
        /// مكان الميلاد بالإنجليزية
        /// </summary>
        [MaxLength(200)]
        public string? PlaceOfBirth { get; set; }

        /// <summary>
        /// Place of birth in Arabic
        /// مكان الميلاد بالعربية
        /// </summary>
        [MaxLength(200)]
        public string? PlaceOfBirthAr { get; set; }

        /// <summary>
        /// Employee gender
        /// جنس الموظف
        /// </summary>
        public Gender? Gender { get; set; }

        /// <summary>
        /// Marital status
        /// الحالة الاجتماعية
        /// </summary>
        public MaritalStatus? MaritalStatus { get; set; }

        /// <summary>
        /// Nationality
        /// الجنسية
        /// </summary>
        [MaxLength(100)]
        public string? Nationality { get; set; }

        /// <summary>
        /// Religion
        /// الديانة
        /// </summary>
        [MaxLength(50)]
        public string? Religion { get; set; }

        /// <summary>
        /// Blood type
        /// فصيلة الدم
        /// </summary>
        [MaxLength(5)]
        public string? BloodType { get; set; }

        // Contact Information - معلومات الاتصال

        /// <summary>
        /// Work email address
        /// البريد الإلكتروني للعمل
        /// </summary>
        [MaxLength(100)]
        [EmailAddress]
        public string? Email { get; set; }

        /// <summary>
        /// Personal email address
        /// البريد الإلكتروني الشخصي
        /// </summary>
        [MaxLength(100)]
        [EmailAddress]
        public string? PersonalEmail { get; set; }

        /// <summary>
        /// Work phone number
        /// رقم هاتف العمل
        /// </summary>
        [MaxLength(20)]
        public string? Phone { get; set; }

        /// <summary>
        /// Personal phone number
        /// رقم الهاتف الشخصي
        /// </summary>
        [MaxLength(20)]
        public string? PersonalPhone { get; set; }

        /// <summary>
        /// Emergency contact name
        /// اسم جهة الاتصال في حالات الطوارئ
        /// </summary>
        [MaxLength(200)]
        public string? EmergencyContact { get; set; }

        /// <summary>
        /// Emergency contact phone
        /// هاتف جهة الاتصال في حالات الطوارئ
        /// </summary>
        [MaxLength(20)]
        public string? EmergencyPhone { get; set; }

        /// <summary>
        /// Address in English
        /// العنوان بالإنجليزية
        /// </summary>
        [MaxLength(500)]
        public string? Address { get; set; }

        /// <summary>
        /// Address in Arabic
        /// العنوان بالعربية
        /// </summary>
        [MaxLength(500)]
        public string? AddressAr { get; set; }

        /// <summary>
        /// City in English
        /// المدينة بالإنجليزية
        /// </summary>
        [MaxLength(100)]
        public string? City { get; set; }

        /// <summary>
        /// City in Arabic
        /// المدينة بالعربية
        /// </summary>
        [MaxLength(100)]
        public string? CityAr { get; set; }

        /// <summary>
        /// Postal code
        /// الرمز البريدي
        /// </summary>
        [MaxLength(20)]
        public string? PostalCode { get; set; }

        // Employment Information - معلومات التوظيف

        /// <summary>
        /// Hire date
        /// تاريخ التوظيف
        /// </summary>
        [Required]
        public DateTime HireDate { get; set; }

        /// <summary>
        /// Probation period end date
        /// تاريخ انتهاء فترة التجربة
        /// </summary>
        public DateTime? ProbationEndDate { get; set; }

        /// <summary>
        /// Confirmation date (end of probation)
        /// تاريخ التثبيت (انتهاء فترة التجربة)
        /// </summary>
        public DateTime? ConfirmationDate { get; set; }

        /// <summary>
        /// Termination date
        /// تاريخ إنهاء الخدمة
        /// </summary>
        public DateTime? TerminationDate { get; set; }

        /// <summary>
        /// Reason for termination
        /// سبب إنهاء الخدمة
        /// </summary>
        [MaxLength(500)]
        public string? TerminationReason { get; set; }

        /// <summary>
        /// Employment type
        /// نوع التوظيف
        /// </summary>
        public EmploymentType EmploymentType { get; set; } = EmploymentType.FullTime;

        /// <summary>
        /// Work location (Office, Remote, Hybrid)
        /// مكان العمل
        /// </summary>
        [MaxLength(200)]
        public string? WorkLocation { get; set; }

        /// <summary>
        /// Direct manager employee ID
        /// معرف موظف المدير المباشر
        /// </summary>
        public Guid? ReportsTo { get; set; }

        // Salary Information - معلومات الراتب

        /// <summary>
        /// Base salary amount
        /// مبلغ الراتب الأساسي
        /// </summary>
        public decimal? BaseSalary { get; set; }

        /// <summary>
        /// Currency code (ISO 4217)
        /// رمز العملة
        /// </summary>
        [MaxLength(3)]
        public string Currency { get; set; } = "EGP";

        /// <summary>
        /// Payroll frequency (Monthly, BiWeekly, Weekly)
        /// تكرار الراتب
        /// </summary>
        [MaxLength(20)]
        public string PayrollFrequency { get; set; } = "Monthly";

        /// <summary>
        /// Bank name
        /// اسم البنك
        /// </summary>
        [MaxLength(200)]
        public string? BankName { get; set; }

        /// <summary>
        /// Bank account number
        /// رقم الحساب البنكي
        /// </summary>
        [MaxLength(50)]
        public string? BankAccountNumber { get; set; }

        /// <summary>
        /// Bank IBAN
        /// رقم الآيبان البنكي
        /// </summary>
        [MaxLength(50)]
        public string? BankIBAN { get; set; }

        // System Information - معلومات النظام

        /// <summary>
        /// Profile picture file path
        /// مسار صورة الملف الشخصي
        /// </summary>
        [MaxLength(500)]
        public string? ProfilePicture { get; set; }

        /// <summary>
        /// Biometric data as JSON (fingerprint, face data)
        /// البيانات البيومترية كـ JSON
        /// </summary>
        public string? BiometricData { get; set; }

        /// <summary>
        /// Work schedule ID
        /// معرف جدول العمل
        /// </summary>
        public Guid? WorkScheduleId { get; set; }

        // Navigation Properties - خصائص التنقل

        /// <summary>
        /// Company that employs this employee
        /// الشركة التي توظف هذا الموظف
        /// </summary>
        public virtual Company Company { get; set; } = null!;

        /// <summary>
        /// Department where employee works
        /// القسم الذي يعمل به الموظف
        /// </summary>
        public virtual Department Department { get; set; } = null!;

        /// <summary>
        /// Employee position
        /// منصب الموظف
        /// </summary>
        public virtual Position? Position { get; set; }

        /// <summary>
        /// User account for system access
        /// حساب المستخدم للوصول للنظام
        /// </summary>
        public virtual User? User { get; set; }

        /// <summary>
        /// Direct manager
        /// المدير المباشر
        /// </summary>
        public virtual Employee? Manager { get; set; }

        /// <summary>
        /// Work schedule
        /// جدول العمل
        /// </summary>
        public virtual WorkSchedule? WorkSchedule { get; set; }

        /// <summary>
        /// Direct reports (subordinates)
        /// المرؤوسون المباشرون
        /// </summary>
        public virtual ICollection<Employee> DirectReports { get; set; } = new List<Employee>();

        /// <summary>
        /// Attendance records
        /// سجلات الحضور
        /// </summary>
        public virtual ICollection<AttendanceRecord> AttendanceRecords { get; set; } = new List<AttendanceRecord>();

        /// <summary>
        /// Leave requests
        /// طلبات الإجازات
        /// </summary>
        public virtual ICollection<LeaveRequest> LeaveRequests { get; set; } = new List<LeaveRequest>();

        /// <summary>
        /// Payroll records
        /// سجلات الرواتب
        /// </summary>
        public virtual ICollection<Payroll> Payrolls { get; set; } = new List<Payroll>();

        /// <summary>
        /// Employee salary components
        /// مكونات راتب الموظف
        /// </summary>
        public virtual ICollection<EmployeeSalary> EmployeeSalaries { get; set; } = new List<EmployeeSalary>();

        // Helper Properties - خصائص مساعدة

        /// <summary>
        /// Full name in English
        /// الاسم الكامل بالإنجليزية
        /// </summary>
        public string FullName => $"{FirstName} {MiddleName} {LastName}".Replace("  ", " ").Trim();

        /// <summary>
        /// Full name in Arabic
        /// الاسم الكامل بالعربية
        /// </summary>
        public string FullNameAr => $"{FirstNameAr} {MiddleNameAr} {LastNameAr}".Replace("  ", " ").Trim();

        /// <summary>
        /// Check if employee is currently employed
        /// التحقق من كون الموظف يعمل حالياً
        /// </summary>
        public bool IsCurrentlyEmployed => IsActive && !TerminationDate.HasValue;

        /// <summary>
        /// Calculate years of service
        /// حساب سنوات الخدمة
        /// </summary>
        public int YearsOfService
        {
            get
            {
                var endDate = TerminationDate ?? DateTime.Now;
                var years = endDate.Year - HireDate.Year;
                if (endDate.Month < HireDate.Month || (endDate.Month == HireDate.Month && endDate.Day < HireDate.Day))
                    years--;
                return Math.Max(0, years);
            }
        }
    }
}