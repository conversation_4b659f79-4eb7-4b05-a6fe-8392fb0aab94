# نموذج قاعدة البيانات الكامل مع العلاقات
## Complete Database Model with Relationships

## 1. مخطط قاعدة البيانات الشامل
### Comprehensive Database Schema

### 1.1 مخطط العلاقات الرئيسي
```mermaid
erDiagram
    Companies ||--o{ Departments : "has"
    Companies ||--o{ Employees : "employs"
    Companies ||--o{ LeaveTypes : "defines"
    Companies ||--o{ SalaryComponents : "configures"
    Companies ||--o{ BiometricDevices : "owns"
    Companies ||--o{ Holidays : "observes"
    Companies ||--o{ WorkShifts : "schedules"
    
    Departments ||--o{ Employees : "contains"
    Departments ||--o{ Positions : "includes"
    
    Employees ||--o{ AttendanceRecords : "records"
    Employees ||--o{ LeaveRequests : "submits"
    Employees ||--o{ Payrolls : "receives"
    Employees ||--o{ EmployeeSalaries : "earns"
    Employees ||--o{ EmployeeDocuments : "owns"
    Employees ||--o{ PerformanceReviews : "undergoes"
    Employees ||--o{ TrainingRecords : "attends"
    
    LeaveTypes ||--o{ LeaveRequests : "categorizes"
    SalaryComponents ||--o{ EmployeeSalaries : "applies"
    BiometricDevices ||--o{ AttendanceRecords : "captures"
    WorkShifts ||--o{ EmployeeShifts : "assigns"
    
    Users ||--o{ AuditLogs : "performs"
    Users ||--o{ Notifications : "receives"
    
    Roles ||--o{ UserRoles : "grants"
    Users ||--o{ UserRoles : "has"
    
    Companies {
        uniqueidentifier Id PK
        nvarchar Name
        nvarchar NameAr
        nvarchar TaxNumber UK
        nvarchar CommercialRegister UK
        nvarchar Address
        nvarchar AddressAr
        nvarchar Phone
        nvarchar Email UK
        nvarchar Logo
        bit IsActive
        datetime2 CreatedAt
        datetime2 UpdatedAt
        uniqueidentifier CreatedBy FK
        uniqueidentifier UpdatedBy FK
    }
    
    Departments {
        uniqueidentifier Id PK
        uniqueidentifier CompanyId FK
        nvarchar Name
        nvarchar NameAr
        nvarchar Description
        nvarchar DescriptionAr
        uniqueidentifier ManagerId FK
        uniqueidentifier ParentDepartmentId FK
        nvarchar Code UK
        bit IsActive
        datetime2 CreatedAt
        datetime2 UpdatedAt
    }
    
    Employees {
        uniqueidentifier Id PK
        uniqueidentifier CompanyId FK
        uniqueidentifier DepartmentId FK
        uniqueidentifier PositionId FK
        nvarchar EmployeeCode UK
        nvarchar FirstName
        nvarchar FirstNameAr
        nvarchar LastName
        nvarchar LastNameAr
        nvarchar Email UK
        nvarchar Phone
        nvarchar NationalId UK
        date DateOfBirth
        nvarchar Gender
        nvarchar MaritalStatus
        nvarchar Address
        nvarchar AddressAr
        date HireDate
        date TerminationDate
        nvarchar TerminationReason
        decimal BaseSalary
        nvarchar Currency
        bit IsActive
        nvarchar ProfilePicture
        nvarchar BiometricData
        datetime2 CreatedAt
        datetime2 UpdatedAt
    }
```

---

## 2. الجداول الأساسية المفصلة
### Detailed Core Tables

### 2.1 جدول الشركات (Companies)
```sql
CREATE TABLE Companies (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(200) NOT NULL,
    NameAr NVARCHAR(200) NOT NULL,
    TaxNumber NVARCHAR(50) UNIQUE,
    CommercialRegister NVARCHAR(50) UNIQUE,
    Address NVARCHAR(500),
    AddressAr NVARCHAR(500),
    Phone NVARCHAR(20),
    Email NVARCHAR(100) UNIQUE,
    Website NVARCHAR(200),
    Logo NVARCHAR(500),
    Industry NVARCHAR(100),
    EmployeeCount INT DEFAULT 0,
    EstablishedDate DATE,
    TimeZone NVARCHAR(50) DEFAULT 'Africa/Cairo',
    Currency NVARCHAR(3) DEFAULT 'EGP',
    FiscalYearStart INT DEFAULT 1, -- January
    IsActive BIT DEFAULT 1,
    SubscriptionPlan NVARCHAR(50) DEFAULT 'Basic',
    SubscriptionExpiry DATETIME2,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    UpdatedBy UNIQUEIDENTIFIER,
    
    CONSTRAINT FK_Companies_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    CONSTRAINT FK_Companies_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id)
);

-- Indexes for performance
CREATE INDEX IX_Companies_TaxNumber ON Companies(TaxNumber);
CREATE INDEX IX_Companies_IsActive ON Companies(IsActive);
CREATE INDEX IX_Companies_SubscriptionExpiry ON Companies(SubscriptionExpiry);
```

### 2.2 جدول المستخدمين والأدوار (Users & Roles)
```sql
-- جدول المستخدمين
CREATE TABLE Users (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Username NVARCHAR(100) UNIQUE NOT NULL,
    Email NVARCHAR(100) UNIQUE NOT NULL,
    PasswordHash NVARCHAR(500) NOT NULL,
    Salt NVARCHAR(100) NOT NULL,
    FirstName NVARCHAR(100) NOT NULL,
    LastName NVARCHAR(100) NOT NULL,
    Phone NVARCHAR(20),
    ProfilePicture NVARCHAR(500),
    PreferredLanguage NVARCHAR(5) DEFAULT 'ar',
    TimeZone NVARCHAR(50) DEFAULT 'Africa/Cairo',
    IsActive BIT DEFAULT 1,
    IsEmailVerified BIT DEFAULT 0,
    IsPhoneVerified BIT DEFAULT 0,
    TwoFactorEnabled BIT DEFAULT 0,
    TwoFactorSecret NVARCHAR(100),
    LastLoginAt DATETIME2,
    LastPasswordChangeAt DATETIME2 DEFAULT GETUTCDATE(),
    FailedLoginAttempts INT DEFAULT 0,
    LockedUntil DATETIME2,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE()
);

-- جدول الأدوار
CREATE TABLE Roles (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(100) UNIQUE NOT NULL,
    NameAr NVARCHAR(100) NOT NULL,
    Description NVARCHAR(500),
    DescriptionAr NVARCHAR(500),
    IsSystemRole BIT DEFAULT 0,
    Permissions NVARCHAR(MAX), -- JSON array of permissions
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE()
);

-- جدول ربط المستخدمين بالأدوار
CREATE TABLE UserRoles (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    UserId UNIQUEIDENTIFIER NOT NULL,
    RoleId UNIQUEIDENTIFIER NOT NULL,
    CompanyId UNIQUEIDENTIFIER NOT NULL,
    AssignedAt DATETIME2 DEFAULT GETUTCDATE(),
    AssignedBy UNIQUEIDENTIFIER,
    
    CONSTRAINT FK_UserRoles_User FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE CASCADE,
    CONSTRAINT FK_UserRoles_Role FOREIGN KEY (RoleId) REFERENCES Roles(Id),
    CONSTRAINT FK_UserRoles_Company FOREIGN KEY (CompanyId) REFERENCES Companies(Id),
    CONSTRAINT FK_UserRoles_AssignedBy FOREIGN KEY (AssignedBy) REFERENCES Users(Id),
    CONSTRAINT UQ_UserRoles_UserCompanyRole UNIQUE (UserId, CompanyId, RoleId)
);
```

### 2.3 جدول الأقسام والمناصب
```sql
-- جدول الأقسام
CREATE TABLE Departments (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    CompanyId UNIQUEIDENTIFIER NOT NULL,
    Name NVARCHAR(200) NOT NULL,
    NameAr NVARCHAR(200) NOT NULL,
    Description NVARCHAR(500),
    DescriptionAr NVARCHAR(500),
    Code NVARCHAR(20) NOT NULL,
    ManagerId UNIQUEIDENTIFIER,
    ParentDepartmentId UNIQUEIDENTIFIER,
    CostCenter NVARCHAR(50),
    Budget DECIMAL(18,2),
    Location NVARCHAR(200),
    LocationAr NVARCHAR(200),
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    CONSTRAINT FK_Departments_Company FOREIGN KEY (CompanyId) REFERENCES Companies(Id),
    CONSTRAINT FK_Departments_Manager FOREIGN KEY (ManagerId) REFERENCES Employees(Id),
    CONSTRAINT FK_Departments_Parent FOREIGN KEY (ParentDepartmentId) REFERENCES Departments(Id),
    CONSTRAINT UQ_Departments_CompanyCode UNIQUE (CompanyId, Code)
);

-- جدول المناصب
CREATE TABLE Positions (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    CompanyId UNIQUEIDENTIFIER NOT NULL,
    DepartmentId UNIQUEIDENTIFIER NOT NULL,
    Title NVARCHAR(200) NOT NULL,
    TitleAr NVARCHAR(200) NOT NULL,
    Description NVARCHAR(1000),
    DescriptionAr NVARCHAR(1000),
    Level NVARCHAR(50), -- Entry, Junior, Senior, Manager, Director
    MinSalary DECIMAL(18,2),
    MaxSalary DECIMAL(18,2),
    RequiredSkills NVARCHAR(MAX), -- JSON array
    Responsibilities NVARCHAR(MAX), -- JSON array
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    CONSTRAINT FK_Positions_Company FOREIGN KEY (CompanyId) REFERENCES Companies(Id),
    CONSTRAINT FK_Positions_Department FOREIGN KEY (DepartmentId) REFERENCES Departments(Id)
);
```

### 2.4 جدول الموظفين المفصل
```sql
CREATE TABLE Employees (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    CompanyId UNIQUEIDENTIFIER NOT NULL,
    DepartmentId UNIQUEIDENTIFIER NOT NULL,
    PositionId UNIQUEIDENTIFIER,
    UserId UNIQUEIDENTIFIER, -- Link to Users table for system access
    EmployeeCode NVARCHAR(50) NOT NULL,
    
    -- Personal Information
    FirstName NVARCHAR(100) NOT NULL,
    FirstNameAr NVARCHAR(100) NOT NULL,
    MiddleName NVARCHAR(100),
    MiddleNameAr NVARCHAR(100),
    LastName NVARCHAR(100) NOT NULL,
    LastNameAr NVARCHAR(100) NOT NULL,
    NationalId NVARCHAR(20) UNIQUE,
    PassportNumber NVARCHAR(20),
    DateOfBirth DATE,
    PlaceOfBirth NVARCHAR(200),
    PlaceOfBirthAr NVARCHAR(200),
    Gender NVARCHAR(10) CHECK (Gender IN ('Male', 'Female')),
    MaritalStatus NVARCHAR(20) CHECK (MaritalStatus IN ('Single', 'Married', 'Divorced', 'Widowed')),
    Nationality NVARCHAR(100),
    Religion NVARCHAR(50),
    BloodType NVARCHAR(5),
    
    -- Contact Information
    Email NVARCHAR(100) UNIQUE,
    PersonalEmail NVARCHAR(100),
    Phone NVARCHAR(20),
    PersonalPhone NVARCHAR(20),
    EmergencyContact NVARCHAR(200),
    EmergencyPhone NVARCHAR(20),
    Address NVARCHAR(500),
    AddressAr NVARCHAR(500),
    City NVARCHAR(100),
    CityAr NVARCHAR(100),
    PostalCode NVARCHAR(20),
    
    -- Employment Information
    HireDate DATE NOT NULL,
    ProbationEndDate DATE,
    ConfirmationDate DATE,
    TerminationDate DATE,
    TerminationReason NVARCHAR(500),
    EmploymentType NVARCHAR(50) DEFAULT 'FullTime', -- FullTime, PartTime, Contract, Intern
    WorkLocation NVARCHAR(200), -- Office, Remote, Hybrid
    ReportsTo UNIQUEIDENTIFIER, -- Manager
    
    -- Salary Information
    BaseSalary DECIMAL(18,2),
    Currency NVARCHAR(3) DEFAULT 'EGP',
    PayrollFrequency NVARCHAR(20) DEFAULT 'Monthly', -- Monthly, BiWeekly, Weekly
    BankName NVARCHAR(200),
    BankAccountNumber NVARCHAR(50),
    BankIBAN NVARCHAR(50),
    
    -- System Information
    ProfilePicture NVARCHAR(500),
    BiometricData NVARCHAR(MAX), -- JSON for fingerprint/face data
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    UpdatedBy UNIQUEIDENTIFIER,
    
    CONSTRAINT FK_Employees_Company FOREIGN KEY (CompanyId) REFERENCES Companies(Id),
    CONSTRAINT FK_Employees_Department FOREIGN KEY (DepartmentId) REFERENCES Departments(Id),
    CONSTRAINT FK_Employees_Position FOREIGN KEY (PositionId) REFERENCES Positions(Id),
    CONSTRAINT FK_Employees_User FOREIGN KEY (UserId) REFERENCES Users(Id),
    CONSTRAINT FK_Employees_ReportsTo FOREIGN KEY (ReportsTo) REFERENCES Employees(Id),
    CONSTRAINT UQ_Employees_CompanyCode UNIQUE (CompanyId, EmployeeCode)
);

-- Indexes for performance
CREATE INDEX IX_Employees_Company ON Employees(CompanyId);
CREATE INDEX IX_Employees_Department ON Employees(DepartmentId);
CREATE INDEX IX_Employees_IsActive ON Employees(IsActive);
CREATE INDEX IX_Employees_HireDate ON Employees(HireDate);
CREATE INDEX IX_Employees_Email ON Employees(Email);
```

---

## 3. جداول الحضور والانصراف
### Attendance and Leave Tables

### 3.1 جداول الحضور
```sql
-- جدول أجهزة البصمة
CREATE TABLE BiometricDevices (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    CompanyId UNIQUEIDENTIFIER NOT NULL,
    DeviceName NVARCHAR(200) NOT NULL,
    DeviceType NVARCHAR(50) NOT NULL, -- Fingerprint, Face, Iris, Card
    Brand NVARCHAR(100), -- ZKTeco, Suprema, Morpho, HID
    Model NVARCHAR(100),
    SerialNumber NVARCHAR(100) UNIQUE,
    IpAddress NVARCHAR(50),
    Port INT,
    Location NVARCHAR(200),
    LocationAr NVARCHAR(200),
    Latitude DECIMAL(10,8),
    Longitude DECIMAL(11,8),
    GeofenceRadius INT DEFAULT 100, -- meters
    IsActive BIT DEFAULT 1,
    LastSync DATETIME2,
    SyncInterval INT DEFAULT 300, -- seconds
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    CONSTRAINT FK_BiometricDevices_Company FOREIGN KEY (CompanyId) REFERENCES Companies(Id)
);

-- جدول الورديات
CREATE TABLE WorkShifts (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    CompanyId UNIQUEIDENTIFIER NOT NULL,
    Name NVARCHAR(200) NOT NULL,
    NameAr NVARCHAR(200) NOT NULL,
    StartTime TIME NOT NULL,
    EndTime TIME NOT NULL,
    BreakDuration INT DEFAULT 0, -- minutes
    GracePeriod INT DEFAULT 15, -- minutes
    IsFlexible BIT DEFAULT 0,
    FlexibleMinutes INT DEFAULT 0,
    WorkingDays NVARCHAR(20) DEFAULT '1,2,3,4,5', -- 1=Sunday, 7=Saturday
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    CONSTRAINT FK_WorkShifts_Company FOREIGN KEY (CompanyId) REFERENCES Companies(Id)
);

-- جدول ربط الموظفين بالورديات
CREATE TABLE EmployeeShifts (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    EmployeeId UNIQUEIDENTIFIER NOT NULL,
    ShiftId UNIQUEIDENTIFIER NOT NULL,
    EffectiveDate DATE NOT NULL,
    EndDate DATE,
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    CONSTRAINT FK_EmployeeShifts_Employee FOREIGN KEY (EmployeeId) REFERENCES Employees(Id),
    CONSTRAINT FK_EmployeeShifts_Shift FOREIGN KEY (ShiftId) REFERENCES WorkShifts(Id)
);

-- جدول سجلات الحضور
CREATE TABLE AttendanceRecords (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    EmployeeId UNIQUEIDENTIFIER NOT NULL,
    DeviceId UNIQUEIDENTIFIER,
    AttendanceDate DATE NOT NULL,
    CheckInTime DATETIME2,
    CheckOutTime DATETIME2,
    ActualCheckInTime DATETIME2, -- Original time before adjustments
    ActualCheckOutTime DATETIME2,
    WorkingHours DECIMAL(4,2) DEFAULT 0,
    OvertimeHours DECIMAL(4,2) DEFAULT 0,
    BreakHours DECIMAL(4,2) DEFAULT 0,
    LateMinutes INT DEFAULT 0,
    EarlyLeaveMinutes INT DEFAULT 0,
    Status NVARCHAR(20) DEFAULT 'Present', -- Present, Absent, Late, EarlyLeave, Holiday, Leave
    AttendanceType NVARCHAR(20) DEFAULT 'Regular', -- Regular, Overtime, Holiday
    Notes NVARCHAR(500),
    NotesAr NVARCHAR(500),
    
    -- Location Information
    CheckInLatitude DECIMAL(10,8),
    CheckInLongitude DECIMAL(11,8),
    CheckOutLatitude DECIMAL(10,8),
    CheckOutLongitude DECIMAL(11,8),
    
    -- Approval Information
    IsApproved BIT DEFAULT 0,
    ApprovedBy UNIQUEIDENTIFIER,
    ApprovedAt DATETIME2,
    ApprovalNotes NVARCHAR(500),
    
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    CONSTRAINT FK_AttendanceRecords_Employee FOREIGN KEY (EmployeeId) REFERENCES Employees(Id),
    CONSTRAINT FK_AttendanceRecords_Device FOREIGN KEY (DeviceId) REFERENCES BiometricDevices(Id),
    CONSTRAINT FK_AttendanceRecords_ApprovedBy FOREIGN KEY (ApprovedBy) REFERENCES Employees(Id),
    CONSTRAINT UQ_AttendanceRecords_EmployeeDate UNIQUE (EmployeeId, AttendanceDate)
);

-- Indexes for performance
CREATE INDEX IX_AttendanceRecords_Employee ON AttendanceRecords(EmployeeId);
CREATE INDEX IX_AttendanceRecords_Date ON AttendanceRecords(AttendanceDate);
CREATE INDEX IX_AttendanceRecords_Status ON AttendanceRecords(Status);
```

### 3.2 جداول الإجازات
```sql
-- جدول أنواع الإجازات
CREATE TABLE LeaveTypes (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    CompanyId UNIQUEIDENTIFIER NOT NULL,
    Name NVARCHAR(200) NOT NULL,
    NameAr NVARCHAR(200) NOT NULL,
    Description NVARCHAR(500),
    DescriptionAr NVARCHAR(500),
    MaxDaysPerYear INT,
    MaxConsecutiveDays INT,
    MinDaysNotice INT DEFAULT 0,
    IsPaid BIT DEFAULT 1,
    RequiresApproval BIT DEFAULT 1,
    RequiresDocument BIT DEFAULT 0,
    CarryForward BIT DEFAULT 0,
    CarryForwardLimit INT DEFAULT 0,
    Gender NVARCHAR(10), -- NULL for both, 'Male', 'Female'
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    CONSTRAINT FK_LeaveTypes_Company FOREIGN KEY (CompanyId) REFERENCES Companies(Id)
);

-- جدول أرصدة الإجازات
CREATE TABLE LeaveBalances (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    EmployeeId UNIQUEIDENTIFIER NOT NULL,
    LeaveTypeId UNIQUEIDENTIFIER NOT NULL,
    Year INT NOT NULL,
    EntitledDays DECIMAL(4,2) DEFAULT 0,
    UsedDays DECIMAL(4,2) DEFAULT 0,
    RemainingDays DECIMAL(4,2) DEFAULT 0,
    CarriedForwardDays DECIMAL(4,2) DEFAULT 0,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    CONSTRAINT FK_LeaveBalances_Employee FOREIGN KEY (EmployeeId) REFERENCES Employees(Id),
    CONSTRAINT FK_LeaveBalances_LeaveType FOREIGN KEY (LeaveTypeId) REFERENCES LeaveTypes(Id),
    CONSTRAINT UQ_LeaveBalances_EmployeeTypeYear UNIQUE (EmployeeId, LeaveTypeId, Year)
);

-- جدول طلبات الإجازات
CREATE TABLE LeaveRequests (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    EmployeeId UNIQUEIDENTIFIER NOT NULL,
    LeaveTypeId UNIQUEIDENTIFIER NOT NULL,
    StartDate DATE NOT NULL,
    EndDate DATE NOT NULL,
    TotalDays DECIMAL(4,2) NOT NULL,
    Reason NVARCHAR(1000),
    ReasonAr NVARCHAR(1000),
    Status NVARCHAR(20) DEFAULT 'Pending', -- Pending, Approved, Rejected, Cancelled
    
    -- Approval Workflow
    ApprovedBy UNIQUEIDENTIFIER,
    ApprovedAt DATETIME2,
    RejectionReason NVARCHAR(500),
    RejectionReasonAr NVARCHAR(500),
    
    -- Documents
    AttachedDocuments NVARCHAR(MAX), -- JSON array of document URLs
    
    -- Emergency Contact (for long leaves)
    EmergencyContact NVARCHAR(200),
    EmergencyPhone NVARCHAR(20),
    
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    CONSTRAINT FK_LeaveRequests_Employee FOREIGN KEY (EmployeeId) REFERENCES Employees(Id),
    CONSTRAINT FK_LeaveRequests_LeaveType FOREIGN KEY (LeaveTypeId) REFERENCES LeaveTypes(Id),
    CONSTRAINT FK_LeaveRequests_ApprovedBy FOREIGN KEY (ApprovedBy) REFERENCES Employees(Id)
);

-- جدول العطل الرسمية
CREATE TABLE Holidays (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    CompanyId UNIQUEIDENTIFIER NOT NULL,
    Name NVARCHAR(200) NOT NULL,
    NameAr NVARCHAR(200) NOT NULL,
    Date DATE NOT NULL,
    IsRecurring BIT DEFAULT 0,
    RecurrenceType NVARCHAR(20), -- Yearly, Monthly
    IsOptional BIT DEFAULT 0,
    AppliesTo NVARCHAR(50) DEFAULT 'All', -- All, Muslims, Christians, etc.
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    CONSTRAINT FK_Holidays_Company FOREIGN KEY (CompanyId) REFERENCES Companies(Id)
);
```

---

## 4. جداول الرواتب والمالية
### Payroll and Financial Tables

### 4.1 جداول مكونات الراتب
```sql
-- جدول عناصر الراتب
CREATE TABLE SalaryComponents (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    CompanyId UNIQUEIDENTIFIER NOT NULL,
    Name NVARCHAR(200) NOT NULL,
    NameAr NVARCHAR(200) NOT NULL,
    Description NVARCHAR(500),
    DescriptionAr NVARCHAR(500),
    Type NVARCHAR(20) NOT NULL, -- Basic, Allowance, Deduction, Tax, Insurance
    Category NVARCHAR(50), -- Housing, Transport, Medical, etc.
    IsPercentage BIT DEFAULT 0,
    Amount DECIMAL(18,2),
    Percentage DECIMAL(5,2),
    MinAmount DECIMAL(18,2),
    MaxAmount DECIMAL(18,2),
    IsTaxable BIT DEFAULT 1,
    IsInsurable BIT DEFAULT 1, -- Subject to social insurance
    CalculationFormula NVARCHAR(1000), -- For complex calculations
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    CONSTRAINT FK_SalaryComponents_Company FOREIGN KEY (CompanyId) REFERENCES Companies(Id)
);

-- جدول رواتب الموظفين
CREATE TABLE EmployeeSalaries (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    EmployeeId UNIQUEIDENTIFIER NOT NULL,
    SalaryComponentId UNIQUEIDENTIFIER NOT NULL,
    Amount DECIMAL(18,2) NOT NULL,
    Percentage DECIMAL(5,2),
    EffectiveDate DATE NOT NULL,
    EndDate DATE,
    IsActive BIT DEFAULT 1,
    Notes NVARCHAR(500),
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    
    CONSTRAINT FK_EmployeeSalaries_Employee FOREIGN KEY (EmployeeId) REFERENCES Employees(Id),
    CONSTRAINT FK_EmployeeSalaries_Component FOREIGN KEY (SalaryComponentId) REFERENCES SalaryComponents(Id),
    CONSTRAINT FK_EmployeeSalaries_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id)
);
```

### 4.2 جداول كشوف الرواتب
```sql
-- جدول كشوف الرواتب الشهرية
CREATE TABLE Payrolls (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    CompanyId UNIQUEIDENTIFIER NOT NULL,
    EmployeeId UNIQUEIDENTIFIER NOT NULL,
    PayrollMonth INT NOT NULL,
    PayrollYear INT NOT NULL,
    
    -- Salary Breakdown
    BasicSalary DECIMAL(18,2) DEFAULT 0,
    TotalAllowances DECIMAL(18,2) DEFAULT 0,
    TotalDeductions DECIMAL(18,2) DEFAULT 0,
    GrossSalary DECIMAL(18,2) DEFAULT 0,
    
    -- Taxes and Insurance
    IncomeTax DECIMAL(18,2) DEFAULT 0,
    SocialInsuranceEmployee DECIMAL(18,2) DEFAULT 0,
    SocialInsuranceEmployer DECIMAL(18,2) DEFAULT 0,
    MedicalInsurance DECIMAL(18,2) DEFAULT 0,
    
    -- Attendance Related
    WorkingDays INT DEFAULT 0,
    ActualWorkingDays DECIMAL(4,2) DEFAULT 0,
    AbsentDays DECIMAL(4,2) DEFAULT 0,
    LeaveDays DECIMAL(4,2) DEFAULT 0,
    OvertimeHours DECIMAL(4,2) DEFAULT 0,
    OvertimeAmount DECIMAL(18,2) DEFAULT 0,
    LateDeduction DECIMAL(18,2) DEFAULT 0,
    
    -- Final Amount
    NetSalary DECIMAL(18,2) DEFAULT 0,
    
    -- Payment Information
    Status NVARCHAR(20) DEFAULT 'Draft', -- Draft, Approved, Paid, Cancelled
    PaymentDate DATE,
    PaymentMethod NVARCHAR(50), -- Bank, Cash, Check
    BankTransferReference NVARCHAR(100),
    
    -- Approval
    ApprovedBy UNIQUEIDENTIFIER,
    ApprovedAt DATETIME2,
    
    -- Additional Information
    Notes NVARCHAR(1000),
    PayslipGenerated BIT DEFAULT 0,
    PayslipPath NVARCHAR(500),
    
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    
    CONSTRAINT FK_Payrolls_Company FOREIGN KEY (CompanyId) REFERENCES Companies(Id),
    CONSTRAINT FK_Payrolls_Employee FOREIGN KEY (EmployeeId) REFERENCES Employees(Id),
    CONSTRAINT FK_Payrolls_ApprovedBy FOREIGN KEY (ApprovedBy) REFERENCES Users(Id),
    CONSTRAINT FK_Payrolls_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    CONSTRAINT UQ_Payrolls_EmployeeMonthYear UNIQUE (EmployeeId, PayrollMonth, PayrollYear)
);

-- جدول تفاصيل كشف الراتب
CREATE TABLE PayrollDetails (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    PayrollId UNIQUEIDENTIFIER NOT NULL,
    SalaryComponentId UNIQUEIDENTIFIER NOT NULL,
    Amount DECIMAL(18,2) NOT NULL,
    Quantity DECIMAL(8,2) DEFAULT 1,
    Rate DECIMAL(18,2),
    Description NVARCHAR(500),
    DescriptionAr NVARCHAR(500),
    
    CONSTRAINT FK_PayrollDetails_Payroll FOREIGN KEY (PayrollId) REFERENCES Payrolls(Id) ON DELETE CASCADE,
    CONSTRAINT FK_PayrollDetails_Component FOREIGN KEY (SalaryComponentId) REFERENCES SalaryComponents(Id)