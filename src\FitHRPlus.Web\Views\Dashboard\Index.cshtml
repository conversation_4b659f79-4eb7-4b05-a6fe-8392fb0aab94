@model FitHRPlus.Web.Controllers.AdminDashboardViewModel
@{
    ViewData["Title"] = "لوحة التحكم الرئيسية";
}

<!-- Stats Cards -->
<div class="row">
    <div class="col-md-3">
        <div class="stats-card card-blue">
            <i class="bi bi-people text-primary"></i>
            <h4>@(Model?.Statistics?.TotalEmployees ?? 1248)</h4>
            <p>عدد الموظفين</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card card-green">
            <i class="bi bi-check-circle text-success"></i>
            <h4>@(Model?.Statistics?.PresentToday ?? 92)%</h4>
            <p>معدل الحضور</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card card-orange">
            <i class="bi bi-calendar-x text-warning"></i>
            <h4>@(Model?.Statistics?.PendingLeaveRequests ?? 42)</h4>
            <p>طلبات الإجازة</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card card-red">
            <i class="bi bi-exclamation-triangle text-danger"></i>
            <h4>@(Model?.Statistics?.PendingNotifications ?? 8)</h4>
            <p>تنبيهات مهمة</p>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="row">
    <div class="col-md-8">
        <div class="chart-container">
            <div class="chart-header">
                <h5>تحليل الحضور الشهري</h5>
            </div>
            <div class="text-center">
                <canvas id="monthlyAttendanceChart" style="height: 300px;"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="chart-container">
            <div class="chart-header">
                <h5>توزيع الموظفين حسب الأقسام</h5>
            </div>
            <div class="text-center">
                <canvas id="departmentChart" style="height: 300px;"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="chart-container">
    <div class="chart-header">
        <h5>الإجراءات السريعة</h5>
    </div>
    <div class="quick-actions">
        <div class="action-card" onclick="location.href='@Url.Action("Create", "Employees")'">
            <i class="bi bi-person-plus text-primary"></i>
            <p>إضافة موظف</p>
        </div>
        <div class="action-card" onclick="location.href='@Url.Action("Generate", "Payroll")'">
            <i class="bi bi-currency-dollar text-success"></i>
            <p>حساب الرواتب</p>
        </div>
        <div class="action-card" onclick="location.href='@Url.Action("Create", "Leave")'">
            <i class="bi bi-calendar-check text-info"></i>
            <p>طلب إجازة</p>
        </div>
        <div class="action-card" onclick="location.href='@Url.Action("DailyReport", "Attendance")'">
            <i class="bi bi-file-earmark-text text-warning"></i>
            <p>تقرير الحضور</p>
        </div>
        <div class="action-card" onclick="location.href='@Url.Action("Index", "Notifications")'">
            <i class="bi bi-bell text-danger"></i>
            <p>الإشعارات</p>
        </div>
        <div class="action-card" onclick="location.href='@Url.Action("Index", "CompanySettings")'">
            <i class="bi bi-gear text-secondary"></i>
            <p>إعدادات النظام</p>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row">
    <div class="col-md-6">
        <div class="chart-container">
            <div class="chart-header">
                <h5>الأنشطة الحديثة</h5>
            </div>
            <div class="list-group list-group-flush">
                @if (Model?.RecentActivities?.Any() == true)
                {
                    @foreach (var activity in Model.RecentActivities.Take(5))
                    {
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="bi <EMAIL> <EMAIL> me-2"></i>
                                @activity.Description
                            </div>
                            <small class="text-muted">@activity.CreatedAt.ToString("HH:mm")</small>
                        </div>
                    }
                }
                else
                {
                    <div class="list-group-item text-center text-muted">
                        <i class="bi bi-info-circle me-2"></i>
                        لا توجد أنشطة حديثة
                    </div>
                }
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="chart-container">
            <div class="chart-header">
                <h5>طلبات الإجازات المعلقة</h5>
            </div>
            <div class="list-group list-group-flush">
                @if (Model?.PendingLeaveRequests?.Any() == true)
                {
                    @foreach (var request in Model.PendingLeaveRequests.Take(5))
                    {
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>@request.EmployeeName</strong>
                                <br>
                                <small class="text-muted">@request.LeaveType - @request.StartDate.ToString("dd/MM/yyyy")</small>
                            </div>
                            <div>
                                <a href="@Url.Action("Details", "Leave", new { id = request.Id })" class="btn btn-sm btn-outline-primary">
                                    عرض
                                </a>
                            </div>
                        </div>
                    }
                }
                else
                {
                    <div class="list-group-item text-center text-muted">
                        <i class="bi bi-check-circle me-2"></i>
                        لا توجد طلبات معلقة
                    </div>
                }
            </div>
        </div>
    </div>
</div>


                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Absent Today -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            الغائبين اليوم
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.Statistics.AbsentToday</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Requests -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            الطلبات المعلقة
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.Statistics.PendingRequests</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Recent Activities -->
<div class="row">
    <!-- Attendance Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    معدل الحضور
                </h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow">
                        <a class="dropdown-item" href="#">
                            تفاصيل أكثر
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="attendanceChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    الأنشطة الحديثة
                </h6>
            </div>
            <div class="card-body">
                <div id="recentActivities">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin"></i>
                        جاري التحميل...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    الإجراءات السريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="@Url.Action("Create", "Employees")" class="btn btn-outline-primary btn-block">
                            <i class="fas fa-user-plus"></i><br>
                            إضافة موظف
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="@Url.Action("Index", "Attendance")" class="btn btn-outline-success btn-block">
                            <i class="fas fa-clock"></i><br>
                            تسجيل حضور
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="@Url.Action("Create", "Leave")" class="btn btn-outline-warning btn-block">
                            <i class="fas fa-calendar-alt"></i><br>
                            طلب إجازة
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="@Url.Action("Index", "Reports")" class="btn btn-outline-info btn-block">
                            <i class="fas fa-chart-bar"></i><br>
                            عرض التقارير
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        $(document).ready(function() {
            loadMonthlyAttendanceChart();
            loadAttendanceChart();
            loadDepartmentChart();
            loadRecentActivities();

            // Refresh data every 5 minutes
            setInterval(function() {
                loadRecentActivities();
            }, 300000);
        });

        function loadMonthlyAttendanceChart() {
            const ctx = document.getElementById('monthlyAttendanceChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'عدد أيام الحضور',
                        data: [22, 20, 23, 21, 22, 20],
                        backgroundColor: 'rgba(30, 58, 138, 0.8)',
                        borderColor: '#1e3a8a',
                        borderWidth: 1,
                        borderRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        function loadAttendanceChart() {
            const ctx = document.getElementById('attendanceChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'معدل الحضور %',
                        data: [85, 89, 92, 88, 94, 92],
                        borderColor: '#1e3a8a',
                        backgroundColor: 'rgba(30, 58, 138, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        function loadDepartmentChart() {
            const ctx = document.getElementById('departmentChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['تكنولوجيا المعلومات', 'الموارد البشرية', 'المالية', 'التسويق', 'المبيعات'],
                    datasets: [{
                        data: [30, 20, 15, 20, 15],
                        backgroundColor: [
                            '#1e3a8a',
                            '#22c55e',
                            '#f59e0b',
                            '#ef4444',
                            '#8b5cf6'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: {
                                    family: 'Tajawal'
                                },
                                padding: 20
                            }
                        }
                    }
                }
            });
        }

        function loadRecentActivities() {
            $.get('@Url.Action("GetRecentActivities", "Dashboard")', function(data) {
                let html = '';
                data.forEach(function(activity) {
                    const message = activity.messageAr || activity.message;
                    const timeAgo = moment(activity.timestamp).fromNow();

                    html += `
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-circle bg-${activity.color} text-white mr-3">
                                <i class="${activity.icon}"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="small text-gray-500">${timeAgo}</div>
                                <div>${message}</div>
                            </div>
                        </div>
                    `;
                });

                $('#recentActivities').html(html);
            });
        }
    </script>
}
